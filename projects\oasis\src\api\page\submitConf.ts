import type { BasicAddResult, BasicBatchSortParams, NullableBasicResult } from '../model/baseModel';
import type { RegexCustomPath } from './model/p4Model';
import type {
  BatchTagParams,
  DiffTriggerWebhookGetResultModel,
  PerforcesItemParams,
  PerforcesListGetResultModel,
  PerforcesListItem,
  PerforcesPageParams,
  PreviewTriggerWebhookGetResultModel,
  TagItemParams,
  TagListGetResultModel,
  TagListItem,
  TagPageParams,
  TriggerArgItemParams,
  TriggerArgListGetResultModel,
  TriggerArgListItem,
  TriggerArgPageParams,
  TriggerTypeItemParams,
  TriggerTypeListGetResultModel,
  TriggerTypeListItem,
  TriggerTypePageParams,
  TriggerWebhookItemParams,
  TriggerWebhookListGetResultModel,
  TriggerWebhookListItem,
  TriggerWebhookPageParams,
} from './model/submitConfModel';
import type { SuccessMessageMode } from '/#/axios';
import { defHttp } from '/@/utils/http/axios';

enum Api {
  Projects = '/api/v1/projects',
  Tags = '/tags',
  TriggerTypes = '/api/v1/trigger/types',
  TriggerArgs = '/api/v1/trigger/args',
  TriggerWebhooks = '/trigger/webhooks',
  AllTriggerWebhooks = '/api/v1/trigger/webhooks',
  Perforces = '/api/v1/trigger/perforces',
}

/**
 * 获取TAG列表
 * @param projectID 项目id
 * @param params 筛选条件
 */
export function getTagListByPage(projectID: number, params?: TagPageParams) {
  return defHttp.get<TagListGetResultModel>({
    url: `${Api.Projects}/${projectID}${Api.Tags}`,
    params,
  });
}

/**
 * 根据id获取TAG信息
 * @param projectID 项目id
 * @param ID TAGid
 */
export function getTagByID(projectID: number, ID: string) {
  return defHttp.get<TagItemParams>({ url: `${Api.Projects}/${projectID}${Api.Tags}/${ID}` });
}

/**
 * 新增TAG
 * @param projectID 项目id
 * @param data TAG数据
 */
export function addTag(projectID: number, data: TagListItem) {
  return defHttp.post<BasicAddResult>({ url: `${Api.Projects}/${projectID}${Api.Tags}`, data });
}

/**
 * 编辑TAG
 * @param projectID 项目id
 * @param data TAG数据
 * @param editId TAGid
 */
export function editTag(projectID: number, data: TagListItem, editId: number) {
  return defHttp.put<null>({ url: `${Api.Projects}/${projectID}${Api.Tags}/${editId}`, data });
}

/**
 * 删除TAG
 * @param projectID 项目id
 * @param editId TAGid
 */
export function deleteTag(projectID: number, editId: number) {
  return defHttp.delete<NullableBasicResult>(
    { url: `${Api.Projects}/${projectID}${Api.Tags}/${editId}` },

  );
}

/**
 * 批量更新项目Tag排序
 * @param projectID 项目id
 * @param data 新tag列表
 */
export function batchTagSort(projectID: number, data: BatchTagParams) {
  return defHttp.put<null>({ url: `${Api.Projects}/${projectID}${Api.Tags}/sort`, data });
}

/**
 * 获取Trigger事件类型列表
 * @param params 筛选条件
 */
export function getTriggerTypeListByPage(params?: TriggerTypePageParams) {
  return defHttp.get<TriggerTypeListGetResultModel>({
    url: Api.TriggerTypes,
    params,
  });
}

/**
 * 根据id获取Trigger事件类型信息
 * @param ID Trigger事件类型id
 */
export function getTriggerTypeByID(ID: string) {
  return defHttp.get<TriggerTypeItemParams>({
    url: `${Api.TriggerTypes}/${ID}`,
  });
}

/**
 * 新增Trigger事件类型
 * @param data Trigger事件类型数据
 */
export function addTriggerType(data: TriggerTypeListItem) {
  return defHttp.post<null>({ url: `${Api.TriggerTypes}`, data });
}

/**
 * 编辑Trigger事件类型
 * @param data Trigger事件类型数据
 * @param editId Trigger事件类型id
 */
export function editTriggerType(data: TriggerTypeListItem, editId: number) {
  return defHttp.put<null>({ url: `${Api.TriggerTypes}/${editId}`, data });
}

/**
 * 删除Trigger事件类型
 * @param editId Trigger事件类型id
 */
export function deleteTriggerType(editId: number) {
  return defHttp.delete<null>({ url: `${Api.TriggerTypes}/${editId}` });
}

/**
 * 获取Trigger可用参数类型列表
 * @param params 筛选条件
 */
export function getTriggerArgListByPage(params?: TriggerArgPageParams) {
  return defHttp.get<TriggerArgListGetResultModel>({
    url: Api.TriggerArgs,
    params,
  });
}

/**
 * 根据id获取Trigger可用参数类型信息
 * @param ID Trigger可用参数类型id
 */
export function getTriggerArgByID(ID: string) {
  return defHttp.get<TriggerArgItemParams>({
    url: `${Api.TriggerArgs}/${ID}`,
  });
}

/**
 * 新增Trigger可用参数类型
 * @param data Trigger可用参数类型数据
 */
export function addTriggerArg(data: TriggerArgListItem) {
  return defHttp.post<null>({ url: `${Api.TriggerArgs}`, data });
}

/**
 * 编辑Trigger可用参数类型
 * @param data Trigger可用参数类型数据
 * @param editId Trigger可用参数类型id
 */
export function editTriggerArg(data: TriggerArgListItem, editId: number) {
  return defHttp.put<null>({ url: `${Api.TriggerArgs}/${editId}`, data });
}

/**
 * 删除Trigger可用参数类型
 * @param editId Trigger可用参数类型id
 */
export function deleteTriggerArg(editId: number) {
  return defHttp.delete<null>({ url: `${Api.TriggerArgs}/${editId}` });
}

/**
 * 获取项目网络钩子列表
 * @param projectID 项目id
 * @param params 筛选条件
 */
export function getTriggerWebhookListByPage(projectID: number, params?: TriggerWebhookPageParams) {
  return defHttp.get<TriggerWebhookListGetResultModel>({
    url: `${Api.Projects}/${projectID}${Api.TriggerWebhooks}`,
    params,
  });
}

/**
 * 获取所有网络钩子列表
 * @param params 筛选条件
 */
export function getAllTriggerWebhookListByPage(params?: TriggerWebhookPageParams) {
  return defHttp.get<TriggerWebhookListGetResultModel>({
    url: Api.AllTriggerWebhooks,
    params,
  });
}

/**
 * 根据id获取TriggerWebhook信息
 * @param projectID 项目id
 * @param ID 网络钩子id
 */
export function getTriggerWebhookByID(projectID: number, ID: string) {
  return defHttp.get<TriggerWebhookItemParams>({
    url: `${Api.Projects}/${projectID}${Api.TriggerWebhooks}/${ID}`,
  });
}

/**
 * 新增网络钩子
 * @param projectID 项目id
 * @param data 网络钩子数据
 */
export function addTriggerWebhook(projectID: number, data: TriggerWebhookListItem) {
  return defHttp.post<BasicAddResult>({ url: `${Api.Projects}/${projectID}${Api.TriggerWebhooks}`, data });
}

/**
 * 编辑网络钩子
 * @param projectID 项目id
 * @param data 网络钩子数据
 * @param editId 网络钩子id
 */
export function editTriggerWebhook(projectID: number, data: TriggerWebhookListItem, editId: number) {
  return defHttp.put<NullableBasicResult>({
    url: `${Api.Projects}/${projectID}${Api.TriggerWebhooks}/${editId}`,
    data,
  });
}

/**
 * 删除网络钩子
 * @param projectID 项目id
 * @param editId 网络钩子id
 */
export function deleteTriggerWebhook(projectID: number, editId: number) {
  return defHttp.delete<NullableBasicResult>({
    url: `${Api.Projects}/${projectID}${Api.TriggerWebhooks}/${editId}`,
  });
}

/**
 * 排序网络钩子
 * @param projectID 项目id
 * @param data 排序ids
 */
export function batchTriggerWebhookSort(projectID: number, data: BasicBatchSortParams & TriggerWebhookListItem) {
  return defHttp.put<null>({ url: `${Api.Projects}/${projectID}${Api.TriggerWebhooks}/sort`, data });
}

/**
 * 测试网络钩子
 * @param projectID 项目id
 * @param data 网络钩子数据
 */
export function testTriggerWebhook(projectID: number, data: TriggerWebhookListItem) {
  return defHttp.post<NullableBasicResult>({
    url: `${Api.Projects}/${projectID}${Api.TriggerWebhooks}/test`,
    data,
  });
}

/**
 * 生效所有网络钩子前的预览
 */
export function effectPreviewAllTriggerWebhook(serverID?: number) {
  return defHttp.get<PreviewTriggerWebhookGetResultModel>({
    url: `${Api.AllTriggerWebhooks}/preview`,
    params: {
      serverID,
    },
  });
}

/**
 * 生效所有网络钩子前的对比
 */
export function effectDiffAllTriggerWebhook(serverID?: number) {
  return defHttp.get<DiffTriggerWebhookGetResultModel>({
    url: `${Api.AllTriggerWebhooks}/diff`,
    params: {
      serverID,
    },
  });
}

/**
 * 生效所有网络钩子
 */
export function effectAllTriggerWebhook(serverID?: number, successMessageMode: SuccessMessageMode = 'message') {
  return defHttp.post<TriggerWebhookListGetResultModel>(
    {
      url: `${Api.AllTriggerWebhooks}/effect`,
      params: {
        serverID,
      },
    },
    {
      successMessageMode,
    },
  );
}

/**
 * 获取p4超管用户列表
 * @param params 筛选条件
 */
export function getPerforcesListByPage(params?: PerforcesPageParams) {
  return defHttp.get<PerforcesListGetResultModel>({
    url: `${Api.Perforces}`,
    params,
  });
}

/**
 * 根据id获取p4超管用户信息
 * @param ID p4超管用户id
 */
export function getPerforceByID(ID: string) {
  return defHttp.get<PerforcesItemParams>({ url: `${Api.Perforces}/${ID}` });
}

/**
 * 新增p4超管用户
 * @param data p4超管用户数据
 */
export function addPerforce(data: PerforcesListItem) {
  return defHttp.post<null>({ url: `${Api.Perforces}`, data });
}

/**
 * 编辑p4超管用户
 * @param data p4超管用户数据
 * @param editId p4超管用户id
 */
export function editPerforce(data: PerforcesListItem, editId: number) {
  return defHttp.put<null>({ url: `${Api.Perforces}/${editId}`, data });
}

/**
 * 删除p4超管用户
 * @param editId p4超管用户id
 */
export function deletePerforce(editId: number) {
  return defHttp.delete<null>({ url: `${Api.Perforces}/${editId}` });
}

/**
 * 查询标签自动勾选路径配置
 * @param id 项目id
 * @param tagId 标签id
 */
export function getTagPathConfig(id: number, tagId: number) {
  return defHttp.get<null>({ url: `${Api.Projects}/${id}/tags/${tagId}/path` });
}
/**
 * 新增标签自动勾选路径配置
 * @param id 项目id
 * @param tagId 标签id
 * @param data 路径配置数据
 * @param data.tagPathItems 路径配置数据
 */
export function editTagPathConfig(id: number, tagId: number, data: { tagPathItems: RegexCustomPath[] }) {
  return defHttp.put<null>({ url: `${Api.Projects}/${id}/tags/${tagId}/path`, data });
}
