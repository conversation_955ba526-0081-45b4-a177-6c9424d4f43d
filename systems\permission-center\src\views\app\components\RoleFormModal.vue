<template>
  <Modal
    :open="show" :title="title" :centered="true" :maskClosable="false" :destroyOnClose="true" :width="496"
    :afterClose="modalDestroy" :bodyStyle="{ padding: '20px 0 0 0', marginBottom: '36px' }"
    @cancel="() => modalCancel()"
  >
    <Form :labelCol="{ style: { width: '96px' } }">
      <FormItem label="角色名称" v-bind="validateInfos.name">
        <Input v-model:value="formValue.name" placeholder="请输入角色名称" :maxlength="10" showCount />
      </FormItem>

      <FormItem v-if="!isEdit" label="复制角色权限" name="fromGroupId">
        <div class="flex items-center gap-8px">
          <Select
            v-if="isMultiTenant" v-model:value="formValue.copyFromTenantId" placeholder="请选择项目" allowClear
            showSearch :options="tenantList" optionFilterProp="tenant" :fieldNames="{ label: 'tenant', value: 'id' }"
            @change="handleTenantChange"
          />

          <Select
            v-model:value="formValue.fromGroupId" placeholder="请选择角色" allowClear showSearch
            :disabled="!formValue.copyFromTenantId && !!isMultiTenant" :loading="loadingTenantGroups"
            :options="tenantGroups" optionFilterProp="name" :fieldNames="{ label: 'name', value: 'id' }"
          />
        </div>
        <div class="FO-Font-R12 mt-8px c-FO-Content-Text3">
          选择已有角色权限点，便于快速配置新角色
        </div>
      </FormItem>
    </Form>
    <Alert v-if="isCopy" message="该操作将复制角色的权限及当前时间下的所属成员" type="info" :showIcon="true" class="b-none" />
    <template #footer>
      <div class="flex justify-end gap-4px">
        <Button class="btn-fill-default w-100px" @click="() => modalCancel()">
          取消
        </Button>
        <Button class="btn-fill-primary w-100px" :loading="submitting" @click="() => handleConfirm()">
          保存
        </Button>
      </div>
    </template>
  </Modal>
</template>

<script setup lang="ts">
import { Alert, Button, Form, FormItem, Input, Modal, Select } from 'ant-design-vue';
import { computed, ref, watch } from 'vue';
import { getPermissionAppGroups } from '../../../api/group.ts';
import type { PermissionAppGroupDetail, PermissionAppGroupListItem, PermissionGroupForm } from '../../../api/group.ts';
import type { PermissionAppInfo, PermissionTenantInfo } from '../../../api/app.ts';
import type { ModalBaseProps } from '@hg-tech/utils-vue';
import { useLatestPromise } from '@hg-tech/utils-vue';
import type { RuleObject } from 'ant-design-vue/es/form/interface';

type RoleFormValue = Pick<PermissionGroupForm, 'name'> & {
  fromGroupId?: number;
  copyFromTenantId?: number;
};

const props = defineProps<ModalBaseProps<{ updatedGroup?: PermissionAppGroupDetail }> & {
  appInfo?: PermissionAppInfo;
  tenantId?: PermissionTenantInfo['id'];
  allGroups?: PermissionAppGroupListItem[];
  tenantList?: PermissionTenantInfo[];
  editGroup?: PermissionAppGroupDetail;
  isCopy?: boolean;
  title?: string;
  sentReq?: (formValue: RoleFormValue) => Promise<PermissionAppGroupDetail | undefined>;
}>();

const submitting = ref(false);
const { data: tenantGroupsData, execute: fetchGroupsData, loading: loadingTenantGroups } = useLatestPromise(getPermissionAppGroups);
const tenantGroups = computed(() => tenantGroupsData.value?.data?.data || []);

const isEdit = computed(() => !!props.editGroup);
const isMultiTenant = computed(() => props.appInfo?.isMultiTenant);

const formValue = ref<RoleFormValue>({
  name: '',
  fromGroupId: undefined,
  copyFromTenantId: undefined,
  ...props.editGroup,
});

const formRule = computed<Record<keyof Pick<RoleFormValue, 'name'>, RuleObject[]>>(() => ({
  name: [
    { required: true, message: '请输入角色名称' },
    { max: 10, message: '角色名称不能超过10个字符' },
  ],
}));

const { validate, validateInfos, resetFields } = Form.useForm(formValue, formRule);

// 监听弹窗打开状态，重置表单
watch(() => props.show, () => {
  if (props.show) {
    resetFields();
    if (!isMultiTenant.value) {
      fetchGroupsData({ appId: props.appInfo?.id }, {});
    }
  }
}, { immediate: true });

async function handleTenantChange(value: any) {
  const tenantId = value as number;
  formValue.value.fromGroupId = undefined;

  if (tenantId && props.appInfo?.id) {
    await fetchGroupsData({ appId: props.appInfo.id, tenantId }, {});
  }
}

async function handleConfirm() {
  try {
    submitting.value = true;
    await validate();
    const updatedGroup = await props.sentReq?.(formValue.value);
    if (updatedGroup) {
      return props.modalConfirm({ updatedGroup });
    }
  } catch (error) {
    console.error('保存失败:', error);
  } finally {
    submitting.value = false;
  }
}
</script>
