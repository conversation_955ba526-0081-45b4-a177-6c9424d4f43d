<template>
  <BasicModal
    title="设备检测"
    :width="800"
    destroyOnClose
    :footer="null"
    :maskClosable="false"
    :afterClose="handleClose"
    wrapClassName="relative"
    @register="registerModal"
  >
    <div class="m-4 flex items-center justify-between gap-6 b-b-1 b-b-FO-Container-Stroke1 p-4">
      <div class="flex items-center gap-6">
        <Icon :icon="isChecking ? 'loading|svg' : (hasException ? 'ant-design:warning-filled' : 'ant-design:smile-outlined')" :class="!isChecking && hasException ? 'c-FO-Functional-Warning1-Default' : 'c-FO-Brand-Primary-Default'" spin :size="50" />
        <div>
          <div class="text-xl">
            {{ isChecking ? '检测中' : '检测完成' }}
          </div>
          <div class="c-FO-Content-Text2">
            <template v-if="isChecking">
              正在获取IT平台的数据并检测异常信息
            </template>
            <template v-else>
              共检测出<span class="c-FO-Functional-Error1-Default">{{ exceptionCount }}</span>项异常
            </template>
          </div>
        </div>
      </div>
      <div>
        <a-button v-if="!isChecking" type="primary" @click="() => handleCheck()">
          重新检测
        </a-button>
      </div>
    </div>
    <div class="m-4 max-h-70vh min-h-400px w-full flex c-FO-Content-Text2">
      <div v-if="isChecking" class="w-full flex items-center justify-center">
        检测中，请稍后
      </div>
      <div v-else-if="hasException" class="w-full flex flex-col gap-4">
        <ACollapse ghost :defaultActiveKey="activeKeys">
          <ACollapsePanel
            v-for="info in showExceptionInfo"
            :key="info.key"
          >
            <template #header>
              <div class="flex items-center justify-between gap-2">
                <span class="text-16px font-bold lh-32px">{{ exceptionMap[info.key] }}（{{ info.list?.length || 0 }}）</span>
                <a-button v-if="canUpdate(info.key)" type="link" class="px-1" :disabled="!canAllSelected(info.key)" @click.stop="() => handleSelectAll(info.key)">
                  全选
                </a-button>
              </div>
            </template>

            <div v-if="!isNoRegistered(info.key)" class="flex flex-col" :class="{ 'gap-4': canUpdate(info.key) }">
              <div v-for="item in info.list" :key="item.deviceID || item.barcode" class="group">
                <div class="w-full flex items-center justify-between gap-2">
                  <div class="flex items-end gap-2">
                    <ACheckbox v-if="canUpdate(info.key) && !item.isUpdated" v-model:checked="item.isSelected" :class="{ 'opacity-0 group-hover:opacity-100': !item.isSelected }" />
                    <EllipsisText class="!max-w-200px">
                      {{ item.deviceName }}
                    </EllipsisText>
                    <div class="text-12px c-FO-Content-Text2">
                      <template v-if="!canUpdate(info.key)">
                        {{ item.assetNo || '无' }}
                      </template>
                      <template v-else>
                        {{ item.barcode || '无' }}
                      </template>
                    </div>
                  </div>
                  <div class="flex items-center">
                    <a-button
                      v-if="canUpdate(info.key) && !item.isUpdated"
                      v-tippy="item.updatedErrorMsg ? `${item.updatedErrorMsg}，点击重试` : undefined"
                      type="link"
                      class="px-1"
                      :loading="item.isUpdating"
                      :danger="!!item.updatedErrorMsg"
                      @click="() => handleUpdate(item, info.key)"
                    >
                      {{ !!item.updatedErrorMsg ? '更新失败' : '更新' }}
                    </a-button>
                    <span v-if="item.isUpdated" class="flex items-center gap-1 c-FO-Content-Text2">
                      <Icon icon="ant-design:check-outlined" />
                      已更新
                    </span>
                    <a-button type="link" class="px-1" @click="() => handleDetail(item.deviceID, info.key)">
                      详情
                    </a-button>
                  </div>
                </div>
                <div v-if="canUpdate(info.key)" class="flex flex-col gap-1 rd-2 bg-FO-Container-Fill2 p-4">
                  <div class="flex items-center gap-2">
                    <template v-if="Number(info.key) === CheckDeviceExceptionTypeEnum.DEVICE_NAME_MISMATCH">
                      设备名称:
                      <EllipsisText class="c-FO-Content-Text2 !w-260px">
                        {{ item.deviceName || '无' }}
                      </EllipsisText>
                      更新为:
                      <EllipsisText class="c-FO-Content-Text2 !w-260px">
                        {{ item.specs || '无' }}
                      </EllipsisText>
                    </template>
                    <template v-if="Number(info.key) === CheckDeviceExceptionTypeEnum.OWNER_MISMATCH">
                      所属人:
                      <EllipsisText class="c-FO-Content-Text2 !w-260px">
                        {{ getUserById(item.ownerID)?.displayName || '无' }}
                      </EllipsisText>
                      更新为:
                      <EllipsisText class="c-FO-Content-Text2 !w-260px">
                        {{ getUserById(item.itUserID)?.displayName || '无' }}
                      </EllipsisText>
                    </template>
                    <template v-if="Number(info.key) === CheckDeviceExceptionTypeEnum.DEPT_MISMATCH">
                      所属部门:
                      <EllipsisText class="c-FO-Content-Text2 !w-260px">
                        {{ formatDept(item.deptID) }}
                      </EllipsisText>
                      更新为:
                      <EllipsisText class="c-FO-Content-Text2 !w-260px">
                        {{ formatDept(item.itUseDeptID) }}
                      </EllipsisText>
                    </template>
                    <template v-if="Number(info.key) === CheckDeviceExceptionTypeEnum.USAGE_MISMATCH">
                      资产用途:
                      <EllipsisText class="c-FO-Content-Text2 !w-260px">
                        {{ item.usage?.label || '无' }}
                      </EllipsisText>
                      更新为:
                      <EllipsisText class="c-FO-Content-Text2 !w-260px">
                        {{ item.assetUsage?.label || '无' }}
                      </EllipsisText>
                    </template>
                  </div>
                </div>
              </div>
            </div>
            <div v-else class="flex flex-col gap-4">
              <div v-for="item in getUserGroupList(info.list)" :key="item.userID">
                <div class="w-full flex items-center justify-between gap-2">
                  <div class="flex items-end gap-2">
                    <template v-if="item.userID">
                      <EllipsisText class="!max-w-200px">
                        {{ getUserById(item.userID)?.displayName }}
                      </EllipsisText>
                    </template>
                    <span v-else>无所属人</span>
                    <span class="text-12px c-FO-Content-Text2">（{{ item.items?.length || 0 }}台）</span>
                  </div>
                  <div v-if="item.userID" class="flex items-center">
                    <a-button type="link" class="px-1" @click="() => handleUserClick(item.userID)">
                      联系对方
                    </a-button>
                  </div>
                </div>
                <div class="flex flex-col gap-1 rd-2 bg-FO-Container-Fill2 p-4">
                  <div v-for="e in item.items" :key="e.barcode" class="flex items-center gap-2">
                    设备名称:
                    <EllipsisText class="c-FO-Content-Text2 !w-260px">
                      {{ e.specs || '无' }}
                    </EllipsisText>
                    资产编号:
                    <EllipsisText class="text-12px c-FO-Content-Text2 !w-260px">
                      {{ e.barcode || '无资产编号' }}
                    </EllipsisText>
                  </div>
                </div>
              </div>
            </div>
          </ACollapsePanel>
        </ACollapse>
      </div>
      <div v-else class="w-full flex items-center justify-center">
        暂无异常
      </div>
    </div>
    <div v-if="selectedCount" class="absolute bottom-0 left-1/2 m-4 w-fit flex items-center justify-center gap-4 rd-2 bg-black/90 px-4 py-2 -translate-x-1/2">
      <div class="c-FO-Content-Components1">
        已选中{{ selectedCount }}项
      </div>
      <a-button type="text" class="!bg-#424242 !c-FO-Content-Components1 !hover:(bg-opacity-80 c-FO-Content-Components1)" @click="() => handleBatchUpdate()">
        批量更新
      </a-button>
      <Icon icon="ant-design:close-outlined" class="cursor-pointer !c-FO-Content-Components1" @click="() => handleClearSelected()" />
    </div>
  </BasicModal>
</template>

<script lang="ts" setup>
import { computed, ref } from 'vue';
import { Checkbox as ACheckbox, Collapse as ACollapse, CollapsePanel as ACollapsePanel } from 'ant-design-vue';
import Icon from '/@/components/Icon';
import { type ModalMethods, BasicModal, useModalInner } from '/@/components/Modal';
import { checkDevice, updateDeviceField, updateDeviceFieldBatch } from '/@/api/page/deptAsset';
import { type CheckDeviceExceptionItem, type CheckDeviceExceptionMap, CheckDeviceExceptionTypeEnum } from '/@/api/page/model/deptAssetModel';
import { useDeptAssetApply } from '../hook';
import { findNode } from '/@/utils/helper/treeHelper';
import type { DeptListItem } from '/@/api/page/model/systemModel';
import { useMessage } from '/@/hooks/web/useMessage';
import { useRouter } from 'vue-router';

const emit = defineEmits<{
  success: [];
  register: [methods: ModalMethods, uuid: number];
}>();

const { createMessage } = useMessage();
const { handleUserClick, getUserById } = useDeptAssetApply();
const { resolve } = useRouter();

/** 是否正在检测 */
const isChecking = ref(false);
/** 部门列表 */
const deptList = ref<DeptListItem[]>([]);

/**
 * 异常信息
 */
const exceptionInfo = ref<CheckDeviceExceptionMap>({});

/**
 * 异常类型映射
 */
const exceptionMap = {
  [CheckDeviceExceptionTypeEnum.ASSET_NO_MISMATCH]: '资产编号不匹配',
  [CheckDeviceExceptionTypeEnum.USER_MISSING]: '借用人信息缺失',
  [CheckDeviceExceptionTypeEnum.DEVICE_NAME_MISMATCH]: '设备名称不一致',
  [CheckDeviceExceptionTypeEnum.OWNER_MISMATCH]: '归属人不一致',
  [CheckDeviceExceptionTypeEnum.DEPT_MISMATCH]: '归属部门不一致',
  [CheckDeviceExceptionTypeEnum.DEVICE_NOT_REGISTERED]: '未注册设备',
  [CheckDeviceExceptionTypeEnum.USAGE_MISMATCH]: '资产用途不一致',
};

const sortList = [
  CheckDeviceExceptionTypeEnum.ASSET_NO_MISMATCH,
  CheckDeviceExceptionTypeEnum.USAGE_MISMATCH,
  CheckDeviceExceptionTypeEnum.USER_MISSING,
  CheckDeviceExceptionTypeEnum.DEVICE_NAME_MISMATCH,
  CheckDeviceExceptionTypeEnum.OWNER_MISMATCH,
  CheckDeviceExceptionTypeEnum.DEPT_MISMATCH,
  CheckDeviceExceptionTypeEnum.DEVICE_NOT_REGISTERED,
];

/**
 * 展示异常信息
 */
const showExceptionInfo = computed(() => {
  const result: { key: CheckDeviceExceptionTypeEnum; list: CheckDeviceExceptionItem[] }[] = [];
  (Object.keys(exceptionInfo.value) as unknown as CheckDeviceExceptionTypeEnum[]).forEach((key) => {
    if (exceptionInfo.value[key]?.length) {
      result.push({ key, list: exceptionInfo.value[key] });
    }
  });

  return result.sort((a, b) => sortList.indexOf(Number(a.key)) - sortList.indexOf(Number(b.key)));
});

/** 是否存在异常 */
const hasException = computed(() => {
  return Object.values(exceptionInfo.value).some((item) => !!item?.length);
});

/**
 * 异常数量
 */
const exceptionCount = computed(() => {
  return Object.values(exceptionInfo.value).reduce((acc, item) => acc + (item?.length || 0), 0);
});

/**
 * 是否未注册设备
 * @param key 异常类型
 */
function isNoRegistered(key: CheckDeviceExceptionTypeEnum) {
  return Number(key) === CheckDeviceExceptionTypeEnum.DEVICE_NOT_REGISTERED;
}

/**
 * 是否可以更新(设备名称不一致、所属人不一致、部门不一致)
 * @param key 异常类型
 */
function canUpdate(key: CheckDeviceExceptionTypeEnum) {
  return [CheckDeviceExceptionTypeEnum.DEPT_MISMATCH, CheckDeviceExceptionTypeEnum.DEVICE_NAME_MISMATCH, CheckDeviceExceptionTypeEnum.OWNER_MISMATCH, CheckDeviceExceptionTypeEnum.USAGE_MISMATCH].includes(Number(key));
}

/** 检测设备 */
async function handleCheck() {
  isChecking.value = true;
  exceptionInfo.value = {};
  const { exceptions } = await checkDevice();
  exceptionInfo.value = exceptions;
  isChecking.value = false;
}

const [registerModal] = useModalInner(async (data) => {
  deptList.value = data.deptList || [];
  await handleCheck();
});

/** 获取异常类型 */
const activeKeys = computed(() => {
  if (!exceptionInfo.value) {
    return [];
  }
  return showExceptionInfo.value.map((item) => Number(item.key));
});

/**
 * 获取用户组列表
 * @param list 异常信息列表
 */
function getUserGroupList(list?: CheckDeviceExceptionItem[]) {
  if (!list) {
    return [];
  }
  return list.reduce((acc, item) => {
    const existingGroup = acc.find((group) => group.userID === item.itUserID);
    if (existingGroup) {
      existingGroup.items.push(item);
    } else {
      acc.push({
        userID: item.itUserID,
        items: [item],
      });
    }
    return acc;
  }, [] as { userID: number | undefined; items: CheckDeviceExceptionItem[] }[]).sort((a, b) => b.items.length - a.items.length);
}

/**
 * 获取部门名称
 * @param ID 部门ID
 */
function formatDept(ID?: number) {
  if (!ID) {
    return '(未录入部门)';
  }
  const findNodeItem = findNode(deptList.value, (n) => n.ID === ID);
  return findNodeItem?.orgPath?.replace('鹰角>', '') || '(未录入部门)';
}

/**
 * 详情
 * @param ID 设备ID
 */
async function handleDetail(ID?: number, key?: CheckDeviceExceptionTypeEnum) {
  if (!ID) {
    return;
  }
  const { fullPath } = resolve({
    name: Number(key) === CheckDeviceExceptionTypeEnum.USER_MISSING ? 'DeptAssetApplyManagement' : 'DeviceManagement',
    query: {
      editId: ID,
    },
  });
  window.open(fullPath, '_blank');
}

/**
 * 全选
 * @param key 异常类型
 */
function handleSelectAll(key: CheckDeviceExceptionTypeEnum) {
  exceptionInfo.value[key]?.forEach((item) => {
    if (!item.isUpdated) {
      item.isSelected = true;
    }
  });
}

/**
 * 是否可以全选
 * @param key 异常类型
 */
function canAllSelected(key: CheckDeviceExceptionTypeEnum) {
  return !exceptionInfo.value[key]?.every((item) => item.isSelected || item.isUpdated);
}

/** 选中的数量 */
const selectedCount = computed(() => {
  return (Object.keys(exceptionInfo.value) as unknown as CheckDeviceExceptionTypeEnum[]).reduce((acc, key) => {
    return acc + (exceptionInfo.value[key]?.filter((item) => !item.isUpdated && item.isSelected).length || 0);
  }, 0);
});

/** 清除选中 */
function handleClearSelected() {
  (Object.keys(exceptionInfo.value) as unknown as CheckDeviceExceptionTypeEnum[]).forEach((key) => {
    exceptionInfo.value[key]?.forEach((item) => {
      item.isSelected = false;
    });
  });
}

/**
 * 更新
 * @param item 异常信息
 * @param key 异常类型
 */
async function handleUpdate(item: CheckDeviceExceptionItem, key: CheckDeviceExceptionTypeEnum) {
  if (!item.deviceID) {
    return;
  }
  try {
    item.isUpdating = true;
    const curKey = Number(key);
    const res = await updateDeviceField(item.deviceID, {
      exceptionType: curKey,
      deviceName: curKey === CheckDeviceExceptionTypeEnum.DEVICE_NAME_MISMATCH ? item.specs : undefined,
      ownerID: curKey === CheckDeviceExceptionTypeEnum.OWNER_MISMATCH ? item.itUserID : undefined,
      deptID: curKey === CheckDeviceExceptionTypeEnum.DEPT_MISMATCH ? item.itUseDeptID : undefined,
      usageID: curKey === CheckDeviceExceptionTypeEnum.USAGE_MISMATCH ? item.assetUsage?.id : undefined,
    });
    if (res?.code === 7) {
      item.updatedErrorMsg = res.msg;
      createMessage.error('更新失败');
    } else {
      if (res?.isUpdateAccessLevel) {
        createMessage.success('已同步更新流通级别');
      }
      item.updatedErrorMsg = '';
      item.isUpdated = true;

      createMessage.success('已更新');
    }
  } finally {
    item.isUpdating = false;
  }
}

/** 批量更新 */
async function handleBatchUpdate() {
  // 获取所有选中的数据
  const updateList = (Object.keys(exceptionInfo.value) as unknown as CheckDeviceExceptionTypeEnum[])
    .reduce((acc, key) => {
      const items = exceptionInfo.value[key]?.filter(
        (item) => !item.isUpdated && item.isSelected && item.deviceID,
      ) || [];
      return [
        ...acc,
        ...items.map((item) => ({
          deviceID: item.deviceID!,
          exceptionType: Number(key),
          deviceName: Number(key) === CheckDeviceExceptionTypeEnum.DEVICE_NAME_MISMATCH
            ? item.specs
            : undefined,
          ownerID: Number(key) === CheckDeviceExceptionTypeEnum.OWNER_MISMATCH
            ? item.itUserID
            : undefined,
          deptID: Number(key) === CheckDeviceExceptionTypeEnum.DEPT_MISMATCH
            ? item.itUseDeptID
            : undefined,
          usageID: Number(key) === CheckDeviceExceptionTypeEnum.USAGE_MISMATCH ? item.assetUsage?.id : undefined,
        })),
      ];
    }, [] as {
      deviceID: number;
      exceptionType: number;
      deviceName?: string;
      ownerID?: number;
      deptID?: number;
    }[]);

  if (!updateList.length) {
    return;
  }

  const loadingMsg = createMessage.loading('批量更新中...', 0);
  try {
    const res = await updateDeviceFieldBatch(updateList);
    loadingMsg();
    if (res?.code !== 7) {
      if (res.result.find((item) => item.isUpdateAccessLevel)) {
        createMessage.success('已同步更新流通级别');
      }
      if (res?.failCount) {
        createMessage.error(`${res.failCount}条更新失败，请检查`);
      } else {
        createMessage.success('全部更新完成');
      }
      // 更新状态
      updateList.forEach((update, i) => {
        const key = update.exceptionType as CheckDeviceExceptionTypeEnum;
        const item = exceptionInfo.value[key]?.find((i) => i.deviceID === update.deviceID);
        if (item) {
          item.isUpdating = false;
          item.isSelected = false;
          if (res?.result[i]?.isSuccess) {
            item.isUpdated = true;
          } else {
            item.updatedErrorMsg = res?.result[i]?.errorMsg;
          }
        }
      });
    }
  } catch (error) {
    loadingMsg();
    console.error('批量更新失败:', error);
    updateList.forEach((update) => {
      const key = update.exceptionType as CheckDeviceExceptionTypeEnum;
      const item = exceptionInfo.value[key]?.find((i) => i.deviceID === update.deviceID);
      if (item) {
        item.isUpdating = false;
      }
    });
  }
}

/** 关闭弹窗 */
async function handleClose() {
  emit('success');
}
</script>

<style lang="less" scoped>
::v-deep(.ant-collapse-header) {
  padding: 0 48px 0 0 !important;
}
::v-deep(.ant-collapse-expand-icon) {
  height: 32px !important;
}
::v-deep(.ant-collapse-content-box) {
  padding: 0 48px 16px 24px !important;
}
</style>
