import { ForgeonTheme } from '@hg-tech/forgeon-style';
import { Loading3QuartersOutlined } from '@/common/components/svg-icons';
import { useAppTheme } from '@/common/hooks';
import { getItem, STORAGE_KEY } from '@/common/utils/localstorage';
import { themeOverrides } from '@/configs/theme-override';
import { ForgeonThemeProvider, useMicroAppInject, usePlatformConfigCtx } from '@hg-tech/oasis-common';
import { dateZhCN, NConfigProvider, NIcon, NMessageProvider, NModalProvider, NP, zhCN } from 'naive-ui';
import { computed, defineComponent, onBeforeMount, watch } from 'vue';
import { RouterView } from 'vue-router';

const AigcApp = defineComponent({
  setup() {
    const { currentTheme, appTheme, setTheme } = useAppTheme();
    const platformConfig = computed(() => {
      if (window.__MICRO_APP_ENVIRONMENT__) {
        return useMicroAppInject(usePlatformConfigCtx);
      }
    });

    watch(() => platformConfig.value, () => {
      if (window.__MICRO_APP_ENVIRONMENT__ && platformConfig.value?.data.value?.theme) {
        setTheme(platformConfig.value?.data.value?.theme);
      }
    }, { immediate: true, deep: true });

    onBeforeMount(() => {
      if (!window.__MICRO_APP_ENVIRONMENT__) {
        setTheme(getItem(`${STORAGE_KEY.APP_THEME}${'1'}`) as ForgeonTheme || ForgeonTheme.Light);
      }
    });

    return () => (
      <ForgeonThemeProvider theme={currentTheme.value}>
        <NConfigProvider
          dateLocale={dateZhCN}
          inline-theme-disabled
          locale={zhCN}
          theme={appTheme.value}
          themeOverrides={themeOverrides[currentTheme.value]}
        >
          <NMessageProvider max={3}>
            <NModalProvider>
              <div class="aigc-app bg-FO-Container-Fill2 h-100vh">
                <RouterView>
                  {({ Component }: { Component: any }) =>
                    (Component
                      ? <Component />
                      : (
                        <div class="bg-FO-Container-Fill2 h-full min-h-500px w-full flex flex-col items-center justify-center">
                          <NIcon depth={3} size={40}>
                            <Loading3QuartersOutlined class="animate-spin" />
                          </NIcon>
                          <NP class="FO-Font-B18">
                            正在加载中...
                          </NP>
                        </div>
                      ))}
                </RouterView>
              </div>
            </NModalProvider>
          </NMessageProvider>
        </NConfigProvider>
      </ForgeonThemeProvider>
    );
  },
});

export {
  AigcApp,
};
