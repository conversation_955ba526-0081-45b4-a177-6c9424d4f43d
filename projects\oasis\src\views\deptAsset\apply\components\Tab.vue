<template>
  <div v-track:v="'lv3cjond99'">
    <div class="bg-FO-Container-Fill1">
      <div class="flex justify-between b-b-1 b-b-FO-Container-Stroke1 px-8 py-4">
        <LineTab
          v-model:activeTab="curActiveTab" :tabList="isSettingsPage ? settingTabList : mobileOwnerOptions"
          tabMargin="36px" :tabAttrs="{ class: '[&[active=true]::after]:(!-bottom-16px !rd-0) pl-1' }"
          @change="() => handleTabChange()"
        >
          <template #item="{ item }">
            <span class="flex items-center gap-1 text-14px">
              <span :class="{ 'c-FO-Brand-Primary-Default font-bold': item.name === curActiveTab }">
                {{ item.title }}
              </span>
              <span v-if="!isSettingsPage" class="c-FO-Content-Text2">({{ counts?.[item.name as keyof DeviceCounts] || 0
              }})</span>
            </span>
          </template>
        </Linetab>
        <slot name="action" />
      </div>
    </div>
    <slot />
  </div>
</template>

<script lang="ts" setup>
import { mobileOwnerOptions, settingTabList } from '../device.data';
import type { DeviceCounts } from '/@/api/page/model/deptAssetModel';
import { ref } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import LineTab from '/@/components/LineTab';
import { PlatformEnterPoint } from '@hg-tech/oasis-common';

const props = withDefaults(defineProps<{
  isSettingsPage?: boolean;
  counts?: DeviceCounts;
  curTab?: string;
}>(), {
  isSettingsPage: true,
  counts: () => ({}),
  curTab: '',
});

const route = useRoute();
const { replace } = useRouter();
const curActiveTab = ref<string>(props.curTab || (props.isSettingsPage ? PlatformEnterPoint.DeviceManagement : 'allDevice'));

async function handleTabChange() {
  if (props.isSettingsPage) {
    replace({ name: curActiveTab.value });
  } else {
    replace({ query: { tab: curActiveTab.value, p: route.query.p } });
  }
}
</script>
