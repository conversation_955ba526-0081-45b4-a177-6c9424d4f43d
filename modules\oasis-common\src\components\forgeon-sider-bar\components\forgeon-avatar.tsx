import { ForgeonTheme } from '@hg-tech/forgeon-style';
import type { RoleListItem, UserInfoModel } from '../../../models';
import type { PropType } from 'vue';
import type { Key } from 'ant-design-vue/es/_util/type';
import { computed, defineComponent, ref } from 'vue';
import { Divider, Menu, Popover } from 'ant-design-vue';
import { PlatformEnterPoint, PlatformRoutePath } from '../../../configs';
import { preprocessFilePath } from '../../../utils/path/file';
import { useForgeOnSider } from '../useForgeOnSider';

import styles from '../style.module.less';

const ForgeonAvatar = defineComponent({
  props: {
    userInfo: {
      type: Object as PropType<UserInfoModel>,
      default: () => ({}),
    },
    theme: {
      type: String as PropType<ForgeonTheme>,
      default: ForgeonTheme.Dark,
    },
    onSwitchRole: {
      type: Function as PropType<(role?: RoleListItem) => void>,
      default: () => {},
    },
    onLogout: {
      type: Function as PropType<() => void>,
      default: () => {},
    },
    onLogin: {
      type: Function as PropType<() => void>,
      default: () => {},
    },
    onToggleDarkMode: {
      type: Function as PropType<(e: MouseEvent, mode: ForgeonTheme) => void>,
      default: () => {},
    },
  },
  setup(props) {
    const visible = ref(false);
    const { routerInstance, setModule, modules, isUserLogin } = useForgeOnSider();

    const isShowManager = computed(() => modules.value.some((item) => item.key === PlatformEnterPoint.ModuleManage));
    const isShowAdmin = computed(() => modules.value.some((item) => item.key === PlatformEnterPoint.SysAdmin));

    const formatNickName = (record?: UserInfoModel) => {
      return record ? (record.nickName + (record.moniker ? `(${record.moniker})` : '')) : '';
    };

    const onHandleAccountSetting = (e: Event) => {
      e.preventDefault();
      setModule(PlatformEnterPoint.ModuleAccount);
      visible.value = false;
    };

    const onHandleToggleThemeMode = (e: MouseEvent, mode: ForgeonTheme) => {
      e.preventDefault();
      visible.value = false;
      props.onToggleDarkMode(e, mode);
    };

    const onHandleManager = (e: Event) => {
      e.preventDefault();
      setModule(PlatformEnterPoint.ModuleManage);
      visible.value = false;
    };

    const onHandleAdmin = (e: Event) => {
      e.preventDefault();
      routerInstance.push?.(PlatformEnterPoint.SysAdmin, true);
      visible.value = false;
    };

    const onHandleLogout = (e: Event) => {
      e.preventDefault();
      visible.value = false;
      props.onLogout();
    };

    const renderPopoverContent = () => {
      return isUserLogin.value
        ? (
          <div class={styles.avatarPopover}>
            <span class="c-FO-Content-Text1 flex p-16px">
              <div class="mr-8px">
                <img alt="avatar" class="h-48px w-48px rd-50%" src={preprocessFilePath(props.userInfo.headerImg!)} />
              </div>
              <div>
                <div class="c-FO-Content-Text1 mb-4px truncate FO-Font-B16">
                  { formatNickName(props.userInfo) }
                </div>
                <div class="c-FO-Content-Text2 FO-Font-R14">
                  {props.userInfo.authority?.authorityName}
                </div>
              </div>
            </span>
            <Divider class="my-2px" />
            <Menu selectable={false} theme={props.theme}>
              {props.userInfo.authorities && props.userInfo.authorities.length > 1
                ? (
                  <>
                    <Menu
                      onUpdate:selectedKeys={
                        ([key]) => {
                          visible.value = false;
                          props.onSwitchRole(props.userInfo.authorities?.find((auth) => auth.authorityId === key));
                        }
                      }
                      selectedKeys={[props.userInfo.authorityId] as Key[]}
                      theme={props.theme}
                    >
                      <Menu.SubMenu popupClassName={styles.avatarSubMenuPopup} popupOffset={[14, 0]} title="切换角色">
                        {props.userInfo.authorities.filter((item) => item.parentId === '0')?.map((auth) => (
                          <Menu.Item
                            key={auth.authorityId}
                          >
                            <span>{auth.authorityName}</span>
                          </Menu.Item>
                        ))}
                      </Menu.SubMenu>
                    </Menu>
                  </>
                )
                : null }
              <>
                <Menu
                  selectedKeys={[props.theme]}
                  theme={props.theme}
                >
                  <Menu.SubMenu popupClassName={styles.avatarSubMenuPopup} popupOffset={[14, 0]} title="主题">
                    <Menu.Item key="light" onClick={(e) => onHandleToggleThemeMode(e, ForgeonTheme.Light)}>
                      <span>浅色</span>
                    </Menu.Item>
                    <Menu.Item key="dark" onClick={(e) => onHandleToggleThemeMode(e, ForgeonTheme.Dark)}>
                      <span>深色</span>
                    </Menu.Item>
                  </Menu.SubMenu>
                </Menu>
              </>

              <Menu.Item>
                <a href={PlatformRoutePath.AccountSettings} onClick={onHandleAccountSetting}>个人设置</a>
              </Menu.Item>
              {isShowManager.value && (
                <Menu.Item>
                  <a onClick={onHandleManager}>应用配置</a>
                </Menu.Item>
              )}
              {isShowAdmin.value && (
                <Menu.Item>
                  <a onClick={onHandleAdmin}>管理后台</a>
                </Menu.Item>
              )}
              <Divider class="my-4px" />
              <Menu.Item>
                <a onClick={onHandleLogout}>退出登录</a>
              </Menu.Item>
            </Menu>
          </div>
        )
        : null;
    };

    return () => (
      <div class={styles.avatarWrapper}>
        <Popover
          arrow={false}
          onOpenChange={(open) => visible.value = open}
          open={visible.value && isUserLogin.value}
          overlayInnerStyle={{
            padding: '4px',
            width: '260px',
            borderRadius: '8px',
            marginLeft: '8px',
            overflow: 'hidden',
          }}
          overlayStyle={{
            borderRadius: '8px',
          }}
          placement="rightTop"
          trigger={['click']}
          v-slots={{
            content: () => (
              renderPopoverContent()
            ),
          }}
        >
          <div class={styles.avatar}>
            {
              isUserLogin.value
                ? <img alt="avatar" class="cursor-pointer" src={preprocessFilePath(props.userInfo.headerImg!)} />
                : (
                  <div
                    class="h-full w-full flex cursor-pointer select-none items-center justify-center"
                    onClick={() => props.onLogin()}
                  >
                    <div>登录</div>
                  </div>
                )
            }
          </div>
        </Popover>
      </div>
    );
  },
});

export { ForgeonAvatar };
