<template>
  <PageWrapper :title="`提交tag：${streamData?.description || streamData?.path}`" headerSticky @back="handleBack">
    <template #subTitle>
      <div class="flex items-center gap-[12px]">
        <BasicButton
          class="custom-rounded-btn" borderColor="black" noIcon size="small"
          @click="handleDelete"
        >
          删除
        </BasicButton>
        <BasicButton
          class="custom-rounded-btn" borderColor="black" size="small" @click="handleaddTag"
        >
          <Icon icon="icon-park-outline:plus" size="16" />
          添加tag
        </BasicButton>
      </div>
    </template>
    <template #extra>
      <span>该页面的更改即时生效</span>
    </template>
    <div class="rounded-md bg-white p-4">
      <Spin :spinning="loadingConfigInfo">
        <BorderBox label="强制绑定tag" subLabel="开启后使用提交工具提交必须绑定tag，否则无法通过提交工具提交。">
          <Spin :spinning="loadingUpdateSwitch">
            <div>
              <span class="FO-Font-B14 mr-[6px]">是否强制绑定tag</span>
              <Switch v-model:checked="formValue.bindTag" size="small" checkedChildren="开" unCheckedChildren="关" @change="handleSwitchChange" />
            </div>
          </Spin>
        </BorderBox>
        <BorderBox label="可提交tag" subLabel="配置提交tag后，此处tag的排列顺序会影响提交工具中tag显示的顺序。">
          <Spin :spinning="loadingUpdateCommitTagConfigTag && loadingConfigInfo">
            <TagCard :tagList="formValue.commit" type="cansubmit" @handleSort="handleSort" @handleReduce="handleReduce" @success="refreshFields(userStore.getProjectId!, props.streamData.ID!)" />
          </Spin>
        </BorderBox>
        <BorderBox label="未配置tag" subLabel="点击加号按钮可为该分支配置对应tag。">
          <Spin :spinning="loadingUpdateCommitTagConfigTag && loadingConfigInfo">
            <TagCard :tagList="formValue.other" type="notconfig" @handleAdd="handleAdd" @success="refreshFields(userStore.getProjectId!, props.streamData.ID!)" />
          </Spin>
        </BorderBox>
      </Spin>
    </div>
    <DeleteModalHolder />
    <EditTagModalHolder />
  </PageWrapper>
</template>

<script setup lang="tsx">
import { PlatformEnterPoint } from '@hg-tech/oasis-common';
import { Spin, Switch } from 'ant-design-vue';
import { PageWrapper } from '../../../../components/Page/index.ts';
import { BorderBox } from '../../../../components/Form/index.ts';
import { useRouter } from 'vue-router';
import { reactive, watch } from 'vue';
import { useLatestPromise, useModalShow } from '@hg-tech/utils-vue';
import { deleteConfigTag, getTagConfigInfo, updateCommitTag, updateTagConfigSwitch } from '/@/api/page/p4.ts';
import { useUserStore } from '/@/store/modules/user.ts';
import TagCard from './config/TagCard.vue';
import { Icon } from '/@/components/Icon';
import type { StreamsListItem } from '/@/api/page/model/p4Model.ts';
import DeleteModal from '../../../../components/DeleteModal.vue';
import EditTagModal from './config/EditTagModal.vue';
import type { TagListItem } from '/@/api/page/model/submitConfModel.ts';
import { addTag } from '/@/api/page/submitConf.ts';
import { BasicButton } from '/@/components/Button';

const props = withDefaults(defineProps<{
  streamData: StreamsListItem;
  allStreamList?: StreamsListItem[];
}>(), {
  allStreamList: () => [],
});

const emit = defineEmits<{
  (e: 'update'): void;
}>();

const router = useRouter();
const userStore = useUserStore();
const [DeleteModalHolder, showDeleteModal] = useModalShow(DeleteModal);
const [EditTagModalHolder, showEditTagModal] = useModalShow(EditTagModal);
const { data: configInfo, execute: fetchConfigInfo, loading: loadingConfigInfo } = useLatestPromise(getTagConfigInfo);
const { execute: updateSwitch, loading: loadingUpdateSwitch } = useLatestPromise(updateTagConfigSwitch);
const { execute: updateConfigTag, loading: loadingUpdateCommitTagConfigTag } = useLatestPromise(updateCommitTag);
const { execute: deleteConfigTagExecute } = useLatestPromise(deleteConfigTag);
const { execute: addTagExecute } = useLatestPromise(addTag);

const formValue = reactive({
  bindTag: false,
  commit: [] as TagListItem[],
  other: [] as TagListItem[],
});
async function refreshFields(projectId: number, streamId: number) {
  await fetchConfigInfo(projectId, streamId);
  formValue.bindTag = configInfo.value?.bindTag ?? false;
  formValue.commit = configInfo.value?.commit ?? [];
  formValue.other = configInfo.value?.other ?? [];
}
function handleSwitchChange(checked: boolean) {
  if (props.streamData.ID && userStore.getProjectId) {
    updateSwitch(userStore.getProjectId, props.streamData.ID, checked);
  }
}
async function handleDelete() {
  await showDeleteModal({
    title: '删除tag配置',
    okType: 'error',
    description: (
      <div>
        <div>即将删除: </div>
        <div>
          <div>
            <span>该分支下的</span>
            <b>tag配置</b>
          </div>
          <div>
            这会使
            <b>提交人无需绑定tag</b>
          </div>
          <div>删除后，您可以随时重新配置</div>
        </div>
      </div>
    ),
    async onOk() {
      if (props.streamData.ID && userStore.getProjectId) {
        return deleteConfigTagExecute(userStore.getProjectId, props.streamData.ID);
      }
    },
  });
  emit('update');
}
function handleBack() {
  router.push({ name: PlatformEnterPoint.P4Depots });
}
async function handleSort(newSortIdList: TagListItem[]) {
  if (!props.streamData.ID || !userStore.getProjectId) {
    return;
  }
  await updateConfigTag(userStore.getProjectId, props.streamData.ID, newSortIdList.map((item) => item.ID!));
  refreshFields(userStore.getProjectId, props.streamData.ID);
}

async function handleAdd(item: TagListItem) {
  if (!props.streamData.ID || !userStore.getProjectId) {
    return;
  }
  await updateConfigTag(userStore.getProjectId, props.streamData.ID, [...formValue.commit.map((item) => item.ID!), item.ID!]);
  refreshFields(userStore.getProjectId, props.streamData.ID);
}

async function handleReduce(item: TagListItem) {
  if (!props.streamData.ID || !userStore.getProjectId) {
    return;
  }
  await updateConfigTag(userStore.getProjectId, props.streamData.ID, formValue.commit.filter((i) => i.ID !== item.ID).map((item) => item.ID!));
  refreshFields(userStore.getProjectId, props.streamData.ID);
}
async function handleaddTag() {
  await showEditTagModal({
    title: '添加tag',
    subLabel: '添加tag后，对如下分支直接启用',
    isUpdate: false,
    formValue: {
      name: '',
      description: '',
      streamIDs: [],
    },
    async sentReq(formValue: TagListItem) {
      const result = await addTagExecute(userStore.getProjectId!, formValue);
      return result!;
    },
  });
  refreshFields(userStore.getProjectId!, props.streamData.ID!);
}
watch([() => userStore.getProjectId, () => props.streamData.ID], ([projectId, streamId], [oldProjectId, oldStreamId]) => {
  if (projectId && streamId && (projectId !== oldProjectId || streamId !== oldStreamId)) {
    refreshFields(projectId, streamId);
  }
}, { immediate: true });
</script>
