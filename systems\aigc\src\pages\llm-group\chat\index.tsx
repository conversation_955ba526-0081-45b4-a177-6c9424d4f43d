import { Add, <PERSON><PERSON>eftW, <PERSON>R<PERSON>W, Interface } from '@/common/components/svg-icons';
import { type ModalReactive, NButton, NIcon, NLayout, NLayoutSider, NTooltip, useModal } from 'naive-ui';
import { computed, defineComponent, onActivated, ref, watch, watchEffect } from 'vue';
import { Communications } from './components/communications';
import { useRouter } from 'vue-router';
import { useChatHook } from './use-chat-hook';
import { traceClickEvent } from '@/plugins/track';
import { TrackEventName } from '@/plugins/event';
import { getItem, setItem, STORAGE_KEY } from '@/common/utils/localstorage';
import { SwitchModal } from './components/switch-modal';
import { usePlatformConfig } from '@/common/hooks/platform-config.hook';
import { ChatHeader } from './header';
import { ChatFeed } from './feed';
import { NewChat } from './new-chat';
import { SearchPanel } from './search-panel';
import type { ChatMessageSearch } from '@/models/chat';
import equal from 'fast-deep-equal';

const AIChat = defineComponent({
  setup() {
    const router = useRouter();
    const modal = useModal();
    const {
      currentModel,
      chatState,
      chatModels,
      chatLoading,
      chatContainerRef,
      chatContainerScrollRef,
      isDeepThinkActive,
      isOnlineSearchActive,
      chatDetail,
      showDownBtn,
      needAutoScroll,
      stopChat,
      sendMessage,
      triggerManualScroll,
      initChatPage,
      mutateGetChatDetail,
      triggerScrollToBottom,
      handleListScroll,
      deleteChat,
      updateChatTitle,
      scrollToItem,
      updateTitleLoading,
      isChatListEnd,
      chatHistoriesLoading,
    } = useChatHook();
    const { isFullScreen } = usePlatformConfig();
    const siderCollapsed = ref(false);
    const isCollapsed = ref(false);
    const isShowSearchPanel = ref<boolean>(false);
    const displaySearchList = ref<ChatMessageSearch[]>([]);

    const communications = computed(() => (Object.values(chatState.value.conversations || {})));
    const modelOptions = computed(() => chatModels.value?.map((item) => ({ label: item.name, value: item.name, desc: item.description })));

    watch(() => chatState.value, async () => {
      const currentSessionId = chatState.value.activeConversationId;
      // 如果有当前会话id，且路由中的会话id不等于当前会话id，则替换url query
      if (currentSessionId && router.currentRoute.value.query.c !== currentSessionId) {
        const currentConversation = chatState.value.conversations?.[currentSessionId];
        isShowSearchPanel.value = false;
        if (!currentConversation) {
          return;
        }
        // 如果没有消息则请求消息补全messages
        if (!currentConversation?.messages.length) {
          await mutateGetChatDetail({ sessionId: currentSessionId });
          chatDetail.value?.messages && currentConversation.messages.push(...chatDetail.value.messages);
        }
      }
      if (needAutoScroll.value) {
        requestIdleCallback(() => {
          triggerScrollToBottom();
        });
      }
    }, {
      deep: true,
      immediate: true,
    });

    watch(() => router.currentRoute.value, async (value) => {
      if (value.query.c) {
        chatState.value.activeConversationId = value.query.c as string;
      }
    }, {
      immediate: true,
    });

    const onResizeChange = () => {
      if (window.innerWidth <= 600) {
        siderCollapsed.value = true;
      } else {
        siderCollapsed.value = false;
      }
    };

    watchEffect((onCleanUp) => {
      window.addEventListener('resize', onResizeChange);
      onCleanUp(() => {
        window.removeEventListener('resize', onResizeChange);
      });
    });

    const handleCreate = () => {
      chatState.value.activeConversationId = undefined;
      router.replace({ query: {
        ...router.currentRoute.value.query,
        c: undefined,
      } });
    };

    const onUpdateModel = (value: string) => {
      traceClickEvent(TrackEventName.AI_CHAT_MODEL_SWITCH);
      const isShow = getItem(STORAGE_KEY.AI_CHAT_MODEL_SWITCH_TIP) !== '1';
      if (value.startsWith('外部') && isShow) {
        let m: ModalReactive;
        const onConfirm = (isCheck: boolean) => {
          setItem(STORAGE_KEY.AI_CHAT_MODEL_SWITCH_TIP, isCheck ? '1' : '0');
          currentModel.value = value;
          m.destroy();
        };
        m = modal.create({
          title: '安全提示',
          preset: 'dialog',
          class: 'w-400px',
          closeOnEsc: true,
          maskClosable: false,
          autoFocus: false,
          showIcon: false,
          content: () => <SwitchModal onConfirm={onConfirm} />,
        });
      } else {
        currentModel.value = value;
      }
    };

    const onTitleEdit = async (data: { sessionId: string; title: string }) => {
      await updateChatTitle(data);
    };

    const onShowSearchPanel = (search: ChatMessageSearch[]) => {
      if (!equal(displaySearchList.value, search || [])) {
        isShowSearchPanel.value = true;
      } else {
        isShowSearchPanel.value = !isShowSearchPanel.value;
      }
      displaySearchList.value = search || [];
    };

    onActivated(async () => {
      if (chatState.value.activeConversationId) {
        router.push({ query: {
          ...router.currentRoute.value.query,
          c: chatState.value.activeConversationId,
        } });
      }
      onResizeChange();
      await initChatPage();
    });

    const renderCreateChat = () => {
      return (
        <NButton
          class="FO-Font-B14 w-full rd-md"
          onClick={handleCreate}
          renderIcon={() => (
            <NIcon>
              <Add />
            </NIcon>
          )}
          secondary
          type="primary"
        >
          新对话
        </NButton>
      );
    };

    const renderSider = () => {
      return (
        <NLayoutSider
          class={['left-0 top-0 z-10 rd-lg bg-FO-Container-Fill1', isCollapsed.value ? 'mr-0' : 'mr-16px']}
          collapsedWidth={0}
          collapseMode="transform"
          onAfterEnter={() => {
            isCollapsed.value = false;
          }}
          onAfterLeave={() => {
            isCollapsed.value = true;
          }}
          v-model:collapsed={siderCollapsed.value}
          width="260"
        >
          <div class="chat-sider h-full w-full flex flex-shrink-0 flex-col py-20px">
            <div class="chat-sider-header mb-24px px-20px flex-c-center">
              {renderCreateChat()}
            </div>
            <div class="chat-sider-menu mb-20px flex-1 overflow-hidden">
              <Communications
                contentClass="px-20px"
                data={communications.value}
                isFinish={isChatListEnd.value}
                isLoading={chatHistoriesLoading.value}
                onDelete={deleteChat}
                onScroll={handleListScroll}
                onTitleChange={onTitleEdit}
                onUpdate:active={(id) => {
                  router.replace({ query: {
                    ...router.currentRoute.value.query,
                    c: id,
                  } });
                }}
                v-model:active={chatState.value.activeConversationId}
              />
            </div>
            <div class="chat-sider-footer flex flex-shrink-0 gap-8px px-20px">
              <NTooltip>
                {{
                  default: () => '点击将前往服务台咨询获取API使用权限',
                  trigger: () => (
                    <NButton
                      class="flex-1"
                      onClick={() => {
                        traceClickEvent(TrackEventName.AI_CHAT_GET_API_CLICK);
                        window.open('https://applink.feishu.cn/T8UcLT15BQsA');
                      }}
                      renderIcon={() => (
                        <NIcon size={16}>
                          <Interface />
                        </NIcon>
                      )}
                    >API申请
                    </NButton>
                  ),
                }}
              </NTooltip>
              <NButton class="px-11px" onClick={() => siderCollapsed.value = true} renderIcon={() => <ArrowLeftW />} />
            </div>
          </div>
        </NLayoutSider>
      );
    };

    const renderFooter = () => {
      return (
        <div class="chat-tip c-FO-Content-Text2 FO-Font-R14 mt-12px text-center">
          AI也可能会犯错。请核查重要信息。有需求/反馈/疑问请飞书联系
          <NTooltip>
            {{
              default: () => '点击将前往技术中心人工服务台咨询或反馈',
              trigger: () => (
                <a
                  class="c-FO-Content-Link-Default mx-4px cursor-pointer"
                  href="https://applink.feishu.cn/T8UcLT15BQsA"
                  target="_blank"
                >
                  @技术中心服务台
                </a>
              ),
            }}
          </NTooltip>

        </div>
      );
    };

    return () => (
      <div class={[
        'hg-chat-wrapper bg-FO-Container-Background',
        !isFullScreen.value ? 'h-[calc(100vh-64px)]' : 'h-100vh',
      ]}
      >
        <div class="h-full flex rd-lg p-20px max-sm:px-12px max-sm:py-8px">
          <NLayout contentClass="bg-FO-Container-Background" hasSider>
            {renderSider()}
            <div class="chat-wrapper bg-FO-Container-Fill1 pos-relative w-full flex flex-col overflow-hidden rd-lg p-20px">
              <ChatHeader
                currentModel={currentModel.value}
                modelOptions={modelOptions.value}
                onCreate={handleCreate}
                onModelChange={onUpdateModel}
                onTitleChange={onTitleEdit}
                sessionId={chatState.value.activeConversationId}
                siderCollapsed={siderCollapsed.value}
                title={chatState.value.activeConversationId ? chatState.value.conversations?.[chatState.value.activeConversationId].title : ''}
                titleUpdateLoading={updateTitleLoading.value}
              />
              {
                chatState.value.activeConversationId
                  ? (
                    <div class="flex flex-1 overflow-hidden">
                      <ChatFeed
                        chatLoading={chatLoading.value}
                        containerRef={chatContainerRef}
                        messages={chatState.value.conversations?.[chatState.value.activeConversationId]?.messages}
                        onManualScroll={triggerManualScroll}
                        onScrollToBottom={() =>
                          setTimeout(() => {
                            triggerScrollToBottom();
                          }, 0)}
                        onScrollToItem={scrollToItem}
                        onSendMessage={({ message, isNew, editId }) => sendMessage({
                          message,
                          editId,
                          isNew,
                          type: 'user',
                        })}
                        onShowSearch={onShowSearchPanel}
                        onStopChat={stopChat}
                        scrollBarRef={chatContainerScrollRef}
                        sessionId={chatState.value.activeConversationId}
                        showDownBtn={showDownBtn.value}
                        v-model:currentModel={currentModel.value}
                        v-model:isDeepThinkActive={isDeepThinkActive.value}
                        v-model:isOnlineSearchActive={isOnlineSearchActive.value}
                      />
                      <SearchPanel
                        data={displaySearchList.value}
                        v-model:visible={isShowSearchPanel.value}
                      />
                    </div>
                  )
                  : (
                    <NewChat
                      chatLoading={chatLoading.value}
                      onSendMessage={(data) => sendMessage({
                        message: data.message,
                        type: 'user',
                        isNew: true,
                      })}
                      onStopChat={stopChat}
                      v-model:currentModel={currentModel.value}
                      v-model:isDeepThinkActive={isDeepThinkActive.value}
                      v-model:isOnlineSearchActive={isOnlineSearchActive.value}
                    />
                  )
              }
              {renderFooter()}
              {siderCollapsed.value
              && (
                <NButton
                  class="pos-absolute bottom-20px left-20px px-11px"
                  onClick={() => siderCollapsed.value = false}
                  renderIcon={() => <ArrowRightW />}
                />
              )}
            </div>
          </NLayout>
        </div>
      </div>
    );
  },
});

export {
  AIChat,
};
