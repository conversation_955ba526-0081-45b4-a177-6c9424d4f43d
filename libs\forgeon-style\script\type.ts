import type * as CSS from 'csstype';

interface CSSProperties extends CSS.Properties<string | number>, CSS.PropertiesHyphen<string | number> {
  [v: `--${string}`]: string | number | undefined; // 支持自定义CSS变量
}

/**
 * forgeon组合设计token
 * @description 不需要以 FO 开头，流程会自动补上 FO 前缀，请使用 PascalCase
 * @example FontB20: { fontSize: '20px', fontWeight: 600, lineHeight: '28px' }
 */
export type ForgeonDesignClass = Record<string, Partial<Record<keyof CSSProperties, string | number>>>;
