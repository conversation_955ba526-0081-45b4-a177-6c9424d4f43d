<template>
  <div class="min-h-54px flex items-center gap-12px overflow-hidden rd-6px px-16px py-8px">
    <slot name="prefix" />
    <div class="w-full flex items-center gap-8px overflow-hidden pr-16px">
      <Avatar :size="32" class="flex-none" :src="data?.avatar">
        <template #icon>
          <UserOutlined />
        </template>
      </Avatar>
      <div class="min-w-0 flex-1">
        <EllipsisText class="FO-Font-R14 c-FO-Content-Text1">
          {{ data?.title }}
        </EllipsisText>
        <EllipsisText class="FO-Font-R12 c-FO-Content-Text3">
          {{ data?.subTitle }}
        </EllipsisText>
      </div>
    </div>
    <slot name="suffix" />
  </div>
</template>

<script setup lang="ts">
import { Avatar } from 'ant-design-vue';
import { UserOutlined } from '@ant-design/icons-vue';
import { EllipsisText } from '@hg-tech/oasis-common';
import type { OrgStructureCellInfo } from './type.ts';

defineProps<{
  data: OrgStructureCellInfo<string> | undefined;
}>();
</script>
