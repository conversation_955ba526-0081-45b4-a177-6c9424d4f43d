<template>
  <ConfigProvider :locale="zhCN" :theme="theme">
    <div class="h-full flex flex-col overflow-hidden">
      <ForgeonHeader
        class="flex-none"
        title="工具中心"
        :onHandleMenuExpand="() => config?.changeMenuExpendStatus(true)"
        :showUnfoldIcon="!config?.isMenuExpanded"
      />
      <div class="flex-auto overflow-auto">
        <RouterView v-slot="{ Component, route }">
          <KeepAlive v-if="route.meta.keepAlive">
            <component :is="Component" />
          </KeepAlive>
          <component :is="Component" v-else />
        </RouterView>
      </div>
    </div>
  </ConfigProvider>
</template>

<script  lang="ts" setup>
import { computed } from 'vue';
import { ConfigProvider } from 'ant-design-vue';
import { AntdOverrideDark, AntdOverrideLight, ForgeonTheme } from '@hg-tech/forgeon-style';
import { ForgeonHeader, useMicroAppInject, usePlatformConfigCtx } from '@hg-tech/oasis-common';

import zhCN from 'ant-design-vue/es/locale/zh_CN';

const { data: config } = useMicroAppInject(usePlatformConfigCtx);
const theme = computed(() => {
  return config.value?.theme === ForgeonTheme.Dark ? AntdOverrideDark : AntdOverrideLight;
});
</script>
