/**
 * 定义事件名称的枚举, 事件名称的格式为: 模块名_事件名
 * @see https://dataark.hypergryph.net/area/11/app/app_manager/event_manager?appId=7l6k0y2vw4rksevp6bibwuht
 */
enum TrackEventName {
  // AIGC-common
  /* AIGC-LLM */
  /**
   * 任务创建按钮点击
   */
  AI_VERSION_NEWS_TASK_CREATE_CLICK = 'version_news_task_create_click',
  /**
   * 二次总结详情按钮点击
   */
  AI_VERSION_NEWS_SECOND_DETAIL_CLICK = 'version_news_second_detail_click',
  /**
   * 一次总结按钮点击
   */
  AI_VERSION_NEWS_FIRST_SUMMARY_CLICK = 'version_news_first_summary_click',
  /**
   * 查看二次总结对应打标数据
   */
  AI_VERSION_NEWS_ORIGIN_FROM_SECOND_SUMMARY = 'version_news_origin_from_second_summary',
  /**
   * 查看一次总结对应打标数据
   */
  AI_VERSION_NEWS_ORIGIN_FROM_FIRST_SUMMARY = 'version_news_origin_from_first_summary',
  /**
   * 查看全量打标数据
   */
  AI_VERSION_NEWS_ORIGIN_FROM_FULL_MARK = 'version_news_origin_from_full_mark',
  /**
   * 导出全量打标数据
   */
  AI_VERSION_NEWS_ORIGIN_DATA_DOWNLOAD = 'version_news_origin_data_download',
  /**
   * AI聊天发送消息
   */
  AI_CHAT_SEND_MESSAGE = 'ai_chat_send_message',
  /**
   * AI聊天取消发送消息
   */
  AI_CHAT_SEND_MESSAGE_CANCEL = 'ai_chat_send_message_cancel',
  /**
   * AI模型切换
   */
  AI_CHAT_MODEL_SWITCH = 'ai_chat_model_switch',
  /**
   * AI聊天 获取api按钮点击
   */
  AI_CHAT_GET_API_CLICK = 'ai_chat_get_api',

  /* AIGC-SD */
  AI_EXTRA_IMAGE_GENERATE = 'extra_image_generate',
  AI_BG_REMOVAL_GENERATE = 'bg_removal_generate',
  AI_IMAGE_DESCRIBE_ASSISTANT_POLISH = 'ai_image_describe_assistant_polish',
  AI_IMAGE_DESCRIBE_ASSISTANT_CHAR_PROMPT = 'ai_image_describe_assistant_char_prompt',
  AI_IMAGE_DESCRIBE_ASSISTANT_IMAGENOW = 'ai_image_describe_assistant_imagenow',
  AI_IMAGE_DESCRIBE_ASSISTANT = 'ai_image_describe_assistant',
  AI_IMAGE_GENERATE = 'ai_image_generate',
  /* AIGC-Voice */
  AI_VOICE_REMAKE_DOWNLOAD = 'voice_remake_download',
  AI_VOICE_REMAKE_GENERATE = 'voice_remake_generate',
  AI_VOICE_SYNTHESIS_GROUP_GENERATE = 'voice_synthesis_group_generate',
  AI_VOICE_SYNTHESIS_SINGLE_DOWNLOAD = 'voice_synthesis_single_download',
  AI_VOICE_SYNTHESIS_GROUP_DOWNLOAD = 'voice_synthesis_group_download',
  AI_VOICE_SYNTHESIS_SINGLE_GENERATE = 'voice_synthesis_single_generate',
  AI_VOICE_RECOGNITION_GROUP_GENERATE = 'voice_recognition_group_generate',
  AI_VOICE_RECOGNITION_SINGLE_GENERATE = 'voice_recognition_single_generate',
}

// 定义每个事件的入参类型
interface AIVersionNewsTaskCreateParams {
  ai_tag: boolean;
}

interface AIChatSendMessageParams {
  in_depth_think: boolean;
  in_online_search: boolean;
  model_use: string;
}

// 定义泛型类型来获取事件的入参类型
 type TrackEventParams<T extends TrackEventName> =
  T extends TrackEventName.AI_VERSION_NEWS_TASK_CREATE_CLICK ? AIVersionNewsTaskCreateParams :
    T extends TrackEventName.AI_CHAT_SEND_MESSAGE ? AIChatSendMessageParams :
      undefined;

export {
  TrackEventName,
};
export type { TrackEventParams };
