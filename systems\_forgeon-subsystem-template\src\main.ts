import type { App } from 'vue';
import { createApp } from 'vue';
import RootApp from './App.vue';
import { router } from './router';
import { store } from './store/pinia.ts';
import { APP_NAME } from './constants/appInfo.ts';

import '@hg-tech/forgeon-style/style';
import 'virtual:uno.css';
import './styles/index.less';

/**
 * 每次挂载需要使用新的实例
 */
function createSysApp() {
  const app = createApp(RootApp);
  app.use(router);
  app.use(store);
  return app;
}

if (window.__MICRO_APP_ENVIRONMENT__) {
  // 在微前端环境
  let app: App;

  window.mount = () => {
    app = createSysApp();
    app.mount('#sub-app');
    console.log(`[${APP_NAME}]子系统已内嵌启动`);
  };
  window.unmount = () => {
    app.unmount();
    console.log(`[${APP_NAME}]子系统已卸载`);
  };
} else {
  // 如果不在微前端环境，则直接执行渲染
  const app = createSysApp();
  app.mount('#sub-app');
  console.log(`[${APP_NAME}]子系统已独立启动`);
}
