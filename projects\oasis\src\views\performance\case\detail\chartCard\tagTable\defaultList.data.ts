import type { FormSchema } from '/@/components/Form';

export const formSchema: FormSchema[] = [
  {
    label: '预设名称',
    field: 'name',
    component: 'Input',
    componentProps: {
      placeholder: '请输入预设名称',
      allowClear: false,
    },
    required: true,
  },
  {
    label: '默认预设',
    field: 'isDefault',
    component: 'Switch',
    itemProps: {
      extra: '设置默认预设后，会在进入报告查看和报告对比页面时，自动使用该预设配置的显示设置。',
    },
  },
  {
    label: '',
    field: 'template',
    component: 'Input',
    slot: 'template',

  },
];
