import { plainVars, themeVarNames, themeVars } from './parsedFigmaVars.ts';
import {
  AUTO_GENERATED_COMMENT,
  formatCssVarName,
  formatPascalCaseVarName,
  formatPascalCaseVarNameWithoutPrefix,
} from '../../src/utils/helper.ts';
import fs from 'node:fs/promises';
import path from 'node:path';
import { ensureDirectoryExists } from '../helper.ts';

const plainVarFileName = 'plain';
const themeFileName = 'theme';
const figmaPlainVarsFileName = 'figmaPlainVars';
const figmaThemeVarsFileName = 'figmaThemeVars';

/**
 * 生成基础变量值文件
 */
async function genPlainTokenFile(genDir: string) {
  const plainTokenContent = `/* This file is automatically generated. Do not edit it manually.  */

/**
 * 基础色值，**请勿直接在业务中使用**
 * 请使用 ForgeonThemeCssVar 获取 css 变量值，或通过 getForgeonColor 获取对应色值
 */
export const ColorPresetToken = {
  ${Object.entries(plainVars).map(([key, value]) => `${formatPascalCaseVarName(key)}: '${value}',`).join('\n  ')}
};\n`;

  await fs.writeFile(path.join(genDir, `${plainVarFileName}.ts`), plainTokenContent);
}

/**
 * 生成基础变量值文件
 */
async function genCssVarMapFile(genDir: string) {
  const themes = Object.entries(themeVars).map(([themeName, styles]) => ({
    themeName,
    vars: styles as Record<string, string>,
  }));

  const themeTokenContent = `/* This file is automatically generated. Do not edit it manually.  */
import { ColorPresetToken } from './${plainVarFileName}';

export enum ForgeonTheme {
  ${Object.keys(themeVars).map((key) => `${formatPascalCaseVarName(key)} = '${key}',`).join('\n  ')}
}

/**
 * 使用 ForgeonTheme 获取到实际的变量值
 */
export const ForgeonThemeMap = {
  ${themes.map(({ themeName, vars }) => `[ForgeonTheme.${formatPascalCaseVarName(themeName)}]: {
    ${Object.entries(vars).map(([key, value]) => `${formatPascalCaseVarNameWithoutPrefix(key)}: ColorPresetToken.${formatPascalCaseVarName(value)},`).join('\n    ')}
  },`).join('\n  ')}
};

/**
 * 映射到对应的 CSS 变量值
 * @example ForgeonThemeCssVar.BrandPrimaryDefault == 'var(--FO-Brand-Primary-Default)'
 */
export const ForgeonThemeCssVar = {
  ${themeVarNames.map((i) => `${formatPascalCaseVarNameWithoutPrefix(i)}: 'var(${formatCssVarName(i)})',`).join('\n  ')}
};

export type ForgeonThemeTokens = keyof typeof ForgeonThemeCssVar;
\n`;

  await fs.writeFile(path.join(genDir, `${themeFileName}.ts`), themeTokenContent);
}

export async function genTokenFile(genDir: string) {
  await ensureDirectoryExists(genDir);
  await Promise.all([
    fs.writeFile(path.join(genDir, `${figmaPlainVarsFileName}.ts`), `${AUTO_GENERATED_COMMENT}

/**
 * @deprecated 仅供当前仓库内部使用
 */
export const FigmaPlainVars = ${JSON.stringify(plainVars, null, 2)};
`),
    fs.writeFile(path.join(genDir, `${figmaThemeVarsFileName}.ts`), `${AUTO_GENERATED_COMMENT}

/**
 * @deprecated 仅供当前仓库内部使用
 */
export const FigmaThemeVars = ${JSON.stringify(themeVars, null, 2)};
`),
    genPlainTokenFile(genDir),
    genCssVarMapFile(genDir),

    fs.writeFile(path.join(genDir, 'index.ts'), `${AUTO_GENERATED_COMMENT}
    
${[figmaPlainVarsFileName, figmaThemeVarsFileName, plainVarFileName, themeFileName].map((i) => `export * from './${i}';`).join('\n')}\n`),
  ]);
}
