<template>
  <Spin :spinning="loadingUserList">
    <div class="flex flex-wrap items-center gap-[8px]">
      <span v-for="i in renderUsers" :key="i.ID" class="my-[6px]">{{ formatNickName(i) }}</span>
      <div v-for="reviewerGroup in userGroupNames" :key="reviewerGroup" class="group-tag">
        <GroupUsersPopover :groupName="reviewerGroup" :serverID="serverID">
          {{ reviewerGroup }}
        </GroupUsersPopover>
      </div>
      <div v-if="!users?.length && !userGroupNames?.length">
        暂无
      </div>
    </div>
  </Spin>
</template>

<script setup lang="ts">
import { Spin } from 'ant-design-vue';
import { formatNickName } from '../hooks/system/useUserList.ts';
import GroupUsersPopover from './GroupUsersPopover';
import { useSysUserList } from '../hooks/useUserList.ts';
import type { UserInfoModel } from '../api/sys/model/userModel.ts';
import { computed } from 'vue';

const props = defineProps<{
  users: (UserInfoModel | number | undefined)[] | undefined;
  userGroupNames: string[] | undefined;
  serverID?: number;
}>();

const { userList, loadingUserList } = useSysUserList({ containResigned: true });
const renderUsers = computed<UserInfoModel[]>(() => {
  return (props.users || [])
    ?.map((user) => (typeof user === 'number' ? userList.value?.find((item) => item.ID === user) : user))
    .filter(Boolean) as UserInfoModel[];
});
</script>

<style lang="less" scoped>
.group-tag {
  margin: 2px 0;
  padding: 3px 6px;
  display: inline-block;
  border-radius: 6px;
  border: 1px solid #bbb;
}
</style>
