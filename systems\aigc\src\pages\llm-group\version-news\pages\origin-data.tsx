import type { VersionNewsOriginItem } from '@/models/version';
import type { DataTableColumns } from 'naive-ui';
import { defineComponent, nextTick, onActivated, onMounted, ref } from 'vue';
import { VersionNewsOrderOriginDataByLabel, VersionNewsOriginListFormModel, VersionNewsSubjectLabel, VersionNewsTrendLabel } from '@/models/version';
import { NDataTable, NEllipsis, NForm, NFormItemGi, NGrid, NH2, NIcon, NP, NPagination, NSelect, NTag, NTooltip, useThemeVars } from 'naive-ui';

import { useApiRequest } from '@/common/hooks';
import { getVersionNewsVersionNewsOriginList } from '@/apis/version.api';
import { IosLink, Loading3QuartersOutlined } from '@/common/components/svg-icons';
import { TooltipIcon } from '@/common/components/tooltip-icon';
import { RouterLink, useRoute } from 'vue-router';
import { PageName } from '@/configs/page-config';
import { useVersionNewsHook } from '../hook';

const OriginData = defineComponent({
  setup() {
    const {
      mutate: mutateGetDetailList,
      data: detailData,
      loading: detailListLoading,
    } = useApiRequest({
      request: getVersionNewsVersionNewsOriginList,
      errorText: '【Error】获取话题原始数据详情失败',
    });
    const route = useRoute();
    const { subjectsFilterConfig, originSiteConfig } = useVersionNewsHook();

    const firstLoad = ref(true);
    const formModel = ref<VersionNewsOriginListFormModel>(new VersionNewsOriginListFormModel());

    const columns: DataTableColumns<VersionNewsOriginItem> = [
      {
        key: 'trend',
        width: 100,
        title: '情感倾向',
        titleAlign: 'center',
        align: 'center',
      },
      {
        key: 'subject',
        width: 100,
        title: '话题分类',
        titleAlign: 'center',
        align: 'center',
        render(row: VersionNewsOriginItem) {
          return row.subject.map((item) => (
            <NTag class="m-4px" type="info">
              <NTooltip>{{
                trigger: () => <div class="max-w-80px truncate">{item}</div>,
                default: () => item,
              }}
              </NTooltip>
            </NTag>
          ));
        },
      },
      {
        key: 'origin',
        title: '站点名称',
        width: 100,
        titleAlign: 'center',
        align: 'center',
      },
      {
        key: 'summary',
        width: 150,
        title: '标题',
        titleAlign: 'center',
        align: 'center',
        render(row: VersionNewsOriginItem) {
          return (
            <div class="max-w-150px">
              <NEllipsis tooltip={{ contentClass: 'max-w-600px' }}>
                {row.summary || '--'}
              </NEllipsis>
            </div>
          );
        },
      },
      {
        key: 'content',
        title: '内容',
        width: 300,
        titleAlign: 'center',
        render(row: VersionNewsOriginItem) {
          return (
            <div>
              <NEllipsis line-clamp={3} tooltip={{ contentClass: 'max-w-600px max-h-40vh whitespace-pre-wrap', scrollable: true }}>
                {row.content || '--'}
              </NEllipsis>
            </div>
          );
        },
      },
      {
        key: 'mainPost',
        width: 150,
        title: '主帖发表内容',
        titleAlign: 'center',
        render(row: VersionNewsOriginItem) {
          return (
            <div class="max-w-150px">
              <NEllipsis line-clamp={2} tooltip={{ contentClass: 'max-w-600px' }}>
                {row.mainPost || '--'}
              </NEllipsis>
            </div>
          );
        },
      },
      {
        key: 'originContent',
        width: 150,
        title: '源内容',
        titleAlign: 'center',
        render(row: VersionNewsOriginItem) {
          return (
            <div class="max-w-150px">
              <NEllipsis line-clamp={2} tooltip={{ contentClass: 'max-w-600px' }}>
                {row.originContent || '--'}
              </NEllipsis>
            </div>
          );
        },
      },
      {
        key: 'interaction',
        title: '总互动量',
        width: 100,
        titleAlign: 'center',
        align: 'center',
      },
      {
        key: 'action',
        title: '操作',
        titleAlign: 'center',
        align: 'center',
        fixed: 'right',
        width: 100,
        render(row: VersionNewsOriginItem) {
          return (
            <div class="flex-c-center gap-20px">
              <TooltipIcon
                disabled={!row.url}
                onClick={() => {
                  window.open(row.url, '_blank');
                }}
                size={22}
              >
                {{
                  trigger: () => <IosLink />,
                  default: () => (row.url ? '查看原始链接' : '暂无原始链接'),
                }}
              </TooltipIcon>
            </div>
          );
        },
      },
    ];

    const getDetailList = async () => {
      if (detailListLoading.value) {
        return;
      }
      formModel.value.subjectId = route.params.summaryId as string;
      formModel.value.taskId = route.params.taskId! as string;
      formModel.value.secondSummaryId = route.params.secondSummaryId as string;
      await mutateGetDetailList(formModel.value);
      if (firstLoad.value) {
        firstLoad.value = false;
      }
    };

    onMounted(() => {
      getDetailList();
    });

    onActivated(() => {
      getDetailList();
    });

    const getDataByFilter = async () => {
      formModel.value.page = 1;
      nextTick(() => {
        getDetailList();
      });
    };

    const renderDataFromInfo = () => {
      return (
        <div style={{
          '--link-color': useThemeVars().value.primaryColor,
        }}
        >
          {
            route.query.secondContent
              ? (
                <RouterLink
                  class="block max-w-80% flex whitespace-nowrap"
                  to={{
                    name: PageName.VersionNewsSecondSummary,
                    params: {
                      taskId: route.params.taskId,
                    },
                  }}
                >
                  <span class="font-600">{'> '}版本报告：</span>
                  <NEllipsis
                    class="hover:underline"
                    tooltip={{ width: 600 }}
                  >
                    <span class="c-[--link-color]">{route.query.secondContent}</span>
                  </NEllipsis>
                </RouterLink>
              )
              : null
          }
          {
            route.query.content && route.params.summaryId && route.params.taskId
              ? (
                <RouterLink
                  class="block max-w-80% flex whitespace-nowrap"
                  to={{
                    name: PageName.VersionNewsFirstSummary,
                    params: {
                      taskId: route.params.taskId,
                      secondSummaryId: route.params.secondSummaryId,
                    },
                    query: {
                      secondContent: route.query.secondContent,
                    },
                  }}
                >
                  <span class="font-600">{'> '}分类观点：</span>
                  <NEllipsis
                    class="hover:underline"
                    tooltip={{ width: 600 }}
                  >
                    <span class="c-[--link-color]">{route.query.content}</span>
                  </NEllipsis>
                </RouterLink>
              )
              : null
          }
          {
            !route.query.content && !route.query.secondContent && route.params.taskId
              ? (
                <RouterLink
                  class="block"
                  to={{
                    name: PageName.VersionNewsSecondSummary,
                    params: {
                      taskId: route.params.taskId,
                    },
                  }}
                >
                  <NEllipsis
                    class="c-[--link-color] hover:underline"
                    tooltip={{ width: 600 }}
                  >
                    {'> '}全量查询
                  </NEllipsis>
                </RouterLink>
              )
              : null
          }
        </div>
      );
    };

    return () => (
      <div class="version-result-container h-full max-w-full flex flex-shrink-0 flex-grow-1 flex-col overflow-auto pl-20px">
        <NH2 class="max-w-80% flex-shrink-0" prefix="bar">
          原始数据
          <NP depth={2} ml-6px>
            {renderDataFromInfo()}
          </NP>
        </NH2>
        {
          detailListLoading.value && firstLoad.value
            ? (
              <div class="flex-c-center h-400px flex-col">
                <NIcon class="mb-20px" depth={2} size={30}>
                  <Loading3QuartersOutlined class="animate-spin" />
                </NIcon>
                <div>加载中...</div>
              </div>
            )
            : (
              <div class="h-full flex flex-col">
                <NForm
                  class="max-w-800px"
                  labelAlign="left"
                  labelWidth={70}
                  model={formModel.value}
                >
                  <NGrid cols="12" xGap={16} yGap={16}>
                    <NFormItemGi label="站点名称" span={3}>
                      <NSelect
                        clearable
                        filterable
                        onUpdate:value={getDataByFilter}
                        options={originSiteConfig.value}
                        placeholder="全部"
                        v-model:value={formModel.value.origin}
                      />
                    </NFormItemGi>
                    {
                      (!route.query.content && !route.query.secondContent && route.params.taskId)
                        ? (
                          <>
                            <NFormItemGi label="情感倾向" span={3}>
                              <NSelect
                                clearable
                                filterable
                                onUpdate:value={getDataByFilter}
                                options={Object.entries(VersionNewsTrendLabel).map(([value, label]) => ({
                                  label,
                                  value,
                                }))}
                                placeholder="全部倾向"
                                v-model:value={formModel.value.trend}
                              />
                            </NFormItemGi>
                            <NFormItemGi label="话题分类" span={3}>
                              <NSelect
                                clearable
                                filterable
                                onUpdate:value={getDataByFilter}
                                options={subjectsFilterConfig.value ?? Object.entries(VersionNewsSubjectLabel).map(([value, label]) => ({
                                  label,
                                  value,
                                }))}
                                placeholder="全部话题"
                                v-model:value={formModel.value.subject}
                              />
                            </NFormItemGi>
                          </>
                        )
                        : null
                    }

                    <NFormItemGi label="排序方式" span={3}>
                      <NSelect
                        clearable
                        onUpdate:value={getDataByFilter}
                        options={Object.entries(VersionNewsOrderOriginDataByLabel).map(([value, label]) => ({
                          label,
                          value,
                        }))}
                        placeholder="默认排序"
                        v-model:value={formModel.value.order}
                      />
                    </NFormItemGi>
                  </NGrid>
                </NForm>
                <NDataTable
                  bordered={true}
                  class="mb-20px max-w-full flex-1"
                  columns={columns}
                  data={detailData.value?.list ?? []}
                  flexHeight
                  loading={detailListLoading.value}
                  render-cell={(value: string | number) => value || '--'}
                  scroll-x={1250}
                  size="small"
                  striped
                />
                <div class="flex justify-end">
                  <NPagination
                    itemCount={detailData.value?.total}
                    onUpdatePage={getDetailList}
                    pageSize={formModel.value.pageSize}
                    prefix={() => {
                      return `当前数据量：${detailData.value?.total ?? '0'}条`;
                    }}
                    show-quick-jumper
                    v-model:page={formModel.value.page}
                  />
                </div>
              </div>
            )
        }
      </div>
    );
  },
});

export {
  OriginData,
};
