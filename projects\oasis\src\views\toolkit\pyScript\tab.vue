<template>
  <div v-track:v="'l077pz6wbo'" :class="prefixCls">
    <div ref="headerRef" :class="`${prefixCls}__header`">
      <div class="flex items-center justify-between">
        <div class="flex items-center">
          <div class="mr-[24px] flex items-center">
            <div class="flex items-center">
              <router-link class="flex px-2" :to="{ name: PlatformEnterPoint.Oasis }">
                <Icon icon="icon-park-outline:left" :size="24" />
              </router-link>
              <span class="font-size-[20px] font-bold">指令集</span>
            </div>
          </div>
          <div class="ml-4 flex items-center">
            <slot name="beforeTab" />
            <LineTab
              :tabList="menuList"
              :defaultActiveTab="curActiveTab"
              tabMargin="16px"
              :tabAttrs="{ class: '!text-xl' }"
              @change="handleChangeTab"
            />
            <slot name="afterTab" />
          </div>
        </div>
        <div class="flex items-center">
          <slot name="action" />
        </div>
      </div>

      <slot name="filter" />
    </div>

    <div :class="`${prefixCls}__body`" :style="{ height: bodyHeight }">
      <slot />
    </div>
    <div ref="footerRef" :class="`${prefixCls}__footer`">
      <slot name="footer" />
    </div>
  </div>
</template>

<script lang="ts" setup name="InstructionsTab">
import { useResizeObserver } from '@vueuse/core';
import { computed, ref } from 'vue';
import LineTab from '/@/components/LineTab';
import { useDesign } from '/@/hooks/web/useDesign';
import { useGo } from '/@/hooks/web/usePage';
import Icon from '/@/components/Icon/index';
import { useLayoutHeight } from '/@/layouts/default/content/useContentViewHeight';
import { checkPermissionPass, PermissionPoint, PlatformEnterPoint } from '@hg-tech/oasis-common';
import { usePermissionInfo } from '../../../service/permission/usePermission.ts';

const props = defineProps({
  curTab: {
    type: String,
    default: '',
  },
});

const TAB_LIST = [
  {
    name: PlatformEnterPoint.InstructionCombinations,
    icon: 'streamline:programming-browser-code-2-code-browser-tags-angle-programming-bracket',
    title: '指令',
    permissionDeclaration: {
      any: [PermissionPoint.InstructionCombinations],
    },
  },
  {
    name: PlatformEnterPoint.InstructionComponents,
    icon: 'material-symbols:settings-input-component-outline',
    title: '元件',
    permissionDeclaration: {
      any: [PermissionPoint.InstructionComponents],
    },
  },
];

const { prefixCls } = useDesign('instructions-tab');
const { headerHeightRef } = useLayoutHeight();
const headerRef = ref<HTMLDivElement | null>(null);
const footerRef = ref<HTMLDivElement | null>(null);
const { permissionInfo } = usePermissionInfo();
const go = useGo();
const menuList = computed(() => {
  return TAB_LIST.filter((item) => checkPermissionPass(item.permissionDeclaration, permissionInfo.value));
});
const curActiveTab = ref(props.curTab || menuList.value[0]?.name);
const bodyHeight = ref<string>('');

function handleChangeTab(tabName: PlatformEnterPoint) {
  go({ name: tabName });
}

useResizeObserver(headerRef, () => {
  const headerHeight = headerRef.value?.clientHeight || 0;
  const footerHeight = footerRef.value?.clientHeight || 0;
  bodyHeight.value = `calc(100vh - ${headerHeight + footerHeight + headerHeightRef.value + 1}px)`;
});
</script>

<style lang="less">
@prefix-cls: ~'hypergryph-instructions-tab';
.@{prefix-cls} {
  &__header {
    padding: 16px;
    background-color: @FO-Container-Fill1;
    border-radius: 8px;
    margin: 20px;
    margin-bottom: 0;
  }

  &__body {
    overflow: auto;
  }
}
</style>
