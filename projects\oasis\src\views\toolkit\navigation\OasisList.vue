<template>
  <div class="function-list">
    <ARow :gutter="[10, 10]">
      <ACol
        v-for="(item, i) in list"
        :key="item.ID || i"
        :sm="24"
        :md="12"
        :lg="8"
        :xl="6"
        flex="0 0 100%"
      >
        <AListItem :class="{ ['function-list__can-drag']: showManage }">
          <ACard
            :hoverable="true"
            class="function-list__card"
            :class="{ '!cursor-move': showManage }"
            @click="handleClick(item)"
          >
            <div class="function-list__card-flex">
              <div class="function-list__card-icon">
                <img :src="item.icon" alt="icon" class="object-fit h-[60px] w-[60px]">
              </div>
              <div class="function-list__card-content">
                <div class="function-list__card-title">
                  {{ item.title }}
                </div>
                <ATypographyText
                  class="function-list__card-detail"
                  :ellipsis="{ tooltip: true }"
                  :content="item.url"
                />
              </div>
              <div v-if="showManage" class="function-list__card-dropdown">
                <Dropdown
                  placement="bottomLeft"
                  :trigger="['hover']"
                  :dropMenuList="tagEditList"
                  :selectedKeys="selectedKeys"
                  overlayClassName="function-list__card-dropdown-menu"
                  @menuEvent="handleMenuEvent($event, item)"
                >
                  <span class="flex cursor-pointer items-center">
                    <Icon icon="ion:settings-outline" />
                  </span>
                </Dropdown>
              </div>
            </div>
          </ACard>
        </AListItem>
      </ACol>
      <ACol v-if="showManage" :span="4">
        <AListItem>
          <ACard :hoverable="true" class="function-list__add" @click="handleAdd">
            <Icon icon="icon-park-outline:plus" class="function-list__add-icon" :size="50" />
          </ACard>
        </AListItem>
      </ACol>
    </ARow>
  </div>
</template>

<script lang="ts" setup>
import type { DropMenu } from '/@/components/Dropdown';
import { Card as ACard, Col as ACol, ListItem as AListItem, Row as ARow, TypographyText as ATypographyText } from 'ant-design-vue';
import { Dropdown } from '/@/components/Dropdown';
import Icon from '/@/components/Icon/index';
import { useUserStoreWithOut } from '/@/store/modules/user';
import { nextTick, ref, watch } from 'vue';
import type { NavigationsListItem } from '/@/api/page/model/navigtionModel';
import { useMessage } from '/@/hooks/web/useMessage';
import { useSortable } from '/@/hooks/web/useSortable';
import { isNullOrUnDef } from '/@/utils/is';

const props = withDefaults(defineProps<{
  list?: NavigationsListItem[];
  showManage?: boolean;
}>(), {
  list: () => [],
  showManage: false,
});
const emit = defineEmits<{
  (e: 'success'): void;
  (e: 'add'): void;
  (e: 'edit', record: Recordable): void;
  (e: 'delete', id: number): void;
  (e: 'sortList', list: number[]): void;
}>();
const selectedKeys = ref<string[]>([]);
const userStore = useUserStoreWithOut();
const { createConfirm } = useMessage();
// TAG编辑下拉框
const tagEditList: DropMenu[] = [
  {
    text: '编辑',
    event: 'edit',
  },
  {
    text: '删除',
    event: 'delete',
    disabled: !userStore.isSuperAdmin,
  },
];
function handleClick(item: NavigationsListItem) {
  if (!props.showManage) {
    open(item.url, '_blank');
  }
}
function handleMenuEvent(menu: DropMenu, record: Recordable) {
  // todo
  const { event } = menu;
  switch (event) {
    case 'edit':
      handleEdit(record);
      break;
    case 'delete':
      handleDelete(record);
      break;
    default:
      break;
  }
}
async function handleDelete(record: Recordable) {
  createConfirm({
    iconType: 'warning',
    title: '确认删除吗？',
    onOk: () => {
      emit('delete', record.ID);
    },
  });
}
function handleAdd() {
  emit('add');
}
function handleEdit(record: Recordable) {
  emit('edit', record);
}
function initDrag() {
  // tab拖动功能
  nextTick(() => {
    const el = document.querySelectorAll('.function-list .ant-row')?.[0] as HTMLElement;
    const { initSortable } = useSortable(el, {
      handle: '.function-list__can-drag',
      onEnd: async ({ oldIndex, newIndex }) => {
        if (isNullOrUnDef(oldIndex) || isNullOrUnDef(newIndex) || oldIndex === newIndex) {
          return;
        }
        const newSortIdList = props.list.map((item) => item.ID!);
        const currRow = newSortIdList.splice(oldIndex as number, 1)[0];
        newSortIdList.splice(newIndex as number, 0, currRow);
        emit('sortList', newSortIdList);
      },
    });
    initSortable();
  });
}
watch(
  () => props.showManage,
  (v) => {
    if (v) {
      initDrag();
    }
  },
);
</script>

<style lang="less">
.function-list {
  width: 90%;
  margin: 0 auto;
}

.function-list__card {
  position: relative;
  width: 100%;
  height: 120px;

  .ant-card-body {
    padding: 16px;
  }

  &-flex {
    display: flex;
    margin-top: 5px;
    margin-right: 10px;
  }

  &-icon {
    display: flex;
    align-items: center;
    width: 60px;
    height: 60px;
    margin: 5px 15px 5px 0;
    overflow: hidden;
    border-radius: 16px;
  }

  &-content {
    max-width: 70%;
    margin-bottom: 30px;
  }

  &-title {
    margin-bottom: 10px;
    color: @FO-Content-Text1;
    font-size: 16px;
    font-weight: bold;
  }

  &-detail {
    color: @FO-Content-Text2;
    font-size: 14px;
  }

  &-dropdown {
    position: absolute;
    top: 15px;
    right: 15px;
    color: @FO-Brand-Primary-Default;
    font-size: 20px !important;
    cursor: pointer !important;
  }
}

.function-list__add {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 120px;
  height: 120px;
}
</style>
