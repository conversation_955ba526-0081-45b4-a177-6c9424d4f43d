import type { ICommonMenuItem } from '../../configs';
import { PlatformRoutePath } from '../../configs';
import { filterTrees, findInTrees } from '@hg-tech/utils';
import { SegmentTrie } from '../../utils';

/**
 * 检查是否在common路由维护的路由中
 */
export function checkInsideRoutePath(path: string): boolean {
  return Object.values(PlatformRoutePath).includes(path as PlatformRoutePath);
}

/**
 * 根据路由path在指定查找菜单项及其父级路径
 */
export function findItemInMenuByPath(menu: ICommonMenuItem[], path: string) {
  const { path: exactPath, target: exactTarget } = findInTrees(menu, (item) => {
    const pathTrie = new SegmentTrie('/', '{*}');
    if (item.path) {
      pathTrie.insert(item.path);
    }
    // 如果存在path，则直接匹配path，否则模糊匹配
    return Boolean(pathTrie.find(path));
  });
  const { path: fuzzyPath, target: fuzzyTarget } = findInTrees(menu, (item) => {
    const pathTrie = new SegmentTrie('/', '{*}');
    if (item.prefix) {
      item.prefix.forEach((prefix: string) => {
        pathTrie.insert(prefix);
      });
    }
    if (item.path) {
      pathTrie.insert(item.path);
    }
    return pathTrie.fuzzy(path);
  }, {
    childrenField: 'children',
    keyField: 'key',
    order: 'post',
  });

  return {
    target: exactTarget ?? fuzzyTarget,
    path: exactPath.length > 0 ? exactPath : fuzzyPath,
  };
}

/**
 * 过滤符合权限的菜单项
 */
export function filterAuthedMenu(
  menuItems: ICommonMenuItem[],
  permissionChecker: (p: ICommonMenuItem['permissionDeclare'], item: ICommonMenuItem) => boolean,
): ICommonMenuItem[] {
  return filterTrees(menuItems, function checkModuleValid(module: ICommonMenuItem): boolean {
    const currentModuleValid = !module.permissionDeclare || permissionChecker(module.permissionDeclare, module);
    if (!module.children?.length || !currentModuleValid) {
      // 没有子模块或者自身无权限
      return currentModuleValid;
    }

    // 存在子模块有权限
    const filteredSubModules = module.children.filter((s) => checkModuleValid(s));
    return filteredSubModules.length > 0;
  });
}
