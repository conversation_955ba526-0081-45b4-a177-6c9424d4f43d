@import (reference) '@hg-tech/forgeon-style/vars.less';

.forgeonProjectSelector {
  display: flex;
  flex-direction: column;
  gap: 8px;
  width: 100%;
  cursor: pointer;
  &[disabled='true'] {
    cursor: not-allowed;
  }

  .displayContainer {
    padding: 0 16px;
    border-radius: 100px;
    position: relative;
    width: 100%;
    color: @FO-Content-Text1;
    background-color: @FO-Container-Fill2;
    height: 32px;
    display: flex;
    line-height: 32px;
    text-align: center;
    justify-content: space-between;
    align-items: center;
  }
}

.forgeonSelectMenu {
  top: 8px !important;
  .menuTitle {
    cursor: default !important;
    color: @FO-Content-Text1 !important;
    height: 46px;
  }
  .menuItem {
    height: 38px;
    margin-bottom: 4px !important;
    border-radius: 8px !important;

    &:global(.ant-dropdown-menu-item-disabled) {
      cursor: default !important;
      background-color: @FO-Brand-Tertiary-Active;
      &:hover {
        background-color: @FO-Brand-Tertiary-Active !important;
      }
    }
  }

  .checked {
    float: right;
    margin-top: 3px;
  }
}
