<template>
  <BasicModal
    :title="getTitle"
    :width="600"
    destroyOnClose
    @register="registerModal"
    @ok="handleSubmit"
  >
    <BasicForm @register="registerForm" />
  </BasicModal>
</template>

<script lang="ts" setup>
import { computed, ref } from 'vue';
import { BasicModal, useModalInner } from '/@/components/Modal';
import { BasicForm, useForm } from '/@/components/Form';
import type { FormSchema, TableFormType } from '/@/components/Form';
import type { ModalMethods } from '/@/components/Modal';
import { addDeviceAdmin, editDeviceAdmin, getDeviceAdminList, setDeptDeviceAccessLevelList } from '/@/api/page/deptAsset';
import type { DeptListItem } from '/@/api/page/model/systemModel';
import { ResultEnum } from '/@/enums/httpEnum';
import { findNode } from '/@/utils/helper/treeHelper';
import { type DeviceAdminListItem, DeviceAccessLevelEnum } from '/@/api/page/model/deptAssetModel';
import { cloneDeep } from 'lodash-es';
import { getAllPaginationList } from '/@/hooks/web/usePagination';

const emit = defineEmits<{
  register: [methods: ModalMethods, uuid: number];
  success: [type: TableFormType];
}>();

const isUpdate = ref(false);
const editId = ref<number>();
const deptList = ref<DeptListItem[]>([]);
const deviceAdminList = ref<DeviceAdminListItem[]>([]);

// 表单配置
const formSchema = computed((): FormSchema[] => [
  {
    field: 'userID',
    label: '管理员',
    component: 'UserSelect',
    required: true,
    componentProps: {
      placeholder: '请选择管理员',
    },
  },
  {
    field: 'deptID',
    label: '部门范围',
    component: 'TreeSelect',
    required: true,
    dynamicDisabled: isUpdate.value,
    componentProps: {
      treeData: deptList.value,
      fieldNames: {
        label: 'name',
        key: 'ID',
        value: 'ID',
        disabled: 'disabled',
      },
      showArrow: true,
      treeNodeLabelProp: 'orgPath',
      showCheckedStrategy: 'SHOW_PARENT',
      placeholder: '请选择部门范围',
      getPopupContainer: () => document.body,
    },
  },
]);

async function getAdminList() {
  const { list } = await getAllPaginationList(getDeviceAdminList);
  deviceAdminList.value = list || [];
  // 设置禁用已配置的部门范围
  deviceAdminList.value?.forEach((item: DeviceAdminListItem) => {
    const findDept = findNode(deptList.value, (dept) => dept.ID === item.deptID);
    if (findDept) {
      findDept.disabled = true;
    }
  });
}

const [registerForm, { resetFields, setFieldsValue, validate }] = useForm({
  labelWidth: 120,
  baseColProps: { span: 22, offset: 1 },
  schemas: formSchema,
  showActionButtonGroup: false,
});

const [registerModal, { setModalProps, closeModal }] = useModalInner(async (data) => {
  setModalProps({ confirmLoading: true });
  await resetFields();

  try {
    isUpdate.value = !!data?.isUpdate;
    deptList.value = cloneDeep(data?.deptList) || [];
    await getAdminList();

    if (isUpdate.value) {
      editId.value = data?.record?.ID;

      // 设置表单值
      await setFieldsValue({
        userID: data?.record?.userID,
        deptID: data?.record?.deptID,
      });
    }
  } finally {
    setModalProps({ confirmLoading: false });
  }
});

// 弹窗标题
const getTitle = computed(() => (!isUpdate.value ? '新增管理员' : '编辑管理员'));

// 表单提交
async function handleSubmit() {
  try {
    const values = await validate();
    setModalProps({ confirmLoading: true });

    if (!isUpdate.value) {
      // 新增管理员
      const res = await addDeviceAdmin(values);
      if (res?.code === ResultEnum.API_ERROR) {
        return;
      }
      emit('success', 'add');
    } else {
      // 编辑管理员
      const res = await editDeviceAdmin(values, editId.value as number);
      if (res?.code === ResultEnum.API_ERROR) {
        return;
      }
      emit('success', 'edit');
    }
    // 修改流通级别
    await setDeptDeviceAccessLevelList({
      deptID: values.deptID as number,
      accessLevel: DeviceAccessLevelEnum.DEPT,
      deptIds: [values.deptID as number],
    });
    closeModal();
  } catch (error) {
    console.error('提交失败:', error);
  } finally {
    setModalProps({ confirmLoading: false });
  }
}
</script>
