/* This file is automatically generated. DO NOT EDIT it manually. */

export const fontCssVarName = {
  FontB20FontSize: 'var(--FO-Font-B20-Font-Size)',
  FontB20FontWeight: 'var(--FO-Font-B20-Font-Weight)',
  FontB20LineHeight: 'var(--FO-Font-B20-Line-Height)',
  FontB18FontSize: 'var(--FO-Font-B18-Font-Size)',
  FontB18FontWeight: 'var(--FO-Font-B18-Font-Weight)',
  FontB18LineHeight: 'var(--FO-Font-B18-Line-Height)',
  FontB16FontSize: 'var(--FO-Font-B16-Font-Size)',
  FontB16FontWeight: 'var(--FO-Font-B16-Font-Weight)',
  FontB16LineHeight: 'var(--FO-Font-B16-Line-Height)',
  FontR16FontSize: 'var(--FO-Font-R16-Font-Size)',
  FontR16FontWeight: 'var(--FO-Font-R16-Font-Weight)',
  FontR16LineHeight: 'var(--FO-Font-R16-Line-Height)',
  FontB14FontSize: 'var(--FO-Font-B14-Font-Size)',
  FontB14FontWeight: 'var(--FO-Font-B14-Font-Weight)',
  FontB14LineHeight: 'var(--FO-Font-B14-Line-Height)',
  FontR14FontSize: 'var(--FO-Font-R14-Font-Size)',
  FontR14FontWeight: 'var(--FO-Font-R14-Font-Weight)',
  FontR14LineHeight: 'var(--FO-Font-R14-Line-Height)',
  FontR12FontSize: 'var(--FO-Font-R12-Font-Size)',
  FontR12FontWeight: 'var(--FO-Font-R12-Font-Weight)',
  FontR12LineHeight: 'var(--FO-Font-R12-Line-Height)',
};
