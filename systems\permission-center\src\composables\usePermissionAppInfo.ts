import { type Ref, computed, watch } from 'vue';
import { createSharedComposable } from '@vueuse/shared';
import { useLatestPromise } from '@hg-tech/utils-vue';
import { getPermissionAppDetail } from '../api/app.ts';

export const usePermissionAppInfo = createSharedComposable((appIdRef: Ref<number | undefined>) => {
  const { data: appInfoRes, execute: fetchAppInfo, loading: appInfoLoading } = useLatestPromise(getPermissionAppDetail, undefined);
  const appInfo = computed(() => appInfoRes.value?.data?.data);

  watch(appIdRef, refreshAppInfo, { immediate: true });

  function refreshAppInfo() {
    if (!appIdRef.value) {
      return;
    }

    return fetchAppInfo({ appId: appIdRef.value }, {});
  }

  return {
    appInfo,
    appInfoLoading,
    refreshAppInfo,
  };
},
);
