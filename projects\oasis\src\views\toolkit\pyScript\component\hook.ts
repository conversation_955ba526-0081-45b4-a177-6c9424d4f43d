import { ref } from 'vue';
import type { PyScriptComponentListItem } from '/@/api/page/model/pyScriptModel';
import { getPyScriptComponentListByPage } from '/@/api/page/pyScript';

const componentList = ref<PyScriptComponentListItem[]>([]);
export function usePyScriptComponent() {
  const getComponentList = async (filterState?: PyScriptComponentListItem) => {
    const { list } = await getPyScriptComponentListByPage({
      page: 1,
      pageSize: 999,
      ...filterState,
    });
    componentList.value = list || [];
  };
  return {
    componentList,
    getComponentList,
  };
}

export function getSortList(input?: object) {
  const list: any[] = [];
  if (!input) {
    return list;
  }
  for (const key in input) {
    list.push({ name: key, type: input[key].type, sort: input[key].sort });
  }
  return list.sort((a, b) => (a.sort || 0) - (b.sort || 0));
}
