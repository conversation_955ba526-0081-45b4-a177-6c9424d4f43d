<template>
  <div :class="prefixCls">
    <a-card hoverable :class="`${prefixCls}__card`">
      <div v-if="showManage" :class="`${prefixCls}__input is-manage`">
        <div class="flex items-center overflow-hidden">
          <span class="mr-3 min-w-28px font-bold">输入</span>
          <div class="flex items-center overflow-auto">
            <template v-for="{ name, type } in getSortList(item.configJson?.input)" :key="name">
              <div class="flex flex-col mr-2" v-tippy="type">
                <div :class="`${prefixCls}__input-tag-name`">
                  {{ name }}
                </div>
                <div
                  :class="`${prefixCls}__input-tag is-manage`"
                  @click="editInput(name, type)"
                  :title="type"
                >
                  {{ getInputValue(item, name, type) }}
                  <Icon icon="icon-park-outline:edit" :size="12" />
                </div>
              </div>
            </template>
          </div>
        </div>
        <a-popconfirm title="确定要删除该元件吗？" @confirm="deleteItem">
          <div :class="`${prefixCls}__close`" v-if="showManage">
            <Icon icon="icon-park-outline:close" :size="14" />
          </div>
        </a-popconfirm>
      </div>
      <div v-else :class="`${prefixCls}__input`">
        <span class="mr-3 min-w-28px font-bold">输入</span>
        <template v-for="{ name, type } in getSortList(item.configJson?.input)" :key="name">
          <span
            :class="`${prefixCls}__input-tag`"
            @click="editInput(name, type)"
            :title="type"
            v-tippy="type"
          >
            {{ name }}
          </span>
        </template>
      </div>
      <div :class="`${prefixCls}__main`">
        <div>
          <div class="flex items-center">
            <a-typography-text
              :content="item.name"
              :ellipsis="{ tooltip: true }"
              class="font-bold text-lg !max-w-160px !c-FO-Content-Components1"
            />
            <div class="ml-1">
              <Icon
                v-for="platform in item.platform"
                :key="platform"
                :icon="PyScriptPlatformOptions.find((e) => e.value === platform)?.icon"
                class="mr-1"
                size="12"
              />
            </div>
            <div>v{{ item.version }}.0</div>
          </div>
          <div class="border-1 border-white rounded w-fit px-1 mt-2px mb-4px" v-if="!showManage">
            {{ formatNickName(item.author) }}
          </div>
          <div
            class="absolute top-3px right-3px rounded-full bg-#FFDA55 w-14px h-14px flex items-center justify-center cursor-pointer"
            v-if="item.isHistory"
            @click="handleUpgrade(item)"
          >
            <Icon icon="tabler:arrow-big-up-filled" :size="10" color="black" />
          </div>
        </div>
        <div v-if="showManage" class="mx-2">{{ index + 1 }}</div>
        <a-button
          @click="downloadByUrl({ url: item.scriptURL! })"
          type="text"
          title="下载文件"
          v-else
        >
          <Icon icon="ic:round-download" size="20" color="white" />
        </a-button>
      </div>
      <div :class="`${prefixCls}__output`">
        <span class="mr-3 min-w-28px font-bold">输出</span>
        <template v-for="{ name, type } in getSortList(item.configJson?.output)" :key="name">
          <span :class="`${prefixCls}__output-tag`" :title="type" v-tippy="type">
            {{ name }}
          </span>
        </template>
      </div>
    </a-card>
    <CombinationInputModal @register="registerModal" @success="handleInputChange" />
  </div>
</template>

<script lang="ts" setup name="PythonScriptComponentItem">
  import {
    Card as ACard,
    Popconfirm as APopconfirm,
    TypographyText as ATypographyText,
  } from 'ant-design-vue';
  import { isArray } from 'lodash-es';
  import { PyScriptPlatformOptions } from '../component/component.data';
  import { getSortList } from '../component/hook';
  import CombinationInputModal from './InputModal.vue';
  import { environTypeOptions } from './combination.data';
  import { PyScriptComponentListItem } from '/@/api/page/model/pyScriptModel';
  import Icon from '/@/components/Icon';
  import { useModal } from '/@/components/Modal';
  import { formatNickName } from '/@/hooks/system/useUserList';
  import { useDesign } from '/@/hooks/web/useDesign';
  import { downloadByUrl } from '/@/utils/file/download';

  const { prefixCls } = useDesign('python-script-component-item');
  const [registerModal, { openModal }] = useModal();

  const props = defineProps({
    item: {
      type: Object as PropType<PyScriptComponentListItem>,
      default: () => ({}),
    },
    showManage: {
      type: Boolean,
      default: false,
    },
    index: {
      type: Number,
      default: 0,
    },
    list: {
      type: Array as PropType<PyScriptComponentListItem[]>,
      default: () => [],
    },
  });

  const emit = defineEmits(['delete', 'inputChange', 'upgrade']);

  function deleteItem() {
    emit('delete', props.item);
  }

  const handleInputChange = (input: any, data: any) => {
    emit('inputChange', { index: props.index, input, data });
  };
  const editInput = (name: string, type: string) => {
    if (!props.showManage) return;
    openModal(true, {
      name,
      type,
      item: props.item,
      index: props.index,
      list: props.list,
    });
  };

  const getInputValue = (item: PyScriptComponentListItem, name: string, type: string) => {
    const curInput = item?.inputObject?.[name]?.data;
    switch (type) {
      case 'pathfile':
        return '用户选择文件路径';
      case 'pathfolder':
        return '用户选择目录路径';
      case 'choice':
        return curInput?.value ?? '';
      default:
        switch (curInput?.showType) {
          case 'input':
            return curInput?.input ? `(${curInput?.input})` : '';
          case 'environ':
            const findEnvirons = environTypeOptions.find((e) => e.value === curInput?.environ);
            return findEnvirons?.label ?? '';
          case 'prevResult':
          case 'allPrevResult':
            const [_, index, key] = curInput?.[curInput?.showType]?.split('@') ?? [];
            const preScript = props.list?.[index];
            return `【${Number(index) + 1}.${preScript.name}】${key}`;
          case 'static':
          default:
            const curValue = curInput?.[curInput?.showType ?? ''] ?? '';
            return isArray(curValue) ? curValue?.join(', ') : curValue;
        }
    }
  };

  const handleUpgrade = (item: PyScriptComponentListItem) => {
    emit('upgrade', item);
  };
</script>

<style lang="less">
@prefix-cls: ~'hypergryph-python-script-component-item';
  .@{prefix-cls} {
    position: relative;

    &__card {
      overflow: hidden !important;
      border: none !important;
      border-radius: 8px !important;
    }

    & .ant-card-body {
      display: flex !important;
      flex-direction: column;
      justify-content: space-between;
      height: 136px;
      padding: 0 !important;
      overflow: hidden;
    }

    &__close {
      padding: 16px 8px 16px 4px;
      background-color: @report-card-background;
      cursor: pointer;

      &:hover {
        color: @FO-Functional-Error1-Default;
      }
    }

    &__input,
    &__output {
      display: flex;
      align-items: center;
      height: 40px;
      padding: 0 16px;
      overflow: auto;
      background-color: @report-card-background;

      &-tag {
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 4px;
        padding: 0 8px;
        border-radius: 8px;
        background-color: @FO-Container-Fill1;
        white-space: nowrap;
        cursor: pointer;

        &-name {
          margin-left: 8px;
          white-space: nowrap;
        }

        &.is-manage {
          width: 100%;
          height: 22px;
          padding: 0 4px;
          text-align: center;
        }
      }
    }

    &__input {
      justify-content: space-between;

      &.is-manage {
        height: 60px;
        padding: 0 0 0 16px;
      }
    }

    &__main {
      display: flex;
      position: relative;
      flex: 1;
      align-items: center;
      justify-content: space-between;
      padding: 0 16px;
      background-color: @FO-Brand-Primary-Default;
      color: #fff;

      &-file {
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 0 8px;

        & > .ant-typography {
          width: 50px !important;
          margin: 0 !important;
        }
      }
    }
  }
</style>
