import { uploadFile } from '../api/file';
import type {
  FileValidationResult,
  UploadResult,
} from '../types/upload';
import { v4 as uuidv4 } from 'uuid';
/**
 * 验证图片文件格式
 * @param file 要验证的文件
 * @returns 验证结果
 */
export function validateImageFormat(file: File): FileValidationResult {
  // 获取文件扩展名
  const fileName = file.name.toLowerCase();
  const fileExtension = fileName.split('.').pop();

  // 获取文件MIME类型
  const fileMimeType = file.type.toLowerCase();

  // 支持的格式列表
  const supportedFormats = ['jpg', 'jpeg', 'png', 'gif', 'webp', 'svg'];
  const supportedMimeTypes = [
    'image/jpeg',
    'image/jpg',
    'image/png',
    'image/gif',
    'image/webp',
    'image/svg+xml',
  ];

  // 验证文件扩展名
  const isValidExtension = fileExtension && supportedFormats.includes(fileExtension);

  // 验证MIME类型
  const isValidMimeType = supportedMimeTypes.includes(fileMimeType);

  if (!isValidExtension || !isValidMimeType) {
    return {
      isValid: false,
      errorMessage: '请上传图片格式的文件',
    };
  }

  return {
    isValid: true,
  };
}

/**
 * 上传图片文件
 * @param file 要上传的文件
 * @param _onProgress 上传进度回调
 * @returns 上传结果
 */
export async function uploadImageFile(
  file: File,
  _onProgress?: (percent: number) => void,
): Promise<UploadResult> {
  try {
    // 创建FormData
    const formData = new FormData();
    const randomName = `app-icon-${uuidv4()}.${file.name.split('.').pop()}`;
    const newFile = new File([file], randomName, { type: file.type });
    formData.append('file', newFile);

    // 调用上传接口
    const response = await uploadFile({}, formData);

    // 检查响应
    if (response.data?.data?.url?.[0]) {
      return {
        success: true,
        url: response.data.data.url[0],
      };
    } else {
      return {
        success: false,
        errorMessage: response.data?.message || '上传失败，请重试',
      };
    }
  } catch (error: any) {
    console.error('图片上传失败:', error);

    // 处理不同类型的错误
    let errorMessage = '上传失败，请重试';

    if (error?.response?.status === 413) {
      errorMessage = '文件过大，请选择更小的文件';
    } else if (error?.response?.status >= 500) {
      errorMessage = '服务器错误，请稍后重试';
    } else if (error?.message?.includes('Network')) {
      errorMessage = '网络错误，请检查网络连接';
    } else if (error?.response?.data?.message) {
      errorMessage = error.response.data.message;
    }

    return {
      success: false,
      errorMessage,
    };
  }
}
