import { type RuleV1ResolveRule, RuleV1FileNameRuleType } from '@hg-tech/api-schema-merge';
import { FormItem, Input, Popover, Select } from 'ant-design-vue';
import { FileNameRuleOptions, MergeRuleOptions } from '../../../models/config.model';
import { type PropType, computed, defineComponent } from 'vue';

const CustomRuleItem = defineComponent({
  props: {
    domain: {
      type: String as PropType<string>,
      default: '',
    },
    ruleType: {
      type: String as PropType<RuleV1FileNameRuleType>,
      default: undefined,
    },
    pattern: {
      type: String as PropType<string>,
      default: '',
    },
    index: {
      type: Number as PropType<number>,
      default: 0,
    },
    resolveRule: {
      type: String as PropType<RuleV1ResolveRule>,
      default: undefined,
    },
  },
  emits: ['update:ruleType', 'update:pattern', 'update:resolveRule'],
  setup(props, { emit }) {
    const ruleType = computed({
      get: () => props.ruleType,
      set: (value) => {
        emit('update:ruleType', value);
      },
    });

    const pattern = computed({
      get: () => props.pattern,
      set: (value) => {
        emit('update:pattern', value);
      },
    });
    const resolveRule = computed({
      get: () => props.resolveRule,
      set: (value) => {
        emit('update:resolveRule', value);
      },
    });

    const rules = {
      ruleType: [{ required: true, message: '请选择规则类型' }],
      pattern: [
        {
          required: true,
          message: '请输入文件名或正则表达式',
        },
        {
          validator(_: unknown, value: string) {
            try {
              const r = new RegExp(value);
              return Promise.resolve();
            } catch {
              return Promise.reject(new Error('请输入合法的正则表达式'));
            }
          },
        },
      ],
      resolveRule: [{ required: true, message: '请选择解决规则' }],
    };

    return () => (
      <div class="w-full flex gap-12px">
        <FormItem>
          若
        </FormItem>
        <FormItem class="w-180px" name={[props.domain, props.index, 'ruleType']} rules={rules.ruleType}>
          <Select
            allowClear
            placeholder="请选择条件"
            v-model:value={ruleType.value}
          >
            {FileNameRuleOptions.map((rule) => (
              <Select.Option key={rule.value} value={rule.value}>
                {rule.label}
              </Select.Option>
            ))}
          </Select>
        </FormItem>
        <FormItem class="flex-1" name={[props.domain, props.index, 'pattern']} rules={rules.pattern}>
          <Input
            placeholder={ruleType.value === RuleV1FileNameRuleType.REGEX ? '请输入正则表达式' : '请输入匹配文本'}
            v-model:value={pattern.value}
          />
        </FormItem>
        <FormItem>
          则使用
        </FormItem>
        <FormItem class="w-180px" name={[props.domain, props.index, 'resolveRule']} rules={rules.resolveRule}>
          <Select
            allowClear
            optionLabelProp="label"
            placeholder="请选择冲突解决规则"
            showSearch
            v-model:value={resolveRule.value}
          >
            {MergeRuleOptions.map((item) => (

              <Select.Option key={item.value} label={item.label} value={item.value}>
                <Popover overlayClassName="w-300px" placement="top" trigger="hover" z-index={2000}>
                  {{
                    default: () => (
                      <div>
                        <div class="FO-Font-R14 truncate c-FO-Content-Text1">{item.label}</div>
                        <div class="FO-Font-R12 ml-4px truncate c-FO-Content-Text2">({item.desc})</div>
                      </div>
                    ),
                    content: () => (
                      <div>
                        <div class="FO-Font-R14 c-FO-Content-Text1">{item.label}</div>
                        <div class="FO-Font-R12 ml-4px c-FO-Content-Text2">({item.desc})</div>
                      </div>
                    ),
                  }}
                </Popover>
              </Select.Option>

            ))}
          </Select>
        </FormItem>

      </div>
    );
  },
});

export {
  CustomRuleItem,
};
