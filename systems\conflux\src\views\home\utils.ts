import type { RuleV1MergeTrigger, RuleV1ResolveRule } from '@hg-tech/api-schema-merge';

export interface NodeData {
  id: number;
  name: string;
  status: string;
  level?: number;
}

export interface EdgeData {
  source: number;
  target: number;
  triggerType: RuleV1MergeTrigger;
  status: boolean;
  resolveRule: RuleV1ResolveRule;
  ruleId?: string;
  taskCount?: number;
}

export interface Group {
  nodes: NodeData[];
  edges: EdgeData[];
}

/**
 * 计算连接节点的分组
 */
export function groupConnectedNodes(
  nodes: NodeData[],
  edges: EdgeData[],
  /**
   * 获取节点层级分数的函数
   */
  getLayerScore: (node: NodeData, inDeg: number, outDeg: number) => number = (node, inDeg) => -inDeg,
): Group[] {
  const idToNode: Record<string, NodeData> = Object.fromEntries(nodes.map((n) => [n.id, { ...n }]));
  const nodeToEdges: Record<string, EdgeData[]> = {};

  for (const edge of edges) {
    nodeToEdges[edge.source] ??= [];
    nodeToEdges[edge.source].push(edge);
    nodeToEdges[edge.target] ??= [];
    nodeToEdges[edge.target].push(edge);
  }

  const visitedEdges = new Set<string>();
  const edgeGroups: EdgeData[][] = [];

  for (const edge of edges) {
    const edgeKey = `${edge.source}|${edge.target}`;
    if (visitedEdges.has(edgeKey)) {
      continue;
    }

    const groupEdges: EdgeData[] = [];
    const stack = [edge];
    const visitedInGroup = new Set<string>();

    while (stack.length) {
      const curr = stack.pop()!;
      const key = `${curr.source}|${curr.target}`;
      if (visitedEdges.has(key)) {
        continue;
      }

      visitedEdges.add(key);
      groupEdges.push(curr);

      for (const nodeId of [curr.source, curr.target]) {
        for (const e of nodeToEdges[nodeId] || []) {
          const k = `${e.source}|${e.target}`;
          if (!visitedEdges.has(k) && !visitedInGroup.has(k)) {
            stack.push(e);
            visitedInGroup.add(k);
          }
        }
      }
    }

    edgeGroups.push(groupEdges);
  }

  const groups: Group[] = [];

  for (const edgeGroup of edgeGroups) {
    const groupNodeIds = new Set<number>();
    for (const e of edgeGroup) {
      groupNodeIds.add(e.source);
      groupNodeIds.add(e.target);
    }

    const groupNodes = Array.from(groupNodeIds).map((id) => idToNode[id]).filter(Boolean);
    const inDeg: Record<number, number> = {};
    const outDeg: Record<number, number> = {};
    const adj: Record<number, Set<number>> = {};

    for (const n of groupNodes) {
      if (!n.id) {
        continue;
      }
      inDeg[n.id] = 0;
      outDeg[n.id] = 0;
      adj[n.id] = new Set();
    }

    for (const e of edgeGroup) {
      // 只处理单向边，忽略双向（即 source->target 和 target->source 都存在的情况）
      const hasReverse = edgeGroup.some(
        (rev) => rev.source === e.target && rev.target === e.source,
      );
      if (hasReverse) {
        continue;
      }
      outDeg[e.source]++;
      inDeg[e.target]++;
      adj[e.source].add(e.target);
    }

    const levelMap: Record<number, number> = {};
    const queue: { node: NodeData; level: number }[] = [];

    const initialNodes = groupNodes
      .filter((n) => inDeg[n.id] === 0)
      .sort((a, b) =>
        getLayerScore(b, inDeg[b.id], outDeg[b.id])
        - getLayerScore(a, inDeg[a.id], outDeg[a.id]),
      );

    for (const n of initialNodes) {
      queue.push({ node: n, level: 0 });
      levelMap[n.id] = 0;
    }

    // 递归函数用于分配层级
    // 通过队列处理每个节点，分配层级并更新相邻
    function assignLevels(nodesQueue: { node: NodeData; level: number }[]) {
      const visitedNodes = new Set<number>();
      let maxLevel = 0;

      const processQueue = (queue: { node: NodeData; level: number }[]) => {
        if (queue.length === 0) {
          return;
        }
        const { node, level } = queue[0];
        const rest = queue.slice(1);
        if (visitedNodes.has(node.id)) {
          processQueue(rest);
          return;
        }
        visitedNodes.add(node.id);
        idToNode[node.id].level = level;
        maxLevel = Math.max(maxLevel, level);
        let newQueue = rest;
        for (const neighborId of adj[node.id]) {
          const prev = levelMap[neighborId] ?? -1;
          if (level + 1 > prev) {
            levelMap[neighborId] = level + 1;
            if (idToNode[neighborId]) {
              newQueue = [...newQueue, { node: idToNode[neighborId], level: level + 1 }];
            }
          }
        }
        processQueue(newQueue);
      };

      processQueue(nodesQueue);
    }
    assignLevels(queue);

    // 保证level为0的节点只有一个，如果有多个，则只保留一个为level0，其余全部+1
    const level0Nodes = groupNodes.filter((n) => n.level === 0);
    if (level0Nodes.length > 1) {
      // 选择一个作为level0，其余全部+1
      // 这里选择id最小的
      const mainNode = level0Nodes.reduce((min, n) => (n.id < min.id ? n : min), level0Nodes[0]);
      for (const n of level0Nodes) {
        if (n.id !== mainNode.id) {
          n.level = 1;
        }
      }
      // 其余节点level也要整体+1（除了mainNode）
      for (const n of groupNodes) {
        if (n.id !== mainNode.id && n.level !== undefined) {
          n.level = n.level! + 1;
        }
      }
      mainNode.level = 0;
    }

    // 按 level 从小到大排序，并尽量让有连接的节点在每层靠近
    const nodesByLevel: Record<number, NodeData[]> = {};
    for (const n of groupNodes) {
      const lvl = n.level ?? 0;
      nodesByLevel[lvl] ??= [];
      nodesByLevel[lvl].push(n);
    }

    // 记录每个节点在上一层的顺序
    const nodeOrder: Record<number, number> = {};

    // 第一层按原顺序
    const sortedGroupNodes: NodeData[] = [];
    const maxLevel = Math.max(...Object.keys(nodesByLevel).map(Number));
    for (let lvl = 0; lvl <= maxLevel; lvl++) {
      const nodes = nodesByLevel[lvl] ?? [];
      if (lvl === 0) {
        nodes.forEach((n, idx) => {
          nodeOrder[n.id] = idx;
        });
        sortedGroupNodes.push(...nodes);
      } else {
      // 统计每个节点与上一层节点的连接顺序
        nodes.sort((a, b) => {
        // 取与上一层连接节点的最小order
          const aOrders = Array.from(adj[a.id] ?? [])
            .filter((id) => (nodesByLevel[lvl - 1] ?? []).some((n) => n.id === id))
            .map((id) => nodeOrder[id]);
          const bOrders = Array.from(adj[b.id] ?? [])
            .filter((id) => (nodesByLevel[lvl - 1] ?? []).some((n) => n.id === id))
            .map((id) => nodeOrder[id]);
          const aMin = aOrders.length ? Math.min(...aOrders) : Number.MAX_SAFE_INTEGER;
          const bMin = bOrders.length ? Math.min(...bOrders) : Number.MAX_SAFE_INTEGER;
          // 有连接的排前面，没连接的排后面
          if (aMin !== bMin) {
            return aMin - bMin;
          }
          // 否则按id排序
          return a.id - b.id;
        });
        nodes.forEach((n, idx) => {
          nodeOrder[n.id] = idx;
        });
        sortedGroupNodes.push(...nodes);
      }
    }

    groups.push({
      nodes: sortedGroupNodes,
      edges: edgeGroup,
    });
  }
  return groups;
}

export function addOpacityToColor(color: string, opacity: number): string {
  // 支持 #RRGGBB, #RGB, rgb(), rgba()
  if (color.startsWith('#')) {
    let hex = color.slice(1);
    if (hex.length === 3) {
      hex = hex.split('').map((c) => c + c).join('');
    }
    if (hex.length === 6) {
      const r = Number.parseInt(hex.slice(0, 2), 16);
      const g = Number.parseInt(hex.slice(2, 4), 16);
      const b = Number.parseInt(hex.slice(4, 6), 16);
      return `rgba(${r},${g},${b},${opacity})`;
    }
    // 不支持的格式，直接返回原色
    return color;
  }
  const rgbMatch = color.match(/^rgb\((\d+),\s*(\d+),\s*(\d+)\)$/);
  if (rgbMatch) {
    const [, r, g, b] = rgbMatch;
    return `rgba(${r},${g},${b},${opacity})`;
  }
  const rgbaMatch = color.match(/^rgba\((\d+),\s*(\d+),\s*(\d+),\s*([0-9.]+)\)$/);
  if (rgbaMatch) {
    const [, r, g, b] = rgbaMatch;
    return `rgba(${r},${g},${b},${opacity})`;
  }
  // 其他格式直接返回
  return color;
}
