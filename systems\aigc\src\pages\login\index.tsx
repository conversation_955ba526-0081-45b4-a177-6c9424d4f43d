import qs from 'qs';
import { defineComponent, onBeforeMount } from 'vue';
import { useRouter } from 'vue-router';

import { CLIENT_ID, LOGIN_PAGE_URI } from '@/common/constants/login.constant';
import { useUserBase } from '@/common/hooks';
import { GlobalEnv } from '@/configs/global-env';
import { useMicroAppInject, useRouteNavigationCtx } from '@hg-tech/oasis-common';
import { NIcon, NP } from 'naive-ui';
import { Loading3QuartersOutlined } from '@/common/components/svg-icons';
import { safeDecodeURL } from '@/common/utils/url/compressor';
import { PageName } from '@/configs/page-config';

const LoginPage = defineComponent({
  setup() {
    const router = useRouter();
    const { checkIsLogin, getUserInfo } = useUserBase();

    const initLoginPageUrl = () => {
      const uri = LOGIN_PAGE_URI[GlobalEnv.HGEnv];
      const redirect = router.currentRoute.value.query.redirect;
      const querystring = qs.stringify({
        client_id: CLIENT_ID,
        redirect_uri: `${location.origin}${router.resolve({ name: PageName.Login }).path}${redirect ? `?redirect=${redirect}` : ''}`,
      });
      return `${uri}/oauth2/authorize?response_type=code&${encodeURI(querystring)}`;
    };

    const login = async () => {
      const { code, redirect, logout } = router.currentRoute.value.query;
      if (logout) {
        location.replace(initLoginPageUrl());
        return;
      }

      if (window.__MICRO_APP_ENVIRONMENT__) {
        await getUserInfo();
      } else {
        if (!code) {
          location.replace(initLoginPageUrl());
        } else {
          await getUserInfo(
            code as string,
            `${location.origin}${router.resolve({ name: PageName.Login }).href}${redirect ? `?redirect=${redirect}` : ''}`,
          );
        }
      }

      if (await checkIsLogin()) {
        // 登录成功，获取decode后的redirect
        const { redirect = '' } = router.currentRoute.value.query;
        router.replace(safeDecodeURL(redirect as string || ''));
      } else {
        if (window.__MICRO_APP_ENVIRONMENT__) {
          const { data } = useMicroAppInject(useRouteNavigationCtx);
          data.value?.onUnauthorized('登录失败，请重新登录');
        }
      }
    };

    onBeforeMount(() => {
      login();
    });

    // 此处作为router page，必须返回一个组件而不能是null，否则会引起transition组件在out-in模式下的报错
    return () => (
      <div class="bg-FO-Container-Fill2 h-full min-h-500px w-full flex flex-col items-center justify-center">
        <NIcon depth={3} size={40}>
          <Loading3QuartersOutlined class="animate-spin" />
        </NIcon>
        <NP class="FO-Font-B18">
          正在登录中...
        </NP>
      </div>
    );
  },

});

export { LoginPage };
