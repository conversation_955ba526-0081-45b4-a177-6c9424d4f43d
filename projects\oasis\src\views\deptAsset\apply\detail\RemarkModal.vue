<template>
  <Modal
    :width="600"
    :open="show"
    :maskClosable="false"
    destroyOnClose
    centered
    :afterClose="modalDestroy"
    @cancel="() => modalCancel()"
  >
    <template #title>
      <div class="text-center">
        <span class="font-size-[16px] font-bold">
          <span>编辑备注</span>
        </span>
      </div>
    </template>
    <div class="m-[20px]">
      <ATextarea v-model:value="remark" :rows="4" placeholder="请输入备注" :maxlength="200" />
    </div>
    <template #footer>
      <div class="mt flex justify-end">
        <a-button @click="modalDestroy()">
          取消
        </a-button>
        <a-button type="primary" class="ml-2" @click="handleConfirm">
          确定
        </a-button>
      </div>
    </template>
  </Modal>
</template>

<script lang="ts" setup>
import { Textarea as ATextarea, Modal } from 'ant-design-vue';
import type { ModalBaseProps } from '@hg-tech/utils-vue';
import { ref } from 'vue';
import type { NullableBasicResult } from '/@/api/model/baseModel';

const props = defineProps< ModalBaseProps<{ updatedItem?: NullableBasicResult }> & {
  remark: string;
  sentReq?: (formValue: string) => Promise<NullableBasicResult | undefined>;
}>();
const remark = ref(props.remark);
async function handleConfirm() {
  const updatedItem = await props.sentReq?.(remark.value);

  return props.modalConfirm({ updatedItem });
}
</script>
