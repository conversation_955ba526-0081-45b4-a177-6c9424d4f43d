<template>
  <div class="toolkit-package-branches">
    <div class="mb-4 text-center text-lg font-bold">
      我的工具配置
    </div>
    <div
      class="toolkit-package-branches__list"
      :style="{ maxHeight: `calc(100vh - 152px)` }"
    >
      <div v-for="branch in branchList" :key="branch.ID" class="toolkit-package-branches__list-item">
        <img :src="branch.icon" alt="" class="ml-2px inline-block w-64px rounded-lg">
        <div class="mx-3 w-0 flex-1">
          <div>
            <span class="c-FO-Content-Text1 font-bold">{{ branch.name }}</span>
            <span class="ml-2 text-xs c-FO-Content-Text2">ID: {{ branch.ID }}</span>
          </div>
          <div class="my-1 flex flex-wrap items-center">
            <div v-for="pID in branch.platforms" :key="pID" class="toolkit-package-branches__list-item-tag">
              {{ getPlatformIconByVal(pID)!.label }}
            </div>
            <div class="toolkit-package-branches__list-item-tag">
              {{ branch?.project?.name }}
            </div>
          </div>
          <div v-if="branch.adminIds?.length" class="my-1 flex flex-wrap items-center gap-1">
            <div>工具管理者： </div>
            <div v-for="(id, index) in branch.adminIds" :key="id">
              {{ id }}
              TODO
              <span v-if="branch.adminIds.length - 1 !== index">、</span>
            </div>
          </div>
        </div>
        <div class="flex flex-wrap items-center">
          <Button
            size="small"
            shape="round"
            class="toolkit-package-branches__list-item-btn"
            @click="handleEdit(branch)"
          >
            <div class="flex items-center">
              <Icon :icon="editorIcon" class="mr-1" />
              <span class="!ml-1">编辑工具</span>
            </div>
          </Button>
          <Button
            size="small"
            shape="round"
            :preIcon="addOneIcon"
            class="toolkit-package-branches__list-item-btn"
            @click="addVersion(branch)"
          >
            <div class="flex items-center">
              <Icon :icon="addOneIcon" class="mr-1" />
              <span class="!ml-1">添加版本</span>
            </div>
          </Button>
          <APopconfirm title="确定要删除吗" @confirm="handleDelete(branch)">
            <Button
              size="small"
              shape="round"
              class="toolkit-package-branches__list-item-btn"
            >
              <div class="flex items-center">
                <Icon :icon="deleteIcon" class="mr-1" />
                <span class="!ml-1">删除</span>
              </div>
            </Button>
          </APopconfirm>
        </div>
      </div>
    </div>
    <div class="toolkit-package-branches__add" @click="handleCreate()">
      <IconWrapper icon="ant-design:plus-outlined" :size="30" />
    </div>
    <ToolkitSettingsHolder />
    <VersionDrawerHolder />
  </div>
</template>

<script lang="ts" setup>
import { Popconfirm as APopconfirm, Button, message } from 'ant-design-vue';
import { cloneDeep } from 'lodash-es';
import { onBeforeMount, ref } from 'vue';
import ToolkitSettingsDrawer from './ToolkitSettingsDrawer.vue';
import VersionDrawer from './VersionDrawer.vue';
import { Icon } from '@iconify/vue';
import editorIcon from '@iconify-icons/icon-park-outline/editor';
import addOneIcon from '@iconify-icons/icon-park-outline/add-one';
import deleteIcon from '@iconify-icons/icon-park-outline/delete';
import type {
  CommonTypeListItem,
  ProjectListItem,
  ToolkitListItem,
} from '../../../api/model';
import {
  deleteToolkit,
  getProjectListByPage,
  getToolkitByID,
  getToolkitListByPage,
  getToolkitTypeListByPage,
} from '../../../api';
import { platformOptions } from '../../configs.tsx';
import { useModalShow } from '@hg-tech/utils-vue';

const clonePlatformOptions = cloneDeep(platformOptions);
const [ToolkitSettingsHolder, ToolkitSettingsShow] = useModalShow(ToolkitSettingsDrawer);
const [VersionDrawerHolder, VersionDrawerShow] = useModalShow(VersionDrawer);

const branchList = ref<ToolkitListItem[]>([]);
const toolkitTypeList = ref<CommonTypeListItem[]>([]);
const projectList = ref<ProjectListItem[]>([]);
const platforms = ref<number[]>([]);
async function getList() {
  const res = await getToolkitListByPage({
    maintain: true,
    page: 1,
    pageSize: 999,
  }, {});
  branchList.value = res.data.data?.list || [];
}

// 获取平台icon
function getPlatformIconByVal(val: number) {
  return clonePlatformOptions.find((e) => e.value === val);
}

onBeforeMount(async () => {
  getToolkitTypeList();
  getProjectList();
  await getList();
});
// 获取工具类型类别
async function getToolkitTypeList() {
  const res = await getToolkitTypeListByPage({ page: 1, pageSize: 999 }, {});
  toolkitTypeList.value = res.data.data?.list || [];
}

// 获取简要项目列表
async function getProjectList() {
  const res = await getProjectListByPage({ page: 1, pageSize: 999 }, {});
  projectList.value = res.data.data?.list || [];
}

async function getToolkitDetail(toolId: number) {
  const res = await getToolkitByID({ ID: toolId }, {});
  const retool = res.data.data?.retool;
  if (!retool) {
    return;
  }
  platforms.value = retool?.platforms || [];
}

function handleCreate() {
  ToolkitSettingsShow({
    isUpdate: false,
    toolkitTypeList: toolkitTypeList.value,
    projectList: projectList.value,
  });
}
async function addVersion(record: ToolkitListItem) {
  if (!record.ID) {
    return;
  }
  await getToolkitDetail(record.ID);
  VersionDrawerShow({
    isUpdate: false,
    toolId: record.ID,
    platforms: platforms.value,
    lastVersion: record.latestVersion,
  });
}
function handleEdit(record: ToolkitListItem) {
  ToolkitSettingsShow({
    record,
    isUpdate: true,
    toolkitTypeList: toolkitTypeList.value,
    projectList: projectList.value,
  });
}
async function handleDelete(record: ToolkitListItem) {
  if (!record.ID) {
    return;
  }
  await deleteToolkit({ editId: record.ID }, {});
  handleSuccess();
  message.success('删除成功');
}
async function handleSuccess() {
  await getList();
}
</script>

<style lang="less" scoped>
@import (reference) '@hg-tech/forgeon-style/vars.less';

.toolkit-package-branches {
  position: relative;
  padding: 16px;
  border-radius: 8px;
  background-color: @FO-Container-Fill1;

  &__add {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 8px;
    transition: all 0.3s ease-in-out;
    border-radius: 8px;
    background-color: var(--member-card-background-color);
    cursor: pointer;
    user-select: none;

    &:hover {
      filter: brightness(0.9);
    }
  }

  &__list {
    overflow: auto;

    &-item {
      display: flex;
      align-items: center;
      margin-bottom: 16px;
      padding: 8px;
      transition: all 0.3s ease-in-out;
      border-radius: 8px;
      background-color: var(--member-card-background-color);
      cursor: pointer;
      user-select: none;

      &:hover {
        filter: brightness(0.9);
      }

      &-tag {
        margin-right: 4px;
        padding: 0 2px;
        transform: scale(0.9);
        transform-origin: left center;
        border: 1px solid @FO-Content-Text1;
        border-radius: 6px;
        font-size: 12px;
      }

      &-btn {
        margin-right: 8px;
        border-color: @FO-Container-Fill6 !important;
        background-color: @FO-Container-Fill6 !important;
        color: @FO-Content-Components1 !important;

        &:hover {
          border-color: #616161 !important;
          background-color: #616161 !important;
        }
      }
    }
  }
}
</style>
