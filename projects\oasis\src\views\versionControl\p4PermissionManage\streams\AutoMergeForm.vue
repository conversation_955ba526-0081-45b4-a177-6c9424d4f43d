<template>
  <div>
    <a-tabs
      v-model:activeKey="activeTab"
      type="editable-card"
      :animated="false"
      @edit="handleOriginEdit"
    >
      <template #addIcon>
        <a-button
          type="success"
          preIcon="ant-design:plus-outlined"
          :iconSize="12"
          size="small"
          ghost
          class="!m-0 !p-0"
          shape="circle"
          @click.stop="onEdit('', 'add')"
        />
      </template>
      <a-tab-pane
        v-for="(config, configIdx) in configList"
        :key="config.key"
        forceRender
        :closable="configList.length > 1"
      >
        <template #tab>
          <div :class="`${prefixCls}__tab`">
            <div :class="`${prefixCls}__tab-text`">
              {{ config.title }}
            </div>
            <div :class="`${prefixCls}__tab-title`" :title="config.title">
              {{ config.title }}
            </div>
          </div>
        </template>
        <template #closeIcon>
          <a-button
            preIcon="ant-design:close-outlined"
            size="small"
            :iconSize="12"
            ghost
            shape="circle"
            class="!m-0 !border-gray-500 !p-0 !text-gray-500 !hover:border-gray-400 !hover:text-gray-400"
            @click.stop="onEdit(config.key, 'remove')"
          />
        </template>
        <BasicForm @register="config.form[0]">
          <template #toStreamID="{ model, field }">
            <a-select
              v-model:value="model[field]"
              :options="showStreamList"
              placeholder="请选择合入分支"
              allowClear
              showSearch
              optionFilterProp="description"
              :dropdownMatchSelectWidth="false"
              :fieldNames="{ label: 'description', value: 'ID' }"
            >
              <template #option="{ path, description }">
                <div>{{ description }}</div>
                <div class="c-FO-Content-Text2 text-xs">
                  {{ path }}
                </div>
              </template>
            </a-select>
          </template>
          <template #tagID="{ model, field }">
            <div class="flex">
              <a-select
                v-model:value="model[field]"
                :options="tagList"
                placeholder="请选择Tag"
                allowClear
                showSearch
                optionFilterProp="name"
                :dropdownMatchSelectWidth="false"
                :fieldNames="{ label: 'name', value: 'ID' }"
                @change="handleTagChange(model, configIdx)"
              >
                <template #option="{ name, description }">
                  <div>{{ name }}</div>
                  <div class="c-FO-Content-Text2 text-xs">
                    {{ description }}
                  </div>
                </template>
              </a-select>
            </div>
          </template>
          <template #rule="{ model, field }">
            <a-select
              v-model:value="model[field]"
              :options="autoMergeRuleOptions"
              placeholder="请选择冲突处理模式"
              allowClear
              showSearch
              optionFilterProp="label"
              :dropdownMatchSelectWidth="false"
            >
              <template #option="{ label, description }">
                <div>{{ label }}</div>
                <div class="c-FO-Content-Text2 text-xs">
                  {{ description }}
                </div>
              </template>
            </a-select>
          </template>
        </BasicForm>
      </a-tab-pane>
    </a-tabs>
  </div>
</template>

<script lang="ts" setup>
import { computed, nextTick, ref } from 'vue';
import {
  autoMergeRuleOptions,
  configAutoMergeFormSchema,
} from './fastAddStream/fastAddStream.data';
import type { P4AutoMergeListItem, streamOption, StreamsListItem } from '/@/api/page/model/p4Model';
import {
  addP4AutoMerge,
  editP4AutoMerge,
  getDepotsListByPage,
  getStreamsListByPage,
  getTagConfigInfo,
} from '/@/api/page/p4';
import type { UseFormReturnType } from '/@/components/Form';
import { BasicForm, useForm } from '/@/components/Form';
import { useDesign } from '/@/hooks/web/useDesign';
import { useMessage } from '/@/hooks/web/useMessage';
import { useUserStoreWithOut } from '/@/store/modules/user';
import { buildUUID } from '/@/utils/uuid';
import { getAllPaginationList } from '/@/hooks/web/usePagination';
import { useLatestPromise } from '@hg-tech/utils-vue';
import type { TagListItem } from '/@/api/page/model/submitConfModel';

defineOptions({
  name: 'AutoMergeModal',
});

defineEmits(['register']);

const { data: configInfo, execute: fetchConfigInfo } = useLatestPromise(getTagConfigInfo);

const { prefixCls } = useDesign('auto-merge-modal');

const userStore = useUserStoreWithOut();
const { createMessage } = useMessage();
const isUpdate = ref(false);
const editIds = ref<number[]>([]);

const streamID = ref<number>();
const streamList = ref<StreamsListItem[]>([]);
const autoMerge = ref<P4AutoMergeListItem[]>([]);

const tagList = ref<TagListItem[]>([]);
const streamOptionList = ref<streamOption[]>([]);
const activeTab = ref<string>();
const formProps = {
  labelWidth: 120,
  schemas: configAutoMergeFormSchema,
  showActionButtonGroup: false,
  baseColProps: {
    span: 20,
  },
};
const configList = ref<{ title: string; form: UseFormReturnType; key: string }[]>([]);

async function getTagList() {
  tagList.value = [];
  if (!userStore.getProjectId || !streamID.value) {
    return;
  }
  await fetchConfigInfo(userStore.getProjectId!, streamID.value);
  if (configInfo.value?.commit && configInfo.value.commit.length > 0) {
    tagList.value = configInfo.value?.commit || [];
  }
}

function getTitleName(ID: number, index: number) {
  return tagList.value?.find((item) => item.ID === ID)?.name || `配置${index + 1}`;
}

// 获取分支列表
async function getStreamList(depotID) {
  const { list } = await getAllPaginationList((p) => getStreamsListByPage(userStore.getProjectId!, {
    depotID,
    ...p,
  }));

  return list;
}

// 获取仓库列表
async function getDepotList() {
  if (userStore.getProjectId) {
    const { list } = await getAllPaginationList((p) => getDepotsListByPage(userStore.getProjectId!, p));

    if (list?.length > 0) {
      streamOptionList.value = [];
      list.map(async (depot) => {
        const streams = await getStreamList(depot.ID);

        streamOptionList.value.push({
          ID: Number(depot.ID),
          description: depot.description,
          options: streams,
        });
        streamOptionList.value = streamOptionList.value.sort(
          (a, b) => (a.ID || 0) - (b.ID || 0),
        );
      });
    } else {
      streamOptionList.value = [];
    }
  }
}

const showStreamList = computed(() =>
  streamList.value?.filter((item) => item.ID !== streamID.value && item.streamType !== 4));

async function init(data) {
  isUpdate.value = !!data.isUpdate;
  editIds.value = data.editIds || [];
  autoMerge.value = data.autoMerge || [];
  streamID.value = data.streamID;
  streamList.value = data.streamList || [];
  configList.value = [];

  await getDepotList();
  await getTagList();

  addTab();

  if (isUpdate.value) {
    editIds.value?.forEach((_, index) => {
      if (index > 0) {
        addTab();
      }
    });
    nextTick(() => {
      configList.value.forEach((item, index) => {
        item.title = getTitleName(autoMerge.value[index].tagID!, index);
        item.form[1].setFieldsValue({
          ...autoMerge.value[index],
          tagID: autoMerge.value[index]?.tagID || undefined,
        });
      });
    });
  }
}

function getPathByStreamID(ID?: number) {
  return streamList.value?.find((item) => item.ID === ID)?.path;
}

async function handleSubmit() {
  if (!streamID.value) {
    return;
  }

  const submitData: Recordable[] = [];

  for (const item of configList.value) {
    const { validate } = item.form[1];

    try {
      const data = await validate();

      if (!data) {
        return;
      }

      const values = {
        ...data,
        toStream: getPathByStreamID(data.toStreamID),
        fromStream: getPathByStreamID(streamID.value),
        tag: tagList.value.find((item) => item.ID === data.tagID)?.name,
      };

      submitData.push(values);
    } catch {
      activeTab.value = item.key;
    }
  }

  submitData.forEach((item, index) => {
    const isToStreamRepeat = submitData.some(
      (item2, index2) => index !== index2 && item.toStream === item2.toStream,
    );
    const isTagRepeat = submitData.some(
      (item2, index2) => index !== index2 && item.tag === item2.tag,
    );

    if (isToStreamRepeat) {
      createMessage.warning('多配置「合入分支」不能重复');

      throw new Error('多配置「合入分支」不能重复');
    }

    if (isTagRepeat) {
      createMessage.warning('多配置「Tag」不能重复');

      throw new Error('多配置「Tag」不能重复');
    }
  });

  if (submitData.length !== configList.value.length) {
    createMessage.warning('请完善表单');

    throw new Error('请完善表单');
  }

  if (!isUpdate.value) {
    await addP4AutoMerge(userStore.getProjectId, streamID.value, submitData);
  } else {
    await editP4AutoMerge(userStore.getProjectId, streamID.value, submitData);
  }
}

async function handleTagAddSuccess(ID: number) {
  await getTagList();

  const index = configList.value.findIndex((item) => item.key === activeTab.value);
  const curConfig = configList.value[index];

  curConfig.title = getTitleName(ID, index);
  await curConfig.form[1].setFieldsValue({
    tagID: ID,
  });
}

function addTab() {
  const newTabIndex = configList.value.length + 1;
  const title = `配置${newTabIndex}`;
  const key = buildUUID();

  configList.value.push({
    title,
    key,
    form: useForm(formProps),
  });
  activeTab.value = key;
}

function removeTab(targetKey: string) {
  configList.value = configList.value.filter((item) => item.key !== targetKey);
  activeTab.value = configList.value[0].key;
}

function handleOriginEdit(targetKey: string | MouseEvent, action: string) {
  // 点击删除空白区相当于激活tab
  if (action === 'remove') {
    activeTab.value = targetKey as string;
  }
}

function onEdit(targetKey: string | MouseEvent, action: string) {
  if (action === 'add') {
    addTab();
  } else {
    removeTab(targetKey as string);
  }
}

function handleTagChange(record: Recordable, configIdx: number) {
  configList.value[configIdx].title = getTitleName(record.tagID, configIdx);
}

defineExpose({
  handleSubmit,
  init,
});
</script>

<style lang="less">
@prefix-cls: ~'hypergryph-auto-merge-modal';
.@{prefix-cls} {
  &__tab {
    display: block;
    position: relative;
    line-height: 2;
    height: 2em;
    overflow: hidden;
    max-width: 120px;
    white-space: normal;
    word-break: break-all;
    background-color: @tab-bg-color;
    transition: all 0.3s ease-in-out;

    &-text {
      max-height: 4em;
      display: block;
    }

    &-title {
      display: block;
      position: relative;
      background: inherit;
      text-align: end;
      overflow: hidden;
      height: 2em;
      top: -4em;

      &::before {
        content: attr(title);
        width: 50%;
        float: right;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        direction: rtl;
        margin-left: -2px;
      }
    }
  }
}

.ant-tabs-tab-active {
  .@{prefix-cls} {
    &__tab {
      background-color: @FO-Container-Fill1;
    }
  }
}
</style>
