import { useResizeObserver } from '@vueuse/core';
import { type TooltipProps, Tooltip } from 'ant-design-vue';
import { type PropType, defineComponent, onMounted, ref } from 'vue';

interface EllipsisTextProps {
  inline?: boolean;
  // 行数
  lines?: string | number;
  // tooltip props
  tooltipProps?: Partial<TooltipProps>;
}

const EllipsisText = defineComponent<EllipsisTextProps>({
  props: {
    inline: {
      type: Boolean,
      default: false,
    },
    lines: {
      type: [String, Number] as PropType<string | number>,
    },
    tooltipProps: {
      type: Object as PropType<Partial<TooltipProps>>,
      default: () => ({}),
    },
  },
  setup(props, { slots }) {
    const tooltipVisible = ref(false);
    const textRef = ref<HTMLSpanElement>();

    function getTooltipVisible() {
      if (!textRef.value) {
        return;
      }
      if (!props.lines) {
        // 单行省略判断
        tooltipVisible.value
          = textRef.value?.scrollWidth > textRef.value?.clientWidth;
      } else {
        // 多行省略判断
        tooltipVisible.value
          = textRef.value?.scrollHeight > textRef.value?.clientHeight;
      }
    }

    onMounted(() => {
      getTooltipVisible();
      useResizeObserver(textRef, () => {
        getTooltipVisible();
      });
    });

    return () => {
      const textElement = (
        <span
          class={[
            props.inline ? 'inline-block' : 'block',
            'max-w-full',
            'break-all',
            { truncate: !props.lines },
            { 'line-clamp-1': !!props.lines },
          ]}
          ref={textRef}
          style={{
            WebkitLineClamp: props.lines,
          }}
        >
          {slots.default?.()}
        </span>
      );

      // 只在 tooltipVisible 为 true 时渲染 Tooltip
      if (tooltipVisible.value) {
        return (
          <Tooltip {...props.tooltipProps}>
            {{
              title: () => slots.title?.() || slots.default?.(),
              default: () => textElement,
            }}
          </Tooltip>
        );
      }

      // tooltipVisible 为 false 时直接返回文本元素
      return textElement;
    };
  },
});

export {
  EllipsisText,
};
