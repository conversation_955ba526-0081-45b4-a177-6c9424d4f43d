{"name": "@hg-tech/aigc", "type": "module", "version": "1.5.4", "private": true, "packageManager": "pnpm@9.6.0", "description": "HG AIGC platform", "engines": {"node": "^22 || ^20"}, "scripts": {"dev": "cross-env NODE_ENV=development HG_ENV=dev vite --mode development", "dev:pre": "cross-env NODE_ENV=development HG_ENV=pre vite --mode pre", "dev:rnd": "cross-env NODE_ENV=development HG_ENV=staging vite --mode rnd", "dev:prod": "cross-env NODE_ENV=development HG_ENV=prod vite --mode production", "build:dev": "cross-env HG_ENV=dev NODE_ENV=production vite build", "build:rnd": "cross-env HG_ENV=test NODE_ENV=production vite build --mode rnd", "build:pre": "cross-env HG_ENV=pre NODE_ENV=production vite build --mode pre", "build": "cross-env HG_ENV=prod NODE_ENV=production vite build", "analyze": "cross-env NODE_ENV=production HG_ENV=analyze vite build", "gen:icons": "node ./builds/icons.js", "gen:prompt": "node ./builds/prompt.js", "oss:upload": "zx ./builds/upload.js", "test": "vue-tsc --noEmit --skipLib<PERSON><PERSON><PERSON>"}, "dependencies": {"@hg-tech/forgeon-style": "workspace:*", "@hg-tech/oasis-common": "workspace:*", "@hg-tech/utils": "workspace:*", "@hg/event-log": "catalog:", "@lancercomet/fetcher": "catalog:", "@lancercomet/style": "catalog:", "@lancercomet/suntori": "catalog:", "@micro-zoe/micro-app": "catalog:", "@microsoft/fetch-event-source": "catalog:", "@sentry/vue": "catalog:", "@unocss/reset": "catalog:", "@vue/devtools-api": "catalog:", "@vueuse/core": "catalog:", "dayjs": "catalog:", "driver.js": "catalog:", "fast-deep-equal": "catalog:", "highlight.js": "catalog:", "katex": "catalog:", "konva": "catalog:", "less": "catalog:", "lodash": "catalog:", "lodash-es": "catalog:", "markdown-it": "catalog:", "markdown-it-emoji": "catalog:", "markdown-it-katex": "catalog:", "mermaid": "catalog:", "naive-ui": "catalog:", "papaparse": "catalog:", "qs": "catalog:", "random-jpn-emoji": "catalog:", "reflect-metadata": "catalog:", "uuid": "catalog:", "vue": "catalog:", "vue-router": "catalog:", "wavesurfer.js": "catalog:"}, "devDependencies": {"@hg-tech/configs": "workspace:*", "@hg-tech/eslint-config": "workspace:*", "@hg-tech/forgeon-uno-config": "workspace:*", "@sentry/vite-plugin": "catalog:", "@swc/core": "catalog:", "@types/color": "catalog:", "@types/katex": "catalog:", "@types/lodash": "catalog:", "@types/markdown-it": "catalog:", "@types/markdown-it-emoji": "catalog:", "@types/papaparse": "catalog:", "@types/qs": "catalog:", "@types/uuid": "catalog:", "@unocss/transformer-attributify-jsx": "catalog:", "@unocss/transformer-directives": "catalog:", "@unocss/transformer-variant-group": "catalog:", "@vitejs/plugin-vue": "catalog:", "@vitejs/plugin-vue-jsx": "catalog:", "@vue/compiler-sfc": "catalog:", "autoprefixer": "catalog:", "cross-env": "catalog:", "postcss": "catalog:", "rollup-plugin-visualizer": "catalog:", "swc-plugin-vue-jsx": "catalog:", "terser": "catalog:", "tslib": "catalog:", "typescript": "catalog:", "unocss": "catalog:", "unplugin-swc": "catalog:", "vite": "catalog:", "vite-plugin-cdn-import": "catalog:", "vite-plugin-mkcert": "catalog:", "vite-svg-loader": "catalog:", "vue-tsc": "catalog:", "zx": "catalog:"}}