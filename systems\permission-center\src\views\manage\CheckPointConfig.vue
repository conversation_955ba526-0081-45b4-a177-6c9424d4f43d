<template>
  <PageLayout>
    <template #headerTitle>
      <Breadcrumb class="FO-Font-B18" separator=">" :routes="routeNav">
        <template #itemRender="{ route: { path, breadcrumbName } }">
          <router-link v-if="path" :to="path" class="!bg-transparent !c-FO-Content-Text2">
            {{ breadcrumbName }}
          </router-link>
          <span v-else class="!c-text-gray1">
            {{ breadcrumbName }}
          </span>
        </template>
      </Breadcrumb>
    </template>
    <div class="h-full flex flex-col gap-16px overflow-auto p-20px">
      <div
        class="flex flex-none items-start justify-between b-rd-[12px] bg-FO-Container-Fill1 px-[16px] pb-[8px] pt-[20px]">
        <PermissionLineTabs v-model:value="activeTab" :tabList="tabList" />
        <span class="flex items-center">
          <span class="mr-[8px]">接口配置</span>
          <Switch :checked="apiConfigEnabled" @change="handleChangeApiCheck" />
        </span>
      </div>
      <div v-if="routerAppId" class="flex flex-auto overflow-auto b-rd-[12px] bg-FO-Container-Fill1 p-16px">
        <PointPanel v-if="activeTab === TabKey.Points" :appId="routerAppId" :apiConfigEnabled="apiConfigEnabled" />
        <InterfacePanel v-else-if="activeTab === TabKey.Interfaces" :appId="routerAppId" />
        <Empty v-else class="m-auto" />
      </div>
    </div>
  </PageLayout>
</template>

<script setup lang="tsx">
import { PlatformEnterPoint } from '@hg-tech/oasis-common';
import PageLayout from '../../components/PageLayout.vue';
import { Breadcrumb, Empty, message, Modal, Switch } from 'ant-design-vue';
import { computed } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { updatePermissionApp } from '../../api/app';
import type { Route } from 'ant-design-vue/es/breadcrumb/Breadcrumb';
import { useRouteQuery } from '@vueuse/router';
import PermissionLineTabs from '../../components/PermissionLineTabs.vue';
import PointPanel from './PointPanel.vue';
import InterfacePanel from './InterfacePanel.vue';
import { usePermissionAppInfo } from '../../composables/usePermissionAppInfo.ts';

enum TabKey {
  Points = 'points',
  Interfaces = 'interfaces',
}

const router = useRouter();
const route = useRoute();

const routerAppId = computed(() => Number(route.params.appId));
const { appInfo, refreshAppInfo } = usePermissionAppInfo(routerAppId);
const apiConfigEnabled = computed(() => Boolean(appInfo.value?.isApiCheck));

const activeTab = useRouteQuery('tab', TabKey.Points, { mode: 'replace' });
const tabList = computed(() => [
  { key: TabKey.Points, label: '权限' },
  ...(apiConfigEnabled.value ? [{ key: TabKey.Interfaces, label: '接口' }] : []),
]);

const routeNav = computed<Route[]>(() => {
  return [
    {
      path: router.resolve({ name: PlatformEnterPoint.PermissionCenterDashboard }).href,
      breadcrumbName: '权限管理中心',
    },
    {
      path: router.resolve({
        name: PlatformEnterPoint.PermissionCenterApp,
        params: { appId: routerAppId.value.toString() },
        query: { tenantId: route.query.tenantId },
      }).href,
      breadcrumbName: appInfo.value?.name || '-',
    },
    {
      path: '',
      breadcrumbName: '编辑权限',
    },
  ];
});

function handleChangeApiCheck(status: string | number | boolean) {
  if (status) {
    Modal.confirm({
      icon: () => null,
      width: 496,
      okText: '开启',
      okType: 'primary',
      centered: true,
      closable: true,
      cancelButtonProps: {
        // @ts-expect-error cancelButtonProps支持class但没有类型定义
        class: 'btn-fill-default',
      },
      title: '开启接口配置',
      content() {
        return (
          <div class="mt-12px pb-8px c-FO-Content-Text2">
            开启接口配置后，平台会向鉴权请求返回权限对应的接口，用户可自主选择权限的接口配置（可为空）
          </div>
        );
      },
      async onOk() {
        try {
          const res = await updatePermissionApp({ appId: routerAppId.value }, { isApiCheck: true });
          if (res.data?.code === 0) {
            await refreshAppInfo();
            message.success('开启接口配置成功');
          }
        } catch (error) {
          console.error(error);
        }
      },
    });
  } else {
    Modal.confirm({
      icon: () => null,
      width: 496,
      okText: '关闭',
      okButtonProps: {
        type: 'primary',
        danger: true,
      },
      cancelButtonProps: {
        // @ts-expect-error cancelButtonProps支持class但没有类型定义
        class: 'btn-fill-default',
      },
      centered: true,
      closable: true,
      title: '关闭接口配置',
      content() {
        return (
          <div class="mt-12px pb-8px c-FO-Content-Text2">
            关闭接口配置后，所有接口会被删除。权限中已配置的接口会被清空，鉴权请求不会再获得权限中接口（已生效的权限不受影响）
          </div>
        );
      },
      async onOk() {
        try {
          const res = await updatePermissionApp({ appId: routerAppId.value }, { isApiCheck: false });
          if (res.data?.code === 0) {
            await refreshAppInfo();
            message.success('关闭接口配置成功');
          }
        } catch (error) {
          console.error(error);
        }
      },
    });
  }
}
</script>
