<template>
  <div :class="prefixCls">
    <div v-if="packageVersionList?.length" :class="`${prefixCls}__card-list`">
      <AList
        :grid="{ gutter: 16 }"
        :data-source="packageVersionList"
        :locale="{ emptyText: '暂无工具包' }"
      >
        <template #renderItem="{ item }">
          <AListItem>
            <div :class="`${prefixCls}__card-list-item`" @click="goToDetail(item.ID)">
              <div :class="`${prefixCls}__card-list-item-content`">
                <img :src="item.icon" alt="icon" class="h-[80px] w-[80px] rounded-2xl">
                <div :class="`${prefixCls}__card-list-item-content-text`">
                  <div class="w-[300px] flex">
                    <EllipsisText class="font-bold">
                      {{ item.name }}
                    </EllipsisText>
                  </div>
                  <div class="flex text-xs">
                    <div>
                      版本:<b>{{ item?.latestVersion?.version }}</b>
                    </div>
                    <div class="ml-2">
                      大小:<b>{{
                        formatKBSize(item.latestVersion ? item.latestVersion.sizeKB : 0)
                      }}</b>
                    </div>
                    <div class="ml-2">
                      发布:<b>{{ dayjs(item.UpdatedAt).format('MM/DD HH:mm') }}</b>
                    </div>
                  </div>
                  <div class="w-[300px] flex flex-items-center">
                    <div v-if="item.platforms.length">
                      <Icon
                        v-for="(p, idx) in item.platforms"
                        :key="getPlatformIconByVal(p)?.label"
                        :class="{ 'ml-1': idx !== 0 }"
                        :title="getPlatformIconByVal(p)?.label"
                        :icon="getPlatformIconByVal(p)?.icon"
                      />
                    </div>
                    <div class="ml-2 h-[100%] border rounded-1 p-[2px] text-xs">
                      {{ item?.project?.name ? item?.project?.name : '通用' }}
                    </div>
                  </div>

                  <div class="c-FO-Content-Text2 flex items-center text-xs">
                    <EllipsisText class="!text-xs" :lines="1">
                      {{ item.description || '-' }}
                    </EllipsisText>
                  </div>
                </div>
              </div>
              <div class="relative w-[80px] flex flex-col justify-center">
                <div class="h-[32px] flex justify-center flex-items-center">
                  <a-button
                    shape="round"
                    :class="`${prefixCls}__btn-pro`"
                    size="small"
                    class="flex flex-items-center"
                    @click="goToDetail(item.ID)"
                  >
                    <Icon icon="charm:download" />
                    <span class="font-bold !m-0">详情</span>
                  </a-button>
                </div>
              </div>
            </div>
          </AListItem>
        </template>
      </AList>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { List as AList } from 'ant-design-vue';
import dayjs from 'dayjs';
import { ref, watch } from 'vue';
import type { ToolkitListItem } from '/@/api/page/model/systemModel';
import { getToolkitListByPage } from '/@/api/page/system';

import Icon from '/@/components/Icon';

import { useDesign } from '/@/hooks/web/useDesign';

import { useGo } from '/@/hooks/web/usePage';

import { cloneDeep } from 'lodash-es';
import type { GamePackagesVersionsListItem } from '/@/api/page/model/testModel';
import { formatKBSize } from '/@/utils/file/size';
import { platformOptions } from '/@/views/toolkit/settings/toolkitSettings.data';

const props = defineProps({
  activeTab: {
    type: Number,
  },
  searchParams: {
    type: Object as PropType<{ platform: number | undefined; labels: number[]; search: string }>,
    default: () => {},
  },
});

const go = useGo();
const AListItem = AList.Item;
const { prefixCls } = useDesign('toolkit-card-list');
const clonePlatformOptions = cloneDeep(platformOptions);
const packageVersionList = ref<GamePackagesVersionsListItem[]>([]);
let allPackageVersionList: GamePackagesVersionsListItem[] = [];

function goToDetail(id) {
  go({
    name: 'ToolkitDetail',
    params: { id },
    query: {
      cardList: encodeURIComponent(JSON.stringify({
        activeTab: props.activeTab,
        searchParams: props.searchParams,
      })),

    },
  });
}

// 获取包体版本列表
async function getPackageVersionList() {
  const { labels, platform, search } = props.searchParams;
  const { list } = await getToolkitListByPage({
    name: search,
    projectIDs: labels,
    page: 1,
    pageSize: 999,
  });

  allPackageVersionList = list;
  filterPackageVersionList(list, platform);
}

function filterPackageVersionList(list, platform) {
  if (list?.length > 0) {
    packageVersionList.value = list;

    // workTypeID筛选
    if (props.activeTab) {
      packageVersionList.value = packageVersionList.value.filter(
        (e: ToolkitListItem) => e.workTypeID === props.activeTab,
      );
    }

    // 平台筛选
    if (platform) {
      packageVersionList.value = packageVersionList.value.filter((e: ToolkitListItem) =>
        (e.platforms ? e.platforms.includes(platform) : ''));
    }
  } else {
    packageVersionList.value = [];
  }
}

// 获取平台icon
function getPlatformIconByVal(val: number) {
  return clonePlatformOptions.find((e) => e.value === val);
}

watch(
  () => [props.activeTab, props.searchParams.platform],
  () => {
    filterPackageVersionList(allPackageVersionList, props.searchParams.platform);
  },
  {
    deep: true,
  },
);

defineExpose({
  getList: getPackageVersionList,
  filterPackageVersionList,
});
</script>

<style lang="less">
@prefix-cls: ~'hypergryph-toolkit-card-list';
.@{prefix-cls} {
  &__compare-popover {
    &-title {
      position: relative;
      padding-left: 4px;
      border-left: 3px solid @FO-Brand-Primary-Default;
      line-height: 20px;
    }

    &-list {
      margin: 16px 0;

      &-item {
        position: relative;
        margin-bottom: 8px;
        overflow: hidden;

        &-delete {
          position: absolute;
          z-index: 1;
          top: 40%;
          right: 2px;
          color: @FO-Content-Text2;
          cursor: pointer;
        }
      }

      &-empty {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 100%;
        padding: 16px;
        color: @FO-Content-Text2;
        font-size: 13px;
      }
    }
  }

  &__card-list {
    padding: 16px 16px 0;

    & .ant-list .ant-list-item {
      padding: 0;
    }

    &-item {
      display: flex;
      position: relative;
      width: 500px;
      padding: 12px;
      transition: all 0.3s ease-in-out;
      border: 2px solid @FO-Container-Stroke1;
      border-radius: 20px;
      background-color: @FO-Container-Fill1;
      cursor: pointer;
      user-select: none;

      &:hover {
        border-color: #dedede;
      }

      &-top {
        position: absolute;
        top: -2px;
        left: -2px;
        width: 60px;
        height: 60px;
        overflow: hidden;
        border-radius: 20px 0 0;

        &-pin {
          display: flex;
          position: absolute;
          z-index: 1;
          top: -30px;
          left: -30px;
          align-items: flex-end;
          justify-content: center;
          width: 60px;
          height: 60px;
          padding: 2px;
          transform: rotate(-45deg);
          background-color: #4e61e4;
          color: #000;
        }
      }

      &-check {
        position: absolute;
        z-index: 2;
        top: 43px;
        right: 43px;
        transform: scale(2.5);

        &:not([disabled='true']) {
          & .ant-checkbox-inner {
            border-color: @FO-Functional-Error1-Default !important;
            background-color: @FO-Container-Fill1 !important;
          }

          & .ant-checkbox-checked > .ant-checkbox-inner {
            background-color: @FO-Functional-Error1-Default !important;
          }

          &[mode='compare'] {
            & .ant-checkbox-inner {
              border-color: @FO-Functional-Warning1-Default !important;
              background-color: @FO-Container-Fill1 !important;
            }

            & .ant-checkbox-checked > .ant-checkbox-inner {
              background-color: @FO-Functional-Warning1-Default !important;
            }
          }
        }

        &[disabled='true'] {
          &::after {
            content: '未检测';
            position: absolute;
            z-index: 1;
            top: 42%;
            left: 2px;
            color: @FO-Content-Text2;
            font-size: 4px;
            line-height: 4px;
            cursor: not-allowed;
            user-select: none;
          }
        }

        &[isDifferent='true'] {
          &::after {
            content: '不同平台';
            position: absolute;
            z-index: 1;
            top: 30%;
            left: 3px;
            color: @FO-Content-Text2;
            font-size: 5px;
            line-height: 5px;
            cursor: not-allowed;
            user-select: none;
          }
        }
      }

      &-content {
        display: flex;
        flex: 1;
        align-items: center;
        width: 0;

        &-text {
          margin: 0 10px;
          line-height: 24px;
        }
      }

      &-hoverable {
        display: flex;
        position: relative;
        align-items: center;
        max-width: 300px;
        min-height: 24px;
        overflow: hidden;

        &:not(:hover) {
          &::after {
            content: '';
            position: absolute;
            z-index: 1;
            top: 0;
            right: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(to right, transparent 90%, @FO-Container-Fill1);
          }
        }

        &:not([disabled='true']):hover {
          z-index: 10;
          width: auto;
          max-width: 316px;
          overflow: visible;

          & > div {
            position: absolute;
            top: -4px;
            left: -8px;
            flex-wrap: wrap;
            padding: 8px;
            border-radius: 8px;
            background-color: @FO-Container-Fill1;
            box-shadow: 0 0 10px 0 #00000040;
            row-gap: 4px;
          }
        }
      }

      &-label {
        display: flex;
        align-items: center;
        margin-right: 4px;
        padding: 0 4px;
        border-radius: 4px;
        color: #fff;
        font-size: 10px;
        line-height: 16px;
        white-space: nowrap;

        &[isSingle='true'] {
          border-radius: 100px;
        }
      }
      // 菱形背景
      &-mark {
        position: relative;

        &-before {
          position: absolute;
          z-index: 1;
          top: 0;
          left: 0;

          &::before {
            content: ' ';
            position: absolute;
            z-index: 2;
            top: 0;
            left: 0;
            width: 0;
            height: 0;
            border-top: 8px solid @FO-Container-Fill1;
            border-right: 8px solid transparent;
            border-bottom: 8px solid @FO-Container-Fill1;
          }
        }

        &-name {
          display: flex;
          position: relative;
          align-items: center;
          margin-right: 4px;
          padding: 0 10px;
          color: #fff;
          font-size: 10px;
          line-height: 16px;
          white-space: nowrap;
        }

        &-after {
          position: absolute;
          top: 0;
          right: 0;

          &::after {
            content: ' ';
            position: absolute;
            z-index: 2;
            top: 0;
            left: -12px;
            width: 0;
            height: 0;
            border-top: 8px solid @FO-Container-Fill1;
            border-bottom: 8px solid @FO-Container-Fill1;
            border-left: 8px solid transparent;
          }
        }
      }
    }
  }

  &__btn {
    border-color: #4d4d4d !important;
    background-color: #4d4d4d !important;
    color: #fff !important;

    &-pro {
      border-color: #4e61e4 !important;
      background-color: #4e61e4 !important;
      color: #fff !important;
    }
  }
}

[data-theme='dark'] {
  .@{prefix-cls} {
    &__card-list-item {
      border-color: @FO-Container-Stroke1;

      &:hover {
        border-color: @FO-Container-Stroke2;
      }

      &-hoverable {
        &:not(:hover) {
          &::after {
            background: linear-gradient(to right, transparent 90%, #151515);
          }
        }
      }
    }
  }
}
</style>
