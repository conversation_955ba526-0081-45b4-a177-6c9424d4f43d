import { VxeUI } from 'vxe-table';
import { VxeLoading, VxeTooltip } from 'vxe-pc-ui';

import zhCN from 'vxe-pc-ui/lib/language/zh-CN';

VxeUI.setI18n('zh-CN', zhCN);
VxeUI.setLanguage('zh-CN');

VxeUI.component(VxeTooltip);
VxeUI.component(VxeLoading);

export { default as BasicVxeTable } from './BasicVxeTable.vue';
export type {
  VxeGlobalRendererHandles,
  VxeGlobalRendererOptions,
  VxeGridInstance,
  VxeGridListeners,
  VxeGridProps,
} from 'vxe-table';
