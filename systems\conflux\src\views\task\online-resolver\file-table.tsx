import { BasicVxeTable } from '@hg-tech/oasis-common';
import { Button, Dropdown, Menu, message, Pagination, Popover, Tooltip } from 'ant-design-vue';
import { type PropType, computed, defineComponent, ref, watch } from 'vue';
import type { VxeGridInstance, VxeGridListeners, VxeGridProps } from 'vxe-table';
import style from './file-table.module.less';
import BasicStrokeChevronRight from '../../../assets/svg/BasicStrokeChevronRight.svg?component';
import Icon from '@ant-design/icons-vue';
import { multiEllipsis } from '@hg-tech/utils';
import { type MergeV1ResolveConflict, MergeV1ResolveRule } from '@hg-tech/api-schema-merge';
import { useClipboard } from '@vueuse/core';
import { UserTag } from '../../../components/UserTag';
import { MergeV1ResolveRuleLabelMap, MergeV1ResolveRuleOperateOptions, renderMergeV1ResolveRuleIcon } from '../../../models/config.model';

import BasicStrokeFile from '../../../assets/svg/BasicStrokeFile.svg?component';
import BasicStrokeChevronDown from '../../../assets/svg/BasicStrokeChevronDown.svg?component';
import BasicStrokeCopy from '../../../assets/svg/BasicStrokeCopy.svg?component';

type RenderRow = MergeV1ResolveConflict;

const FileTable = defineComponent({
  props: {
    data: {
      type: Array as PropType<RenderRow[]>,
      default: () => [],
    },
  },
  emits: ['update:data'],
  setup(props, { emit }) {
    const page = ref(1);
    const pageSize = ref(10);
    const fileTableRef = ref<VxeGridInstance<RenderRow>>();
    const displayData = computed({
      get: () => props.data,
      set: (value) => emit('update:data', value),
    });
    const selectData = ref<RenderRow[]>([]);
    const tableRows = computed(() =>
      displayData.value
        .slice((page.value - 1) * pageSize.value, page.value * pageSize.value));

    watch(tableRows, (newRows) => {
      newRows.forEach((row) => {
        if (selectData.value.some((item) => item.sourceFile === row.sourceFile && item.targetFile === row.targetFile)) {
          fileTableRef.value?.setCheckboxRow(row, true);
        }
      });
    }, { deep: true });

    const onSelectAll = () => {
      fileTableRef.value?.setCheckboxRow(props.data, true);
      selectData.value = props.data;
    };
    const onClearAllSelect = () => {
      fileTableRef.value?.clearCheckboxReserve();
      fileTableRef.value?.clearCheckboxRow();
      selectData.value = [];
    };

    const renderFileItem = (params: {
      path: string;
      fileName: string;
      maxWidth: number;
      keepLastSegment?: number;
    }) => {
      const isHover = ref(false);
      return (
        <div
          class="branch-item flex items-center gap-4px"
          onMouseenter={() => {
            isHover.value = true;
          }}
          onMouseleave={() => {
            isHover.value = false;
          }}
        >
          <Popover placement="top" z-index={2000}>
            {{
              default: () => (
                <div class="flex-1 truncate">
                  <div class="c-text-gray3 FO-Font-R12">
                    <span>{multiEllipsis({
                      text: params.path,
                      maxWidth: params.maxWidth + 20,
                      keepLastSegment: params.keepLastSegment ?? 8,
                      font: '12px sans-serif',
                    }).map((seg) => seg.content).join('')}
                    </span>
                  </div>
                  <div class="FO-Font-R14 c-FO-Content-Text1">
                    <Icon class="mr-4px c-FO-Content-Icon2" component={<BasicStrokeFile />} />
                    <span>{multiEllipsis({
                      text: params.fileName,
                      maxWidth: params.maxWidth,
                      keepLastSegment: params.keepLastSegment ?? 8,
                      font: '14px sans-serif',
                    }).map((seg) => seg.content).join('')}
                    </span>
                  </div>
                </div>
              ),
              title: () => (
                <div class="branch-info">
                  <div class="c-text-gray3 FO-Font-R12">{params.path}</div>
                  <div class="FO-Font-R14 c-FO-Content-Text1">
                    <Icon class="mr-4px c-FO-Content-Icon2" component={<BasicStrokeFile />} />
                    {params.fileName || '--'}
                  </div>
                </div>
              ),
            }}
          </Popover>
          <Tooltip placement="top" title="复制文件路径">
            <div class={isHover.value ? 'opacity-100' : 'opacity-0'}>
              <Icon
                class="cursor-pointer font-size-18px c-FO-Content-Icon2"
                component={<BasicStrokeCopy />}
                onClick={() => {
                  const { copy } = useClipboard();
                  copy(`${params.path}/${params.fileName}`);
                  message.success('已复制到剪贴板');
                }}
              />
            </div>
          </Tooltip>
        </div>
      );
    };

    /* 更新文件处理信息 */
    const updateResolveInfo = (row: RenderRow, type: 'source' | 'target' | 'default') => {
      displayData.value = displayData.value.map((item) => {
        if (row.sourceFile === item.sourceFile && row.targetFile === item.targetFile) {
          return {
            ...item,
            resolveRule: type === 'source'
              ? MergeV1ResolveRule.ACCEPT_SOURCE
              : type === 'target'
                ? MergeV1ResolveRule.ACCEPT_TARGET
                : MergeV1ResolveRule.RESOLVE_RULE_INVALID,
          };
        }
        return item;
      });
    };

    const tableColumns = computed<VxeGridProps<RenderRow>['columns']>(() => [
      { type: 'checkbox', width: 60 },
      { field: 'sourceFile', title: '源路径和文件', showOverflow: 'title', slots: {
        default({ row }) {
          const fileNameIndex = row.sourceFile?.lastIndexOf('/') || -1;
          const path = row.sourceFile?.slice(0, fileNameIndex) || '';
          const pathLastSegment = path.length - path.lastIndexOf('/');
          return (
            <div class="flex items-center gap-8px">
              <div class="flex-1">{renderFileItem({
                path,
                fileName: fileNameIndex < 0 ? '' : (row.sourceFile?.slice(fileNameIndex + 1) ?? ''),
                maxWidth: 150,
                keepLastSegment: Math.min(pathLastSegment, 8),
              })}
              </div>
              <Icon class="flex-shrink-0 font-size-18px" component={<BasicStrokeChevronRight />} />
            </div>
          );
        },
      } },
      { field: 'targetFile', title: '目标路径和文件', slots: {
        default({ row }) {
          const fileNameIndex = row.targetFile?.lastIndexOf('/') || -1;
          const path = row.targetFile?.slice(0, fileNameIndex) || '';
          const pathLastSegment = path.length - path.lastIndexOf('/');
          return renderFileItem({
            path,
            fileName: fileNameIndex < 0 ? '' : (row.targetFile?.slice(fileNameIndex + 1) ?? ''),
            maxWidth: 170,
            keepLastSegment: Math.min(pathLastSegment, 8),
          });
        },
      } },
      { field: 'user', title: '目标分支提交人', width: 150, slots: { default({ row }) {
        return (
          <UserTag user={{
            openId: row.targetStreamSubmitUser?.feishuOpenId || '',
            name: row.targetStreamSubmitUser?.name || row.targetStreamSubmitUser?.hgAccount || '--',
            avatar: row.targetStreamSubmitUser?.avatar || '',
            nickname: row.targetStreamSubmitUser?.nickname || '',
          }}
          />
        );
      } } },
      { field: 'resolveRule', title: '处理状态', width: 200, slots: {
        default({ row }) {
          return (
            <Dropdown placement="bottomLeft" trigger="click" z-index={2000}>
              {{
                default: () => (
                  <div class="FO-Font-R14 flex cursor-pointer items-center gap-4px c-FO-Content-Text1">
                    {renderMergeV1ResolveRuleIcon[row.resolveRule as MergeV1ResolveRule]?.()}
                    {row.resolveRule ? MergeV1ResolveRuleLabelMap[row.resolveRule] : '--'}
                    <Icon class="c-FO-Content-Icon2" component={<BasicStrokeChevronDown />} />
                  </div>
                ),
                overlay: () => (
                  <Menu class="w-200px">
                    {
                      [
                        MergeV1ResolveRule.ACCEPT_SOURCE,
                        MergeV1ResolveRule.ACCEPT_TARGET,
                        MergeV1ResolveRule.RESOLVE_RULE_INVALID,
                      ].filter((rule) =>
                        rule !== row.resolveRule).map((rule) => (
                        <Menu.Item
                          key={rule}
                          onClick={() => {
                            updateResolveInfo(row, rule === MergeV1ResolveRule.ACCEPT_SOURCE
                              ? 'source'
                              : rule === MergeV1ResolveRule.ACCEPT_TARGET
                                ? 'target'
                                : 'default');
                          }}
                        >
                          {MergeV1ResolveRuleOperateOptions[rule]}
                        </Menu.Item>
                      ))
                    }
                  </Menu>
                ),
              }}
            </Dropdown>

          );
        },
      } },
    ]);
    const gridOptions = computed(() => ({
      showOverflow: true,
      height: 'auto',
      loading: false,
      border: 'none',
      rowConfig: {
        keyField: 'id',
      },
      rowClassName: style.fileTableRow,
      checkboxConfig: {
        highlight: true,
        reserve: true,
        showReserveStatus: true,
      },
      virtualYConfig: {
        enabled: true,
        gt: 20,
      },
      columns: tableColumns.value,
      data: tableRows.value,
    }) as VxeGridProps);

    const updateSelectState = (type: 'source' | 'target') => {
      const select = selectData.value;
      if (select.length === 0) {
        message.warning('请先选择文件');
        return;
      }
      displayData.value = displayData.value.map((item) => {
        if (select.includes(item)) {
          return {
            ...item,
            resolveRule: type === 'source'
              ? MergeV1ResolveRule.ACCEPT_SOURCE
              : MergeV1ResolveRule.ACCEPT_TARGET,
          };
        }
        return item;
      });
      onClearAllSelect();
    };

    const gridEvent: VxeGridListeners<RenderRow> = ({
      checkboxChange: ({ checked, row }) => {
        if (selectData.value.some((item) => item.sourceFile === row.sourceFile && item.targetFile === row.targetFile)) {
          if (!checked) {
            selectData.value.splice(selectData.value.indexOf(row), 1);
          }
        } else {
          if (checked) {
            selectData.value.push(row);
          }
        }
      },
      checkboxAll: ({ checked }) => {
        if (checked) {
          selectData.value = selectData.value.concat(
            tableRows.value.filter((item) => !selectData.value.includes(item)),
          );
        } else {
          selectData.value = selectData.value.filter((item) => !tableRows.value.includes(item));
        }
      },
    });

    return () => (
      <div class="file-table-wrapper flex flex-col">
        <div class="FO-Font-B16 mb-16px">
          冲突文件列表
        </div>
        <div class="file-action mb-8px flex items-center justify-between">
          <div class="flex items-center gap-16px">
            <div class="flex items-center">
              已选择
              <span class="FO-Font-B16 mx-2px">{selectData.value.length}</span>
              个文件
            </div>
            <div class="flex items-center">
              <Button onClick={onClearAllSelect} type="text">
                <span class="FO-Font-B14">取消选择</span>
              </Button>
              <Button onClick={onSelectAll} type="text">
                <span class="FO-Font-B14 c-FO-Brand-Primary-Default">选择全部</span>
              </Button>
            </div>
          </div>
          <div class="flex gap-8px">
            <Button class="btn-fill-secondary" disabled={selectData.value.length === 0} onClick={() => updateSelectState('source')}>
              <span class="FO-Font-B14">接受源分支的修改</span>
            </Button>
            <Button class="btn-fill-secondary" disabled={selectData.value.length === 0} onClick={() => updateSelectState('target')}>
              <span class="FO-Font-B14">接受目标分支的修改</span>
            </Button>
          </div>
        </div>
        <div class="mb-12px h-100% flex-grow-1 overflow-auto">
          <BasicVxeTable
            class={style.fileTable}
            events={gridEvent}
            options={gridOptions.value}
            ref={fileTableRef}
          />
        </div>
        <div class="file-table-footer flex items-center justify-between">
          <div class="FO-Font-R14 mb-12px c-FO-Content-Text2">
            共计
            {props.data.length}
            条数据
          </div>
          <Pagination
            total={props.data.length}
            v-model:current={page.value}
            v-model:pageSize={pageSize.value}
          />
        </div>
      </div>
    );
  },
});

export {
  FileTable,
};
