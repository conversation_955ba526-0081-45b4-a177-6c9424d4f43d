<template>
  <BasicModal
    v-bind="$attrs" :wrapClassName="`${prefixCls} `" :canFullscreen="false" :footer="null" :width="800"
    :afterClose="handleClose" :maskClosable="!isEditMode"
    :bodyStyle="{ background: colorList[newDetail && newDetail.color ? newDetail.color : 0][isDark ? 2 : 0], borderRadius: '20px' }"
    class="b-rd-[20px]"
    @register="registerModal"
  >
    <template #closeIcon>
      <div class="mt-[2px] text-lg mr-[8px]!">
        <Icon icon="ant-design:close-outlined" class="text-lg!" />
      </div>
    </template>
    <div v-track:v="'cdmka3tmwa'">
      <div class="flex items-center gap-[12px]">
        <div class="relative relative flex-none overflow-hidden rounded-2xl">
          <img class="h-[130px] w-[130px]" :src="preprocessFilePath(detail?.icon)" alt="logo">
          <div
            v-if="!isRecycle && detail?.doctorID"
            v-tippy="{ content: '点击查看检测报告', placement: 'right' }"
            class="absolute bottom-0 right-0 h-[40px] w-[40px] flex cursor-pointer items-end justify-end"
            @click.stop="() => openDoctorPage(detail!)"
          >
            <div class="absolute bottom-[-50%] right-[-50%] h-full w-full rotate-[45deg] scale-115 bg-#007E0D transition-all hover:bg-#0db31d" />
            <Icon class="pointer-events-none z-1 m-[4px] c-FO-Content-Components1" icon="gameStore-doctor|svg" />
          </div>
        </div>
        <div class="flex-auto">
          <div class="mb-1 w-full flex items-center">
            <EllipsisText v-if="pkg?.name" class="max-w-[250px] text-3xl font-bold">
              {{ pkg?.name }}
            </EllipsisText>
            <Popconfirm v-if="isRecycle" title="确定恢复?" @confirm="handleRecycle()">
              <BasicButton
                shape="round" :class="`${prefixCls}__btn-pro ml-3`"
                size="small"
              >
                <Icon icon="icon-park-outline:undo" size="14" class="mr-[2px]" />
                <span class="font-bold !m-0">恢复</span>
              </BasicButton>
            </Popconfirm>
            <div v-else class="ml-3 flex items-center gap-2">
              <!-- 非苹果非外包 -->
              <ButtonFolded v-if="!noPkg && !oasis && !isApple && !userStore.isOutSourcing">
                <template #default="{ hovered }">
                  <BasicButton
                    v-track="'jo22nprtos'"
                    shape="round"
                    class="font-bold"
                    :class="[hovered ? `${prefixCls}__download-btn-pro` : `${prefixCls}__download-btn-pro-hover`]"
                    size="small" @click="() => handleDownload()"
                  >
                    <Icon icon="charm:download" />
                    <span class="!m-0">下载</span>
                  </BasicButton>
                </template>
                <template #addition>
                  <BasicButton
                    v-track="'jo22nprtos'"
                    shape="round"
                    :class="`${prefixCls}__download-btn-pro`"
                    size="small"
                    @click.stop="handleDownloadByOasis"
                  >
                    <Icon icon="charm:download" />
                    <span class="font-bold !m-0">通过Oasis下载</span>
                  </BasicButton>
                </template>
              </ButtonFolded>
              <!-- 排除非苹果非外包的所有情况 -->
              <template v-if="!noPkg && (oasis || isApple || userStore.isOutSourcing)">
                <!-- 出现plist下载 -->
                <ButtonFolded v-if="!noPkg && detail?.platform === DevicePlatform.iOS && isApple">
                  <template #default="{ hovered }">
                    <BasicButton
                      v-track="'jo22nprtos'" shape="round"
                      class="font-bold"
                      :class="[hovered ? `${prefixCls}__download-btn-pro` : `${prefixCls}__download-btn-pro-hover`]"
                      size="small" @click="() => handleDownload()"
                    >
                      <Icon icon="charm:download" />
                      <span class="!m-0">下载</span>
                    </BasicButton>
                  </template>
                  <template #addition>
                    <BasicButton
                      v-track="'jo22nprtos'" shape="round"
                      :class="`${prefixCls}__download-btn-pro font-bold`" size="small" @click="() => handleDownload(true)"
                    >
                      <Icon icon="charm:download" />
                      <span class="!m-0">plist下载</span>
                    </BasicButton>
                  </template>
                </ButtonFolded>
                <BasicButton
                  v-else
                  v-track="'jo22nprtos'" shape="round" :class="`${prefixCls}__btn-pro`"
                  size="small" @click="() => handleDownload()"
                >
                  <Icon icon="charm:download" />
                  <span class="!m-0">下载</span>
                </BasicButton>
              </template>
              <BasicButton
                v-if="isPackageAdmin && !versionID"
                shape="round"
                :class="`${prefixCls}__btn-pro`"
                size="small" @click="handlePinTop(detail)"
              >
                <Icon :icon="detail?.top ? 'ic:baseline-pin-off' : 'mdi:pin'" />
                <span class="!m-0">{{ detail?.top ? '取消置顶' : '置顶' }}</span>
              </BasicButton>
              <ButtonFolded v-if="!noPkg">
                <template #default="{ hovered }">
                  <BasicButton
                    v-track="'rmsp1okgpl'" shape="round"
                    class="font-bold"
                    :class="[hovered ? `${prefixCls}__download-btn-pro` : 'bg-[#f8cc28]! c-[#000]! b-[#f8cc28]!']"
                    size="small"
                    @click="() => handleShare()"
                  >
                    <Icon icon="icon-park-outline:share-one" />
                    <span class="!m-0">分享</span>
                  </BasicButton>
                </template>
                <template #addition>
                  <BasicButton
                    v-track="'rmsp1okgpl'" shape="round" :class="`${prefixCls}__download-btn-pro `"
                    class="font-bold"
                    size="small" @click="handleOasisShare()"
                  >
                    <Icon icon="icon-park-outline:share-one" />
                    <span class="!m-0">分享Oasis渠道链接</span>
                  </BasicButton>
                </template>
              </ButtonFolded>
            </div>
          </div>
          <EllipsisText v-if="detail?.version" :lines="2" class="font-bold">
            {{ detail?.version }}
          </EllipsisText>

          <div class="my-1 flex items-center">
            <div class="mr flex">
              <div class="mr-1">
                平台:
              </div>
              <b>{{ getPlatformIconByVal(detail?.platform)?.label }}</b>
            </div>
            <div class="mr flex">
              <div class="mr-1">
                大小:
              </div>
              <b> {{ formatKBSize(detail?.sizeKB) }}</b>
            </div>
            <div class="flex">
              <div class="mr-1">
                发布时间:
              </div>
              <b>{{ dayjs(detail?.CreatedAt).format('YYYY/MM/DD HH:mm') }}</b>
            </div>
          </div>
          <div class="flex items-center justify-between">
            <div v-if="detail" class="flex items-center gap-[12px]">
              <BasicButton
                v-if="!isRecycle && detail?.doctorID"
                shape="round"
                type="success"
                size="small"
                @click.stop="() => openDoctorPage(detail!)"
              >
                查看检测信息
                <ArrowRightOutlined />
              </BasicButton>
              <BasicButton
                v-if="hasCloudPhonePermission && detail?.platform && [DevicePlatform.Android, DevicePlatform.iOS].includes(detail.platform)"
                shape="round"
                :class="`${prefixCls}__btn-pro`"
                size="small"
                @click.stop="() => openCloudPhonePage(pkg?.ID, detail?.ID)"
              >
                使用云真机运行
                <ArrowRightOutlined />
              </BasicButton>
            </div>
            <div v-if="isRecycle" class="flex items-center">
              <div class="flex items-center c-FO-Functional-Error1-Default font-bold">
                <Icon icon="ri:hourglass-fill" />
                {{
                  detail?.remainDays === 0
                    ? '即将彻底删除'
                    : `${detail?.remainDays}天后彻底删除`
                }}
              </div>
            </div>
            <div v-else-if="!detail?.top" class="flex items-center">
              <div v-if="!detail?.reserve || isEditMode" class="flex items-center text-[#67b800] font-bold">
                <Icon icon="ri:hourglass-fill" />
                {{
                  isEditMode && detail?.reserve
                    ? '非长期保留'
                    : detail?.remainDays === 0
                      ? '即将删除'
                      : `保留${detail?.remainDays}天`
                }}
              </div>
              <Switch
                v-if="isEditMode" v-model:checked="newDetail!.reserve" size="small"
                :class="`${prefixCls}__reserve-switch`" @change="handleEditDetail('reserve')"
              />
              <div v-if="detail?.reserve || isEditMode" class="flex items-center text-[#bc9e00] font-bold">
                <Icon icon="pepicons-pop:rewind-time" />
                长期保留
              </div>
            </div>
          </div>
        </div>
        <div v-if="!isRecycle && isMobilePlatform(detail?.platform) && !noPkg" class="w-[100px] self-end dark:mr-2">
          <QrCode :value="qrCodeUrl" tag="img" :width="100" />
        </div>
      </div>

      <div :class="`${prefixCls}__footer`">
        <div v-if="isEditMode || showLabels.length" :class="`${prefixCls}__footer-item`">
          <div class="w-[70px] line-height-[24px]">
            标签
          </div>
          <div :class="`${prefixCls}__footer-item-content`">
            <template v-for="label in showLabels" :key="label.ID">
              <div :class="`${prefixCls}__footer-label`">
                <div class="mr-2 font-bold">
                  {{ label.name }}
                </div>
                <div
                  v-for="val in label.values" :key="val.ID" :class="`${prefixCls}__footer-label-item`"
                  :isEditMode="isEditMode" :style="{
                    backgroundColor: val.checked && !isEditMode ? label.color || defaultColorList[0] : undefined,
                    color:
                      isEditMode && val.checked
                        ? label.color || defaultColorList[0]
                        : isEditMode
                          ? ForgeonThemeCssVar.ContentText2
                          : val.checked
                            ? ForgeonThemeCssVar.ContentComponents1
                            : ForgeonThemeCssVar.ContentComponents2,
                    borderColor:
                      isEditMode && val.checked
                        ? label.color || defaultColorList[0]
                        : 'transparent',
                  }" :isSingle="label.singleChoice" :checked="val.checked" @click="handleTagClick(val, label)"
                >
                  {{ val.value }}
                </div>
              </div>
            </template>
          </div>
        </div>

        <div v-if="isEditMode || detail?.marks?.length" :class="`${prefixCls}__footer-item`">
          <div class="w-[70px] line-height-[30px]">
            标记
          </div>

          <div :class="`${prefixCls}__footer-item-content`">
            <template v-if="!isEditMode">
              <div v-for="mark in detail?.marks" :key="mark.name" :class="`${prefixCls}__footer-mark`">
                <div :class="`${prefixCls}__footer-mark-before`" />
                <div
                  :class="`${prefixCls}__footer-mark-name`" :style="{
                    backgroundColor: mark.color || defaultColorList[0],
                  }"
                >
                  {{ mark.name }}
                </div>
                <div :class="`${prefixCls}__footer-mark-after`" />
              </div>
            </template>
            <template v-else>
              <div
                v-for="(mark, i) in newDetail?.marks" :key="i" :class="`${prefixCls}__footer-mark-edit`" :style="{
                  borderColor: mark.color || defaultColorList[0],
                }" :isNew="!!mark.UUID"
              >
                <Input
                  v-model:value="mark.name" placeholder="请输入标记名" :maxlength="20" :bordered="false" :style="{
                    color: !isDark ? mark.color || defaultColorList[0] : undefined,
                  }" :class="`${prefixCls}__footer-mark-input`" @blur="handleEditDetail('marks')"
                />
                <ColorPopover
                  v-model:value="mark.color" :class="`${prefixCls}__footer-mark-color-picker`"
                  @change="handleEditDetail('color')"
                />
                <Popconfirm title="确定删除该标记吗?" overlayClassName="!w-190px" @confirm="handleMarkDelete(mark)">
                  <BasicButton
                    preIcon="ant-design:minus-outlined" :class="`${prefixCls}__mark-del-btn`" shape="circle"
                    size="small" @click.stop
                  />
                </Popconfirm>
              </div>
            </template>
            <BasicButton
              v-if="isEditMode && !hasNewMarkEdit" preIcon="ant-design:plus-outlined"
              :class="`${prefixCls}__btn !px-[4px]`" size="small" shape="circle" @click="handleMarkAdd()"
            />
          </div>
        </div>

        <div v-if="detail?.attachments?.length || isEditMode" :class="`${prefixCls}__footer-item`">
          <div class="w-[70px] line-height-[30px]">
            关联资源
          </div>
          <div :class="`${prefixCls}__footer-item-content`">
            <template v-for="att in newDetail?.attachments" :key="att.ID">
              <div
                :class="`${prefixCls}__footer-item-att`" :disabled="isEditMode || isRecycle"
                @click="handleAttDownload(att)"
              >
                <Input
                  v-if="isEditMode" v-model:value="att.name" :bordered="false" class="!w-[100px] !px-1"
                  @blur="handleAttEdit(att)"
                />
                <EllipsisText v-else class="!max-w-[100px]">
                  {{ att.name }}
                </EllipsisText>

                <Icon v-if="!isRecycle" :icon="isEditMode ? 'ant-design:edit-outlined' : 'charm:download'" />

                <Popconfirm title="确定删除该资源吗?" overlayClassName="!w-190px" @confirm="handleAttDelete(att.ID!)">
                  <BasicButton
                    v-if="isEditMode" preIcon="ant-design:minus-outlined" :class="`${prefixCls}__mark-del-btn`"
                    shape="circle" size="small" @click.stop
                  />
                </Popconfirm>
              </div>
            </template>
            <BasicUpload
              v-if="isEditMode" uploadType="appstore" valueFormat="string" :maxNumber="1"
              :multiple="false" noAlert :showUploadList="false" @change="handleAttAdd"
            >
              <template #default="{ loading, stateMsg }">
                <div class="flex items-center">
                  <BasicButton
                    preIcon="ant-design:plus-outlined" :class="`${prefixCls}__btn !px-[4px]`" size="small"
                    shape="circle" :disabled="loading"
                  />
                  <div v-if="stateMsg" class="inline-block pl-3 c-FO-Functional-Warning1-Default">
                    {{ stateMsg }}
                    <span :class="`${uploadPrefixCls}__loading-dot`">...</span>
                  </div>
                </div>
              </template>
            </BasicUpload>
          </div>
        </div>
        <div :class="`${prefixCls}__footer-item !items-start`">
          <div class="w-[70px] text-nowrap line-height-[24px]">
            颜色
          </div>
          <ColorSelect v-if="newDetail" v-model="newDetail.color" :isEdit="isEditMode" @change="updateColor" />
        </div>
        <div v-if="isEditMode || newBuildInfo.engineCL || newBuildInfo.shelveCL || newBuildInfo.projectCL" :class="`${prefixCls}__footer-item !items-start`">
          <div class="w-[70px] text-nowrap line-height-[24px]">
            打包信息
          </div>

          <div class="font-normal">
            <div class="flex items-center gap-2">
              <div class="flex items-center gap-2">
                <span class="min-w-[50px] text-right text-nowrap">引擎CL:</span>
                <InputNumber
                  v-if="isEditMode" v-model:value="newBuildInfo.engineCL" size="small" :controls="false"
                  :class="`${prefixCls}__footer-item-desc-edit`" @blur="handleEditBuildInfo()"
                />
                <div v-else class="w-[170px] flex items-center">
                  <span>{{ newBuildInfo.engineCL }}</span>
                  <tippy>
                    <template #content>
                      <div>
                        <b>提交人: </b>{{ getNickNameByFieldName(buildInfoClDetail.engineCL?.user) }}
                      </div>
                      <div>
                        <b>日期: </b>{{ formatTimestampToDate(buildInfoClDetail.engineCL?.time) }}
                      </div>
                      <div> <b>提交描述: </b>{{ buildInfoClDetail.engineCL?.desc }} </div>
                    </template>
                    <Icon v-if="newBuildInfo.engineCL" icon="icon-park-outline:info" class="ml-1" :size="13" />
                  </tippy>
                </div>
              </div>
              <div class="flex items-center gap-2">
                <span class="min-w-[70px] text-right text-nowrap">Shelve CL:</span>

                <InputNumber
                  v-if="isEditMode" v-model:value="newBuildInfo.shelveCL" size="small" :controls="false"
                  :class="`${prefixCls}__footer-item-desc-edit`" @blur="handleEditBuildInfo()"
                />
                <div v-else class="w-[170px] flex items-center">
                  <span>{{ newBuildInfo.shelveCL }}</span>
                  <tippy>
                    <template #content>
                      <div>
                        <b>提交人: </b>{{ getNickNameByFieldName(buildInfoClDetail.shelveCL?.user) }}
                      </div>
                      <div>
                        <b>日期: </b>{{ formatTimestampToDate(buildInfoClDetail.shelveCL?.time) }}
                      </div>
                      <div> <b>提交描述: </b>{{ buildInfoClDetail.shelveCL?.desc }} </div>
                    </template>
                    <Icon v-if="newBuildInfo.shelveCL" icon="icon-park-outline:info" class="ml-1" :size="13" />
                  </tippy>
                </div>
              </div>
            </div>
            <div class="mt-2 flex items-center gap-2">
              <div class="flex items-center gap-2">
                <span class="min-w-[50px] text-right text-nowrap">工程CL: </span>
                <InputNumber
                  v-if="isEditMode" v-model:value="newBuildInfo.projectCL" size="small" :controls="false"
                  :class="`${prefixCls}__footer-item-desc-edit`" @blur="handleEditBuildInfo()"
                />
                <div v-else class="w-[170px] flex items-center">
                  <span>{{ newBuildInfo.projectCL }}</span>
                  <tippy>
                    <template #content>
                      <div>
                        <b>提交人: </b>{{ getNickNameByFieldName(buildInfoClDetail.projectCL?.user) }}
                      </div>
                      <div>
                        <b>日期: </b>{{ formatTimestampToDate(buildInfoClDetail.projectCL?.time) }}
                      </div>
                      <div> <b>提交描述: </b>{{ buildInfoClDetail.projectCL?.desc }} </div>
                    </template>
                    <Icon v-if="newBuildInfo.projectCL" icon="icon-park-outline:info" class="ml-1" :size="13" />
                  </tippy>
                </div>
              </div>
              <div class="flex items-center gap-2">
                <span class="min-w-[70px] text-right text-nowrap"> 触发人: </span>
                {{ getNickNameByFieldName(newBuildInfo.trigger) }}
              </div>
            </div>
          </div>
        </div>
        <div v-if="isEditMode || newDetail?.extLinks?.length" :class="`${prefixCls}__footer-item`">
          <div class="w-[70px] line-height-[30px]">
            附加链接
          </div>
          <div :class="`${prefixCls}__footer-item-content`">
            <template v-for="(item, index) in newDetail?.extLinks" :key="item">
              <div
                v-if="isEditMode"
                :class="`${prefixCls}__footer-item-att`"
              >
                <Input
                  v-model:value="item.title"
                  :bordered="false"
                  class="extLinksTitle !w-[100px] !px-1"
                  placeholder="输入标题"
                  @click="item.isOpen = true"
                  @pressEnter="pressEnter"
                  @blur="e => extLinksUrlBlur(e, item, index, 'title')"
                />

                <Input
                  v-if="item.isOpen"
                  v-model:value="item.URL"
                  :bordered="false"
                  placeholder="输入URL"
                  :class="[`${prefixCls}__footer-item-att-url-input`, { 'left-[-122px]!': (index + 1) % 4 === 0 }]"
                  class="absolute left-0 top-[36px] z-99 b-rd-[34px] !w-[300px] bg-FO-Container-Fill5! !px !shadow-[1px_2px_2px_#0003]"
                  @pressEnter="pressEnter"
                  @blur="e => extLinksUrlBlur(e, item, index, 'url') "
                />
                <Icon icon="mi:arrow-right-up" />

                <Popconfirm title="确定删除该链接吗?" overlayClassName="!w-190px" @confirm="handleExtLinksDelete(item)">
                  <BasicButton
                    v-if="isEditMode" preIcon="ant-design:minus-outlined" :class="`${prefixCls}__mark-del-btn`"
                    shape="circle" size="small" @click.stop
                  />
                </Popconfirm>
              </div>
              <div
                v-else
                :class="`${prefixCls}__footer-item-att`"
                @click="() => handleExternalLink(item.URL)"
              >
                <EllipsisText class="!max-w-[100px]">
                  {{ item.title }}
                </EllipsisText>
                <Icon icon="mi:arrow-right-up" />
              </div>
            </template>

            <BasicButton
              v-if="isEditMode && !hasNewExtLinkEdit" preIcon="ant-design:plus-outlined"
              :class="`${prefixCls}__btn !px-[4px]`" size="small" shape="circle"
              @click="handleExtLinkAdd"
            />
          </div>
        </div>
        <div :class="`${prefixCls}__footer-item`" class="z-0!">
          <div class="w-[70px]">
            MD5
          </div>
          <span class="font-normal">
            {{ getMD5ByDownloadUrl(detail?.downloadLink) }}
          </span>
        </div>

        <div v-if="isEditMode || detail?.releaseNote" :class="`${prefixCls}__footer-item `" class="items-center z-0!">
          <div class="w-[70px] line-height-[30px]">
            备注
          </div>
          <!-- eslint-disable vue/attribute-hyphenation -->
          <ATextarea
            v-if="isEditMode" v-model:value="newDetail!.releaseNote"
            :bordered="isEditMode" :readOnly="!isEditMode" auto-size :maxlength="200" placeholder="请输入备注"
            :class="`${prefixCls}__footer-item-desc${isEditMode ? '-edit' : ''}`"
            @blur="handleEditDetail('releaseNote')"
          />
          <span v-if="!isEditMode" class="font-normal">
            {{ newDetail!.releaseNote }}
          </span>
        </div>
        <div class="mb-7 w-full">
          <div v-if="isPackageAdmin && !isRecycle" class="flex items-center justify-center">
            <BasicButton v-if="!isEditMode" shape="round" class="!border-none dark:!bg-[#333]" @click="handleEdit()">
              <Icon icon="ant-design:edit-outlined" />
              <span class="font-bold !m-0">修改</span>
            </BasicButton>
            <template v-else>
              <BasicButton shape="round" class="!border-none dark:!bg-[#333]" @click="handleEditCancel()">
                <Icon icon="ph:caret-left-bold" :size="14" />
                <span class="font-bold !m-0">返回</span>
              </BasicButton>
            </template>
          </div>
          <div v-else class="mb-7 w-full flex items-center justify-center">
            <div class="h-[32px]" />
          </div>
        </div>

        <Icon
          class="absolute right-[10px] opacity-60 -bottom-[36px]" :icon="getPlatformIconByVal(detail?.platform)?.icon"
          :style="{ color: getPlatformIconByVal(detail?.platform)?.color }" :size="120"
        />
      </div>
    </div>
  </BasicModal>
</template>

<script lang="ts" setup>
import { Input, InputNumber, Popconfirm, Switch } from 'ant-design-vue';
import { ForgeonTheme, ForgeonThemeCssVar } from '@hg-tech/forgeon-style';
import { ArrowRightOutlined } from '@ant-design/icons-vue';
import { chunk, cloneDeep, isEmpty, isEqual, omit, pick, sortBy } from 'lodash-es';
import { computed, ref, watch } from 'vue';
import { useRouter } from 'vue-router';
import dayjs from 'dayjs';
import ColorSelect from './ColorSelect.vue';
import { getMD5ByDownloadUrl } from './helper';
import { colorList } from './color.data';
import {
  type extLinksItem,
  type GameAttachmentsListItem,
  type GameLabelsListItem,
  type GameLabelValuesListItem,
  type GamePackagesListItem,
  type GamePackagesVersionMarksListItem,
  type GamePackagesVersionsListItem,
  type ProjectPkgBuildInfoCLDetailItem,
  type ProjectPkgBuildInfoListItem,
  LogSource,
} from '/@/api/page/model/testModel';
import {
  addExtLinks,
  addGameAttachment,
  addProjectPkgBuildInfo,
  deleteExtLinks,
  deleteGameAttachment,
  deleteGamePackagesVersionTag,
  editGameAttachment,
  editGamePackagesVersion,
  editProjectPkgBuildInfo,
  getGamePackagesRecycleVersionByID,
  getGamePackagesVersionByID,
  getGamePackagesVersionLogSource,
  getProjectPkgBuildInfoCLDetail,
  getProjectPkgBuildInfoListByPage,
  newGamePackagesVersionTag,
  recoverGamePackagesRecycleVersionByID,
  updateExtLinks,
} from '/@/api/page/test';
import { PlatformEnterPoint, preprocessFilePath } from '@hg-tech/oasis-common';
import { getTempToken } from '/@/api/sys/user';
import { ColorPopover, defaultColorList } from '/@/components/ColorPopover';
import Icon from '/@/components/Icon';
import { BasicModal, useModalInner } from '/@/components/Modal';
import { QrCode } from '/@/components/Qrcode';
import { BasicUpload } from '/@/components/Upload';
import { useRootSetting } from '/@/hooks/setting/useRootSetting';
import { useUserList } from '/@/hooks/system/useUserList';
import { useDesign } from '/@/hooks/web/useDesign';
import { useMessage } from '/@/hooks/web/useMessage';
import { useGo } from '/@/hooks/web/usePage';
import { EllipsisText } from '/@/components/EllipsisText';
import { useUserStoreWithOut } from '/@/store/modules/user';
import { openWindow } from '/@/utils';
import { copyText } from '/@/utils/copyTextToClipboard';
import { formatTimestampToDate } from '/@/utils/dateUtil';
import { downloadByUrl } from '/@/utils/file/download';
import { formatKBSize } from '/@/utils/file/size';
import { isApple } from '/@/utils/is';
import { buildNumberUUID } from '/@/utils/uuid';
import { isMobilePlatform, platformOptions } from '/@/views/test/gamePackage/settings/settings.data';
import { useGlobSetting } from '/@/hooks/setting';
import { router } from '../../../../router';
import { DevicePlatform } from '../type.ts';
import { useIsPackageAdmin } from '../useIsPackageAdmin.ts';
import BasicButton from '../../../../components/Button/src/BasicButton.vue';
import ButtonFolded from '../../../../components/ButtonFolded.vue';
import { usePermissionCheckPoint } from '../../../../service/permission/usePermission.ts';
import { useCloudPhonePage } from './useCloudDevicePage.ts';

const emit = defineEmits(['register', 'success']);

const { prefixCls } = useDesign('game-package-card-detail-modal');
const { prefixCls: uploadPrefixCls } = useDesign('single-upload');
const detail = ref<GamePackagesVersionsListItem>();
const newDetail = ref<GamePackagesVersionsListItem>();
const pkg = ref<GamePackagesListItem>();
const needDownload = ref<boolean>(false);
const { currentRoute, resolve } = useRouter();
const go = useGo();
const userStore = useUserStoreWithOut();
const { isPackageAdmin } = useIsPackageAdmin();
const downloadNow = Number(currentRoute.value.query?.downloadNow);
const versionID = Number(currentRoute.value.query?.v);
const oasis = Number(currentRoute.value.query?.oasis || 0);
const [hasCloudPhonePermission] = usePermissionCheckPoint({
  scope: PlatformEnterPoint.DeptAsset,
  any: ['useCloudPhone'],
});
const { createMessage } = useMessage();
const allLabels = ref<GameLabelsListItem[]>([]);
const showLabels = ref<GameLabelsListItem[]>([]);
const noPkg = ref<boolean>(false);
const isEditMode = ref<boolean>(false);
const hasChange = ref<boolean>(false);
const hasNewMarkEdit = ref<boolean>(false);
const hasNewExtLinkEdit = ref<boolean>(false);
/** 打包信息 */
const buildInfo = ref<ProjectPkgBuildInfoListItem>();
const newBuildInfo = ref<ProjectPkgBuildInfoListItem>({});
/** CL详情 */
const buildInfoClDetail = ref<{
  engineCL?: ProjectPkgBuildInfoCLDetailItem;
  shelveCL?: ProjectPkgBuildInfoCLDetailItem;
  projectCL?: ProjectPkgBuildInfoCLDetailItem;
}>({
  engineCL: undefined,
  shelveCL: undefined,
  projectCL: undefined,
});

const { getUserList, getNickNameByFieldName } = useUserList();
const { getDarkMode } = useRootSetting();
const isDark = computed(() => getDarkMode.value === ForgeonTheme.Dark);
const isDownload = ref<boolean>(false);
const isRecycle = ref<boolean>(false);

const [registerModal, { setModalProps, redoModalHeight, closeModal }] = useModalInner(async (data) => {
  setModalProps({ confirmLoading: true });
  isEditMode.value = false;
  hasChange.value = false;
  hasNewMarkEdit.value = false;
  hasNewExtLinkEdit.value = false;
  detail.value = data?.detail;
  newDetail.value = cloneDeep(detail.value);
  pkg.value = data?.pkg;
  allLabels.value = data?.allLabels || [];
  isRecycle.value = !!data?.isRecycle;
  await getDetailInfo();
  await getBuildInfo();
  await getUserList();
  noPkg.value = data?.detail?.pkgFile === 'null';
  handleLabelCheck();
  needDownload.value = !!data?.needDownload;

  if (needDownload.value && downloadNow === 1 && !isDownload.value) {
    handleDownload();
    isDownload.value = true;
  }

  setModalProps({ confirmLoading: false });
});
const { beOrigin } = useGlobSetting();
/** ios二维码下载用前缀 */
const itms = 'itms-services://?action=download-manifest&url=';

const qrCodeUrl = computed(() => `${detail.value?.platform === 2 ? itms : ''}${beOrigin}${detail.value?.shortURL?.shortURL}`);

function handleExternalLink(url: string) {
  if (oasis !== 0) {
    // 在 oasis 中点击使用原生浏览器打开
    const oasisUrl = `oasisdownload://open-browser?url=${encodeURIComponent(url)}`;
    window.open(oasisUrl, '_blank');
  } else {
    window.open(url, '_blank');
  }
}

async function handleDownload(isPlist = false) {
  let tempToken = '';

  if (!detail.value?.pkgID || !detail.value?.ID) {
    return;
  }

  if (oasis === 0) {
    const { token } = await getTempToken();

    tempToken = token;
  }

  const originUrl
      = detail.value?.[isPlist ? 'downloadLink' : 'pkgFile']
      + (oasis === 2
        ? `?projectID=${userStore.getProjectId}&pkgID=${detail.value.pkgID}&versionID=${detail.value.ID}`
        : '');
  const fileName = originUrl?.split('/').pop() || '';
  const authUrl = `${beOrigin}/download/appstore/projects/${userStore.getProjectId}/pkgs/${detail.value.pkgID}/versions/${detail.value.ID}/package/${fileName}?token=${tempToken}`;
  const preUrl = isPlist ? itms : '';
  const downloadUrl = preUrl + (oasis !== 0 || isPlist ? `${beOrigin}/${originUrl}` : authUrl);
  const downList: { url: string; fileName: string }[] = [];

  downList.push({ url: downloadUrl, fileName });

  if (!oasis) {
    // 非 OASIS 内使用 AuthUrl 下载附件
    newDetail.value?.attachments?.forEach((item) => {
      if (item.downloadLink) {
        if (isEditMode.value || isRecycle.value) {
          return;
        }

        const fileName = item.downloadLink.split('/').pop();
        const authUrl = `${beOrigin}/download/appstore/projects/${userStore.getProjectId}/pkgs/${detail.value?.pkgID}/versions/${detail.value?.ID}/atts/${item.ID}/${fileName}?token=${tempToken}`;

        downList.push({ url: authUrl, fileName: item.name! });
      }
    });
  }

  chunk(downList, 10).forEach((list, i) => {
    setTimeout(
      () => {
        list.forEach((item) => {
          downloadByUrl(item);
        });
      },
      i === 0 ? 0 : 3000,
    );
  });
  getGamePackagesVersionLogSource(userStore.getProjectId, detail.value.pkgID, detail.value.ID, oasis !== 0 ? LogSource.Oasis : LogSource.Web);

  createMessage.success(oasis !== 0 ? '已唤起Oasis' : '已开始下载游戏包...');
}

function pressEnter(e: KeyboardEvent) {
// e失焦
  (e.target as any)?.blur();
}

// URL失焦
function extLinksUrlBlur(e: Event, item: extLinksItem, index: number, type: 'title' | 'url') {
  // 获取上一个兄弟元素
  const previousNode = (e.target as HTMLElement).previousElementSibling;
  const nextNode = (e.target as HTMLElement).nextElementSibling;

  setTimeout(async () => {
    // 点击外部失焦
    if ((type === 'url' && previousNode && previousNode !== document.activeElement) || (type === 'title' && nextNode && nextNode !== document.activeElement)) {
      item.isOpen = false;
      hasNewExtLinkEdit.value = false;

      if (item.URL && item.title) {
        if (!item.URL.includes('.')) {
          createMessage.error('请输入正确的URL');

          return;
        }

        if (!item.URL?.startsWith('http://') && !item.URL?.startsWith('https://')) {
          item.URL = `https://${item.URL}`;
        }

        if (item.ID) {
          const thisExtLink = detail.value?.extLinks?.find((e) => e.ID === item.ID);

          if (thisExtLink?.title === item.title && thisExtLink.URL === item.URL) {
            return;
          }

          await updateExtLinks(userStore.getProjectId, item.ID, {
            pkgID: pkg.value?.ID,
            versionID: detail.value?.ID,
            title: item.title,
            URL: item.URL,
          });
        } else {
          await addExtLinks(userStore.getProjectId, {
            pkgID: pkg.value?.ID,
            versionID: detail.value?.ID,
            title: item.title,
            URL: item.URL,
          });
        }

        const { repkgVersion } = await getGamePackagesVersionByID(
          Number(userStore.getProjectId),
          pkg.value!.ID!,
          detail.value!.ID!,
        );

        detail.value = repkgVersion;

        if (detail.value?.extLinks?.[index]) {
          newDetail.value!.extLinks![index] = cloneDeep(detail.value.extLinks[index]);
        } else {
          newDetail.value?.extLinks?.splice(index, 1);
        }

        redoModalHeight();
      } else {
        if (detail.value?.extLinks?.[index]) {
          newDetail.value!.extLinks![index] = cloneDeep(detail.value.extLinks[index]);
        } else {
          newDetail.value?.extLinks?.splice(index, 1);
        }
      }
    }
  });
}

async function handleDownloadByOasis() {
  openWindow(`oasisdownload://type=1&project=${userStore.getProjectId}&branch=${detail.value?.pkgID}&version=${detail.value?.ID}`, { target: '_self' });
}

function getPlatformIconByVal(val?: number) {
  return platformOptions.find((e) => e.value === val);
}

async function getDetailInfo() {
  if (isRecycle.value) {
    const { reVersion } = await getGamePackagesRecycleVersionByID(
      Number(userStore.getProjectId),
      pkg.value!.ID!,
      detail.value!.ID!,
    );

    detail.value = reVersion;
    newDetail.value = cloneDeep(detail.value);
  } else {
    const { repkgVersion } = await getGamePackagesVersionByID(
      Number(userStore.getProjectId),
      pkg.value!.ID!,
      detail.value!.ID!,
    );

    detail.value = repkgVersion;
    newDetail.value = cloneDeep(detail.value);
  }

  redoModalHeight();
}

async function getBuildInfo() {
  const { list } = await getProjectPkgBuildInfoListByPage(userStore.getProjectId, {
    page: 1,
    pageSize: 999,
  });
  const findInfo = list?.find((e) => e.versionID === detail.value?.ID);

  buildInfo.value = findInfo
    ? {
      ...findInfo,
      /** 去掉为0的值 */
      engineCL: findInfo.engineCL || undefined,
      shelveCL: findInfo.shelveCL || undefined,
      projectCL: findInfo.projectCL || undefined,
    }
    : undefined;
  newBuildInfo.value = cloneDeep(
    buildInfo.value || {
      versionID: detail.value?.ID,
    },
  );

  if (buildInfo.value) {
    await getCurCLDetail(1, buildInfo.value.engineCL);
    await getCurCLDetail(2, buildInfo.value.shelveCL);
    await getCurCLDetail(3, buildInfo.value.projectCL);
  }
}

async function getCurCLDetail(clType: number, cl?: number) {
  if (!cl) {
    return;
  }

  const { clInfo } = await getProjectPkgBuildInfoCLDetail(
    userStore.getProjectId,
    buildInfo.value!.ID!,
    {
      pkgID: pkg.value!.ID!,
      clType: clType === 3 ? 1 : clType,
      cl,
    },
  );

  buildInfoClDetail.value = cloneDeep({
    ...buildInfoClDetail.value,
    [clType === 1 ? 'engineCL' : clType === 2 ? 'shelveCL' : 'projectCL']: clInfo,
  });
}

/** 打开包体检测详情页 */
function openDoctorPage(item: GamePackagesVersionsListItem) {
  if (!item.doctorID) {
    return;
  }

  const { href } = resolve({
    name: PlatformEnterPoint.GamePackageDoctor,
    params: {
      id: pkg.value?.ID,
      doctorID: item.doctorID,
    },
    query: {
      fs: 1,
      p: userStore.getProjectId,
    },
  });

  openWindow(href);
}
const { openCloudPhonePage } = useCloudPhonePage(oasis);

/** 处理标签选中 */
function handleLabelCheck() {
  if (isEditMode.value) {
    showLabels.value = cloneDeep(allLabels.value);
  } else {
    showLabels.value = cloneDeep(detail.value?.labels ?? []);
  }

  showLabels.value = sortBy(showLabels.value, 'sort');
  showLabels.value?.forEach((e) => {
    e.values?.forEach((val) => {
      const findVal = detail.value?.labels
        ?.find((d) => d.ID === e.ID)
        ?.values
        ?.find((v) => v.ID === val.ID);

      val.checked = !!findVal;
    });
  });
  redoModalHeight();
}

async function updateColor(color: number) {
  newDetail.value!.color = color;

  const res = await editVersion();

  if (res?.code !== 7) {
    emit('success');
    await getDetailInfo();
  }
}

/** 编辑版本 */
async function editVersion() {
  return await editGamePackagesVersion(
    userStore.getProjectId,
    pkg.value!.ID!,
    {
      ...omit(newDetail.value, [
        'CreatedAt',
        'UpdatedAt',
        'ID',
        'labels',
        'shortURL',
        'attachments',
      ]),
      marks: newDetail.value?.marks?.map((e) => pick(e, ['name', 'color'])),
    },
    detail.value!.ID!,
  );
}

/** 置顶 */
async function handlePinTop(item?: GamePackagesVersionsListItem) {
  if (!item) {
    return;
  }

  newDetail.value!.top = !item.top;

  const res = await editVersion();

  if (res?.code !== 7) {
    await getDetailInfo();
    emit('success');
  }
}

function handleShare() {
  const link = router.resolve({
    name: currentRoute.value.name,
    query: {
      p: userStore.getProjectId,
      b: pkg.value?.ID,
      v: detail.value?.ID,
      downloadNow: '1',
    },
  }).href;

  copyText(window.location.origin + link, '下载链接已复制到剪贴板!');
}

function handleOasisShare() {
  const link = router.resolve({
    path: '/link/app',
    query: {
      fs: '1',
      app: 'oasisdownload',
      type: '1',
      project: userStore.getProjectId,
      branch: detail.value?.pkgID,
      version: detail.value?.ID,
    },
  }).href;

  copyText(window.location.origin + link, 'Oasis下载链接已复制到剪贴板!');
}

/** 添加标记 */
function handleMarkAdd() {
  if (newDetail.value!.marks?.some((e) => isEmpty(e.name?.trim()))) {
    return;
  }

  hasNewMarkEdit.value = true;

  if (!newDetail.value!.marks) {
    newDetail.value!.marks = [];
  }

  newDetail.value!.marks.push({
    name: '',
    color: defaultColorList[0],
    UUID: buildNumberUUID(),
  });
}

/** 添加扩展链接 */
function handleExtLinkAdd() {
  hasNewExtLinkEdit.value = true;

  if (!newDetail.value!.extLinks) {
    newDetail.value!.extLinks = [];
  }

  newDetail.value!.extLinks.push({
    title: '',
    URL: '',
  });
  redoModalHeight();
}

/** 删除标记 */
function handleMarkDelete(mark: GamePackagesVersionMarksListItem) {
  if (mark.UUID) {
    newDetail.value!.marks = newDetail.value!.marks?.filter((e) => e.UUID !== mark.UUID);
    hasNewMarkEdit.value = false;

    return;
  }

  newDetail.value!.marks = newDetail.value!.marks?.filter((e) => e.name !== mark.name);
  handleEditDetail('marks');
}

// 删除扩展链接
async function handleExtLinksDelete(item: { ID?: number }) {
  newDetail.value!.extLinks = newDetail.value!.extLinks?.filter((e) => e.ID !== item.ID);

  if (!item.ID) {
    hasNewExtLinkEdit.value = false;

    return;
  }

  const res = await deleteExtLinks(userStore.getProjectId, item.ID);

  if (res?.code !== 7) {
    await getDetailInfo();
  }

  hasNewExtLinkEdit.value = false;
}

/** 添加附件 */
async function handleAttAdd(url: string) {
  if (!url) {
    return;
  }

  await addGameAttachment(userStore.getProjectId, {
    downloadLink: url,
    name: url.split('/').pop(),
    versionID: detail.value!.ID!,
  });
  await getDetailInfo();
}

/** 编辑附件 */
async function handleAttEdit(att: GameAttachmentsListItem) {
  if (isEmpty(att.name?.trim())) {
    createMessage.warning('资源名不能为空');

    return;
  }

  const originAtt = detail.value?.attachments?.find((e) => e.ID === att.ID);

  if (originAtt?.name === att.name) {
    return;
  }

  await editGameAttachment(userStore.getProjectId, att, att.ID!);
  await getDetailInfo();
}

async function handleAttDelete(ID: number) {
  await deleteGameAttachment(userStore.getProjectId, ID);
  await getDetailInfo();
}

async function handleAttDownload(att: GameAttachmentsListItem) {
  if (isEditMode.value || isRecycle.value) {
    return;
  }

  let tempToken = '';

  if (oasis === 0) {
    const { token } = await getTempToken();

    tempToken = token;
  }

  const fileName = att.downloadLink?.split('/').pop();
  const authUrl = `${beOrigin}/download/appstore/projects/${userStore.getProjectId}/pkgs/${detail.value?.pkgID}/versions/${detail.value?.ID}/atts/${att.ID}/${fileName}?token=${tempToken}`;

  downloadByUrl({
    url: oasis !== 0 ? preprocessFilePath(att.downloadLink, { origin: beOrigin }) : authUrl,
    fileName: att.name!,
  });
  createMessage.success(oasis !== 0 ? '已唤起Oasis' : '已开始下载游戏包...');
}

/** 编辑详情 */
async function handleEditDetail(editLabel: keyof GamePackagesVersionsListItem) {
  const newVal = newDetail.value?.[editLabel === 'color' ? 'marks' : editLabel];
  const oldVal = detail.value?.[editLabel === 'color' ? 'marks' : editLabel];

  if (
    ['color', 'marks'].includes(editLabel)
    && Array.isArray(newVal)
    && newVal?.length
    && newVal.some((e) => !(e as GamePackagesVersionMarksListItem)?.name?.trim())
  ) {
    if (editLabel === 'marks') {
      createMessage.warning('标记名不能为空');
    }

    return;
  } else if (editLabel === 'marks') {
    const names = (newVal as GamePackagesVersionsListItem['marks'])?.map((e) => e.name?.trim());
    const set = new Set(names);

    if (set.size !== names?.length) {
      createMessage.warning('标记名不能重复');

      return;
    }
  }

  if (isEqual(newVal, oldVal)) {
    return;
  }

  hasChange.value = true;

  const res = await editVersion();

  if (res?.code !== 7) {
    await getDetailInfo();
    hasNewMarkEdit.value = false;
  }
}

/** 进入编辑模式 */
function handleEdit() {
  isEditMode.value = true;
}

/** 取消编辑 */
async function handleEditCancel() {
  isEditMode.value = false;
  hasNewMarkEdit.value = false;
  hasNewExtLinkEdit.value = false;

  newDetail.value = cloneDeep(detail.value);

  if (hasChange.value) {
    await getDetailInfo();
    await getBuildInfo();
    handleLabelCheck();
    emit('success');
  }

  hasChange.value = false;
}

function isIntegerOrEmpty(val: string | number | undefined) {
  return !val || Number.isInteger(Number(val));
}

async function handleEditBuildInfo() {
  const needEqualList = ['engineCL', 'shelveCL', 'projectCL'];

  if (isEqual(pick(newBuildInfo.value, needEqualList), pick(buildInfo.value, needEqualList))) {
    return;
  }

  hasChange.value = true;

  // 只能为整数或空
  if (
    !isIntegerOrEmpty(newBuildInfo.value.engineCL)
    || !isIntegerOrEmpty(newBuildInfo.value.shelveCL)
    || !isIntegerOrEmpty(newBuildInfo.value.projectCL)
  ) {
    createMessage.warning('CL只能为整数');

    return;
  }

  const submitData = omit(newBuildInfo.value, [
    'ID',
    'CreatedAt',
    'UpdatedAt',
    'shelveCLInfo',
    'projectID',
  ]);

  if (buildInfo.value?.ID) {
    await editProjectPkgBuildInfo(userStore.getProjectId, submitData, buildInfo.value.ID!);
  } else {
    await addProjectPkgBuildInfo(userStore.getProjectId, submitData);
  }

  getBuildInfo();
}

/** 标签修改 */
async function handleTagClick(val: GameLabelValuesListItem, label: GameLabelsListItem) {
  let needDeleteID: number | undefined;

  if (isEditMode.value) {
    // 如果是单选，且需先删除之前的选项
    if (label.singleChoice && label.values?.length) {
      label.values.forEach((e) => {
        if (!!e.checked && val.ID !== e.ID) {
          e.checked = false;
          needDeleteID = e.ID;
        }
      });

      if (needDeleteID) {
        const res = await deleteGamePackagesVersionTag(
          userStore.getProjectId,
          pkg.value!.ID!,
          detail.value!.ID!,
          label.ID!,
          {
            valueID: needDeleteID,
          },
        );

        if (res?.code !== 7 && val.checked) {
          createMessage.success('去除标签成功');
        }
      }
    }

    val.checked = !val.checked;

    if (val.checked) {
      const res = await newGamePackagesVersionTag(
        userStore.getProjectId,
        pkg.value!.ID!,
        detail.value!.ID!,
        {
          valueID: val.ID,
        },
      );

      if (res?.code !== 7) {
        createMessage.success(needDeleteID ? '修改标签成功' : '添加标签成功');
      }
    } else {
      const res = await deleteGamePackagesVersionTag(
        userStore.getProjectId,
        pkg.value!.ID!,
        detail.value!.ID!,
        label.ID!,
        {
          valueID: val.ID,
        },
      );

      if (res?.code !== 7) {
        createMessage.success('去除标签成功');
      }
    }

    hasChange.value = true;
    getDetailInfo();
  }
}

async function handleRecycle() {
  const res = await recoverGamePackagesRecycleVersionByID(Number(userStore.getProjectId), pkg.value!.ID!, detail.value!.ID!);

  if (res?.code !== 7) {
    emit('success');
    closeModal();
  }
}

async function handleClose() {
  if (currentRoute.value.query.v) {
    go({ query: { ...currentRoute.value.query, v: undefined, downloadNow: undefined } }, true);
  }

  showLabels.value = [];
  buildInfoClDetail.value = {
    engineCL: undefined,
    shelveCL: undefined,
    projectCL: undefined,
  };
  newBuildInfo.value = {};
  handleEditCancel();
}

watch(
  () => isEditMode.value,
  () => {
    handleLabelCheck();
  },
);
</script>

<style lang="less">
@prefix-cls: ~'hypergryph-game-package-card-detail-modal';

.@{prefix-cls} {
  .ant-modal-content {
    border-radius: 20px !important;
  }
  &__desc {
    flex: 1;
    padding: 16px;

    &-title {
      padding-bottom: 10px;
      font-size: 18px;
      font-weight: bold;
    }

    &-content {
      max-height: 350px;
      padding-right: 16px;
      transform: scale(0.95);
      color: @FO-Content-Text2;
    }
  }
  &__download-btn {
    &-pro {
      border-color: #414141;
      background-color: #414141;
      color: #f8cc28;

      &:hover {
        border-color: #f8cc28 !important;
        background-color: #f8cc28 !important;
        color: #000 !important;
      }
      &:focus {
        border-color: #414141;
        background-color: #414141;
        color: #f8cc28;
      }

      &-hover {
        border-color: #f8cc28;
        background-color: #f8cc28;
        color: #000;
      }
    }
  }
  &__mark-del-btn,
  &__btn {
    border-color: #4d4d4d !important;
    background-color: #4d4d4d !important;
    color: #fff !important;

    &-pro {
      display: flex;
      align-items: center;
      border-color: #f8cc28 !important;
      background-color: #f8cc28 !important;
      color: #000 !important;
      font-weight: bold;
    }
  }

  &__mark-del-btn {
    display: flex;
    position: absolute !important;
    top: -6px;
    right: -6px;
    align-items: center;
    justify-content: center;
    min-width: 8px !important;
    height: 16px !important;
    font-size: 6px !important;
  }

  &__footer {
    position: relative;
    min-height: 200px;
    margin-top: 8px;
    padding: 8px;
    overflow: hidden;
    border-radius: 0 0 16px 16px;
    background-color: @member-card-background;

    &-item {
      display: flex;
      position: relative;
      z-index: 1;

      padding: 8px 16px;
      border-radius: 8px;
      background: @FO-Container-Fill1;
      font-weight: bold;

      &:not(:last-child) {
        margin-bottom: 8px;
      }

      &-desc {
        min-height: 22px !important;
        padding: 0 8px !important;
        line-height: 32px !important;
        &-edit {
          border-radius: 5px !important;
          background-color: @FO-Container-Fill5 !important;
        }
      }

      &-att {
        display: flex;
        position: relative;
        align-items: center;
        margin-right: 16px;
        padding: 2px 10px;
        border-radius: 100px;
        background-color: @FO-Container-Fill5;
        font-size: 12px;
        cursor: pointer !important;

        &[disabled='true'] {
          cursor: default !important;
        }
      }

      &-content {
        display: flex;
        flex: 1;
        flex-wrap: wrap;
        align-items: center;
        width: 0;
        row-gap: 4px;
      }
    }
    /** 菱形背景 */
    &-mark {
      position: relative;

      &-before {
        position: absolute;
        z-index: 1;
        top: 0;
        left: 0;

        &::before {
          content: ' ';
          position: absolute;
          z-index: 2;
          top: -2px;
          left: 0;
          width: 0;
          height: 0;
          border-top: 12px solid @FO-Container-Fill1;
          border-right: 12px solid transparent;
          border-bottom: 12px solid @FO-Container-Fill1;
        }
      }

      &-name {
        display: flex;
        position: relative;
        align-items: center;
        margin-right: 4px;
        padding: 2px 10px;
        color: #fff;
        font-size: 12px;
        line-height: 16px;
        white-space: nowrap;
      }

      &-after {
        position: absolute;
        top: 0;
        right: 0;

        &::after {
          content: ' ';
          position: absolute;
          z-index: 2;
          top: -2px;
          left: -16px;
          width: 0;
          height: 0;
          border-top: 12px solid @FO-Container-Fill1;
          border-bottom: 12px solid @FO-Container-Fill1;
          border-left: 12px solid transparent;
        }
      }

      &-color-picker {
        position: absolute;
        top: 8px;
        right: 8px;
        width: 16px;
        height: 16px;
        transform: rotate(45deg);
      }

      &-edit {
        position: relative;
        margin-right: 8px;
        border: 1px solid transparent;
        border-radius: 5px;
        background-color: @FO-Container-Fill5 !important;

        &[isNew='true'] {
          border-style: dashed !important;
        }
      }

      &-input {
        width: 90% !important;
      }
    }

    &-label {
      display: flex;
      flex-wrap: wrap;
      padding: 2px 2px 2px 8px;
      border-radius: 15px;
      background-color: @FO-Container-Fill5;
      font-size: 12px;
      row-gap: 4px;

      &:not(:last-child) {
        margin-right: 4px;
      }

      &-item {
        display: flex;
        align-items: center;
        padding: 0 8px;
        border: 1px solid transparent;
        border-radius: 4px;
        color: @FO-Content-Components1;
        font-size: 10px;
        line-height: 16px;
        white-space: nowrap;
        user-select: none;

        &[isSingle='false'] {
          margin-right: 4px;
        }

        &[isSingle='true'] {
          border-radius: 100px;
        }

        &[isEditMode='true'] {
          cursor: pointer;
        }
      }
    }
  }

  & .scroll-container .scrollbar__wrap {
    margin-bottom: 0 !important;
  }

  &__reserve-switch {
    margin: 0 4px !important;

    &.ant-switch-checked {
      background-color: #bc9e00 !important;
    }
  }

  & .ant-modal-header {
    padding: 0 !important;
  }
}

[data-theme='dark'] .@{prefix-cls} {
  & img {
    filter: brightness(0.8);
  }
}
</style>
