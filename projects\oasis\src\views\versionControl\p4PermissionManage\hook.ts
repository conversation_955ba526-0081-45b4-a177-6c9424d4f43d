import { map } from 'lodash-es';
import { type Ref, computed, ref, watch } from 'vue';
import type { DepotsListItem, StreamsListItem } from '/@/api/page/model/p4Model';
import {
  getDepotsListByPage,
  getDM01StreamsListByPage,
  getStreamsByID,
  getStreamsListByPage,
} from '/@/api/page/p4';
import { useUserStore, useUserStoreWithOut } from '/@/store/modules/user';
import { useLatestPromise } from '@hg-tech/utils-vue';

const depotList = ref<DepotsListItem[]>([]);
const activeKey = ref<number[]>([]);
const userStore = useUserStoreWithOut();
const curDepot = ref<DepotsListItem>();

export function useP4Depot(depotID?: number) {
  const getDepotsList = async () => {
    if (!userStore.getProjectId) {
      return;
    }
    const { list } = await getDepotsListByPage(userStore.getProjectId, {
      page: 1,
      pageSize: 999,
    });
    if (list?.length > 0) {
      depotList.value = list;
      activeKey.value = map(list, 'ID') as number[];
      if (depotID) {
        curDepot.value = getCurDepot(depotID);
      }
    } else {
      depotList.value = [];
    }
  };

  function getCurDepot(id: number) {
    return depotList.value.find((item) => item.ID === id);
  }

  return {
    depotList,
    activeKey,
    getDepotsList,
    getCurDepot,
    curDepot,
  };
}

export function useP4DepotStream() {
  const allStreamList = ref<StreamsListItem[]>([]);

  const getAllStreamList = async (depotID: number | null, isDMO1 = false, filterProject?: boolean, tagConfig?: boolean) => {
    if (!userStore.getProjectId) {
      return;
    }
    const params = {
      depotID,
      filterProject,
      tagConfig,
      page: 1,
      pageSize: 999,
    };
    const { list } = isDMO1
      ? await getDM01StreamsListByPage(params)
      : await getStreamsListByPage(userStore.getProjectId, params);

    if (list?.length > 0) {
      allStreamList.value = list;
    } else {
      allStreamList.value = [];
    }
  };

  return {
    getAllStreamList,
    allStreamList,
  };
}

export function useP4StreamInfo(
  depotID: Ref<number>,
  streamID: Ref<number>,
) {
  const userStore = useUserStore();
  const { data, execute, loading } = useLatestPromise(getStreamsListByPage);

  watch([() => userStore.getProjectId, depotID], () => refresh(), { immediate: true });

  function refresh() {
    return execute(userStore.getProjectId, {

      depotID: depotID.value,
      page: 1,
      pageSize: 999,
    });
  }

  const allStreamList = computed(() => data.value?.list ?? []);
  const curStream = computed(() => allStreamList.value.find((e) => e.ID === streamID.value));

  return {
    curStream,
    allStreamList,
    refresh,
    loadingStream: loading,
  };
}
export function useCopyTagInfo(
  depotID: Ref<number>,
  streamID: Ref<number>,
) {
  const userStore = useUserStore();
  const { data, execute, loading } = useLatestPromise(getStreamsListByPage);
  const { data: streamInfo, execute: fetchStreamInfo } = useLatestPromise(getStreamsByID);

  watch([() => userStore.getProjectId, depotID], () => {
    refresh();
    refreshStreamInfo();
  }, { immediate: true });

  function refresh() {
    return execute(userStore.getProjectId!, {
      tagConfig: true,
      filterProject: true,
      page: 1,
      pageSize: 999,
    });
  }
  function refreshStreamInfo() {
    return fetchStreamInfo(userStore.getProjectId!, streamID.value);
  }
  const allStreamList = computed(() => data.value?.list ?? []);
  const curStream = computed(() => streamInfo.value?.restream);

  return {
    curStream,
    refreshStreamInfo,
    allStreamList,
    refresh,
    loadingStream: loading,
  };
}
