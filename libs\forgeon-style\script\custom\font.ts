import type { ForgeonDesignClass } from '../type.ts';

export const FontPresetToken = {
  FontB20: {
    fontSize: '20px',
    fontWeight: 600,
    lineHeight: '28px',
  },
  FontB18: {
    fontSize: '18px',
    fontWeight: 600,
    lineHeight: '26px',
  },
  FontB16: {
    fontSize: '16px',
    fontWeight: 600,
    lineHeight: '24px',
  },
  FontR16: {
    fontSize: '16px',
    fontWeight: 400,
    lineHeight: '24px',
  },
  FontB14: {
    fontSize: '14px',
    fontWeight: 600,
    lineHeight: '22px',
  },
  FontR14: {
    fontSize: '14px',
    fontWeight: 400,
    lineHeight: '22px',
  },
  FontR12: {
    fontSize: '12px',
    fontWeight: 400,
    lineHeight: '16px',
  },
} satisfies ForgeonDesignClass;
