<template>
  <ACard :class="prefixCls" v-bind="$attrs">
    <div class="flex">
      <div>
        <span class="mr-3">周期</span>
        <a-select
          v-model:value="period"
          placeholder="请选择周期"
          :options="periodOptions"
          @change="init()"
        />
      </div>
      <div class="ml-4">
        <span class="mr-3">范围</span>
        <a-select
          v-model:value="range"
          placeholder="请选择范围"
          :options="showRangeOptions"
          @change="init()"
        />
      </div>
    </div>
    <div
      v-show="!isNoData"
      ref="chartRef"
      :class="`${prefixCls}__chart`"
      :style="{ height: '250px', width: '100%' }"
    />
    <AEmpty v-show="isNoData" :image="emptyImg" class="h-250px flex items-center justify-center" />
  </ACard>
</template>

<script lang="ts" setup>
import { Card as ACard, Empty as AEmpty } from 'ant-design-vue';
import type { BarSeriesOption, PieSeriesOption } from 'echarts';
import { isEmpty } from 'lodash-es';
import type { Ref } from 'vue';
import { computed, onBeforeMount, ref, watch } from 'vue';
import { periodOptions, rangeOptions } from '../analysis.data';
import type { TrackingToolChartsItemResultModel } from '/@/api/page/model/trackingModel';
import { useDesign } from '/@/hooks/web/useDesign';
import { useECharts } from '/@/hooks/web/useECharts';
import { ECHARTS_COLOR_LIST } from '/@/settings/designSetting';
import { isNullOrUnDef } from '/@/utils/is';

defineOptions({
  name: 'TrackingAnalysisDistributionCard',
});

const props = defineProps({
  projectID: {
    type: Number,
    default: 0,
  },
  toolID: {
    type: Number,
    required: true,
  },
  chartApi: {
    type: Function as PropType<(p: any) => Promise<any>>,
    default: () => {},
  },
});
const { prefixCls } = useDesign('tracking-analysis-distribution-card');
const emptyImg = AEmpty.PRESENTED_IMAGE_SIMPLE;

const chartRef = ref<HTMLDivElement | null>(null);
const { setOptions, getInstance } = useECharts(chartRef as Ref<HTMLDivElement>);

const lineData = ref<TrackingToolChartsItemResultModel[]>([]);
const pieData = ref<{ [key: string]: number }>({});
const period = ref(1);
const range = ref(5);
const isNoData = ref(false);

const showRangeOptions = computed(() => {
  return rangeOptions.map((item) => {
    const curPeriod = periodOptions.find((e) => e.value === period.value);

    return { label: item.label + curPeriod?.label || '', value: item.value };
  });
});

async function getToolVersions() {
  if (isNullOrUnDef(props.projectID)) {
    return;
  }

  const { line, pie } = await props.chartApi({
    toolID: props.toolID,
    period: period.value,
    range: range.value,
    projectID: props.projectID,
  });

  lineData.value = line || [];
  pieData.value = pie || {};
  isNoData.value = isEmpty(pie);
}

async function init() {
  await getToolVersions();

  if (isNoData.value) {
    return;
  }

  const xAxisData: string[] = [];
  const series: (BarSeriesOption | PieSeriesOption)[] = [];
  let index = 0;

  for (const key in pieData.value) {
    series.push({
      color: ECHARTS_COLOR_LIST[index],
      name: key,
      type: 'bar',
      stack: '总量',
      emphasis: {
        focus: 'series',
      },
      data: [],
    });
    index++;
  }

  series.push({
    type: 'pie',
    radius: '60%',
    color: ECHARTS_COLOR_LIST,
    center: ['80%', '55%'],
    label: {
      position: 'inner',
      color: '#fff',
      formatter: (params) => {
        if (params.percent! < 5) {
          return '';
        } else {
          return params.name;
        }
      },
    },
    emphasis: {
      focus: 'self',
    },
    labelLine: {
      show: false,
    },
    tooltip: {
      trigger: 'item',
      formatter: (params: any) => {
        return `${params.marker}${params.name}: ${params.value}`;
      },
    },
    data: Object.keys(pieData.value).map((key) => {
      return {
        name: key,
        value: pieData.value[key],
      };
    }),
  });
  lineData.value.forEach((item) => {
    xAxisData.push(item.time);
    series.forEach((s) => {
      s.data?.push(item.data?.[s.name!] || null);
    });
  });
  setOptions({
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow',
      },
      formatter: (params: any) => {
        const { name } = params[0];
        let str = `${name}<br/>`;
        const reParams = params.reverse();

        reParams.forEach((item: any) => {
          str += `${item.marker}${item.seriesName}: ${item.value || '-'}<br/>`;
        });

        return str;
      },
    },
    toolbox: {
      itemSize: 20,
      feature: {
        magicType: {
          type: ['stack'],
        },
      },
    },
    grid: {
      right: '40%',
      left: '2%',
      bottom: '4%',
      containLabel: true,
    },
    xAxis: [
      {
        type: 'category',
        data: xAxisData,
      },
    ],
    yAxis: [
      {
        type: 'value',
      },
    ],
    series,
  });
  getInstance()?.resize();
  // 监听鼠标hover事件
  getInstance()?.on('mouseover', 'series', (event) => {
    const { seriesType, name, seriesName } = event;

    if (seriesType === 'pie') {
      // 鼠标停留在饼图上时，触发柱状图对应数据项的高亮效果
      getInstance()?.dispatchAction({
        type: 'highlight',
        seriesIndex: series.findIndex((s) => s.name === name),
      });
    } else if (seriesType === 'bar') {
      // 先取消柱状图所有数据项的高亮效果
      getInstance()?.dispatchAction({
        type: 'downplay',
        seriesIndex: series.length - 1,
      });
      // 鼠标停留在柱状图上时，触发饼图对应数据项的高亮效果
      getInstance()?.dispatchAction({
        type: 'highlight',
        seriesIndex: series.length - 1,
        name: seriesName,
      });
    }
  });
}

onBeforeMount(async () => {
  watch(
    () => props.toolID,
    (val, oldVal) => {
      if (val !== oldVal) {
        init();
      }
    },
    {
      immediate: true,
    },
  );
  watch(
    () => props.projectID,
    (val, oldVal) => {
      if (val !== oldVal) {
        init();
      }
    },
  );
});
</script>

<style lang="less">
@prefix-cls: ~'hypergryph-tracking-analysis-distribution-card';
.@{prefix-cls} {
  & .ant-card-body {
    padding: 16px;
  }
}
</style>
