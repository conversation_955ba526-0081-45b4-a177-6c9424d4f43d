<template>
  <div :class="prefixCls">
    <div v-for="param in paramList" :key="param.ID" :class="`${prefixCls}__item`">
      <div :class="`${prefixCls}__item-label`">
        <ATypographyText :ellipsis="{ tooltip: true }" :content="param.name" />
        <ATypographyText
          :ellipsis="{ tooltip: true }"
          :content="param.description"
          class="text-xs c-FO-Content-Text2 !block"
        />
      </div>
      <div :class="`${prefixCls}__hover-icon`">
        <div :class="`${prefixCls}__hover-icon`">
          <a-button
            type="text"
            class="!px-2 !c-FO-Brand-Primary-Default"
            title="编辑参数"
            @click.stop="handleEdit(param)"
          >
            <Icon icon="clarity:note-edit-line" />
          </a-button>
          <APopconfirm
            placement="right"
            title="确定要删除该参数吗？"
            @confirm="handleDelete(param)"
          >
            <a-button
              v-if="isSuperAdminOrProjectAdmin"
              type="text"
              danger
              class="!px-2"
              title="删除参数"
              @click.stop
            >
              <Icon icon="clarity:trash-line" />
            </a-button>
          </APopconfirm>
        </div>
      </div>
      <div class="flex-1">
        <a-input
          v-if="param.type === 1"
          v-model:value="submitParam[param.name]"
          :bordered="false"
          class="bg-FO-Container-Fill1"
          @change="emit('change', submitParam, jobID)"
        />
        <ARadioGroup
          v-else-if="param.type === 2"
          v-model:value="submitParam[param.name]"
          :class="`${prefixCls}__item-box`"
          @change="emit('change', submitParam, jobID)"
        >
          <ARadio v-for="option in param.options" :key="option" :value="option">
            {{ option }}
          </ARadio>
        </ARadioGroup>
        <ACheckboxGroup
          v-else-if="param.type === 3"
          v-model:value="submitParam[param.name]"
          :class="`${prefixCls}__item-box`"
          @change="emit('change', submitParam, jobID)"
        >
          <ACheckbox v-for="option in param.options" :key="option" :value="option">
            {{ option }}
          </ACheckbox>
        </ACheckboxGroup>
      </div>
    </div>
    <a-button
      v-if="isSuperAdminOrProjectAdmin"
      v-tippy="{
        content: '添加参数',
        placement: 'bottom',
      }"
      block
      class="mt-3 text-lg !border-0 !bg-FO-Container-Fill1 !text-dark-50 dark:!bg-#151515"
      @click="handleAdd"
    >
      +
    </a-button>
    <ParamModal @register="registerModal" @success="handleSuccess" />
  </div>
</template>

<script lang="ts" setup name="JenkinsAutoTaskParamList">
import {
  Checkbox as ACheckbox,
  Popconfirm as APopconfirm,
  Radio as ARadio,
  TypographyText as ATypographyText,
} from 'ant-design-vue';
import { onBeforeMount, ref, toRefs } from 'vue';
import ParamModal from './ParamModal.vue';
import { deleteJobParam, getJobParamsListByPage } from '/@/api/page/jenkins';
import type { JobParamsListItem } from '/@/api/page/model/jenkinsModel';
import Icon from '/@/components/Icon';
import { useModal } from '/@/components/Modal';
import { useDesign } from '/@/hooks/web/useDesign';
import { useUserStoreWithOut } from '/@/store/modules/user';
import { useAdmin } from '../../../../../hooks/useProjects.ts';

const props = defineProps({
  jobID: {
    type: Number,
    default: 0,
  },
});

const emit = defineEmits(['change']);

const { prefixCls } = useDesign('jenkins-auto-task-param-list');

const ACheckboxGroup = ACheckbox.Group;
const ARadioGroup = ARadio.Group;

const userStore = useUserStoreWithOut();
const [registerModal, { openModal }] = useModal();
const { jobID } = toRefs(props);
const paramList = ref<JobParamsListItem[]>([]);
const submitParam = ref<Recordable>({});
const { isSuperAdminOrProjectAdmin } = useAdmin();

async function getParamList() {
  if (!jobID.value) {
    return;
  }
  const { list } = await getJobParamsListByPage(userStore.getProjectId, {
    jobID: jobID.value,
    page: 1,
    pageSize: 999,
  });
  paramList.value = list || [];
  list?.forEach((item) => {
    submitParam.value[item.name] = item.type === 3 ? item.defaults : item.defaults?.[0];
  });
  emit('change', submitParam.value, props.jobID);
}

onBeforeMount(() => {
  getParamList();
});

async function handleSuccess() {
  await getParamList();
}

function handleAdd() {
  openModal(true, {
    isUpdate: false,
    jobID: jobID.value,
  });
}

function handleEdit(record: Recordable) {
  openModal(true, {
    record,
    isUpdate: true,
    jobID: jobID.value,
  });
}

async function handleDelete(record: Recordable) {
  await deleteJobParam(userStore.getProjectId, record.ID);
  await handleSuccess();
}
</script>

<style lang="less">
@prefix-cls-task: ~'hypergryph-jenkins-auto-task';
@prefix-cls: ~'hypergryph-jenkins-auto-task-param-list';

.@{prefix-cls-task} {
  &__list-item {
    &:hover {
      .@{prefix-cls} {
        &__hover-icon {
          opacity: 1;
        }
      }
    }
  }
}

.@{prefix-cls} {
  &__item {
    display: flex;
    align-items: center;
    margin: 6px 16px;
    line-height: 32px;

    &-label {
      width: 100px;
      line-height: 16px;
    }

    &-box {
      width: 100%;
      padding: 4px 8px;
      border-radius: 4px;
      background-color: @FO-Container-Fill1;

      & .ant-checkbox-wrapper-checked,
      & .ant-radio-wrapper-checked {
        color: @FO-Brand-Primary-Default;
        font-weight: bold;
      }

      &:hover,
      &:focus {
        border-color: @FO-Brand-Primary-Default;
      }
    }
  }

  &__hover-icon {
    display: inline-block;
    width: 80px;
    transition: all 0.3s ease-in-out;
    opacity: 0;
  }
}
</style>
