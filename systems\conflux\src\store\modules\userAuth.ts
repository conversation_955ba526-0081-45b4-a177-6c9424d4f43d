import { defineStore } from 'pinia';
import { useMicroAppInject, useUserAuthInfoCtx, useUserProfileInfoCtx } from '@hg-tech/oasis-common';

/**
 * 从父应用获取用户信息
 * TODO 如果需要独立启动则需要再此独立获取用户信息
 */
export const useUserAuthStore = defineStore('userAuth', () => {
  const { data, loading } = useMicroAppInject(useUserAuthInfoCtx);
  const { data: userProfile } = useMicroAppInject(useUserProfileInfoCtx);

  return {
    userAuthInfo: data,
    userProfile,
    loading,
  };
});
