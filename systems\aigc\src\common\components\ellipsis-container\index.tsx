import { type PropType, computed, defineComponent, nextTick, onMounted, ref, watch } from 'vue';

const EllipsisContainer = defineComponent({
  props: {
    text: { type: String as PropType<string>, required: true },
    lineClamp: { type: Number as PropType<number>, default: 3 },
    contentClass: {
      type: String as PropType<string>,
      default: '',
    },
  },
  setup(props, { slots }) {
    const containerRef = ref<HTMLElement | null>(null);
    const isClamped = ref(false);
    const expanded = ref(false);

    const checkClamped = async () => {
      await nextTick();
      const el = containerRef.value;
      if (!el) {
        return;
      }
      const original = el.style.webkitLineClamp;
      el.style.webkitLineClamp = props.lineClamp?.toString() || '2';
      isClamped.value = el.scrollHeight > el.offsetHeight + 1;
      el.style.webkitLineClamp = original;
    };

    watch(() => props.text, checkClamped, { immediate: true });
    watch(() => props.lineClamp, checkClamped);

    onMounted(checkClamped);

    const toggle = () => {
      expanded.value = !expanded.value;
    };

    const containerStyle = computed(() => {
      if (expanded.value) {
        return {};
      }
      return {
        display: '-webkit-box',
        WebkitBoxOrient: 'vertical',
        WebkitLineClamp: props.lineClamp,
        overflow: 'hidden',
      } as any;
    });

    return () => (
      <div class="ellipsis-container pos-relative w-full">
        <div
          class={props.contentClass}
          ref={containerRef}
          style={containerStyle.value}
        >
          {props.text}
        </div>
        {isClamped.value && slots.default?.({ expanded: expanded.value, toggle })}
      </div>
    );
  },
});

export { EllipsisContainer };
