import { Add, <PERSON><PERSON>, DsChatAvatar, Fill<PERSON>heck } from '@/common/components/svg-icons';
import { NButton, NIcon, NInput, NPopselect, NTooltip, useMessage } from 'naive-ui';
import { type PropType, defineComponent, ref, watch } from 'vue';

const ChatHeader = defineComponent({
  props: {
    sessionId: {
      type: String as PropType<string>,
      default: '',
    },
    title: {
      type: String as PropType<string>,
      default: '',
    },
    modelOptions: {
      type: Array as PropType<{ label: string; desc: string }[]>,
      default: () => [],
    },
    currentModel: {
      type: String as PropType<string>,
      default: '',
    },
    titleUpdateLoading: {
      type: Boolean as PropType<boolean>,
      default: false,
    },
    siderCollapsed: {
      type: Boolean as PropType<boolean>,
      default: false,
    },
    onCreate: {
      type: Function as PropType<() => void>,
      default: () => {},
    },
    onModelChange: {
      type: Function as PropType<(model: string) => void>,
      default: () => {},
    },
    onTitleChange: {
      type: Function as PropType<(data: { sessionId: string; title: string }) => void>,
      default: () => {},
    },
  },
  setup(props) {
    const message = useMessage();
    const isTitleEdit = ref(false);
    const editTitle = ref('');

    const onTitleEdit = ({ sessionId, title }: { sessionId: string; title: string }) => {
      if (title.trim().length > 12) {
        message.warning('话题长度不能超过12个字');
        return;
      }
      props.onTitleChange({ sessionId, title });
    };

    watch(() => props.titleUpdateLoading, (newVal, oldVal) => {
      if (!newVal && oldVal) {
        isTitleEdit.value = false;
        editTitle.value = '';
      }
    });

    return () => (
      <div class={['model-switch mb-20px gap-12px rd-4px', props.sessionId ? 'flex-c-between' : 'flex-c-center']}>
        {
          props.sessionId
          && (

            <div class="overflow-hidden flex-c-start">
              {props.siderCollapsed && (
                <NButton
                  class="mr-24px flex-shrink-0 rd-md px-11px FO-Font-B14"
                  onClick={props.onCreate}
                  renderIcon={() => (
                    <NIcon>
                      <Add />
                    </NIcon>
                  )}
                  secondary
                  type="primary"
                />
              )}
              <NIcon class="mr-8px flex-shrink-0" size={36}>
                <DsChatAvatar class="c-#4e4e4e" />
              </NIcon>
              {
                !isTitleEdit.value
                  ? (
                    <NTooltip>
                      {{
                        trigger: () => (
                          <div
                            class="mr-8px flex overflow-hidden"
                            onClick={() => {
                              if (!props.sessionId) {
                                return;
                              }
                              isTitleEdit.value = true;
                              editTitle.value = props.title;
                            }}
                          >
                            <span class="mr-8px h-full flex-grow-1 truncate FO-Font-B16">
                              { props.title }
                            </span>
                          </div>
                        ),
                        default: () => '点击修改聊天主题',
                      }}

                    </NTooltip>

                  )
                  : (

                    <div class="gap-8px flex-c-center">
                      <NInput
                        class="h-40px py-6px"
                        clearable
                        maxlength={12}
                        showCount
                        size="small"
                        v-model:value={editTitle.value}
                      />
                      <NButton onClick={() => isTitleEdit.value = false} text>取消</NButton>
                      <NButton
                        disabled={props.titleUpdateLoading || (editTitle.value ?? '').trim().length <= 0}
                        loading={props.titleUpdateLoading}
                        onClick={() => onTitleEdit({ sessionId: props.sessionId, title: editTitle.value.trim() })}
                        text
                        type="primary"
                      >{ props.titleUpdateLoading ? '保存中...' : '保存'}
                      </NButton>
                    </div>
                  )
              }
            </div>
          )
        }
        <div class="flex-shrink-0">
          <NPopselect
            class="rd-12px px-4px"
            onUpdate:value={props.onModelChange}
            options={props.modelOptions}
            renderLabel={(option: { label: string; desc: string }, selected: boolean) => {
              return (
                <div class="flex items-center p-12px">
                  <div class="mr-16px">
                    <div class="mb-2px FO-Font-B14">{ option.label } </div>
                    <div class="FO-Font-R12">{ option.desc }</div>
                  </div>
                  {
                    selected && (
                      <NIcon>
                        <FillCheck />
                      </NIcon>
                    )
                  }
                </div>
              );
            }}
            showCheckmark={false}
            value={props.currentModel}
          >
            <NButton
              class="c-FO-Content-Text1 FO-Font-B14"
              renderIcon={() => (
                <NIcon>
                  <ArrowLR />
                </NIcon>
              )}
            >
              { props.currentModel }
            </NButton>
          </NPopselect>
        </div>
      </div>
    );
  },
});

export {
  ChatHeader,
};
