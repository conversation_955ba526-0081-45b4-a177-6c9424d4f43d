<template>
  <BasicModal
    v-bind="$attrs"
    showFooter
    :title="getTitle"
    width="580px"
    :maskClosable="false"
    @register="registerModal"
    @ok="handleSubmit"
  >
    <BasicForm @register="registerForm">
      <template #ruleType="{ model, field }">
        <a-select
          v-model:value="model[field]"
          :options="ruleTypes"
          placeholder="请选择规则类型"
          :fieldNames="ruleTypeFieldNames"
          showSearch
          optionFilterProp="name"
        >
          <template #option="{ name, identifier }">
            <span>{{ name }}</span>
            <span v-if="identifier" class="ml-1 text-12px c-FO-Content-Text2">{{ identifier }}</span>
          </template>
        </a-select>
      </template>
      <template #filters="{ model, field }">
        <a-button size="small" class="mt-1" @click="handlePathOverview">
          适用路径总览
        </a-button>
        <P4StateTree
          ref="p4TreeRef"
          :permissionList="model[field]"
          class="mt-1 max-h-360px overflow-auto"
          onlyDir
          :customCheckList="checkList"
        />
      </template>
    </BasicForm>
    <PathOverviewModal @register="registerTypeModal" />
  </BasicModal>
</template>

<script lang="ts" setup>
import { computed, ref, unref } from 'vue';
import { useRouter } from 'vue-router';
import { checkList, formSchema, ruleTypeFieldNames } from './rules.data';
import type { P4PermissionListItem } from '/@/api/page/model/p4Model';
import type { CheckPathsListItem, CheckRuleTypesListItem } from '/@/api/page/model/resourceCheckModel';
import { addCheckRule, editCheckRule } from '/@/api/page/resourceCheck';
import { BasicForm, useForm } from '/@/components/Form';
import { BasicModal, useModal, useModalInner } from '/@/components/Modal';
import { useUserStoreWithOut } from '/@/store/modules/user';
import P4StateTree from '/@/views/versionControl/p4PermissionManage/p4StateTree/index.vue';
import PathOverviewModal from '/@/views/versionControl/p4PermissionManage/submitPermission/PathOverviewModal.vue';

defineOptions({
  name: 'ResourceCheckRulesModal',
});

const emit = defineEmits(['success', 'register']);

const userStore = useUserStoreWithOut();
const { currentRoute } = useRouter();
const streamID = Number(unref(currentRoute).params.stream_id);
const p4TreeRef = ref<typeof P4StateTree>();
const enable = ref<boolean>(false);
const isUpdate = ref(true);
const editId = ref();
const rootPath = ref<string>('');

const ruleTypes = ref<CheckRuleTypesListItem[]>([]);
const [registerTypeModal, { openModal }] = useModal();

const [registerForm, { resetFields, setFieldsValue, validate }] = useForm({
  labelWidth: 100,
  schemas: formSchema,
  showActionButtonGroup: false,
  baseColProps: { span: 24 },
});

const [registerModal, { setModalProps, closeModal }] = useModalInner(async (data) => {
  await resetFields();
  isUpdate.value = !!data?.isUpdate;
  editId.value = data?.record?.ID;
  ruleTypes.value = data?.ruleTypes;
  rootPath.value = data?.rootPath || '';
  if (unref(isUpdate)) {
    const filters: P4PermissionListItem[] = [];
    data.record.filters?.forEach((item: CheckPathsListItem) => {
      if (item.excludePath?.length) {
        item.excludePath.forEach((path) => {
          filters.push({
            path: rootPath.value + path,
            permit: 2,
          });
        });
      }
      filters.push({
        path: rootPath.value + item.path,
        permit: 1,
      });
    });
    await setFieldsValue({
      ...data.record,
      filters,
    });
    enable.value = !!data.record.enable;
  }
  setModalProps({ confirmLoading: false });
});

const getTitle = computed(() => (!unref(isUpdate) ? '新增规则' : '编辑规则'));

function handlePathOverview() {
  openModal(true, {
    treeData: unref(p4TreeRef)?.treeData,
    title: '适用路径总览',
    emptyText: '暂无适用路径',
  });
}

async function handleSubmit() {
  try {
    const values = await validate();
    const paths = p4TreeRef.value?.getOptimalList();
    const filters: CheckPathsListItem[] = [];

    paths?.forEach((item) => {
      if (item.permit === 1) {
        filters.push({
          path: item.path.replace(rootPath.value, ''),
          excludePath: [],
        });
      } else if (item.path !== rootPath.value) {
        const parentPath = `${item.path.split('/').slice(0, -2).join('/')}/`;
        const find = filters.find((e) => e.path === parentPath.replace(rootPath.value, ''));
        if (!find) {
          filters.push({
            path: parentPath.replace(rootPath.value, ''),
            excludePath: [item.path.replace(rootPath.value, '')],
          });
        } else {
          find.excludePath!.push(item.path.replace(rootPath.value, ''));
        }
      }
    });

    const submitData = Object.assign({}, values, {
      streamID,
      filters,
      enable: enable.value,
    });

    setModalProps({ confirmLoading: true });
    if (!unref(isUpdate)) {
      await addCheckRule(userStore.getProjectId, submitData);
      emit('success', 'add');
    } else if (unref(editId)) {
      await editCheckRule(userStore.getProjectId, submitData, unref(editId));
      emit('success', 'edit');
    }
    closeModal();
  } finally {
    setModalProps({ confirmLoading: false });
  }
}
</script>
