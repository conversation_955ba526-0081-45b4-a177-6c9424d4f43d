import type { VersionNewsCreateFormModel, VersionNewsTaskItem } from '@/models/version';
import type { ModalReactive } from 'naive-ui';
import { ControlPointFilled, KeyboardDoubleArrowUpFilled } from '@/common/components/svg-icons';
import { NAlert, NButton, NEmpty, NForm, NFormItemGi, NGrid, NH1, NIcon, NInput, NLayoutSider, NPopselect, NSelect, NTab, NTabs, NVirtualList, useModal, useThemeVars } from 'naive-ui';
import { defineComponent, Transition } from 'vue';

import styles from './index.module.less';
import { VersionTaskCard } from './components/task-card';
import { useVersionNewsHook } from './hook';
import { VersionNewsCreateModal } from './components/create-modal';
import { shock } from 'random-jpn-emoji';
import { useRouter } from 'vue-router';
import { traceClickEvent } from '@/plugins/track';
import { TrackEventName } from '@/plugins/event';
import { PlatformEnterPoint } from '@hg-tech/oasis-common';

const VersionNewsForm = defineComponent({
  setup() {
    const themeVars = useThemeVars();
    const {
      activeTaskTab,
      currentNamespace,
      currentItem,
      taskList,
      favTaskList,
      virtualListRef,
      virtualListScrollHeight,
      virtualFavListRef,
      virtualFavListScrollHeight,
      namespacesConfig,
      projectConfig,
      promptConfig,
      taskFormModel,
      createTaskLoading,
      subjectsConfig,
      createTask,
      onVirtualListScroll,
      refreshTaskList,
      refreshFavTaskList,
      onHandleFavClick,
      onVirtualFavListScroll,
    } = useVersionNewsHook();
    const modal = useModal();
    const router = useRouter();

    const onHandleCreate = () => {
      let m: ModalReactive;
      const onCancel = () => {
        m.destroy();
      };
      const onConfirm = async (params: VersionNewsCreateFormModel) => {
        traceClickEvent(TrackEventName.AI_VERSION_NEWS_TASK_CREATE_CLICK, {
          ai_tag: Boolean(params.autoLabel),
        });
        const res = await createTask(params);
        if (res) {
          onCancel();
          refreshTaskList();
        }
      };
      m = modal.create({
        title: '新建分析任务',
        preset: 'card',
        class: 'w-60% min-w-600px',
        maskClosable: false,
        autoFocus: false,
        onClose: () => onCancel(),
        content: () => (
          <VersionNewsCreateModal
            loading={createTaskLoading.value}
            namespace={taskFormModel.value.namespace}
            onCancel={onCancel}
            onConfirm={onConfirm}
            projects={projectConfig.value}
            promptConfig={promptConfig.value}
            subjectsConfig={subjectsConfig.value}
          />
        ),
      });
    };

    const onHandleTaskClick = (task: VersionNewsTaskItem) => {
      currentItem.value = task;
      router.replace({
        name: PlatformEnterPoint.VersionNewsSecondSummary,
        params: { taskId: task.taskId },
      });
    };

    return () => (
      <NLayoutSider collapsedWidth={40} contentClass="bg-FO-Container-Fill1 p-20px" show-trigger="arrow-circle" width={500}>
        <div class="version-form-container h-full flex flex-col">
          <NH1 class="mb-[24px] flex-shrink-0" prefix="bar">
            版本要闻
          </NH1>

          <div class="form-header mb-12px flex-shrink-0 gap-12px flex-c-between">
            <div>
              当前所处空间：
              <NPopselect
                options={namespacesConfig.value}
                trigger="click"
                v-model:value={currentNamespace.value}
              >
                <NButton>{taskFormModel.value.namespace || '--'}</NButton>

              </NPopselect>
            </div>

            <NButton onClick={onHandleCreate}>
              <NIcon class="mr-8px" size="18">
                <ControlPointFilled />
              </NIcon>
              新建任务
            </NButton>
          </div>

          <NTabs
            animated
            class="mb-12px w-full"
            type="segment"
            v-model:value={activeTaskTab.value}
          >
            <NTab name="all">全部任务</NTab>
            <NTab name="fav">我的收藏</NTab>
          </NTabs>

          <div class={`${styles.versionTaskPool} flex flex-1 overflow-hidden`}>
            <NAlert class="max-w-full flex flex-1" show-icon={false}>

              <div class="max-w-full flex flex-1 flex-col" v-show={activeTaskTab.value === 'all'}>
                <NForm
                  labelAlign="left"
                  labelWidth={70}
                >
                  <NGrid cols="12" xGap={12}>
                    <NFormItemGi showLabel={false} span={5}>
                      <NSelect
                        clearable
                        onUpdate:value={refreshTaskList}
                        options={projectConfig.value}
                        placeholder="项目选择"
                        v-model:value={taskFormModel.value.project}
                      />
                    </NFormItemGi>
                    <NFormItemGi showLabel={false} span={5}>
                      <NInput clearable placeholder="输入任务名称搜索" v-model:value={taskFormModel.value.name} />
                    </NFormItemGi>
                    <NFormItemGi showLabel={false} span={2}>
                      <NButton onClick={refreshTaskList} type="primary">搜索</NButton>
                    </NFormItemGi>
                  </NGrid>
                </NForm>

                {
                  taskList.value.length > 0
                    ? (
                      <NVirtualList
                        class="flex-1"
                        itemResizable
                        items={taskList.value}
                        itemSize={130}
                        onScroll={onVirtualListScroll}
                        ref={virtualListRef}
                      >
                        {{
                          default: ({ item }: { item: VersionNewsTaskItem }) => (
                            <VersionTaskCard
                              current={currentItem.value?.taskId}
                              onFavClick={onHandleFavClick}
                              onTaskClick={onHandleTaskClick}
                              task={item}
                            />
                          ),
                        }}
                      </NVirtualList>
                    )
                    : (
                      <div class="flex flex-1 flex-col justify-center">
                        <NEmpty description={`暂无任务信息，请先创建分析任务吧 ${shock()}`} />
                      </div>
                    )
                }

                <Transition enterActiveClass="a-fade-in" leaveActiveClass="a-fade-out">
                  {virtualListScrollHeight.value > 500
                    ? (
                      <NButton
                        circle
                        class="absolute bottom-30px left-30px"
                        onClick={() => {
                          virtualListRef.value?.scrollTo({ index: 0, behavior: 'smooth' });
                        }}
                        type="primary"
                      >
                        <NIcon>
                          <KeyboardDoubleArrowUpFilled />
                        </NIcon>
                      </NButton>
                    )
                    : null}
                </Transition>
              </div>
              <div class="max-w-full flex flex-1 flex-col" v-show={activeTaskTab.value === 'fav'}>
                <NButton class="mb-12px" onClick={refreshFavTaskList}>刷新当前列表</NButton>
                {
                  favTaskList.value.length > 0
                    ? (
                      <NVirtualList
                        class="flex-1"
                        itemResizable
                        items={favTaskList.value}
                        itemSize={130}
                        onScroll={onVirtualFavListScroll}
                        ref={virtualFavListRef}
                      >
                        {{
                          default: ({ item }: { item: VersionNewsTaskItem }) => (
                            <VersionTaskCard
                              current={currentItem.value?.taskId}
                              onFavClick={onHandleFavClick}
                              onTaskClick={
                                (task: VersionNewsTaskItem) => {
                                  currentItem.value = task;
                                }
                              }
                              task={item}
                            />
                          ),
                        }}
                      </NVirtualList>

                    )
                    : (
                      <div class="flex flex-1 flex-col justify-center">
                        <NEmpty description={`暂无收藏信息，请先去全部任务里点击收藏吧 ${shock()}`} />
                      </div>
                    )
                }

                <Transition enterActiveClass="a-fade-in" leaveActiveClass="a-fade-out">
                  {virtualFavListScrollHeight.value > 500
                    ? (
                      <NButton
                        circle
                        class="absolute bottom-30px left-30px"
                        color={themeVars.value.railColor}
                        onClick={() => {
                          virtualFavListRef.value?.scrollTo({ index: 0, behavior: 'smooth' });
                        }}
                      >
                        <NIcon>
                          <KeyboardDoubleArrowUpFilled />
                        </NIcon>
                      </NButton>
                    )
                    : null}
                </Transition>
              </div>
            </NAlert>
          </div>
        </div>
      </NLayoutSider>
    );
  },
});

export { VersionNewsForm };
