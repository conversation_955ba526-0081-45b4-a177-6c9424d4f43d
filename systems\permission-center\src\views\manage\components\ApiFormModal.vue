<template>
  <Modal
    :width="496" :open="show" :title="title" :centered="true" :maskClosable="false"
    :bodyStyle="{ padding: '20px 0 0 0', marginBottom: '36px' }" @cancel="() => modalCancel()"
  >
    <Form :labelCol="{ style: { width: '80px' } }">
      <FormItem label="接口名称" v-bind="validateInfos.name">
        <Input
          v-model:value="formValue.name" placeholder="请输入接口名称" :allowClear="true" :showCount="true"
          :maxlength="20"
        />
      </FormItem>
      <FormItem label="接口方法" v-bind="validateInfos.method">
        <Select v-model:value="formValue.method" placeholder="请选择接口方法" :options="methodOptions" :allowClear="true" />
      </FormItem>
      <FormItem label="api路径" v-bind="validateInfos.path">
        <Input v-model:value="formValue.path" placeholder="请输入api路径" :allowClear="true" :maxlength="100" />
      </FormItem>
    </Form>
    <template #footer>
      <div class="flex justify-end gap-4px">
        <Button class="btn-fill-default w-100px" @click="() => modalCancel()">
          取消
        </Button>
        <Button
          v-if="!props.initData" class="btn-fill-primary w-100px" :loading="submitting"
          @click="() => handleConfirm(true)"
        >
          保存并新增
        </Button>
        <Button
          v-if="props.initData" class="btn-fill-primary w-100px" :loading="submitting"
          @click="() => handleConfirm(false)"
        >
          保存
        </Button>
      </div>
    </template>
  </Modal>
</template>

<script setup lang="tsx">
import type { ModalBaseProps } from '@hg-tech/utils-vue';
import type {
  PermissionCheckPointApi,
  PermissionCheckPointApiCreate,
} from '../../../api/manage.ts';
import { Button, Form, FormItem, Input, Modal, Select } from 'ant-design-vue';
import { computed, ref, watch } from 'vue';
import type { RuleObject } from 'ant-design-vue/es/form/interface';
import ApiMethodTag from './ApiMethodTag.vue';

type ApiFormValue = Pick<PermissionCheckPointApi, 'name' | 'method' | 'path'>;

const props = defineProps<ModalBaseProps<{ updatedItem?: PermissionCheckPointApi; createAnother: boolean }> & {
  title?: string;
  initData?: PermissionCheckPointApi;
  sentReq?: (formValue: PermissionCheckPointApiCreate) => Promise<PermissionCheckPointApi | undefined>;
}>();

const formValue = ref<ApiFormValue>({
  name: undefined,
  method: undefined,
  path: undefined,
});
const submitting = ref(false);
const methodOptions = [
  'get',
  'post',
  'put',
  'delete',
  'patch',
].map((i) => ({
  label: () => <ApiMethodTag method={i} />,
  value: i,
}));

const formRule = computed<Record<keyof ApiFormValue, RuleObject[]>>(() => ({
  name: [
    { required: true, message: '请输入接口名称' },
    { max: 20, message: '长度不能超过30个字符' },
  ],
  method: [
    { required: true, message: '请选择接口方法' },
  ],
  path: [
    { required: true, message: '请输入api路径' },
    { max: 100, message: '长度不能超过100个字符' },
    // eslint-disable-next-line regexp/no-super-linear-backtracking
    { pattern: /^\/(?:[\w\-.*]|:\w+|\{\w+\})*(?:\/(?:[\w\-.]+|:\w+|\{\w+\}|\*))*\/?$/, message: '请输入正确的路径' },
  ],
}));

const { validate, validateInfos, resetFields } = Form.useForm(formValue, formRule);

watch(() => props.initData, (newVal) => {
  if (newVal) {
    formValue.value = {
      name: newVal.name,
      method: newVal.method,
      path: newVal.path,
    };
  } else {
    resetFields();
  }
}, { immediate: true });

watch(() => props.show, (newVal) => {
  if (!newVal) {
    resetFields();
  }
}, { immediate: true });

async function handleConfirm(createAnother: boolean) {
  submitting.value = true;
  try {
    await validate();
    const updatedItem = await props.sentReq?.(formValue.value);
    submitting.value = false;
    return props.modalConfirm({ createAnother, updatedItem });
  } catch (error) {
    console.error('保存失败:', error);
  } finally {
    submitting.value = false;
  }
}
</script>
