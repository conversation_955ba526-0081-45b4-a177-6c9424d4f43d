import { type Ref, ref, watch } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { deferMs } from '@hg-tech/utils';

const visitedRecord: Record<string, number> = {};
const visiteThreshold = 1000; // 同一个地址最短重试时间，单位毫秒

export function useExceptionRedirect(
  redirectLocation: Ref<string | undefined>,
  autoRedirect: Ref<boolean>,
) {
  const refreshing = ref(false); // 用于给用户反馈
  const router = useRouter();
  const route = useRoute();

  watch([redirectLocation, autoRedirect, route], async ([url, enable]) => {
    if (!enable || !url) {
      // 如果没有开启自动重定向，或者没有指定重定向地址，则不进行任何操作
      return;
    }
    if (!(url in visitedRecord) || Date.now() - visitedRecord[url] > visiteThreshold) {
      // 如果当前地址不在访问记录中，或者距离上次访问时间超过阈值，则进行重定向
      visitedRecord[url] = Date.now();
      refresh();
    } else {
      // 如果当前地址在访问记录中，且距离上次访问时间未超过阈值，则不进行重定向
      console.warn(`Redirect to ${url} is throttled to prevent rapid retries.`);
    }
  }, { immediate: true, flush: 'pre' });

  async function refresh() {
    refreshing.value = true;
    try {
      await Promise.all([
        router.replace(redirectLocation.value ?? router.currentRoute.value),
        deferMs(200), // 最少 200ms，给用户一个点击反馈
      ]);
    } finally {
      refreshing.value = false;
    }
  }

  return {
    refresh,
    refreshing,
  };
}
