<template>
  <div :class="prefixCls">
    <div class="mb-4 text-center text-lg font-bold">
      我的工具配置
    </div>
    <div
      :class="`${prefixCls}__list`"
      :style="{ maxHeight: `calc(100vh - 152px - ${headerHeightRef}px)` }"
    >
      <div v-for="branch in branchList" :key="branch.ID" :class="`${prefixCls}__list-item`">
        <img :src="branch.icon" alt="" class="ml-2px inline-block w-64px rounded-lg">
        <div class="mx-3 w-0 flex-1">
          <div>
            <span class="c-FO-Content-Text1 font-bold">{{ branch.name }}</span>
            <span class="c-FO-Content-Text2 ml-2 text-xs">ID: {{ branch.ID }}</span>
          </div>
          <div class="my-1 flex flex-wrap items-center">
            <div v-for="pID in branch.platforms" :key="pID" :class="`${prefixCls}__list-item-tag`">
              {{ getPlatformIconByVal(pID)!.label }}
            </div>
            <div :class="`${prefixCls}__list-item-tag`">
              {{ branch?.project?.name }}
            </div>
          </div>
          <div v-if="branch.adminIds?.length" class="my-1 flex flex-wrap items-center gap-1">
            <div>工具管理者： </div>
            <div v-for="(id, index) in branch.adminIds" :key="id">
              {{ getNickNameByFieldName(id, 'ID') }}
              <span v-if="branch.adminIds.length - 1 !== index">、</span>
            </div>
          </div>
        </div>
        <div class="flex flex-wrap items-center">
          <a-button
            size="small"
            shape="round"
            preIcon="icon-park-outline:editor"
            :class="`${prefixCls}__list-item-btn`"
            @click="handleEdit(branch)"
          >
            <span class="!ml-1">编辑工具</span>
          </a-button>
          <a-button
            size="small"
            shape="round"
            preIcon="icon-park-outline:add-one"
            :class="`${prefixCls}__list-item-btn`"
            @click="addVersion(branch)"
          >
            <span class="!ml-1">添加版本</span>
          </a-button>
          <APopconfirm title="确定要删除吗" @confirm="handleDelete(branch)">
            <a-button
              size="small"
              shape="round"
              :class="`${prefixCls}__list-item-btn`"
              preIcon="icon-park-outline:delete"
            >
              <span class="!ml-1">删除</span>
            </a-button>
          </APopconfirm>
        </div>
      </div>
    </div>
    <div :class="`${prefixCls}__add`" @click="handleCreate()">
      <Icon icon="ant-design:plus-outlined" :size="30" />
    </div>
    <ToolkitSettingsDrawer @register="registerDrawer" @success="handleSuccess" />
    <VersionDrawer @register="registerVersionDrawer" />
  </div>
</template>

<script lang="ts" setup>
import { Popconfirm as APopconfirm, message } from 'ant-design-vue';
import { cloneDeep } from 'lodash-es';
import { onBeforeMount, ref } from 'vue';
import ToolkitSettingsDrawer from './ToolkitSettingsDrawer.vue';
import VersionDrawer from './VersionDrawer.vue';
import type {
  CommonTypeListItem,
  ProjectListItem,
  ToolkitListItem,
} from '/@/api/page/model/systemModel';
import {
  deleteToolkit,
  getProjectListByPage,
  getToolkitByID,
  getToolkitListByPage,
  getToolkitTypeListByPage,
} from '/@/api/page/system';
import { useDrawer } from '/@/components/Drawer';
import Icon from '/@/components/Icon';
import { useUserList } from '/@/hooks/system/useUserList';
import { useDesign } from '/@/hooks/web/useDesign';
import { useLayoutHeight } from '/@/layouts/default/content/useContentViewHeight';
import { platformOptions } from '/@/views/toolkit/settings/toolkitSettings.data';

const clonePlatformOptions = cloneDeep(platformOptions);
const { getNickNameByFieldName, getUserList } = useUserList();

const { prefixCls } = useDesign('toolkit-package-branches');
const [registerDrawer, { openDrawer }] = useDrawer();
const [registerVersionDrawer, { openDrawer: openVersionDrawer }] = useDrawer();
const { headerHeightRef } = useLayoutHeight();

const branchList = ref<ToolkitListItem[]>([]);
const toolkitTypeList = ref<CommonTypeListItem[]>([]);
const projectList = ref<ProjectListItem[]>([]);
const platforms = ref<number[]>([]);
async function getList() {
  const { list } = await getToolkitListByPage({
    maintain: true,
    page: 1,
    pageSize: 999,
  });
  branchList.value = list || [];
}

// 获取平台icon
function getPlatformIconByVal(val: number) {
  return clonePlatformOptions.find((e) => e.value === val);
}

onBeforeMount(async () => {
  getToolkitTypeList();
  getProjectList();
  getUserList();

  await getList();
});
// 获取工具类型类别
async function getToolkitTypeList() {
  const { list } = await getToolkitTypeListByPage({ page: 1, pageSize: 999 });
  toolkitTypeList.value = list;
}

// 获取简要项目列表
async function getProjectList() {
  const { list } = await getProjectListByPage({ page: 1, pageSize: 999 });
  projectList.value = list;
}

async function getToolkitDetail(toolId: number) {
  const { retool } = await getToolkitByID(toolId);
  platforms.value = retool.platforms || [];
}

function handleCreate() {
  openDrawer(true, {
    isUpdate: false,
    toolkitTypeList: toolkitTypeList.value,
    projectList: projectList.value,
  });
}
async function addVersion(record: Recordable) {
  await getToolkitDetail(record.ID);
  openVersionDrawer(true, {
    isUpdate: false,
    toolId: record.ID,
    platforms: platforms.value,
    lastVersion: record.latestVersion,
  });
}
function handleEdit(record: Recordable) {
  openDrawer(true, {
    record,
    isUpdate: true,
    toolkitTypeList: toolkitTypeList.value,
    projectList: projectList.value,
  });
}
async function handleDelete(record: Recordable) {
  await deleteToolkit(record.ID);
  handleSuccess();
  message.success('删除成功');
}
async function handleSuccess() {
  await getList();
}
</script>

<style lang="less">
@prefix-cls: ~'hypergryph-toolkit-package-branches';
.@{prefix-cls} {
  position: relative;
  padding: 16px;
  border-radius: 8px;
  background-color: @FO-Container-Fill1;

  &__add {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 8px;
    transition: all 0.3s ease-in-out;
    border-radius: 8px;
    background-color: @member-card-background;
    cursor: pointer;
    user-select: none;

    &:hover {
      filter: brightness(0.9);
    }
  }

  &__list {
    overflow: auto;

    &-item {
      display: flex;
      align-items: center;
      margin-bottom: 16px;
      padding: 8px;
      transition: all 0.3s ease-in-out;
      border-radius: 8px;
      background-color: @member-card-background;
      cursor: pointer;
      user-select: none;

      &:hover {
        filter: brightness(0.9);
      }

      &-tag {
        margin-right: 4px;
        padding: 0 2px;
        transform: scale(0.9);
        transform-origin: left center;
        border: 1px solid @FO-Content-Text1;
        border-radius: 6px;
        font-size: 12px;
      }

      &-btn {
        margin-right: 8px;
        border-color: @FO-Container-Fill6 !important;
        background-color: @FO-Container-Fill6 !important;
        color: @FO-Content-Components1 !important;

        &:hover {
          border-color: #616161 !important;
          background-color: #616161 !important;
        }
      }
    }
  }
}
</style>
ProjectListItem, ToolkitListItem,
