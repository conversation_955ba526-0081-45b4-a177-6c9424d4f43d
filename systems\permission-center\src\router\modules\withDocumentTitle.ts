import type { Router } from 'vue-router';
import { getMicroAppData, usePlatformConfigCtx } from '@hg-tech/oasis-common';

export function withDocumentTitle(router: Router) {
  if (window.__MICRO_APP_ENVIRONMENT__) {
    const configCtx = getMicroAppData(usePlatformConfigCtx);
    router.afterEach(async (to) => {
      const customTitle = to.meta.title as string;
      configCtx?.setDocumentTitle?.(customTitle);
    });
  } else {
    // TODO 如果需要独立启动则需要单独实现
  }
}
