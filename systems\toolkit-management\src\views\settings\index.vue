<template>
  <div class="toolkit-package-settings">
    <div class="toolkit-package-settings__left">
      <div class="toolkit-package-settings__left-back-btn" @click="goBack()">
        <Icon icon="ph:caret-left-bold" :size="16" />
        返回工具商店
      </div>

      <div class="toolkit-package-settings__left-tabs">
        <div
          v-for="tab in tabList"
          :key="tab.name"
          class="toolkit-package-settings__left-tabs-item"
          :active="curTab === tab.name"
          @click="curTab = tab.name"
        >
          <Icon :icon="tab.icon" class="mr-1" />
          {{ tab.name }}
        </div>
      </div>
    </div>
    <div class="toolkit-package-settings__right">
      <component :is="currentComponent" />
    </div>
  </div>
</template>

<script lang="ts" setup>
import { computed, ref } from 'vue';
import { useRouter } from 'vue-router';
import { Icon } from '@iconify/vue';
import { PlatformEnterPoint, useMicroAppInject, useUserProfileInfoCtx } from '@hg-tech/oasis-common';
import BranchSetting from './branch/index.vue';

import ClassificationSettingSetting from './classificationSetting.vue';
import VersionManagementSetting from './VersionManagement.vue';

const router = useRouter();
const { data } = useMicroAppInject(useUserProfileInfoCtx);
const tabList = computed(() => (!data.value?.isSuperAdmin
  ? [
    {
      name: '工具配置',
      component: BranchSetting,
      icon: 'icon-park-outline:tool',
    },
    {
      name: '版本管理',
      component: VersionManagementSetting,
      icon: 'icon-park-outline:tag-one',
    },
    {
      name: '分类配置',
      component: ClassificationSettingSetting,
      icon: 'tabler:layout-grid',
    },
  ]
  : [
    {
      name: '工具配置',
      component: BranchSetting,
      icon: 'icon-park-outline:tool',
    },
    {
      name: '版本管理',
      component: VersionManagementSetting,
      icon: 'icon-park-outline:tag-one',
    },
  ]));

const curTab = ref<string>(tabList.value[0].name);
const currentComponent = computed(() => tabList.value.find((tab) => tab.name === curTab.value)!.component);

function goBack() {
  router.push({ name: PlatformEnterPoint.Toolkit, query: { cardList: router.currentRoute.value.query.cardList } });
}
</script>

<style lang="less" scoped>
@import (reference) '@hg-tech/forgeon-style/vars.less';

.toolkit-package-settings {
  display: flex;
  position: relative;
  overflow: auto;

  &__left {
    display: flex;
    position: sticky;
    top: 0;
    flex-direction: column;
    width: 180px;
    margin-left: 16px;
    padding: 16px 16px 16px 0;

    &-back-btn {
      margin-bottom: 16px;
      padding: 8px;
      border-radius: 8px;
      background-color: @FO-Container-Fill1;
      font-size: 16px;
      font-weight: bold;
      cursor: pointer;
      display: flex;
      align-items: center;
    }

    &-tabs {
      &-item {
        display: flex;
        align-items: center;
        padding: 12px;
        border-radius: 16px;
        font-weight: bold;
        cursor: pointer;
        user-select: none;

        &[active='true'],
        &:hover {
          background-color: @FO-Container-Fill1;
        }

        &:not(:last-child) {
          margin-bottom: 16px;
        }
      }
    }
  }

  &__right {
    flex: 1;
    width: 0;
    margin-right: 16px;
    padding: 16px 0;
  }
}
</style>
