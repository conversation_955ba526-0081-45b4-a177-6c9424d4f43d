<template>
  <PageWrapper contentBackground :class="prefixCls">
    <div class="m-5">
      <div class="flex items-center justify-between">
        <ATypographyTitle :level="5">
          <div class="flex items-center">
            <router-link class="flex px-2" :to="{ name: PlatformEnterPoint.Oasis }">
              <Icon icon="icon-park-outline:left" :size="24" />
            </router-link>
            <span>项目飞书群聊</span>
          </div>
        </ATypographyTitle>
        <div v-if="isSuperAdminOrProjectAdmin">
          <span>编辑模式</span>
          <ASwitch v-model:checked="showManage" class="!ml-3" />
        </div>
      </div>

      <OasisList :list="cardList" :showManage="showManage" @success="handleSuccess" @add="handleAdd" @edit="handleEdit" @sortList="handleSortList" @delete="handleDelete" />
    </div>
    <RemarkModalHolder />
  </PageWrapper>
</template>

<script lang="ts" setup>
import type { NavigationsListItem } from '/@/api/page/model/navigtionModel';
import { ref, watch } from 'vue';
import NavigationModal from '../navigation/NavigationModal.vue';
import Icon from '/@/components/Icon/index';
import { PageWrapper } from '/@/components/Page';
import { useDesign } from '/@/hooks/web/useDesign';
import { useUserStoreWithOut } from '/@/store/modules/user';
import { PlatformEnterPoint } from '@hg-tech/oasis-common';
import { useAdmin } from '../../../hooks/useProjects.ts';
import OasisList from '../navigation/OasisList.vue';
import { Switch as ASwitch, TypographyTitle as ATypographyTitle } from 'ant-design-vue';
import { useModalShow } from '@hg-tech/utils-vue';
import { addFeishuGroupChatsList, deleteFeishuGroupChatsList, editFeishuGroupChatsList, getFeishuGroupChatsListByPage, sortFeishuGroupChatsList } from '/@/api/page/feishuGroupChat.ts';

const { prefixCls } = useDesign('resource-check-switches');
const cardList = ref<NavigationsListItem[]>([]);
const userStore = useUserStoreWithOut();
const { isSuperAdminOrProjectAdmin } = useAdmin();
const [RemarkModalHolder, showRemarkModal] = useModalShow(NavigationModal);
const showManage = ref<boolean>(false);

async function getTagList() {
  const { list } = await getFeishuGroupChatsListByPage(userStore.getProjectId!, {
    page: 1,
    pageSize: 999,
  });

  if (list.length > 0) {
    cardList.value = list.sort((a, b) => (a.sort || 0) - (b.sort || 0));
  } else {
    cardList.value = [];
  }
}
getTagList();

async function handleAdd() {
  await showRemarkModal({
    title: '添加项目飞书群聊',
    type: 'groupChat',
    record: {},
    sentReq: async (formValue) => {
      const res = await addFeishuGroupChatsList(userStore.getProjectId!, { ...formValue, sort: cardList.value.length + 1 });
      return res;
    },
  });
  await handleSuccess();
}

async function handleDelete(id: number) {
  await deleteFeishuGroupChatsList(userStore.getProjectId!, id);
  await handleSuccess();
}

async function handleEdit(record: Recordable) {
  await showRemarkModal({
    record,
    type: 'groupChat',
    title: '编辑项目飞书群聊',
    sentReq: async (formValue) => {
      const res = await editFeishuGroupChatsList(userStore.getProjectId!, { ...formValue, sort: cardList.value.length + 1 }, record.ID!);
      return res;
    },
  });
  await handleSuccess();
}

async function handleSuccess() {
  await getTagList();
}

async function handleSortList(list: number[]) {
  await sortFeishuGroupChatsList(userStore.getProjectId!, list);
  await handleSuccess();
}

watch(
  () => userStore.getProjectId,
  (v, oldValue) => {
    if (v && v !== oldValue) {
      handleSuccess();
    }
  },
);
</script>

<style lang="less">
  @prefix-cls: ~'hypergryph-resource-check-switches';
.@{prefix-cls} {
  &__function-title {
    position: relative;
    width: 90%;
    margin: 15px auto 0;

    &::after {
      content: '';
      position: absolute;
      bottom: -6px;
      left: 16px;
      width: 50px;
      height: 4px;
      transform: translateX(-50%);
      border-radius: 1px;
      background-color: @FO-Brand-Primary-Default;
      filter: brightness(1.2);
    }
  }
}
</style>
