<template>
  <StatusButton
    :enabled="Boolean(extraInfo?.approveChatID)"
    :loading="loadingExtraInfo"
    @click="handleShowReviewGroupModal"
  >
    Review通知总群
  </StatusButton>
  <StatusButton
    :enabled="Boolean(extraInfo?.chatID)"
    :loading="loadingExtraInfo"
    @click="handleShowAuditGroupModal"
  >
    审批通知总群
  </StatusButton>
  <Modal
    width="700px"
    okText="保存"
    :open="showModalType != null"
    destroyOnClose
    :maskClosable="false"
    :confirmLoading="updatingExtraInfo"
    @ok="handleSubmit"
    @cancel="showModalType = undefined"
  >
    <template #title>
      <div class="w-full flex justify-center">
        配置{{ showModalType }}通知总群
      </div>
    </template>
    <div class="mx-auto w-560px text-lg">
      <div class="mt-4">
        配置{{ showModalType }}通知总群后，该分支所有<b>git</b>审查组触发的merge
        request在approve后都会将审查通知额外再发送至群聊
      </div>
      <div class="mb-6">
        若不配置群聊则只将审查通知发送至<b>{{ showModalType }}通知人</b>
      </div>
      <div>
        <b>{{ showModalType }}通知总群</b>
        <span class="ml-1 !text-xs">(需先将<b class="mx-[4px]">gitlab审查</b>应用添加至群中，不填写则只发送私聊至{{ showModalType }}通知人)</span>
      </div>
      <div class="flex items-center">
        <Select
          v-model:value="selectedChatId"
          class="mr-[12px] flex-auto"
          :placeholder="`请选择${showModalType}通知总群`"
          :options="chatGroupList"
          :fieldNames="{ label: 'name', value: 'chat_id' }"
          :loading="loadingChatGroupList"
        />
        <BasicButton
          size="small"
          shape="round"
          type="primary"
          danger
          @click="() => selectedChatId = undefined"
        >
          清空群聊
        </BasicButton>
      </div>
    </div>
  </Modal>
</template>

<script lang="tsx" setup>
import { Modal, Select } from 'ant-design-vue';
import { updateBranchExtras } from '../../../../../api/page/gitlab';
import { useUserStore } from '../../../../../store/modules/user';
import { BasicButton } from '../../../../../components/Button';
import { getBranchExtras } from '../../../../../api/page/gitlab.ts';
import type { BranchesBaseItemListItem } from '../../../../../api/page/model/gitlabModel.ts';
import { useLatestPromise } from '@hg-tech/utils-vue';
import { ref, watch } from 'vue';
import { match } from 'ts-pattern';
import StatusButton from './StatusButton.vue';
import { useChatGroupList } from '../../../../../hooks/useChatGroupList.ts';

const props = defineProps<{
  repoID: number;
  branchInfo: BranchesBaseItemListItem;
}>();

const userStore = useUserStore();
const showModalType = ref<'Review' | '审批'>();
const selectedChatId = ref<string>();

const { chatGroupList, loadingChatGroupList } = useChatGroupList('gitlab');

const { data: extraInfo, execute: fetchExtraInfo, loading: loadingExtraInfo } = useLatestPromise(() => getBranchExtras(userStore.getProjectId, {
  page: 1,
  pageSize: 1,
  repoID: props.repoID,
  branchID: props.branchInfo.ID,
}).then((res) => res.list?.[0]));
watch([() => props.repoID, () => props.branchInfo.ID], () => {
  if (props.repoID && props.branchInfo.ID) {
    fetchExtraInfo();
  }
}, { immediate: true });
const { execute: updateExtraInfo, loading: updatingExtraInfo } = useLatestPromise(updateBranchExtras);

async function handleShowReviewGroupModal() {
  showModalType.value = 'Review';
  selectedChatId.value = extraInfo.value?.approveChatID || undefined;
}
async function handleShowAuditGroupModal() {
  showModalType.value = '审批';
  selectedChatId.value = extraInfo.value?.chatID || undefined;
}

async function handleSubmit() {
  if (!showModalType.value) {
    return;
  }
  await updateExtraInfo(userStore.getProjectId, extraInfo?.value?.ID, {
    ...extraInfo.value,
    repoID: props.repoID,
    branchID: props.branchInfo.ID,
    ...match(showModalType.value)
      .with('Review', () => ({ approveChatID: selectedChatId.value }))
      .with('审批', () => ({ chatID: selectedChatId.value }))
      .exhaustive(),
  });
  await fetchExtraInfo();
  showModalType.value = undefined;
}
</script>
