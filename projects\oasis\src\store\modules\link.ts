import { defineStore } from 'pinia';
import { store } from '/@/store';

export interface oasisQueryType {
  type: string;
  project: string;
  version: string;
  branch?: string;
}
interface LinkState {
  // app前缀
  appPrefix: string;
  // 参数列表
  queryList: string[];
  // 是否是oasis
  isOasis: boolean;
  // oasis的参数
  oasisQuery: Nullable<oasisQueryType>;
}

export const useLinkStore = defineStore({
  id: 'app-link',
  state: (): LinkState => ({
    appPrefix: '',
    queryList: [],
    isOasis: false,
    oasisQuery: null,
  }),
  getters: {
    getAppPrefix(state): string {
      return state.appPrefix;
    },
    getQueryList(state): string[] {
      return state.queryList;
    },
    getIsOasis(state): boolean {
      return state.isOasis;
    },
    getOasisQuery(state): Nullable<oasisQueryType> {
      return state.oasisQuery;
    },
  },
  actions: {
    setAppPrefix(val: string) {
      this.appPrefix = val;
    },
    setQueryList(val: string[]) {
      this.queryList = val;
    },
    setIsOasis(val: boolean) {
      this.isOasis = val;
    },
    setOasisQuery(val: Nullable<oasisQueryType>) {
      this.oasisQuery = val;
    },
  },
});

// Need to be used outside the setup
export function useLinkStoreWithOut() {
  return useLinkStore(store);
}
