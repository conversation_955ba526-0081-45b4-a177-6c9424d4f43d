import { describe, expect, it } from 'vitest';
import {
  AUTO_GENERATED_COMMENT,
  formatCssVarName,
  formatPascalCaseVarName,
  formatPascalCaseVarNameWithoutPrefix,
  normalizeFigmaVarName,
  toCssVarDeclares,
} from './helper';

describe('helper utils', () => {
  describe('aUTO_GENERATED_COMMENT', () => {
    it('should have the correct value', () => {
      expect(AUTO_GENERATED_COMMENT).toBe('/* This file is automatically generated. DO NOT EDIT it manually. */');
    });
  });

  describe('normalizeFigmaVarName', () => {
    it('should convert figma variable name to normalized format', () => {
      expect(normalizeFigmaVarName('$fo.$brand.primary-default')).toBe('FO-Brand-Primary-Default');
      expect(normalizeFigmaVarName('$fo.$theme.colors.grey-100')).toBe('FO-Theme-Colors-Grey-100');
      expect(normalizeFigmaVarName('$fo.typography.heading-1')).toBe('FO-Typography-Heading-1');
    });
  });

  describe('formatPascalCaseVarName', () => {
    it('should convert figma variable name to PascalCase', () => {
      expect(formatPascalCaseVarName('$fo.$brand.primary-default')).toBe('FOBrandPrimaryDefault');
      expect(formatPascalCaseVarName('$fo.$theme.colors.grey-100')).toBe('FOThemeColorsGrey100');
      expect(formatPascalCaseVarName('$fo.typography.heading-1')).toBe('FOTypographyHeading1');
    });
  });

  describe('formatPascalCaseVarNameWithoutPrefix', () => {
    it('should convert figma variable name to PascalCase without FO prefix', () => {
      expect(formatPascalCaseVarNameWithoutPrefix('$fo.$brand.primary-default')).toBe('BrandPrimaryDefault');
      expect(formatPascalCaseVarNameWithoutPrefix('$fo.$theme.colors.grey-100')).toBe('ThemeColorsGrey100');
      expect(formatPascalCaseVarNameWithoutPrefix('$fo.typography.heading-1')).toBe('TypographyHeading1');
    });
  });

  describe('formatCssVarName', () => {
    it('should convert figma variable name to CSS variable format', () => {
      expect(formatCssVarName('$fo.$brand.primary-default')).toBe('--FO-Brand-Primary-Default');
      expect(formatCssVarName('$fo.$theme.colors.grey-100')).toBe('--FO-Theme-Colors-Grey-100');
      expect(formatCssVarName('$fo.typography.heading-1')).toBe('--FO-Typography-Heading-1');
    });
  });

  describe('toCssVarDeclares', () => {
    it('should convert variable objects to CSS variable declarations', () => {
      const vars = {
        '$fo.$brand.primary-default': '$fo.$colors.blue-500',
        '$fo.$brand.secondary': '$fo.$colors.green-300',
      };

      const expected = [
        '--FO-Brand-Primary-Default: var(--FO-Colors-Blue-500);',
        '--FO-Brand-Secondary: var(--FO-Colors-Green-300);',
      ];

      expect(toCssVarDeclares(vars)).toEqual(expected);
    });

    it('should handle empty object', () => {
      expect(toCssVarDeclares({})).toEqual([]);
    });
  });
});
