import { Tooltip } from 'ant-design-vue';
import { ForgeonTheme } from '@hg-tech/forgeon-style';
import { type PropType, computed, defineComponent } from 'vue';
import MoonIcon from '../../assets/svg/component/theme-switch-moon.svg?component';
import SunIcon from '../../assets/svg/component/theme-switch-sun.svg?component';
import Icon from '@ant-design/icons-vue';
import type { TooltipPlacement } from 'ant-design-vue/es/tooltip';
import styles from './index.module.less';

const ThemeSwitch = defineComponent({
  props: {
    theme: {
      type: String as PropType<ForgeonTheme>,
    },
    onToggle: {
      type: Function as PropType<(theme: ForgeonTheme) => void>,
      required: true,
    },
    placement: {
      type: String as PropType<TooltipPlacement>,
      default: 'top',
    },
    position: {
      type: String as PropType<'left' | 'right'>,
      default: 'right',
    },
  },
  setup(props) {
    const isDark = computed(() => props.theme === ForgeonTheme.Dark);
    const handleToggle = () => {
      props.onToggle(props.theme === ForgeonTheme.Dark ? ForgeonTheme.Light : ForgeonTheme.Dark);
    };

    function handleToggleClick(event: MouseEvent) {
      const x = event.clientX;
      const y = event.clientY;
      const endRadius = Math.hypot(Math.max(x, innerWidth - x), Math.max(y, innerHeight - y));

      // 兼容性处理
      if (!(document as any).startViewTransition) {
        handleToggle();
        return;
      }
      const transition = (document as any).startViewTransition(() => {
        handleToggle();
      });
      transition.ready.then(() => {
        const clipPath = [`circle(0px at ${x}px ${y}px)`, `circle(${endRadius}px at ${x}px ${y}px)`];
        document.documentElement.animate(
          {
            clipPath: !isDark.value ? clipPath.reverse() : clipPath,
          },
          {
            duration: 600,
            easing: 'ease-in',
            pseudoElement: !isDark.value
              ? '::view-transition-old(root)'
              : '::view-transition-new(root)',
          },
        );
      });
    }

    return () => (
      <div class={styles.themeSwitch}>
        <Tooltip placement={props.placement} title={`切换至${props.theme === ForgeonTheme.Dark ? '亮色' : '暗色'}模式`}>
          <div
            class={[
              'h-40px w-40px font-size-[20px] flex cursor-pointer items-center justify-center rd-full bg-FO-Brand-Primary-Default hover:bg-FO-Brand-Primary-Hover',
              'hover:translate-x-0% transition-all',
              `${props.position === 'left' ? 'translate-x--50%' : 'translate-x-50%'}`,
            ]}
            onClick={handleToggleClick}
          >
            <Icon
              class="theme-switch-icon"
              component={
                props.theme === ForgeonTheme.Dark
                  ? <SunIcon class="c-FO-Content-Text0" />
                  : <MoonIcon class="c-FO-Content-Text0" />
              }
            />
          </div>
        </Tooltip>

      </div>
    );
  },
});

export {
  ThemeSwitch,
};
