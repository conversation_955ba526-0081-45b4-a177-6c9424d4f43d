<template>
  <div class="tag-card-list">
    <ARow v-if="tagList.length" :gutter="[16, 16]">
      <template v-for="item in tagList" :key="item.ID">
        <ACol>
          <AListItem>
            <ACard class="tagCard bg-gray-100">
              <div class="mr-8px w-20px">
                <Icon
                  v-if="props.type === 'cansubmit'"
                  icon="icon-park-outline:pause"
                  size="20"
                  class="can-drag mr-8px mt-30px cursor-move"
                />
              </div>
              <div class="flex flex-1 justify-end">
                <div class="tagCard-content">
                  <div class="tagCard-title">
                    <ATypographyText
                      class="!mb-0 !max-w-200px"
                      :ellipsis="{ tooltip: true }"
                      :content="item.name"
                    />
                    <div>
                      <BasicButton
                        v-if="isSuperAdminOrProjectAdmin"
                        type="text"
                        size="small"
                        @click="handleEdit(item)"
                      >
                        <Icon :icon="EditIcon" />
                      </BasicButton>
                      <BasicButton type="text" size="small" class="px-1 c-FO-Content-Text2 hover:c-FO-Content-Text2" :class="{ '!c-FO-Brand-Primary-Default': item.tagPathItems?.length }" @click="handleAutoCheck(item)">
                        <Icon :icon="LightningIcon" />
                      </BasicButton>
                      <BasicButton danger type="text" size="small" class="px-1" @click="handleDelete(item)">
                        <Icon :icon="DeleteIcon" />
                      </BasicButton>
                      <BasicButton v-if="props.type !== 'cansubmit'" type="text" size="small" class="!px-1 c-FO-Brand-Primary-Default!" @click="handleAdd(item)">
                        <Icon :icon="AddOneIcon" />
                      </BasicButton>
                      <BasicButton v-else type="text" size="small" class="px-1 c-FO-Brand-Primary-Default!" @click="handleReduce(item)">
                        <Icon :icon="ReduceOneIcon" />
                      </BasicButton>
                    </div>
                  </div>
                  <ATypographyParagraph
                    class="tagCard-detail"
                    :ellipsis="{ rows: 2, expandable: false, tooltip: true }"
                    :content="item.description"
                  />
                </div>
              </div>
            </ACard>
          </AListItem>
        </ACol>
      </template>
    </ARow>
    <div v-else class="rounded-md bg-white p-4">
      未配置
    </div>
    <DeleteModalHolder />
    <EditTagModalHolder />
    <ConfigPathRegexModalHolder />
  </div>
</template>

<script setup lang="tsx">
import { Icon } from '/@/components/Icon';
import { Card as ACard, Col as ACol, ListItem as AListItem, Row as ARow, TypographyParagraph as ATypographyParagraph, TypographyText as ATypographyText } from 'ant-design-vue';
import { useAdmin } from '/@/hooks/useProjects';
import { computed, nextTick, watch } from 'vue';
import { useSortable } from '/@/hooks/web/useSortable';
import { isNullOrUnDef } from '/@/utils/is';
import { cloneDeep, omit } from 'lodash';
import DeleteModal from '../../../../../components/DeleteModal.vue';
import { useLatestPromise, useModalShow } from '@hg-tech/utils-vue';
import { getTagUsedStreams } from '/@/api/page/p4';
import { useUserStore } from '/@/store/modules/user';
import { deleteTag, editTag, editTagPathConfig } from '/@/api/page/submitConf';
import EditTagModal from './EditTagModal.vue';
import type { TagListItem } from '/@/api/page/model/submitConfModel';
import ConfigPathRegexModal from '../../../swarmSettings/components/ConfigPathRegexModal.vue';
import { BasicButton } from '/@/components/Button';
import DeleteIcon from '@iconify-icons/icon-park-outline/delete';
import EditIcon from '@iconify-icons/icon-park-outline/edit';
import AddOneIcon from '@iconify-icons/icon-park-outline/add-one';
import ReduceOneIcon from '@iconify-icons/icon-park-outline/reduce-one';
import LightningIcon from '@iconify-icons/icon-park-outline/lightning';
import type { RegexCustomPath } from '/@/api/page/model/p4Model';

const props = withDefaults(defineProps<{
  tagList: TagListItem[];
  type: 'cansubmit' | 'notconfig';
}>(), {
  tagList: () => [],
  type: 'cansubmit',
});
const emit = defineEmits(['handleSort', 'handleAdd', 'handleReduce', 'success']);
const userStore = useUserStore();
const [DeleteModalHolder, showDeleteModal] = useModalShow(DeleteModal);
const [EditTagModalHolder, showEditTagModal] = useModalShow(EditTagModal);
const [ConfigPathRegexModalHolder, showConfigPathRegexModal] = useModalShow(ConfigPathRegexModal);
const { execute: tagUsedStreams } = useLatestPromise(getTagUsedStreams);
const { execute: deleteTagExecute } = useLatestPromise(deleteTag);
const { execute: editTagExecute } = useLatestPromise(editTag);

const tagList = computed(() => props.tagList);
const { isSuperAdminOrProjectAdmin } = useAdmin();
async function handleEdit(item: TagListItem) {
  if (!item.ID) {
    return;
  }
  const res = await tagUsedStreams(userStore.getProjectId!, item.ID);
  await showEditTagModal({
    title: '修改tag',
    subLabel: '修改选择后，可更改对应分支中该tag的启用状态。',
    isUpdate: true,
    formValue: { ...item, streamIDs: res?.streams.map((item) => item.ID!) },
    async sentReq(formValue: TagListItem) {
      const result = await editTagExecute(userStore.getProjectId!, omit(formValue, ['ID', 'CreatedAt', 'UpdatedAt', 'projectID']), item.ID!);
      return result;
    },

  });
  emit('success');
}
async function handleAutoCheck(item: TagListItem) {
  if (!item.ID) {
    return;
  }
  await showConfigPathRegexModal({
    regexList: item.tagPathItems?.map((item) => ({ ...item })) || [],
    async sentReq(regexList: RegexCustomPath[]) {
      const res = await editTagPathConfig(userStore.getProjectId!, item.ID!, { tagPathItems: regexList });
      return res;
    },
  });
  emit('success');
}
async function handleDelete(item: TagListItem) {
  // 获取使用的分支
  if (!item.ID) {
    return;
  }
  const res = await tagUsedStreams(userStore.getProjectId!, item.ID);
  showDeleteModal({
    hasDeleteIcon: false,
    title: '删除tag',
    description: res?.streams.length
      ? (
        <div class="min-h-300px flex flex-col items-center justify-center p-8">
          <div class="mb w-100% flex justify-between text-lg font-bold">
            当前如下DevGuard配置正在使用该tag，确认同步删除?
          </div>
          <div class="FO-Font-B18 flex flex-wrap gap-2 text-lg">
            {res?.streams.map((item, index) => {
              return (<div>{item.description} {index + 1 === res?.streams.length ? '' : ','}</div>);
            })}
          </div>
        </div>
      )
      : (
        <div class="min-h-300px flex flex-col items-center justify-center text-lg font-bold">
          确认删除?
        </div>

      ),
    async onOk() {
      await deleteTagExecute(userStore.getProjectId!, item.ID!);

      emit('success');
    },
  });
}
function initDrag() {
  // tab拖动功能
  nextTick(() => {
    const el = document.querySelectorAll('.tag-card-list .ant-row')?.[0] as HTMLElement;

    const { initSortable } = useSortable(el, {
      handle: `.can-drag`,
      animation: 100,
      disabled: !isSuperAdminOrProjectAdmin.value,
      onEnd: async ({ oldIndex, newIndex }) => {
        if (isNullOrUnDef(oldIndex) || isNullOrUnDef(newIndex) || oldIndex === newIndex) {
          return;
        }
        const newSortIdList = cloneDeep(tagList.value);
        const currRow = newSortIdList.splice(oldIndex as number, 1)[0];
        newSortIdList.splice(newIndex as number, 0, currRow);
        emit('handleSort', newSortIdList);
      },
    });
    initSortable();
  });
}
function handleAdd(item: TagListItem) {
  emit('handleAdd', item);
}
function handleReduce(item: TagListItem) {
  emit('handleReduce', item);
}
watch(
  () => props.tagList,
  (newValue, oldValue) => {
    if (!oldValue.length && newValue.length && props.type === 'cansubmit') {
      initDrag();
    }
  },
);
</script>

<style lang="less">
.tag-card-list {
  width: 100%;
  margin: 0 auto;
}

.tagCard {
  width: 400px;
  height: 100px;
  border-radius: 10px;

  .ant-card-body {
    display: flex;
    align-items: flex-start;
    padding: 10px;
  }

  &-content {
    width: 330px;
    overflow: hidden;
  }

  &-title {
    display: flex;
    margin-bottom: 3px;
    color: @FO-Content-Text1;
    font-size: 16px;
    font-weight: bold;
    justify-content: space-between;
  }

  &-detail {
    margin-top: 5px;
    color: @FO-Content-Text2 !important;
    font-size: 12px !important;
  }
}
</style>
