import type { SearchFeedbackMsgListParams } from '../../apis/feedback.ts';
import type { PagedQuery } from '../../apis/common.ts';
import React, { useCallback, useDeferredValue, useMemo, useRef } from 'react';
import { Button, Empty, Flex, message, Spin } from 'antd';
import { Link } from '@tanstack/react-router';
import AutoSizer from 'react-virtualized-auto-sizer';
import { useDebounceEffect, useLocalStorageState } from 'ahooks';
import HeaderFrame from '../../components/Frame/Header.tsx';
import UserMenu from '../../components/Frame/UserMenu.tsx';
import { useUserInfo } from '../../hooks/useUserInfo.ts';
import { themeColors } from '../../constants/theme.ts';
import ProjectSelector from '../../components/ProjectSelector.tsx';
import { fetchFeedbackMsgList } from '../../apis/feedback.ts';
import { useInfinityLoad } from '../../hooks/useInfinityLoad.ts';
import { useProjectId } from '../../hooks/useProject.ts';
import IconArrowLeft from '../../components/Icons/IconArrowLeft.tsx';
import FilterPanel from './Filters/FilterPanel.tsx';
import FeedbackList from './Content/FeedbackList.tsx';
import { useLarkExportFeedbackMsgs } from './hooks/useLarkExportFeedbackMsgs.tsx';

const defaultFormValue: SearchFeedbackMsgListParams = {
  status: undefined,
  work_item_type: undefined,
  feedback_type_id: undefined,
  assignee_employee_ids: undefined,
  operator_employee_id: undefined,
  dealer_ids: undefined,
  severity: undefined,
  priority: undefined,
  start_time: undefined,
  end_time: undefined,
};

const Entry: React.FC = () => {
  const { userInfo } = useUserInfo();
  const { currentProjectId, loadingProjectId } = useProjectId();

  const [localFilterValue, setFilterValue] = useLocalStorageState<{ [userId_projectId: string]: SearchFeedbackMsgListParams }>('feedback-filters');
  const localFilterKey = userInfo?.userId && currentProjectId ? `${userInfo?.userId}_${currentProjectId}` : undefined;
  const filterValue = useDeferredValue(useMemo(() => localFilterValue?.[localFilterKey!] || defaultFormValue, [localFilterKey, localFilterValue]));
  const handleChangeFilter = useCallback((v: SearchFeedbackMsgListParams) => {
    if (localFilterKey) {
      setFilterValue((s) => ({
        ...s,
        [localFilterKey]: v,
      }));
    }
  }, [localFilterKey, setFilterValue]);

  const { exportFeedbackMsg, exporting } = useLarkExportFeedbackMsgs(filterValue);

  const fetchMsg = useCallback(async (query: PagedQuery) => {
    if (!currentProjectId) {
      return;
    }

    const res = await fetchFeedbackMsgList({
      ...query,
      ...filterValue,
      projectId: currentProjectId,
    }, {});

    return res.data.data ?? undefined;
  }, [filterValue, currentProjectId]);
  const virtualContainerRef = useRef<HTMLDivElement | null>(null);

  const { currentList, updateListItem, pageInfo, loadMore, reloading, reload, end } = useInfinityLoad(fetchMsg, {
    getKey: (item) => item.id?.toString(),
  });

  useDebounceEffect(() => {
    virtualContainerRef.current?.scrollTo(0, 0);
    reload();
  }, [reload], { wait: 100 });

  function handleWip() {
    // TODO
    return message.info('开发中...');
  }

  return (
    <div className="h-full w-full flex flex-col">
      <HeaderFrame
        widgetLeft={(
          <span className="flex items-center">
            <Link to="/" className="mr-[12px] flex items-center">
              <IconArrowLeft size={24} fills={[themeColors.primary]} className="mr-[9px]" />
              <div
                className="select-none text-align-center font-size-[24px] c-FO-Brand-Primary-Default font-bold line-height-[32px]"
              >
                反馈收集平台
              </div>
            </Link>
            { pageInfo.total > 0 && (
              <span className="color-gray font-bold">
                当前筛选下共 {pageInfo.total} 个反馈
              </span>
            )}
          </span>
        )}
        widgetRight={(
          <div className="flex items-center gap-[16px]">
            <ProjectSelector />
            <Link
              disabled={!currentProjectId}
              to="/feedback/$projectId/configs/collections"
              params={{
                projectId: currentProjectId?.toString() ?? '',
              }}
            >
              <Button className="border-rd-full">配置</Button>
            </Link>
            <Button className="border-rd-full" loading={exporting} onClick={exportFeedbackMsg}>导出飞书文档</Button>
            <Button className="border-rd-full" onClick={handleWip}>导出Excel</Button>
            <UserMenu userInfo={userInfo} />
          </div>
        )}
      />
      <div className="h-full w-full flex flex-auto flex-col overflow-auto p-x-[16px] pb-[16px]">
        <FilterPanel loading={loadingProjectId} className="mb-[16px] flex-none" value={filterValue} onChange={handleChangeFilter} />
        <div className="flex-auto overflow-hidden">
          <AutoSizer disableWidth={false}>
            {({ height, width }) => (
              <Flex align="center" justify="center" className="overflow-hidden border-[2px] border-lightGray border-rd-[10px] border-style-solid" style={{ width, height }}>
                <Spin size="large" spinning={loadingProjectId || reloading} delay={200}>
                  {currentList.length > 0
                    ? (
                      <FeedbackList
                        ref={virtualContainerRef}
                        height={height}
                        width={width}
                        currentList={currentList}
                        updateListItem={updateListItem}
                        loadMore={loadMore}
                        itemCount={end ? currentList.length : currentList.length + 1}
                        end={end}
                      />
                    )
                    : <Empty />}
                </Spin>
              </Flex>
            )}
          </AutoSizer>
        </div>
      </div>
    </div>
  );
};

export default Entry;
