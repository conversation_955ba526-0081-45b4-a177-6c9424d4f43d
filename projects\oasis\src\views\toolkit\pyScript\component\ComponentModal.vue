<template>
  <BasicModal
    v-bind="$attrs"
    @register="registerModal"
    :title="getTitle"
    @ok="handleSubmit"
    :maskClosable="false"
    :width="830"
  >
    <template #title>
      <div class="flex items-center text-lg justify-center">
        {{ getTitle }}
        <a-popconfirm title="确认删除该元件吗?" @confirm="handleDelete" v-if="isUpdate">
          <a-button shape="round" danger class="ml-2" size="small"> 删除该元件 </a-button>
        </a-popconfirm>
      </div>
    </template>
    <BasicForm @register="registerForm">
      <template #version="{ model, field }"> v{{ model[field] }}.0 </template>
    </BasicForm>
    <div class="mx-6">
      <a-divider orientation="left" plain class="!my-0">输入</a-divider>
      <EditFormTable
        :tableData="inputData"
        :columns="inputColumns"
        :initAddData="inputAddData"
        @change="handleInputDataChange"
        ref="inputTableRef"
      />
      <a-divider orientation="left" plain class="!my-1">输出</a-divider>
      <EditFormTable
        :tableData="outputData"
        :columns="inputColumns"
        :initAddData="inputAddData"
        @change="handleOutputDataChange"
        ref="outputTableRef"
      />
    </div>
  </BasicModal>
</template>
<script lang="ts" setup name="ComponentModal">
  import { Divider as ADivider, Popconfirm as APopconfirm } from 'ant-design-vue';
  import { cloneDeep, pick, reduce } from 'lodash-es';
  import { computed, ref, unref } from 'vue';
  import { formSchema, inputAddData, inputColumns } from './component.data';
  import { PyScriptComponentInput } from '/@/api/page/model/pyScriptModel';
  import {
    addPyScriptComponent,
    deletePyScriptComponent,
    editPyScriptComponent,
  } from '/@/api/page/pyScript';
  import { BasicForm, useForm } from '/@/components/Form';
  import { BasicModal, useModalInner } from '/@/components/Modal';
  import { EditFormTable } from '/@/components/Table';
  import { formatNickName } from '/@/hooks/system/useUserList';
  import { buildUUID } from '/@/utils/uuid';

  const emit = defineEmits(['success', 'register']);

  const isUpdate = ref(false);
  const editId = ref();
  const inputData = ref<PyScriptComponentInput[]>([]);
  const newInputData = ref<PyScriptComponentInput[]>([]);
  const inputTableRef = ref();
  const outputData = ref<PyScriptComponentInput[]>([]);
  const newOutputData = ref<PyScriptComponentInput[]>([]);
  const outputTableRef = ref();

  const [registerForm, { resetFields, setFieldsValue, validate, updateSchema, scrollToField }] =
    useForm({
      labelWidth: 120,
      schemas: formSchema,
      showActionButtonGroup: false,
      baseColProps: {
        span: 23,
      },
    });

  const [registerModal, { setModalProps, closeModal }] = useModalInner(async (data) => {
    await resetFields();
    isUpdate.value = !!data?.isUpdate;
    editId.value = data?.record?.ID;
    inputData.value = [];
    outputData.value = [];
    newInputData.value = [];
    newOutputData.value = [];

    await updateSchema({
      field: 'nickName',
      ifShow: () => isUpdate.value,
    });

    if (unref(isUpdate)) {
      await setFieldsValue({
        ...data.record,
        nickName: formatNickName(data.record?.author),
      });
      const { input, output } = data?.record?.configJson;

      if (input) {
        for (const key in input) {
          inputData.value.push({
            name: key,
            type: input[key]?.type || '',
            sort: input[key]?.sort || 0,
            editable: true,
            isNew: true,
            key: buildUUID(),
          });
        }
        inputData.value = inputData.value.sort((a, b) => (a.sort || 0) - (b.sort || 0));
      }
      if (output) {
        for (const key in output) {
          outputData.value.push({
            name: key,
            type: output[key]?.type || '',
            sort: output[key]?.sort || 0,
            editable: true,
            isNew: true,
            key: buildUUID(),
          });
        }
        outputData.value = outputData.value.sort((a, b) => (a.sort || 0) - (b.sort || 0));
      }
    }

    newInputData.value = cloneDeep(inputData.value);
    newOutputData.value = cloneDeep(outputData.value);
    setModalProps({ confirmLoading: false });
  });

  const getTitle = computed(() => (!unref(isUpdate) ? '添加' : '编辑') + '元件');

  function changeListToObject(list: PyScriptComponentInput[]) {
    const pickList = cloneDeep(list)?.map((e) => {
      return pick(e, ['name', 'type']);
    });
    return reduce(
      pickList,
      (result, { name, type }, index) => {
        result[name!] = { type, sort: index };
        return result;
      },
      {},
    );
  }

  async function handleSubmit() {
    try {
      const values = await validate();
      setModalProps({ confirmLoading: true });

      // 输入表单校验
      let inputValidate = !!newInputData.value.find((e) => !e.name || !e.type);
      if (inputValidate) {
        inputTableRef.value.handleValidate();
        return;
      }

      // 输出表单校验
      let outputValidate = !!newOutputData.value.find((e) => !e.name || !e.type);
      if (outputValidate) {
        outputTableRef.value.handleValidate();
        return;
      }

      const newData = Object.assign({}, values, {
        configJson: {
          input: changeListToObject(newInputData.value) || {},
          output: changeListToObject(newOutputData.value) || [],
        },
        version: unref(isUpdate) ? values.version + 1 : 1,
      });

      if (!unref(isUpdate)) {
        await addPyScriptComponent(newData);
        emit('success', 'add');
      } else if (unref(editId)) {
        await editPyScriptComponent(newData, unref(editId));
        emit('success', 'edit');
      }
      closeModal();
      await resetFields();
    } catch (error: any) {
      if (error?.errorFields?.length) {
        scrollToField(error?.errorFields[0]?.name[0], {
          behavior: 'smooth',
          block: 'center',
        });
      }
    } finally {
      setModalProps({ confirmLoading: false });
    }
  }

  const handleInputDataChange = (inputData: PyScriptComponentInput[]) => {
    console.log('inputData: ', inputData);
    newInputData.value = inputData;
  };

  const handleOutputDataChange = (outputData: PyScriptComponentInput[]) => {
    newOutputData.value = outputData;
  };

  const handleDelete = async () => {
    await deletePyScriptComponent(editId.value);
    closeModal();
    emit('success', 'delete');
  };
</script>
