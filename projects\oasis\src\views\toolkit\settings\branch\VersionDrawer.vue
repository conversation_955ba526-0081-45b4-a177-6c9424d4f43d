<template>
  <BasicDrawer
    v-bind="$attrs"
    showFooter
    :title="getTitle"
    destroyOnClose
    width="620px"
    @register="registerDrawer"
    @ok="handleSubmit"
  >
    <BasicForm @register="registerForm">
      <template #mdContent="{ model, field }">
        <MarkDown v-model:value="model[field]" placeholder="请输入发版公告" />
      </template>
    </BasicForm>
  </BasicDrawer>
</template>

<script lang="ts" setup>
import { omit } from 'lodash-es';
import { computed, ref } from 'vue';
import { platformOptions } from '../toolkitSettings.data';
import { formSchema, UPDATETYPES } from './version.data';
import type { ToolkitVersionListItem } from '/@/api/page/model/systemModel';
import { addToolkitVersion, editToolkitVersion, getToolkitVersionListByPage, removeFile, unzipFile } from '/@/api/page/system';
import { findFileApi } from '/@/api/sys/upload';
import { BasicDrawer, useDrawerInner } from '/@/components/Drawer';
import { BasicForm, useForm } from '/@/components/Form/index';
import { MarkDown } from '/@/components/Markdown';
import { useMessage } from '/@/hooks/web/useMessage';
import { deferMs } from '@hg-tech/utils';
import { getAllPaginationList } from '/@/hooks/web/usePagination';
import dayjs from 'dayjs';

defineOptions({
  name: 'ToolkitSettingsVersionDrawer',
});
const emit = defineEmits(['success', 'register']);

const isUpdate = ref(true);
const editId = ref();
const toolId = ref();
const recordData = ref<ToolkitVersionListItem>();
const zipFileUrl = ref<string>();
const originHotLink = ref<string>();
const platforms = ref<number[]>([]);
const { createWarningModal } = useMessage();
const fileMd5 = ref<string>('');
const fileName = ref<string>('');
const hotFileFileMd5 = ref<string>('');
const hotFileName = ref<string>('');
const needHotFindFile = ref(true);
const needFindFile = ref(true);
const [registerForm, { resetFields, setFieldsValue, validate, updateSchema }] = useForm({
  labelWidth: 140,
  schemas: formSchema,
  showActionButtonGroup: false,
  baseColProps: { span: 23 },
});

const [registerDrawer, { setDrawerProps, closeDrawer }] = useDrawerInner(async (data) => {
  await resetFields();
  setDrawerProps({ confirmLoading: false });
  needHotFindFile.value = true;
  needFindFile.value = true;
  isUpdate.value = !!data?.isUpdate;
  editId.value = data?.record?.ID;
  recordData.value = data?.record;
  toolId.value = data?.toolId;
  zipFileUrl.value = data?.record?.downloadLink;
  originHotLink.value = data?.record?.hotLink;
  platforms.value = data?.platforms || [];
  const lastVersion = data?.lastVersion;
  const showPlatformOptions = platformOptions.filter((e) =>
    platforms.value.includes(e.value!),
  );

  // 获取所有的版本
  const { list } = await getAllPaginationList((p) => getToolkitVersionListByPage(toolId.value, {

    ...p,
  }));

  const earliest = list.length > 0
    ? list.reduce((min, cur) =>
      (dayjs(cur?.CreatedAt).isBefore(dayjs(min?.CreatedAt)) ? cur : min), list[0])
    : null;

  // 表单平台选择仅可选择工具支持的
  await updateSchema([
    {
      field: 'version',
      itemProps: {
        extra: lastVersion ? `*上一个版本号为：${lastVersion.version}` : undefined,
      },
    },
    {
      field: 'platform',
      componentProps: {
        options: showPlatformOptions,
      },
    },
    {
      field: 'minHotCompatibleVersionID',
      componentProps: {
        options: list.map((item) => {
          return {
            label: item?.version,
            value: item?.ID,
          };
        }),
      },
    },
  ]);
  // 上传文件同步改变抽屉加载状态
  await updateSchema({
    field: 'readmeLink',
    componentProps: {
      onIsLoading: (val: boolean) => {
        setDrawerProps({ confirmLoading: val });
      },
    },
  });
  // 上传文件同步改变抽屉加载状态
  await updateSchema({
    field: 'downloadLink',
    componentProps: {
      onIsLoading: (val?: boolean) => {
        setDrawerProps({ confirmLoading: val });
      },
      onGetFileSize: (size?: number) => {
        setFieldsValue({
          sizeKB: size,
        });
      },
      onGetFileMd5: (md5: string) => {
        fileMd5.value = md5;
      },
      onGetFileName: (name: string) => {
        fileName.value = name;
      },
      // 根据文件后缀自动选择平台
      onChange: (val: string) => {
        const suffix = val.substring(val.lastIndexOf('.') + 1);
        let curPlatform = [1, 2, 3, 4, 5];
        switch (suffix) {
          case 'apk':
            curPlatform = [1];
            break;
          case 'ipa':
            curPlatform = [2];
            break;
          case 'dmg':
          case 'pkg':
            curPlatform = [4, 5];
            break;
          case 'exe':
            curPlatform = [3];
            break;
          default:
            break;
        }
        if (!showPlatformOptions.find((e) => curPlatform.includes(e.value!))) {
          createWarningModal({
            title: '请注意',
            content:
                    '上传文件不在当前工具支持范围，请先修改工具平台范围！（若判断有误，请忽视该警告）',
          });
        } else {
          if (!isUpdate.value && curPlatform.length === 1) {
            setFieldsValue({
              platform: curPlatform[0],
            });
          }
        }
      },
    },
  });
  await updateSchema({
    field: 'hotDownloadLink',
    componentProps: {
      onIsLoading: (val?: boolean) => {
        setDrawerProps({ confirmLoading: val });
      },
      onGetFileMd5: (md5: string) => {
        hotFileFileMd5.value = md5;
      },
      onGetFileName: (name: string) => {
        hotFileName.value = name;
      },
    },
  });
  if (isUpdate.value) {
    await setFieldsValue({
      ...data.record,
      minHotCompatibleVersionID: data.record.minHotCompatibleVersionID ? data.record.minHotCompatibleVersionID : undefined,
    });
  } else {
    await setFieldsValue({
      enableGray: false,
      enableHot: false,
      updateType: UPDATETYPES.OPTIONALUPDATE,
      minHotCompatibleVersionID: earliest ? earliest.ID : undefined,
    });

    // 新增 平台默认选中第一个
    await setFieldsValue({
      platform: showPlatformOptions?.[0]?.value,
    });
  }
});

const getTitle = computed(() => (!isUpdate.value ? '新增版本' : '编辑版本'));

async function findHotFile() {
  const {
    file: { IsFinish },
  } = await findFileApi({
    fileMd5: hotFileFileMd5.value,
    fileName: hotFileName.value,
  });
  needHotFindFile.value = !IsFinish;
  await deferMs(500);
  await handleSubmit();
}
async function findFile() {
  const {
    file: { IsFinish },
  } = await findFileApi({
    fileMd5: fileMd5.value,
    fileName: fileName.value,
  });

  needFindFile.value = !IsFinish;
  await deferMs(500);
  await handleSubmit();
}
async function handleSubmit() {
  try {
    const values = await validate();
    setDrawerProps({ confirmLoading: true });
    if (!values.enableHot) {
      if (isUpdate.value && originHotLink.value) {
        await removeFile({ path: originHotLink.value! });
      }

      values.hotLink = '';
    } else if (
    // 文件变更 或者 热更新链接不存在 或者 有填写解压子目录 则需要解压
      zipFileUrl.value !== values.hotDownloadLink
      || !originHotLink.value
      || values.output
    ) {
      if (recordData.value?.hotDownloadLink !== values.hotDownloadLink && needHotFindFile.value) {
        await findHotFile();
        return;
      }
      if (recordData.value?.downloadLink !== values.downloadLink && needFindFile.value) {
        await findFile();
        return;
      }
      // 热更新链接存在且填了解压子目录则先删除原来解压路径
      if (originHotLink.value && values.output) {
        await removeFile({ path: originHotLink.value! });
      }
      const { path } = await unzipFile({
        path: values.hotDownloadLink,
        output: 'output',
      });
      if (!path) {
        const { createMessage } = useMessage();
        createMessage.error('解压失败！请重试');
        return;
      }
      values.hotLink = path;
    }
    let success = false;
    if (!isUpdate.value) {
      const res = await addToolkitVersion(toolId.value, values);
      if (res?.code !== 7) {
        success = true;
        emit('success');
      }
    } else if (editId.value) {
      const BASE_OMIT_FIELDS = [
        'key',
        'CreatedAt',
        'UpdatedAt',
        'ID',
        'toolID',
        'author',
        'shortURL',
        'minHotCompatibleVersion',
      ];

      const HOT_SPECIFIC_FIELDS = [
        'hotDownloadLink',
        'minHotCompatibleVersionID',
      ];

      const omitFields = values?.enableHot
        ? BASE_OMIT_FIELDS
        : [...BASE_OMIT_FIELDS, ...HOT_SPECIFIC_FIELDS];

      const submitData = {
        ...omit(recordData.value, omitFields),
        ...values,
      };
      const res = await editToolkitVersion(toolId.value, submitData, editId.value);
      if (res?.code !== 7) {
        success = true;
        emit('success', 'edit');
      }
    }
    if (success) {
      closeDrawer();
    }
  } finally {
    setDrawerProps({ confirmLoading: false });
  }
}
</script>
