import { CloseCircleOutline } from '@/common/components/svg-icons';
import { NButton, NIcon, NScrollbar, NText } from 'naive-ui';
import { type PropType, defineComponent, Transition } from 'vue';
import { SearchItem } from './components/search-item';
import type { ChatMessageSearch } from '@/models/chat';

const SearchPanel = defineComponent({
  props: {
    data: {
      type: Array as PropType<ChatMessageSearch[]>,
      required: true,
    },
    visible: {
      type: Boolean as PropType<boolean>,
      default: true,
    },
  },
  emits: ['update:visible'],
  setup(props, { emit }) {
    return () => (
      <Transition
        enterActiveClass="a-move-in-left"
        leaveActiveClass="a-move-out-right"
      >
        {
          props.visible
          && (
            <div class="max-w-400px min-w-260px w-30% flex flex-shrink-0 flex-col b-1px b-FO-Container-Stroke2 rd-12px p-12px">
              <div class="mb-20px flex-c-between">
                <NText class="FO-Font-B18">搜索结果</NText>
                <NButton
                  class="FO-Font-B14"
                  onClick={() => {
                    emit('update:visible', false);
                  }}
                  renderIcon={() => (
                    <NIcon>
                      <CloseCircleOutline />
                    </NIcon>
                  )}
                  text
                />
              </div>
              <div class="flex-grow-1 overflow-hidden">
                <NScrollbar>
                  <div class="w-full">
                    <div class="flex flex-col gap-8px">
                      {props.data.map((item) => (
                        <div class="rd-12px bg-FO-Container-Fill3 p-8px hover:bg-FO-Brand-Tertiary-Active" key={item.url}>
                          <SearchItem info={item} />
                        </div>
                      ))}
                    </div>
                  </div>
                </NScrollbar>
              </div>
            </div>
          )
        }
      </Transition>
    );
  },
});

export {
  SearchPanel,
};
