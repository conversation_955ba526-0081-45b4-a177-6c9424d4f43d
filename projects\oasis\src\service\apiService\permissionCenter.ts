import { createRequestService } from '@hg-tech/oasis-common';
import { handleForbidden, handleUnauthorized } from './helper.ts';
import { useUserStoreWithOut } from '../../store/modules/user.ts';

const userStore = useUserStoreWithOut();
export const PermissionCenterApiService = createRequestService(
  {
    authTokens: [
      {
        accessTokenKey: 'Access-Token',
        getAccessToken: () => userStore.getAccessToken,
      },
      {
        accessTokenKey: 'X-Token',
        getAccessToken: () => userStore.getToken,
        newTokenKey: 'new-token',
        setNewToken: userStore.setToken,
      },
    ],
    onUnauthorized: handleUnauthorized,
    onForbidden: handleForbidden,
  },
  {
    baseURL: import.meta.env.VITE_BASE_API_ORIGIN_PERMISSION_CENTER,
  },
);
