import { computed, ref, watch } from 'vue';
import { v4 as uuidv4 } from 'uuid';
import { useEventStream } from '@/common/hooks/event-stream.hook';
import { createChatSession, deleteSession, getChatModel, getChatSessionDetail, getChatSessionList, updateSessionDetail } from '@/apis/chat.api';
import { useApiRequest } from '@/common/hooks';
import { getItem, setItem, STORAGE_KEY } from '@/common/utils/localstorage';
import { type ChatMessage, type ChatMessageSearch, type ChatSessionItem, ChatMessageStatus } from '@/models/chat';
import { router } from '@/plugins/router';
import { traceClickEvent } from '@/plugins/track';
import { TrackEventName } from '@/plugins/event';
import { useMessage } from 'naive-ui';

interface IChatState {
  conversations?: Record<string, ChatSessionItem>; // 所有对话
  activeConversationId?: string; // 当前活跃的对话ID
}

const ScrollBreakThreshold = 10; // 滚动触发阈值
const ScrollBtnShowThreshold = 300; // 滚动触发阈值

const chatState = ref<IChatState>({});
const currentModel = ref<string>(getItem(STORAGE_KEY.AI_CHAT_CURRENT_MODEL) ?? '');
const isDeepThinkActive = ref(false);
const isOnlineSearchActive = ref(false);
const showDownBtn = ref<boolean>(false);

const page = ref(1);
const pageSize = ref(20);
const isEnd = ref(false);

const {
  data: chatModels,
  mutate: mutateGetChatModel,
} = useApiRequest({
  request: getChatModel,
  errorText: '获取聊天模型失败, 请刷新页面后重试',
});

const {
  data: newSession,
  mutate: mutateCreateSession,
} = useApiRequest({
  request: createChatSession,
  errorText: '开启对话过程中发生错误，请稍后重试',
});

const {
  code: chatHistoriesCode,
  data: chatHistories,
  loading: chatHistoriesLoading,
  error: chatHistoriesError,
  mutate: mutateGetChatHistories,
} = useApiRequest({
  request: getChatSessionList,
  errorText: '获取历史会话列表失败, 请刷新页面后重试',
});

const {
  data: chatDetail,
  code: chatDetailCode,
  mutate: mutateGetChatDetail,
} = useApiRequest({
  request: getChatSessionDetail,
  errorText: '获取历史会话详情失败, 已返回新对话模式',
});

const {
  code: deleteCode,
  mutate: mutatedeleteChat,
} = useApiRequest({
  request: deleteSession,
  errorText: '删除失败',
});

const {
  code: updateCode,
  loading: updateTitleLoading,
  mutate: mutateUpdateChatTitle,
} = useApiRequest({
  request: updateSessionDetail,
  errorText: '提交失败',
});

function useChatHook() {
  const message = useMessage();
  const chatContainerScrollRef = ref<HTMLDivElement | null>(null);
  const chatContainerRef = ref<HTMLDivElement | null>(null);
  const needAutoScroll = ref(true);
  const tempScrollTop = ref(0);
  const { start: sendChat, abort: cancelChat, loading: chatLoading } = useEventStream({
    url: '/v1/deepseek/chat',
  });

  watch(() => chatModels.value, (value) => {
    currentModel.value = getItem(STORAGE_KEY.AI_CHAT_CURRENT_MODEL) || value?.models[0].name || '';
    isDeepThinkActive.value = getItem(STORAGE_KEY.AI_CHAT_DEEP_THINK) === '1';
    isOnlineSearchActive.value = getItem(STORAGE_KEY.AI_CHAT_ONLINE_SEARCH) === '1';
  });
  watch(() => currentModel.value, (value, prv) => {
    if (prv === value) {
      return;
    }
    setItem(STORAGE_KEY.AI_CHAT_CURRENT_MODEL, value);
    isDeepThinkActive.value = false;
    isOnlineSearchActive.value = false;
  });
  watch(() => isDeepThinkActive.value, (value) => {
    setItem(STORAGE_KEY.AI_CHAT_DEEP_THINK, value ? '1' : '0');
  });
  watch(() => isOnlineSearchActive.value, (value) => {
    setItem(STORAGE_KEY.AI_CHAT_ONLINE_SEARCH, value ? '1' : '0');
  });

  const scrollToBottom = () => {
    chatContainerScrollRef.value?.scrollTo({ top: (chatContainerRef.value?.scrollHeight || 0) + 100, behavior: 'smooth' });
  };

  const scrollToItem = (y: number) => {
    chatContainerScrollRef.value?.scrollTo({ top: y - 100, behavior: 'smooth' });
  };

  const getCurrentConversation = () => {
    return chatState.value.conversations?.[chatState.value.activeConversationId!];
  };

  /**
   * 监听滚动事件，判断是否需要自动滚动
   */
  const triggerManualScroll = (e: Event) => {
    const target = e.target as HTMLElement;

    // 滚动到底部时，需要自动滚动
    if (target.scrollTop > target.scrollHeight - target.clientHeight - ScrollBreakThreshold) {
      needAutoScroll.value = true;
    }

    if (tempScrollTop.value > target.scrollTop) {
      needAutoScroll.value = false;
      tempScrollTop.value = 0;
    } else {
      tempScrollTop.value = target.scrollTop;
    }

    if (target.scrollTop > target.scrollHeight - target.clientHeight - ScrollBtnShowThreshold) {
      showDownBtn.value = false;
    } else {
      showDownBtn.value = true;
    }
  };

  const stopChat = () => {
    traceClickEvent(TrackEventName.AI_CHAT_SEND_MESSAGE_CANCEL);
    cancelChat();
    const currentConversation = getCurrentConversation();
    const lastMessage = currentConversation?.messages[currentConversation.messages.length - 1];
    if (lastMessage) {
      lastMessage.state = ChatMessageStatus.Finish;
    }
    chatLoading.value = false;
  };

  const getHistoryChatList = async () => {
    if (isEnd.value) {
      return;
    }
    await mutateGetChatHistories({ page: page.value, pageSize: pageSize.value });
    if (chatHistoriesCode.value !== 0 || chatHistoriesError.value) {
      return;
    }
    if (!chatState.value.conversations) {
      chatState.value.conversations = {};
    }
    chatHistories.value?.sessionList.forEach((session) => {
      chatState.value.conversations![session.sessionId] = {
        sessionId: session.sessionId,
        messages: chatState.value.conversations![session.sessionId]?.messages ?? [],
        title: session.title,
        updateTime: session.updateTime,
      };
    });
    isEnd.value = Boolean(chatHistories.value?.total && chatHistories.value?.total <= page.value * pageSize.value);
    page.value++;
  };

  const handleListScroll = (e: Event) => {
    const target = e.target as HTMLDivElement;
    const { scrollTop, scrollHeight, clientHeight } = target;
    if (
      scrollTop + clientHeight + 30 >= scrollHeight
      && !chatHistoriesLoading.value
      && !isEnd.value
    ) {
      getHistoryChatList();
    }
  };

  const triggerScrollToBottom = () => {
    needAutoScroll.value = true;
    scrollToBottom();
  };

  const getChatDetail = async (disableScroll: boolean = false) => {
    if (!chatState.value.activeConversationId) {
      return;
    }
    await mutateGetChatDetail({ sessionId: chatState.value.activeConversationId });
    if (chatDetailCode.value !== 0) {
      chatState.value.activeConversationId = undefined;
      router.replace({ query: {} });
      return;
    }

    if (chatDetail.value?.messages.length) {
    // 如果有历史对话，且当前对话不存在，则添加到对话列表
      if (chatState.value.conversations && !chatState.value.conversations?.[chatState.value.activeConversationId]) {
        chatState.value.conversations[chatState.value.activeConversationId] = {
          sessionId: chatState.value.activeConversationId,
          title: chatDetail.value.title,
          messages: chatDetail.value.messages,
        };
      } else {
      // 如果有历史对话，且当前对话存在，则合并到对话列表
        chatState.value.conversations![chatState.value.activeConversationId].messages = chatDetail.value.messages;
        chatState.value.conversations![chatState.value.activeConversationId].title = chatDetail.value.title;
      }

      if (!disableScroll) {
        setTimeout(() => {
          scrollToBottom();
        }, 0);
      }
    }
  };

  const sendMessage = async (params: {
    message: string;
    type: 'user' | 'bot';
    isNew: boolean;
    editId?: number;
    messageId?: number | null;
  }) => {
    let messageId: number | null = null;
    if (params.isNew) {
      // 新建对话
      await mutateCreateSession();
      const chatId = newSession.value?.sessionId || uuidv4();
      chatState.value.conversations = {
        [chatId]: {
          sessionId: chatId,
          messages: [],
          title: params.message,
          updateTime: new Date().getTime() / 1000,
        },
        ...chatState.value.conversations,
      };
      chatState.value.activeConversationId = chatId;
    }
    // 编辑对话重发，获取当前id之前bot的id，并清空后续所有聊天内容
    if (params.editId) {
      const currentConversation = getCurrentConversation();
      if (!currentConversation) {
        return;
      }
      const index = currentConversation.messages.findIndex((item) => item.id === params.editId);
      if (index > 1) {
        messageId = currentConversation.messages[index - 1].id;
        currentConversation.messages.splice(index, currentConversation.messages.length - index);
      } else {
        // 如果没有找到对应的消息，则清空所有消息
        currentConversation.messages = [];
        messageId = 0;
      }
    }

    const currentConversation = getCurrentConversation();
    if (!currentConversation) {
      return;
    }

    // 更新对话
    if (params.type === 'user') {
      const newMessage: ChatMessage = {
        id: Number(Math.random().toString(16).slice(2)),
        role: params.type,
        content: params.message,
        parentId: params.isNew ? '-1' : currentConversation.sessionId,
        state: ChatMessageStatus.Finish,
      };
      currentConversation.messages.push(newMessage);
      await sendMessage({
        message: params.message,
        type: 'bot',
        isNew: false,
        messageId: messageId ?? null,
      });
    }

    needAutoScroll.value
    && chatContainerRef.value
    && chatContainerScrollRef.value
      ?.scrollBy({
        top: chatContainerRef.value?.scrollHeight,
        behavior: 'smooth',
      });

    if (params.type === 'bot') {
      const lastMessage = currentConversation.messages[currentConversation.messages.length - 1];

      if (lastMessage.role === 'user') {
        needAutoScroll.value = true;
        traceClickEvent(TrackEventName.AI_CHAT_SEND_MESSAGE, {
          in_depth_think: isDeepThinkActive.value,
          in_online_search: isOnlineSearchActive.value,
          model_use: currentModel.value,
        });
        currentConversation.messages.push({
          id: Number(Math.random().toString(16).slice(2)),
          role: params.type,
          content: '',
          parentId: currentConversation.sessionId,
          state: ChatMessageStatus.Progress,
        });
      }
      if (currentConversation.messages) {
        sendChat({
          data: {
            session_id: currentConversation.sessionId,
            message: params.message,
            deep_think: isDeepThinkActive.value,
            search_enabled: isOnlineSearchActive.value,
            model: currentModel.value,
            message_id: params.messageId ?? null,
          },
          onSearch: (data: ChatMessageSearch[]) => {
            const searchMessage = currentConversation.messages[currentConversation.messages.length - 1];
            if (searchMessage) {
              searchMessage.search = data;
            }
          },
          onMessage: (data) => {
            currentConversation.messages[currentConversation.messages.length - 1].content += data;
            if (needAutoScroll.value) {
              scrollToBottom();
            }
          },
          onFinished: () => {
            currentConversation.messages[currentConversation.messages.length - 1].state = ChatMessageStatus.Finish;
            currentConversation.updateTime = new Date().getTime() / 1000;
            cancelChat();
            getChatDetail(true);
          },
          onError: () => {
            currentConversation.messages[currentConversation.messages.length - 1].state = ChatMessageStatus.Error;
            cancelChat();
          },
        });
      }
    }
  };

  const deleteChat = async (sessionId: string) => {
    await mutatedeleteChat({ sessionId });
    if (deleteCode.value !== 0) {
      return;
    }
    message.success('删除成功');
    if (chatState.value.conversations) {
      delete chatState.value.conversations[sessionId];
    }
    if (chatState.value.activeConversationId === sessionId) {
      chatState.value.activeConversationId = undefined;
      router.replace({ query: {} });
    }
  };

  const updateChatTitle = async (data: { sessionId: string; title: string }) => {
    await mutateUpdateChatTitle(data);
    if (updateCode.value !== 0) {
      return;
    }
    message.success('提交成功');
    if (chatState.value.conversations) {
      chatState.value.conversations[data.sessionId].title = data.title;
    }
  };

  const initChatPage = async () => {
    await Promise.all([mutateGetChatModel(), getHistoryChatList()]);
    await getChatDetail();
  };

  return {
    needAutoScroll,
    currentModel,
    chatState,
    chatLoading,
    chatContainerRef,
    chatContainerScrollRef,
    chatModels: computed(() => chatModels.value?.models),
    isDeepThinkActive,
    isOnlineSearchActive,
    chatDetail,
    showDownBtn,
    chatHistoriesLoading,
    updateTitleLoading,
    isChatListEnd: computed(() => isEnd.value),
    getCurrentConversation,
    sendMessage,
    stopChat,
    triggerManualScroll,
    initChatPage,
    mutateGetChatDetail,
    triggerScrollToBottom,
    handleListScroll,
    deleteChat,
    updateChatTitle,
    scrollToItem,
  };
}

export {
  useChatHook,
};
