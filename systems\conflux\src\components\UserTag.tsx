import { UserOutlined } from '@ant-design/icons-vue';
import { Avatar, message } from 'ant-design-vue';
import { type PropType, computed, defineComponent } from 'vue';

export interface UserTagInfo {
  openId: string;
  name: string;
  nickname: string;
  avatar: string;
}

const UserTag = defineComponent({
  props: {
    user: {
      type: Object as PropType<UserTagInfo>,
      required: true,
    },
    avatarSize: {
      type: Number as PropType<number>,
      default: 16,
    },
  },
  setup(props) {
    const feishuUrl = computed(() =>
      (props.user.openId ? `https://applink.feishu.cn/client/chat/open?openId=${props.user.openId}` : ''),
    );

    const onUserContact = () => {
      if (!props.user.openId) {
        message.warning('当前用户信息暂不完整，无法直接唤起飞书');
        return;
      }
      window.open(feishuUrl.value, '_blank');
    };
    return () => (
      <span class="user-tag inline-flex cursor-pointer items-center gap-4px" onClick={onUserContact}>
        <Avatar class="flex-shrink-0" icon={props.user.avatar ? undefined : <UserOutlined />} size={props.avatarSize} src={props.user.avatar} />
        <span class="user-name">{props.user.name}{props.user.nickname ? `(${props.user.nickname})` : ''}</span>
      </span>
    );
  },
});

export { UserTag };
