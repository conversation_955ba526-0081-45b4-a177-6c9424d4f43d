<template>
  <BasicModal destroyOnClose :width="1200" :title="`${isUpdate ? '编辑' : '新增'}显示预设`" @register="registerModal" @ok="save">
    <div class="max-h-80vh overflow-auto px-4 py-8">
      <BasicForm @register="registerForm">
        <template #template>
          <Grouped v-if="isUpdate" :paramsList="paramsList" :currentColumns="currentColumns" :selectedParams="selectedParams" @changeSelectedParams="changeSelectedParams" />
        </template>
      </BasicForm>
    </div>
  </BasicModal>
</template>

<script lang="ts" setup>
import { type ModalMethods, BasicModal, useModalInner } from '/@/components/Modal';
import { formSchema } from './defaultList.data';
import { BasicForm, useForm } from '/@/components/Form/index';
import Grouped from './Grouped.vue';
import { ref } from 'vue';
import { addPerfTemplates, editPerfTemplates } from '/@/api/page/performance';
import { useUserStoreWithOut } from '/@/store/modules/user';
import type { BasicColumn } from '/@/components/Table';

const emit = defineEmits<{
  register: [methods: ModalMethods, uuid: number];
  success: [value: { template: { selectedParams: string[] };name: string;isDefault: boolean }];
}>();
const userStore = useUserStoreWithOut();
const paramsList = ref();
const currentColumns = ref();
const templateID = ref<number>();
const selectedParams = ref();
const isUpdate = ref();
const [registerForm, { validate, setFieldsValue }] = useForm({
  schemas: formSchema,
  showActionButtonGroup: false,
  baseColProps: { span: 20, push: 2 },
});
const [registerModal, { setModalProps, closeModal }] = useModalInner(async (data) => {
  paramsList.value = data.columns?.filter((col: BasicColumn) => col.dataIndex !== 'tagName' && col.dataIndex !== 'caseName') || [];
  currentColumns.value = data.currentColumns || [];
  templateID.value = data.template?.ID || 0;
  selectedParams.value = data.currentColumns?.map((item: BasicColumn) => item.dataIndex as string) || [];
  isUpdate.value = data.isUpdate;
  if (isUpdate.value) {
    await setFieldsValue({
      name: data.template?.name,
      isDefault: data.template?.isDefault,
    });
  }
});

function changeSelectedParams(list: string[]) {
  selectedParams.value = list || [];
}
async function save() {
  const data = await validate();
  setModalProps({ confirmLoading: true });
  let res;
  if (!isUpdate.value) {
    res = await addPerfTemplates(userStore.getProjectId, {
      name: data.name,
      isDefault: !!data.isDefault,
      template: { selectedParams: selectedParams.value },
    });
  } else {
    if (templateID.value) {
      res = await editPerfTemplates(userStore.getProjectId, templateID.value, {
        name: data.name,
        isDefault: !!data.isDefault,
        template: { selectedParams: selectedParams.value },
      });
    }
  }
  setModalProps({ confirmLoading: false });
  if (res?.code !== 7) {
    setTimeout(() => {
      emit('success', {
        name: data.name,
        isDefault: !!data.isDefault,
        template: { selectedParams: selectedParams.value },
      });
      closeModal();
    }, 500);
  }
}
</script>
