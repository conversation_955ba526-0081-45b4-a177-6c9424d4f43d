<template>
  <Drawer
    :open="show"
    width="900px"
    :closable="false"
    placement="right"
    @afterOpenChange="v => !v && modalDestroy()"
    @close="() => modalConfirm()"
  >
    <Form ref="formRef" :model="formValue" :labelCol="{ span: 7 }" :wrapperCol="{ span: 17 }" :rules="rules">
      <BorderBox label="基本配置">
        <FormItem label="名称" name="name">
          <Input v-model:value="formValue.name" placeholder="请输入" />
        </FormItem>
        <FormItem label="工具类型" name="workTypeID">
          {{ toolkitTypeList }}
          <RadioGroup
            v-model:value="formValue.workTypeID"
            showSearch
            :options="toolkitTypeList"
            placeholder="请选择工具类型"
            buttonStyle="solid"
            optionType="button"
          />
        </FormItem>
        <FormItem label="可管理者" name="adminIds">
          <RadioGroup
            v-model:value="formValue.adminIds"
            showSearch
            :options="toolkitTypeList"
            placeholder="请选择工具类型"
            buttonStyle="solid"
            optionType="button"
          />
        </FormItem>
        <FormItem label="适用项目" name="projectID">
          <Select
            v-model:value="formValue.projectID"
            showSearch
            optionFilterProp="name"
            :options="projectList"
            placeholder="请选择适用项目"
            :fieldNames="commonFieldNames"
            allowClear
          />
        </FormItem>
        <FormItem label="支持平台" name="platforms">
          <CheckboxGroup
            v-model:value="formValue.platforms"

            :options="platformOptions"
          />
        </FormItem>
        <FormItem label="支持平台" name="platforms">
          <CheckboxGroup
            v-model:value="formValue.platforms"
            :options="platformOptions"
          />
        </FormItem>
      </BorderBox>
    </Form>
    <template #footer>
      <div class="flex justify-end">
        <BasicButton @click="close">
          关闭
        </BasicButton>
      </div>
    </template>
  </Drawer>
</template>

<script lang="tsx" setup>
import { Drawer, Form, FormItem, Input, RadioGroup, Select } from 'ant-design-vue';
import type { ModalBaseProps } from '@hg-tech/utils-vue';
import { ref } from 'vue';
import { BasicButton } from '@hg-tech/oasis-common/deprecated';
import BorderBox from '../../components/BorderBox.vue';
import { commonFieldNames, platformOptions } from '../../configs.tsx';

const props = defineProps<ModalBaseProps & {
  isUpdate: boolean;
  toolkitTypeList: any;
  projectList: any;
  record?: any;
}>();
const formRef = ref();
const formValue = ref(props.isUpdate ? props.record : {});
const rules = ref({
  name: [
    { required: true, message: '请输入tag名称' },
  ],
});
function close() {
  return props.modalConfirm();
}
</script>
