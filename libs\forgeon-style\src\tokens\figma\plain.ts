/* This file is automatically generated. Do not edit it manually.  */

/**
 * 基础色值，**请勿直接在业务中使用**
 * 请使用 ForgeonThemeCssVar 获取 css 变量值，或通过 getForgeonColor 获取对应色值
 */
export const ColorPresetToken = {
  LightViolet1: 'rgba(235,235,255,1.00)',
  LightViolet2: 'rgba(212,209,255,1.00)',
  LightViolet3: 'rgba(182,176,255,1.00)',
  LightViolet4: 'rgba(155,143,255,1.00)',
  LightViolet5: 'rgba(124,106,255,1.00)',
  LightViolet6: 'rgba(94,69,255,1.00)',
  LightViolet7: 'rgba(78,53,219,1.00)',
  LightViolet8: 'rgba(63,39,184,1.00)',
  LightViolet9: 'rgba(49,27,148,1.00)',
  LightViolet10: 'rgba(36,17,112,1.00)',
  LightBlue1: 'rgba(224,240,255,1.00)',
  LightBlue2: 'rgba(196,224,255,1.00)',
  LightBlue3: 'rgba(153,200,255,1.00)',
  LightBlue4: 'rgba(107,173,255,1.00)',
  LightBlue5: 'rgba(61,145,255,1.00)',
  LightBlue6: 'rgba(13,114,255,1.00)',
  LightBlue7: 'rgba(5,91,219,1.00)',
  LightBlue8: 'rgba(0,71,184,1.00)',
  LightBlue9: 'rgba(0,54,148,1.00)',
  LightBlue10: 'rgba(0,39,112,1.00)',
  LightLightblue1: 'rgba(219,246,255,1.00)',
  LightLightblue2: 'rgba(188,237,254,1.00)',
  LightLightblue3: 'rgba(142,223,253,1.00)',
  LightLightblue4: 'rgba(96,208,252,1.00)',
  LightLightblue5: 'rgba(50,191,251,1.00)',
  LightLightblue6: 'rgba(0,171,250,1.00)',
  LightLightblue7: 'rgba(0,139,208,1.00)',
  LightLightblue8: 'rgba(0,108,167,1.00)',
  LightLightblue9: 'rgba(0,79,125,1.00)',
  LightLightblue10: 'rgba(0,51,83,1.00)',
  LightTeal1: 'rgba(216,248,239,1.00)',
  LightTeal2: 'rgba(178,240,225,1.00)',
  LightTeal3: 'rgba(135,225,204,1.00)',
  LightTeal4: 'rgba(84,211,183,1.00)',
  LightTeal5: 'rgba(39,196,165,1.00)',
  LightTeal6: 'rgba(0,181,148,1.00)',
  LightTeal7: 'rgba(0,151,126,1.00)',
  LightTeal8: 'rgba(0,121,103,1.00)',
  LightTeal9: 'rgba(0,91,79,1.00)',
  LightTeal10: 'rgba(0,60,53,1.00)',
  LightGreen1: 'rgba(218,248,224,1.00)',
  LightGreen2: 'rgba(181,242,194,1.00)',
  LightGreen3: 'rgba(142,229,161,1.00)',
  LightGreen4: 'rgba(101,215,128,1.00)',
  LightGreen5: 'rgba(65,202,98,1.00)',
  LightGreen6: 'rgba(34,189,75,1.00)',
  LightGreen7: 'rgba(27,158,64,1.00)',
  LightGreen8: 'rgba(20,126,52,1.00)',
  LightGreen9: 'rgba(14,95,39,1.00)',
  LightGreen10: 'rgba(9,63,27,1.00)',
  LightLightgreen1: 'rgba(233,250,210,1.00)',
  LightLightgreen2: 'rgba(207,245,171,1.00)',
  LightLightgreen3: 'rgba(182,236,132,1.00)',
  LightLightgreen4: 'rgba(156,226,95,1.00)',
  LightLightgreen5: 'rgba(131,217,61,1.00)',
  LightLightgreen6: 'rgba(106,207,29,1.00)',
  LightLightgreen7: 'rgba(86,173,24,1.00)',
  LightLightgreen8: 'rgba(66,138,18,1.00)',
  LightLightgreen9: 'rgba(47,103,12,1.00)',
  LightLightgreen10: 'rgba(30,69,8,1.00)',
  LightYellow1: 'rgba(255,253,204,1.00)',
  LightYellow2: 'rgba(255,249,176,1.00)',
  LightYellow3: 'rgba(255,241,135,1.00)',
  LightYellow4: 'rgba(252,229,93,1.00)',
  LightYellow5: 'rgba(252,215,50,1.00)',
  LightYellow6: 'rgba(252,197,0,1.00)',
  LightYellow7: 'rgba(213,155,0,1.00)',
  LightYellow8: 'rgba(170,116,0,1.00)',
  LightYellow9: 'rgba(128,80,0,1.00)',
  LightYellow10: 'rgba(85,49,0,1.00)',
  LightOrange1: 'rgba(255,240,212,1.00)',
  LightOrange2: 'rgba(254,227,180,1.00)',
  LightOrange3: 'rgba(253,207,139,1.00)',
  LightOrange4: 'rgba(252,185,98,1.00)',
  LightOrange5: 'rgba(251,162,60,1.00)',
  LightOrange6: 'rgba(250,134,17,1.00)',
  LightOrange7: 'rgba(208,101,12,1.00)',
  LightOrange8: 'rgba(167,72,8,1.00)',
  LightOrange9: 'rgba(125,47,5,1.00)',
  LightOrange10: 'rgba(83,27,2,1.00)',
  LightRed1: 'rgba(254,232,226,1.00)',
  LightRed2: 'rgba(253,208,197,1.00)',
  LightRed3: 'rgba(251,175,161,1.00)',
  LightRed4: 'rgba(249,145,129,1.00)',
  LightRed5: 'rgba(247,112,99,1.00)',
  LightRed6: 'rgba(242,79,68,1.00)',
  LightRed7: 'rgba(209,42,37,1.00)',
  LightRed8: 'rgba(174,26,26,1.00)',
  LightRed9: 'rgba(138,16,21,1.00)',
  LightRed10: 'rgba(102,9,16,1.00)',
  LightPink1: 'rgba(253,228,239,1.00)',
  LightPink2: 'rgba(250,211,229,1.00)',
  LightPink3: 'rgba(246,168,207,1.00)',
  LightPink4: 'rgba(241,127,188,1.00)',
  LightPink5: 'rgba(237,87,172,1.00)',
  LightPink6: 'rgba(229,64,163,1.00)',
  LightPink7: 'rgba(196,36,137,1.00)',
  LightPink8: 'rgba(161,24,120,1.00)',
  LightPink9: 'rgba(125,15,92,1.00)',
  LightPink10: 'rgba(89,8,66,1.00)',
  LightPurple1: 'rgba(250,231,251,1.00)',
  LightPurple2: 'rgba(245,210,250,1.00)',
  LightPurple3: 'rgba(235,167,247,1.00)',
  LightPurple4: 'rgba(219,123,240,1.00)',
  LightPurple5: 'rgba(202,83,235,1.00)',
  LightPurple6: 'rgba(188,64,229,1.00)',
  LightPurple7: 'rgba(140,30,183,1.00)',
  LightPurple8: 'rgba(107,19,148,1.00)',
  LightPurple9: 'rgba(77,11,112,1.00)',
  LightPurple10: 'rgba(58,6,89,1.00)',
  LightGray0: 'rgba(255,255,255,1.00)',
  LightGray1: 'rgba(245,246,247,1.00)',
  LightGray2: 'rgba(237,239,242,1.00)',
  LightGray3: 'rgba(227,230,237,1.00)',
  LightGray4: 'rgba(211,216,224,1.00)',
  LightGray5: 'rgba(194,200,212,1.00)',
  LightGray6: 'rgba(177,183,196,1.00)',
  LightGray7: 'rgba(157,164,178,1.00)',
  LightGray8: 'rgba(138,146,161,1.00)',
  LightGray9: 'rgba(122,130,145,1.00)',
  LightGray10: 'rgba(105,112,128,1.00)',
  LightGray11: 'rgba(88,95,110,1.00)',
  LightGray12: 'rgba(72,78,92,1.00)',
  LightGray13: 'rgba(56,61,74,1.00)',
  LightGray14: 'rgba(41,46,56,1.00)',
  LightGray15: 'rgba(28,31,38,1.00)',
  LightGray16: 'rgba(0,0,0,1.00)',
  DarkViolet1: 'rgba(51,56,99,1.00)',
  DarkViolet2: 'rgba(60,66,126,1.00)',
  DarkViolet3: 'rgba(70,77,155,1.00)',
  DarkViolet4: 'rgba(81,87,185,1.00)',
  DarkViolet5: 'rgba(95,99,216,1.00)',
  DarkViolet6: 'rgba(111,111,247,1.00)',
  DarkViolet7: 'rgba(136,134,247,1.00)',
  DarkViolet8: 'rgba(166,164,251,1.00)',
  DarkViolet9: 'rgba(195,194,253,1.00)',
  DarkViolet10: 'rgba(222,222,255,1.00)',
  DarkBlue1: 'rgba(37,60,94,1.00)',
  DarkBlue2: 'rgba(44,75,122,1.00)',
  DarkBlue3: 'rgba(50,90,150,1.00)',
  DarkBlue4: 'rgba(54,104,180,1.00)',
  DarkBlue5: 'rgba(57,118,210,1.00)',
  DarkBlue6: 'rgba(59,132,241,1.00)',
  DarkBlue7: 'rgba(97,158,246,1.00)',
  DarkBlue8: 'rgba(133,183,249,1.00)',
  DarkBlue9: 'rgba(170,208,252,1.00)',
  DarkBlue10: 'rgba(209,232,255,1.00)',
  DarkLightblue1: 'rgba(31,65,89,1.00)',
  DarkLightblue2: 'rgba(37,83,115,1.00)',
  DarkLightblue3: 'rgba(42,106,145,1.00)',
  DarkLightblue4: 'rgba(47,125,173,1.00)',
  DarkLightblue5: 'rgba(50,144,201,1.00)',
  DarkLightblue6: 'rgba(53,162,229,1.00)',
  DarkLightblue7: 'rgba(90,181,237,1.00)',
  DarkLightblue8: 'rgba(128,198,242,1.00)',
  DarkLightblue9: 'rgba(166,216,250,1.00)',
  DarkLightblue10: 'rgba(204,238,255,1.00)',
  DarkTeal1: 'rgba(29,71,70,1.00)',
  DarkTeal2: 'rgba(32,89,87,1.00)',
  DarkTeal3: 'rgba(33,107,103,1.00)',
  DarkTeal4: 'rgba(33,128,121,1.00)',
  DarkTeal5: 'rgba(34,148,138,1.00)',
  DarkTeal6: 'rgba(33,166,152,1.00)',
  DarkTeal7: 'rgba(89,186,172,1.00)',
  DarkTeal8: 'rgba(129,207,192,1.00)',
  DarkTeal9: 'rgba(166,227,213,1.00)',
  DarkTeal10: 'rgba(201,248,235,1.00)',
  DarkGreen1: 'rgba(38,74,45,1.00)',
  DarkGreen2: 'rgba(42,90,53,1.00)',
  DarkGreen3: 'rgba(48,108,62,1.00)',
  DarkGreen4: 'rgba(50,126,71,1.00)',
  DarkGreen5: 'rgba(54,145,80,1.00)',
  DarkGreen6: 'rgba(54,163,88,1.00)',
  DarkGreen7: 'rgba(96,183,118,1.00)',
  DarkGreen8: 'rgba(131,202,147,1.00)',
  DarkGreen9: 'rgba(165,222,176,1.00)',
  DarkGreen10: 'rgba(208,240,194,1.00)',
  DarkLightgreen1: 'rgba(59,79,40,1.00)',
  DarkLightgreen2: 'rgba(69,98,45,1.00)',
  DarkLightgreen3: 'rgba(78,117,48,1.00)',
  DarkLightgreen4: 'rgba(86,136,53,1.00)',
  DarkLightgreen5: 'rgba(92,156,56,1.00)',
  DarkLightgreen6: 'rgba(97,176,58,1.00)',
  DarkLightgreen7: 'rgba(124,191,92,1.00)',
  DarkLightgreen8: 'rgba(152,207,126,1.00)',
  DarkLightgreen9: 'rgba(179,224,159,1.00)',
  DarkLightgreen10: 'rgba(208,240,194,1.00)',
  DarkYellow1: 'rgba(84,66,35,1.00)',
  DarkYellow2: 'rgba(111,87,40,1.00)',
  DarkYellow3: 'rgba(137,106,42,1.00)',
  DarkYellow4: 'rgba(163,126,42,1.00)',
  DarkYellow5: 'rgba(190,148,42,1.00)',
  DarkYellow6: 'rgba(217,169,39,1.00)',
  DarkYellow7: 'rgba(223,190,82,1.00)',
  DarkYellow8: 'rgba(230,210,121,1.00)',
  DarkYellow9: 'rgba(239,230,160,1.00)',
  DarkYellow10: 'rgba(250,248,200,1.00)',
  DarkOrange1: 'rgba(87,57,40,1.00)',
  DarkOrange2: 'rgba(112,72,47,1.00)',
  DarkOrange3: 'rgba(140,88,53,1.00)',
  DarkOrange4: 'rgba(168,104,57,1.00)',
  DarkOrange5: 'rgba(196,118,59,1.00)',
  DarkOrange6: 'rgba(222,135,60,1.00)',
  DarkOrange7: 'rgba(229,162,90,1.00)',
  DarkOrange8: 'rgba(237,188,123,1.00)',
  DarkOrange9: 'rgba(245,212,159,1.00)',
  DarkOrange10: 'rgba(255,236,201,1.00)',
  DarkRed1: 'rgba(89,46,46,1.00)',
  DarkRed2: 'rgba(112,58,56,1.00)',
  DarkRed3: 'rgba(138,69,65,1.00)',
  DarkRed4: 'rgba(164,81,75,1.00)',
  DarkRed5: 'rgba(191,93,84,1.00)',
  DarkRed6: 'rgba(219,102,90,1.00)',
  DarkRed7: 'rgba(230,129,118,1.00)',
  DarkRed8: 'rgba(239,155,146,1.00)',
  DarkRed9: 'rgba(247,181,175,1.00)',
  DarkRed10: 'rgba(254,211,206,1.00)',
  DarkPink1: 'rgba(92,50,83,1.00)',
  DarkPink2: 'rgba(116,59,103,1.00)',
  DarkPink3: 'rgba(140,66,120,1.00)',
  DarkPink4: 'rgba(164,74,138,1.00)',
  DarkPink5: 'rgba(190,80,156,1.00)',
  DarkPink6: 'rgba(217,85,173,1.00)',
  DarkPink7: 'rgba(227,121,189,1.00)',
  DarkPink8: 'rgba(237,153,205,1.00)',
  DarkPink9: 'rgba(245,185,222,1.00)',
  DarkPink10: 'rgba(253,215,238,1.00)',
  DarkPurple1: 'rgba(81,53,97,1.00)',
  DarkPurple2: 'rgba(99,59,121,1.00)',
  DarkPurple3: 'rgba(117,67,145,1.00)',
  DarkPurple4: 'rgba(137,75,170,1.00)',
  DarkPurple5: 'rgba(158,82,196,1.00)',
  DarkPurple6: 'rgba(177,89,222,1.00)',
  DarkPurple7: 'rgba(194,123,230,1.00)',
  DarkPurple8: 'rgba(210,155,238,1.00)',
  DarkPurple9: 'rgba(225,186,245,1.00)',
  DarkPurple10: 'rgba(239,216,251,1.00)',
  DarkGray0: 'rgba(26,30,36,1.00)',
  DarkGray1: 'rgba(34,39,48,1.00)',
  DarkGray2: 'rgba(43,48,59,1.00)',
  DarkGray3: 'rgba(53,58,71,1.00)',
  DarkGray4: 'rgba(63,70,84,1.00)',
  DarkGray5: 'rgba(79,86,99,1.00)',
  DarkGray6: 'rgba(95,102,117,1.00)',
  DarkGray7: 'rgba(116,120,135,1.00)',
  DarkGray8: 'rgba(132,138,153,1.00)',
  DarkGray9: 'rgba(148,153,168,1.00)',
  DarkGray10: 'rgba(168,172,184,1.00)',
  DarkGray11: 'rgba(187,190,199,1.00)',
  DarkGray12: 'rgba(200,201,209,1.00)',
  DarkGray13: 'rgba(214,215,219,1.00)',
  DarkGray14: 'rgba(224,225,229,1.00)',
  DarkGray15: 'rgba(235,235,240,1.00)',
  DarkGray16: 'rgba(242,243,247,1.00)',
  BasicBlack0: 'rgba(0,0,0,0.00)',
  BasicBlack5: 'rgba(0,0,0,0.05)',
  BasicBlack10: 'rgba(0,0,0,0.10)',
  BasicBlack20: 'rgba(0,0,0,0.20)',
  BasicBlack30: 'rgba(0,0,0,0.30)',
  BasicBlack40: 'rgba(0,0,0,0.40)',
  BasicBlack50: 'rgba(0,0,0,0.50)',
  BasicBlack60: 'rgba(0,0,0,0.60)',
  BasicBlack70: 'rgba(0,0,0,0.70)',
  BasicBlack80: 'rgba(0,0,0,0.80)',
  BasicBlack90: 'rgba(0,0,0,0.90)',
  BasicBlack100: 'rgba(0,0,0,1.00)',
  BasicWhite0: 'rgba(255,255,255,0.00)',
  BasicWhite5: 'rgba(255,255,255,0.05)',
  BasicWhite10: 'rgba(255,255,255,0.10)',
  BasicWhite20: 'rgba(255,255,255,0.20)',
  BasicWhite30: 'rgba(255,255,255,0.30)',
  BasicWhite40: 'rgba(255,255,255,0.40)',
  BasicWhite50: 'rgba(255,255,255,0.50)',
  BasicWhite60: 'rgba(255,255,255,0.60)',
  BasicWhite70: 'rgba(255,255,255,0.70)',
  BasicWhite80: 'rgba(255,255,255,0.80)',
  BasicWhite90: 'rgba(255,255,255,0.90)',
  BasicWhite100: 'rgba(255,255,255,1.00)',
};
