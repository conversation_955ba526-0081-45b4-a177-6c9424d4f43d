<template>
  <BasicButton
    size="small"
    class="custom-rounded-btn"
    borderColor="black"
    :disabled="loading || disabled"
  >
    <LoadingOutlined v-if="loading" />
    <Icon v-else icon="carbon:dot-mark" :class="enabled ? 'c-FO-Functional-Success1-Default' : 'c-FO-Content-Text2'" />
    <slot />
  </BasicButton>
</template>

<script setup lang="ts">
import { BasicButton } from '../../../../../components/Button';
import { LoadingOutlined } from '@ant-design/icons-vue';
import { Icon } from '../../../../../components/Icon';

const props = defineProps<{
  enabled?: boolean;
  disabled?: boolean;
  loading?: boolean;
}>();
</script>
