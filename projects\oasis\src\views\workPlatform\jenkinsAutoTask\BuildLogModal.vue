<template>
  <BasicModal
    @register="registerModal"
    :title="`『${jobName}』的执行记录`"
    :width="700"
    :footer="null"
  >
    <a-timeline class="!mt-3 min-h-150 !ml-3">
      <a-timeline-item
        v-for="buildLog in buildLogList"
        :key="buildLog.ID"
        :color="getStateModel(buildLog?.status!)?.color"
      >
        <template #dot>
          <Icon :icon="getStateModel(buildLog?.status!)?.icon" />
        </template>
        <div :class="getStateModel(buildLog?.status!)?.classColor">
          {{ getStateModel(buildLog?.status!)?.name }}
        </div>
        <div>
          参数:
          <span v-for="(val, key) in buildLog?.params" :key="key">「{{ key }}: {{ val }}」</span>
        </div>
        <div>
          执行人:
          <span class="c-FO-Brand-Primary-Default">{{ buildLog?.trigger?.nickName }}</span>
        </div>
        <div>
          执行时间:
          <span class="c-FO-Content-Text2">{{ formatTISOToDate(buildLog?.CreatedAt) }}</span>
        </div>
      </a-timeline-item>
    </a-timeline>
  </BasicModal>
</template>
<script lang="ts" setup name="JenkinsAutoTaskBuildLogModal">
  import { Timeline as ATimeline } from 'ant-design-vue';
  import { ref } from 'vue';
  import { useJenkinsBuildState } from './hook';
  import { JobBuildsListItem } from '/@/api/page/model/jenkinsModel';
  import Icon from '/@/components/Icon';
  import { BasicModal, useModalInner } from '/@/components/Modal';
  import { formatTISOToDate } from '/@/utils/dateUtil';

  defineEmits(['register']);

  const ATimelineItem = ATimeline.Item;
  const jobName = ref<string>('');
  const buildLogList = ref<JobBuildsListItem[]>([]);

  const [registerModal, { setModalProps }] = useModalInner(async (data) => {
    setModalProps({ confirmLoading: true });
    buildLogList.value = data?.buildLogList;
    jobName.value = data?.buildLogList?.[0]?.job?.description || '';
    setModalProps({ confirmLoading: false });
  });

  const getStateModel = (state: string) => {
    const { getBuildStateModel } = useJenkinsBuildState(state || '');
    return getBuildStateModel();
  };
</script>
