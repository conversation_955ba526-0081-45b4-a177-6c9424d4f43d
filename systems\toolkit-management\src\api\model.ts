export interface BasicPageParams {
  page: number;
  pageSize: number;
}

export interface BasicFetchResult<T> {
  list: T[];
  total: number;
}

export interface BaseItem {
  ID?: number;
  CreatedAt?: string;
  UpdatedAt?: string;
  description?: string;
}

export interface BasicResult<T = any> {
  code: number;
  msg: string;
  data?: T;
}

export interface BasicBatchSortParams {
  idList: number[];
}

export interface ApiListType<T> {
  type: 'add' | 'edit' | 'delete' | 'test';
  data: T;
}

export interface UserInfoModel {
  ID?: number;
  userName?: string;
  moniker?: string;
  displayName?: string;
  nickName?: string;
  headerImg?: string;
  desc?: string;
  homePath?: string;
  baseColor?: string;
  activeColor?: string;
  phone?: string;
  email?: string;
  uuid?: string;
  openID?: string;
  disabled?: boolean;
  authorities?: RoleListItem[];
  authority?: RoleListItem;
  authorityId?: string;
  lastProjectId?: number;
  isOutSourcing?: boolean;
  /** 是否离职 */
  inactive?: boolean;
}

export interface BaseItem {
  ID?: number;
  CreatedAt?: string;
  UpdatedAt?: string;
  description?: string;
}

export interface BasicFetchResult<T> {
  list: T[];
  total: number;
}

export interface BasicPageParams {
  page: number;
  pageSize: number;
}

export type AccountParams = BasicPageParams & {
  account?: string;
  nickname?: string;
};

// 角色列表 接口参数
export interface RoleParams {
  authorityName?: string;
  authorityId?: string;
  menusIds?: number[];
}

export type RolePageParams = BasicPageParams & RoleParams;

export interface DeptParams {
  deptName?: string;
  status?: string;
}

export interface MenuParams {
  menuName?: string;
  status?: string;
}

export interface AccountListItem {
  id: string;
  account: string;
  email: string;
  nickname: string;
  role: number;
  createTime: string;
  description: string;
  status: number;
}

export interface DeptListItem extends BaseItem {
  name?: string;
  sort?: number;
  ownerID?: number;
  parentID?: number;
  children?: DeptListItem[];
  disabled?: boolean;
  orgPath?: string;
  orgNo?: string;
}

export type DeptListPageParams = DeptListItem;
export type DeptListGetResultModel = BasicFetchResult<DeptListItem>;

// 批量更新部门干员列表接口参数 Model
export interface BatchDeptMemberParams {
  idList: number[];
}

// 干员所在部门列表 Model
export interface MemberDeptListItem extends BaseItem {
  departments?: DeptListItem[];
}

// 项目列表 Model
export interface ProjectListItem extends BaseItem {
  name?: string;
  // 代号
  alias?: string;
  projectTypeID?: number;
  // 管理列表
  admins?: UserInfoModel[];
  // 成员总数
  totalMember?: number;
  // 当前用户是否管理员
  isAdmin?: boolean;
  // 引擎类型 1:Unity 2:Unreal
  engineType?: number;
  perforceIds?: number[];
}

// 创建项目返回参数
export interface addProjectItemResult {
  projectID: number;
}

// id获取项目信息 接口参数
export interface ProjectItemParams {
  projects: ProjectListItem;
}

// 项目列表 接口参数
export type ProjectPageParams = BasicPageParams & ProjectListItem;

// 项目权限子列表 Model
export interface ProjectPermissionChildrenListItem {
  checked?: boolean;
  ID?: number;
  path?: string;
  description?: string;
  apiGroup?: string;
  method?: string;
  value?: ProjectPermissionChildrenListItem;
}
// 项目权限列表 Model
export interface ProjectPermissionListItem {
  name?: string;
  ID?: number;
  value?: ProjectPermissionChildrenListItem[];
  children?: ProjectPermissionChildrenListItem[];
}

// 编辑项目权限参数列表 model
export interface EditProjectPermissionParamsItem {
  method?: string;
  path?: string;
}
// 编辑项目权限参数列表 接口参数
export interface EditProjectPermissionParams {
  casbin_infos: EditProjectPermissionParamsItem[];
}

// 项目干员组列表 Model
export interface ProjectGroupListItem {
  ID?: string;
  name?: string;
  description?: string;
  userCount?: number;
  count?: number;
  deptsCount?: number;
  projectID?: number;
  priority?: number;
  pmpsCount?: number;
}

// 项目干员组列表 接口参数
export type ProjectGroupPageParams = BasicPageParams & ProjectGroupListItem;

// 变更干员组干员 接口参数
export interface ChangeProjectMemberParams {
  memberIds: number[];
}

// 转移干员组干员 接口参数
export type TransferProjectMemberParams = {
  newGroupId: number;
} & ChangeProjectMemberParams;

// 变更干员组部门 接口参数
export interface ChangeProjectDeptParams {
  idList: number[];
}
// 项目干员列表 接口参数
export type ProjectMemberPageParams = BasicPageParams & UserInfoModel;

// 成员组的PMP组列表 接口参数
export type ProjectPmpGroupListPageParams = BasicPageParams;

// LDAP组列表 接口参数
export type ProjectLDAPGroupPageParams = BasicPageParams & {
  serverID: number;
};

// LDAP组查询参数
export interface LDAPMemberSubGroupParams {
  name: string;
  serverID: number;
}

export interface ChangeProjectPmpGroupParams {
  nameList: number[];
}

export interface MenuListItem {
  ID: number | string;
  orderNo: string;
  createTime: string;
  status: number;
  icon: string;
  component: string;
  permission: string;
}

export interface RoleListItem {
  authorityName?: string;
  authorityId?: string;
  CreatedAt?: string;
  UpdatedAt?: string;
  children?: RoleListItem[];
}

// 轮播图列表
export interface CarouselListItem {
  ID?: string;
  title?: string;
  description?: string;
  enable?: boolean;
  link?: string;
  backgroundImg?: string;
  path?: string;
  previewPath?: string;
}

// id获取轮播图信息 接口参数
export interface CarouselItemParams {
  recarousel: CarouselListItem;
}
// 预览路径获取轮播图信息 接口参数
export interface CarouselItemPreviewParams {
  recarousel: CarouselListItem;
}

// 轮播图列表 接口参数
export type CarouselPageParams = BasicPageParams & CarouselListItem;

// 介绍页子功能列表
export interface IntroChildListItem {
  ID?: string;
  title?: string;
  carouselID?: number;
  description?: string;
  link?: string;
  icon?: string;
  order?: number;
}

// 介绍页子功能列表 接口参数
export type IntroChildPageParams = BasicPageParams & IntroChildListItem;

// id获取介绍页子功能信息 接口参数
export interface IntroChildItemParams {
  re_reception: IntroChildListItem;
}

// 工具商店列表
export interface ToolkitListItem extends BaseItem {
  name?: string;
  workTypeID?: number;
  description?: string;
  latestLink?: string;
  latestHot?: string;
  latestVersion: ToolkitVersionListItem;
  icon?: string;
  visible?: boolean;
  projectID?: number;
  projectIDs?: number[];
  project?: ProjectListItem;
  platforms?: DevicePlatform[];
  // APP ID 仅iOS需要
  bundleID?: string;
  readme?: string;
  maintain?: boolean;
  adminIds?: number[];
}

// 工具商店列表 接口参数
export type ToolkitPageParams = BasicPageParams & ToolkitListItem;

// id获取工具信息 接口参数
export interface ToolkitItemParams {
  retool: ToolkitListItem;
}

// 短连接 Model
export interface ShortURLItem extends BaseItem {
  shortURL: string;
  longURL: string;
}

// 工具版本列表
export interface ToolkitVersionListItem extends BaseItem {
  version?: string;
  downloadLink?: string;
  // plist文件链接
  plistLink?: string;
  toolID?: string;
  author?: UserInfoModel;
  latestHot?: string;
  readmeLink?: string;
  releaseNote?: string;
  // 文件大小
  sizeKB?: number;
  // 平台：1：安卓，2：iOS，3：Windows，4：macOS
  platform?: number;

  shortURL?: ShortURLItem;
}
// p4接触流程配置
export interface P4ConfigListItem extends BaseItem {
  bizId: number;
  content: string;
  parentId: number;
  order: number;
}
// 工具版本列表 接口参数
export type ToolkitVersionPageParams = BasicPageParams & ToolkitVersionListItem;

// id获取工具版本信息 接口参数
export interface ToolkitVersionItemParams {
  retoolVersion: ToolkitVersionListItem;
}

// 获取游戏商店最新版本 接口参数
export interface GameStoreLatestVersionParams {
  version: ToolkitVersionListItem;
}

// 解压文件参数
export interface unzipFileItem {
  path: string;
  output?: string;
}

// 解压文件返回参数
export interface unzipFileGetResultModel {
  path: string;
}

// 删除文件参数
export interface removeFileItem {
  path: string;
}

// 工具/项目类型列表
export interface CommonTypeListItem extends BaseItem {
  name?: string;
  editable?: boolean;
  isNew?: boolean;
  description?: string;
}

// 工具/项目类型列表 接口参数
export type CommonTypePageParams = BasicPageParams & CommonTypeListItem;

// id获取工具/项目类型信息 接口参数
export interface CommonTypeItemParams {
  workType: CommonTypeListItem;
}

// 私人令牌列表 Model
export interface AccessTokenListItem {
  ID?: string;
  CreatedAt?: string;
  name: string;
  expire: string;
  scopes: string[];

  token?: string;
}

// id获取私人令牌信息 接口参数
export interface AccessTokenItemParams {
  access_token: AccessTokenListItem;
}

// 私人令牌列表 接口参数
export type AccessTokenPageParams = BasicPageParams;

// 私人令牌新增 接口返回参数
export interface AccessTokenOnceResult {
  token: string;
}

/**
 * @description: Request list return value
 */
export type AccountListGetResultModel = BasicFetchResult<AccountListItem>;

export interface MenuListGetResultModel {
  hierarchy: MenuListItem[];
}

export type RolePageListGetResultModel = BasicFetchResult<RoleListItem>;

export type RoleListGetResultModel = RoleListItem[];

export type ProjectListGetResultModel = BasicFetchResult<ProjectListItem>;

export type ProjectGroupListGetResultModel = BasicFetchResult<ProjectGroupListItem>;

export interface ProjectPermissionListGetResultModel {
  apis: ProjectPermissionListItem[];
}

export type ProjectMemberListGetResultModel = BasicFetchResult<UserInfoModel>;
export type ProjectPmpGroupListGetResultModel = BasicFetchResult<PmpGroupListItem>;
export interface PmpGroupListItem {
  name: string;
}
export type CarouselPageListGetResultModel = BasicFetchResult<CarouselListItem>;

export type IntroChildPageListGetResultModel = BasicFetchResult<IntroChildListItem>;

export type ToolkitPageListGetResultModel = BasicFetchResult<ToolkitListItem>;

export type ToolkitVersionPageListGetResultModel = BasicFetchResult<ToolkitVersionListItem>;

export type CommonTypeListGetResultModel = BasicFetchResult<CommonTypeListItem>;

export type AccessTokenListGetResultModel = BasicFetchResult<AccessTokenListItem>;

// 飞书群聊列表 Model
export interface FeishuChatListItem {
  avatar: string;
  chat_id: string;
  name: string;
  owner_id: string;
  owner_id_type: string;
  tenant_key: string;

  disabled?: boolean;
}

export type FeishuChatListGetResultModel = BasicFetchResult<FeishuChatListItem>;

// 新增&编辑bug姬群聊 参数
export interface BugChatParams {
  chatID: string;
  chatName: string;
  avatar: string;
}

// 新增&编辑bug姬群聊列表 Model
export type BugChatListItem = BugChatParams & BaseItem;

export type BugChatListGetResultModel = BasicFetchResult<BugChatListItem>;

// bug姬群聊列表 接口参数
export type BugChatPageParams = BasicPageParams;

// 获取应用所在群聊列表 接口参数
export interface AppChatsParams {
  robot?: 'tech' | 'bug' | 'swarm' | 'submit' | 'gitlab';
  appId?: string;
  appSecret?: string;
}

// 项目管理工具 Model
export interface ManagementToolListItem extends BaseItem {
  // 1：飞书项目，2：Redmine
  pmType?: number;
  workspace?: string;
}

export type ManagementToolListGetResultModel = BasicFetchResult<ManagementToolListItem>;

// 项目管理工具列表 接口参数
export type ManagementToolPageParams = BasicPageParams;
export interface OrientationListItem {
  CreatedAt: string;
  ID: number;
  UpdatedAt: string;
  content: string;
  order: number;
  parentId: number;
  title: string;
  bizId: number;
}

export type OrientationListGetResultModel = BasicFetchResult<OrientationListItem>;
export interface UserSchedule {
  orientation: {
    CreatedAt: string;
    ID: number;
    UpdatedAt: string;
    bizId: number;
    orientationId: number;
    orientationParentId: number;
    status: number;
    uid: number;
  };
}

export enum DevicePlatform {
  Android = 1,
  iOS = 2,
  HarmonyOS = 11,
  Windows = 3,
  MacOS = 4,
  MacOSIntel = 5,
  PS = 6,
  Switch = 7,
  Xbox = 8,
  DS = 9,
  GS = 10,
}

/** 项目游戏包版本列表 Model */
export interface GamePackagesVersionsListItem extends BaseItem {
  version?: string;
  pkgID?: number;
  /** 1 表示 Android，2 表示 IOS */
  platform?: DevicePlatform;
  /** 包文件 */
  pkgFile?: string;
  /** 下载地址 */
  downloadLink?: string;
  /** 是否长期保留 */
  reserve?: boolean;
  icon?: string;
  releaseNote?: string;
  /** 包体检测ID */
  doctorID?: number;
  /** 下载地址短连接 */
  shortURL?: ShortURLItem;
  /** 是否置顶 */
  top?: boolean;
  sizeKB?: number;
  passDoctor?: boolean;
  lbs?: string[];
  unfinished?: boolean;
  /** 过期天数 */
  remainDays?: number;

  /** 前端用 */
  checked?: boolean;
  color?: number;
  colors?: number[];
}
/**
 * 文件验证结果
 */
export interface FileValidationResult {
  /** 验证是否通过 */
  isValid: boolean;
  /** 错误信息 */
  errorMessage?: string;
}

/**
 * 上传响应结果
 */
export interface UploadResult {
  /** 上传是否成功 */
  success: boolean;
  /** 文件URL */
  url?: string;
  /** 错误信息 */
  errorMessage?: string;
}
