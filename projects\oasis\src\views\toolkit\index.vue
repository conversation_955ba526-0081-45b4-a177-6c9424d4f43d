<template>
  <div
    v-track:v="'qsczx6ixuk'"
    :class="prefixCls"
    :style="{ maxHeight: `calc(100vh - ${headerHeightRef}px)` }"
  >
    <div :class="`${prefixCls}__left`">
      <div class="!h-[30px]">
        <Icon icon="toolkitStore-roof-curtain|svg" :size="200" class="!h-inherit" />
      </div>

      <div
        class="mt-4 flex flex-col gap-2 overflow-auto"
        :style="{ maxHeight: `calc(100vh - ${headerHeightRef + 30 + (isFullScreen ? 64 : 0)}px)` }"
      >
        <template v-if="commonTypeList?.length > 0">
          <div
            v-for="comType in commonTypeList"
            :key="comType.ID"
            :class="`${prefixCls}__left-tab`"
            :active="activeTab === comType.ID"
            @click="handleTabChange(comType.ID!)"
          >
            <EllipsisText :class="`${prefixCls}__left-tab-title`">
              {{ comType.name }}
            </EllipsisText>
          </div>
        </template>
        <AEmpty v-else :image="emptyImg" description="暂无分支数据" />
      </div>
    </div>
    <div :class="`${prefixCls}__right`">
      <div :class="`${prefixCls}__right-filter`">
        <div
          class="w-full flex flex-col-reverse justify-start gap-y-4 2xl:flex-row 2xl:items-center 2xl:justify-between"
        >
          <div class="flex items-center">
            <div class="ml-5 flex items-center">
              <span class="mr-3 w-[33px] self-start font-bold leading-6">项目:</span>
              <TagSelect
                v-model="searchParams.labels"
                :options="allLabels"
                isMultiple
                :fieldNames="{ label: 'name', value: 'ID' }"
                :disabledFunc="disabledFunc"
                class="flex-1"
                @change="getCardList"
              />
            </div>
          </div>
          <div :class="`${prefixCls}__right-filter-btn-list`">
            <div :class="`${prefixCls}__right-filter-btn ml`" @click="goToSettingsPage()">
              <Icon icon="icon-park-outline:setting-config" class="mr-1" />
              上传与配置
            </div>
          </div>
        </div>
        <div class="flex items-center">
          <div class="ml-5 mt-4 flex items-center">
            <span class="mr-3 font-bold">平台:</span>
            <TagSelect
              v-model="searchParams.platform"
              :options="clonePlatformOptions"
              :disabledFunc="disabledFunc"
            />
          </div>
        </div>
        <div class="flex items-center">
          <div v-if="allLabels?.length" class="mt-4 flex flex-1 flex-wrap items-center">
            <span class="ml-5 mr-3 font-bold">搜索:</span>
            <a-input
              v-model:value="searchParams.search"
              placeholder="请输入"
              style="width: 200px"
              @change="getCardListNySearch"
            />
          </div>
        </div>
      </div>
      <div :class="`${prefixCls}__right-content`">
        <CardList v-bind="getCardListProps" ref="cardListRef" />
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { useDebounceFn } from '@vueuse/core';
import { Empty as AEmpty } from 'ant-design-vue';
import { cloneDeep } from 'lodash-es';
import { computed, nextTick, onMounted, ref } from 'vue';
import { useRouter } from 'vue-router';
import CardList from './CardList.vue';
import type { CommonTypeListItem, ProjectListItem } from '/@/api/page/model/systemModel';
import { getToolkitTypeListByPage } from '/@/api/page/system';
import Icon from '/@/components/Icon';
import TagSelect from '/@/components/TagSelect';
import { useDesign } from '/@/hooks/web/useDesign';
import { useMessage } from '/@/hooks/web/useMessage';
import { useLayoutHeight } from '/@/layouts/default/content/useContentViewHeight';
import { platformOptions } from '/@/views/toolkit/settings/toolkitSettings.data';
import { getAllPaginationList } from '/@/hooks/web/usePagination';
import { useUserStoreWithOut } from '/@/store/modules/user';
import { useCachedProjectList } from '../../hooks/useProjects.ts';

const clonePlatformOptions = cloneDeep(platformOptions);
const { prefixCls } = useDesign('toolkit-list');
const emptyImg = AEmpty.PRESENTED_IMAGE_SIMPLE;
const { currentRoute, push } = useRouter();
const { createMessage } = useMessage();
const { headerHeightRef } = useLayoutHeight();
const isFullScreen = currentRoute.value.query?.fs === '1';

const urlCardList = JSON.parse(decodeURIComponent(currentRoute.value.query?.cardList as string || 'null'));

const urlBranch = Number(currentRoute.value.query?.b);
const activeTab = ref<number>();
const commonTypeList = ref<CommonTypeListItem[]>([]);
const allLabels = ref<ProjectListItem[]>([]);
const cardListRef = ref();
const userStore = useUserStoreWithOut();
const { data: projectList } = useCachedProjectList({ loadImmediately: true });

const searchParams = ref<{ platform: number | undefined; labels: number[]; search: string }>({
  platform: undefined,
  labels: [],
  search: '',
});

const getCardListProps = computed(() => ({
  activeTab: activeTab.value,
  searchParams: searchParams.value,
}));

function getCardList() {
  nextTick(() => {
    cardListRef.value?.getList();
  });
}

const getCardListNySearch = useDebounceFn(getCardList, 300);

async function getLabels() {
  const general: ProjectListItem = {
    ID: 0,
    name: '通用',
  };

  allLabels.value = [general].concat(projectList.value || []);
}

function disabledFunc() {
  createMessage.warning('请先退出选择模式');
}

// 获取列表
async function getCommonTypeList() {
  activeTab.value = undefined;

  const { list } = await getAllPaginationList((p) => getToolkitTypeListByPage(p));

  if (list?.length > 0) {
    const findBranch = list.find((item) => item.ID === urlBranch);

    commonTypeList.value = [{ ID: 0, name: '全部' }, ...list];
    activeTab.value = findBranch ? urlBranch : urlCardList ? urlCardList.activeTab : 0;
  } else {
    commonTypeList.value = [];
  }
}

function handleTabChange(ID: number) {
  if (ID !== activeTab.value) {
    activeTab.value = ID;
  }
}

async function init() {
  // 赋值
  if (urlCardList) {
    searchParams.value.labels = urlCardList.searchParams.labels;
    searchParams.value.platform = urlCardList.searchParams.platform;
    searchParams.value.search = urlCardList.searchParams.search;
  }

  await getCommonTypeList();
  getLabels();
  getCardList();
}

function goToSettingsPage() {
  push({
    name: 'ToolkitPackageSettings',
    query: {
      cardList: encodeURIComponent(JSON.stringify(getCardListProps.value)),
    },
  });
}

onMounted(async () => {
  await init();
});
</script>

<style lang="less">
@prefix-cls: ~'hypergryph-toolkit-list';

.@{prefix-cls} {
  display: flex;
  position: relative;
  overflow: auto;

  &__left {
    display: flex;
    position: sticky;
    top: 0;
    flex-direction: column;
    width: 220px;
    margin-left: 20px;
    padding: 20px 16px 16px 0;

    &-tab {
      position: relative;
      padding: 8px 12px;
      cursor: pointer;
      user-select: none;
      display: flex;
      align-items: center;

      &[disabled='true'] {
        background-color: transparent !important;
        cursor: not-allowed;
      }

      &[active='true'],
      &:hover {
        border-radius: 20px;
        background-color: @FO-Container-Fill1;
      }

      &-icon {
        position: absolute;
        top: 10px;
        left: 12px;
        width: 20px;
        border-radius: 6px;
      }

      &-title {
        width: 150px;
        margin-left: 26px;
        font-weight: bold;
      }
    }
  }

  &__right {
    flex: 1;
    width: 0;
    margin-right: 20px;
    padding: 20px 0;

    &-filter {
      width: 100%;
      margin-bottom: 10px;
      padding: 16px;
      border-radius: 10px;
      background-color: @FO-Container-Fill1;

      &-btn {
        display: inline-flex;
        align-items: center;
        justify-content: center;
        padding: 2px 10px;
        border-radius: 16px;
        color: @FO-Content-Components1;
        background-color: @FO-Container-Fill6;
        border: 1px solid @FO-Container-Fill6;
        font-weight: bold;
        cursor: pointer;
        user-select: none;

        &-delete {
          display: inline-flex;
          align-items: center;
          margin: 0 8px;
          border-radius: 16px;
          background-color: @table-head-border;
        }
      }
    }

    &-content {
      width: 100%;
      min-height: calc(100vh - 220px);
      border-radius: 10px;
      background-color: @FO-Container-Fill1;
    }
  }
}

[data-theme='dark'] .@{prefix-cls} {
  & img {
    filter: brightness(0.8);
  }
}
</style>
