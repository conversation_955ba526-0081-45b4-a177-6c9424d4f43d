<template>
  <PageWrapper class="gitlab-group-tab" headerSticky @back="goBack">
    <template #title>
      <div class="flex items-center">
        Git审查列表：{{ branchInfo?.branch?.name || '-' }}
      </div>
    </template>
    <template #footer>
      <div class="flex flex-wrap-reverse items-center justify-between gap-y-4 pb-6">
        <LineTab v-model:activeTab="curActiveStatus" :tabList="tabList" showIcon>
          <template #item="{ item }">
            <div class="max-w-[220px] flex items-center">
              <Icon v-if="item.title !== '全部'" icon="carbon:dot-mark" :class="item.color" />
              <EllipsisText>{{ item.title }}</EllipsisText>
            </div>
          </template>
        </LineTab>
      </div>
    </template>
    <Spin :spinning="loadingReviewList || loadingReviewItem">
      <div v-if="renderList?.length" class="mb-[45px] flex flex-col gap-4">
        <ReviewRow
          v-for="(item, index) in renderList"
          :key="item?.ID"
          :data="item"
          :disabled="isDelete"
          @update="() => handleRowUpdate(item, index)"
        />
      </div>
      <div v-else class="bg-FO-Container-Fill1 mb-4 b-rd-[10px] p-4">
        <Empty :image="Empty.PRESENTED_IMAGE_SIMPLE" description="该项目无审查列表" />
      </div>
    </Spin>
    <template #rightFooter>
      <div v-if="pageInfo.totalNum" class="py-2">
        <Pagination
          v-model:current="pageInfo.page"
          v-model:pageSize="pageInfo.pageSize"
          size="small" :total="pageInfo.totalNum"
          showSizeChanger
          :pageSizeOptions="['10', '20', '50', '100']"
          :showTotal="(total) => `共 ${total} 条数据`"
          @change="handleChangePage"
        />
      </div>
    </template>
  </PageWrapper>
</template>

<script lang="ts" setup>
import { Empty, Pagination, Spin } from 'ant-design-vue';
import { computed, reactive, ref, watch } from 'vue';
import { useRouter } from 'vue-router';
import { useRouteParams, useRouteQuery } from '@vueuse/router';
import { getBranchesByBranchID, getReviewItem, getReviewResults } from '/@/api/page/gitlab';
import Icon from '/@/components/Icon';
import LineTab from '/@/components/LineTab';
import { PageWrapper } from '/@/components/Page';
import { useUserStore } from '/@/store/modules/user';
import { EllipsisText } from '../../../../components/EllipsisText';
import { PlatformEnterPoint } from '@hg-tech/oasis-common';
import { useLatestPromise } from '@hg-tech/utils-vue';
import ReviewRow from './ReviewRow.vue';
import { type GitlabReviewItem, ReviewStatus } from '../../../../api/page/model/gitlabModel.ts';

const userStore = useUserStore();
const router = useRouter();
const branchID = useRouteParams('branch_id', undefined, { transform: Number });
const depotID = useRouteParams('id', undefined, { transform: Number });
const isDelete = useRouteQuery('isDelete', undefined, { transform: (v) => v === '1' });
const curActiveStatus = ref<ReviewStatus>(ReviewStatus.Ready);

const tabList = [
  { color: 'opacity-0', title: '全部', name: null },
  { color: '!c-FO-Functional-Warning1-Default', title: '未处理', name: ReviewStatus.Ready },
  { color: '!c-FO-Functional-Success1-Default', title: '已通过', name: ReviewStatus.Approve },
  { color: '!c-FO-Functional-Error1-Default', title: '已拒绝', name: ReviewStatus.Refuse },
];

const pageInfo = reactive({
  page: 1,
  pageSize: 20,
  totalNum: 0,
});

const { data: listRes, execute: fetchReviewList, loading: loadingReviewList } = useLatestPromise(getReviewResults);
const { execute: fetchReviewItem, loading: loadingReviewItem } = useLatestPromise(getReviewItem);
const updatedItem = ref<{
  idx: number;
  data: GitlabReviewItem;
}>();
const renderList = computed(() => {
  const rawList = [...(listRes.value?.list || [])];
  if (updatedItem.value) {
    const foundIdx = rawList.findIndex((item) => item.ID === updatedItem.value?.data.ID);
    if (foundIdx > -1) {
      // 替换更新的节点
      rawList.splice(foundIdx, 1, updatedItem.value.data);
    } else {
      // 插入更新的节点
      rawList.splice(updatedItem.value.idx, 0, updatedItem.value.data);
    }
  }
  return rawList;
});
async function updateReviewList() {
  const res = await fetchReviewList(userStore.getProjectId, {
    page: pageInfo.page,
    pageSize: pageInfo.pageSize,
    repoID: depotID.value,
    branchID: branchID.value,
    reviewStatus: curActiveStatus.value,
  });
  pageInfo.totalNum = res?.total || 0;
}
watch([() => userStore.getProjectId, branchID, depotID, curActiveStatus], () => {
  pageInfo.page = 1;
  updateReviewList();
  updatedItem.value = undefined;
}, { immediate: true });

async function handleChangePage() {
  await updateReviewList();
  updatedItem.value = undefined;
}

const { data: branchInfo, execute: fetchBranchInfo } = useLatestPromise(getBranchesByBranchID);
watch([() => userStore.getProjectId, branchID], () => fetchBranchInfo(userStore.getProjectId, branchID.value), { immediate: true });

async function handleRowUpdate(item: GitlabReviewItem, idx: number) {
  fetchReviewItem(userStore.getProjectId, item.ID).then((data) => {
    if (data) {
      updatedItem.value = { idx, data };
    }
  });
  updateReviewList();
}

function goBack() {
  router.push({ name: PlatformEnterPoint.Gitlab });
}
</script>
