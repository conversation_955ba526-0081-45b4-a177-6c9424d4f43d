<template>
  <div>
    <a-collapse
      v-if="versionList?.length > 0"
      v-model:activeKey="collapseActiveKey"
      :class="`${prefixCls}__collapse`"
      expand-icon-position="right"
      accordion
      @change="handleVersionClick"
    >
      <a-collapse-panel
        v-for="(item, i) in versionList"
        :key="item.ID"
        :class="`${prefixCls}__collapse-panel`"
      >
        <template #header>
          <div>
            <div class="font-bold" />
            <span
              :class="`${prefixCls}__title cursor-pointer`"
              @click="handleVersionClick(item.ID)"
            >
              {{ item.version }}
            </span>
            <ATag v-if="i === 0" color="orange">
              最新版本
            </ATag>
            <ATag v-if="curVersionID === item.ID" color="blue">
              当前版本
            </ATag>
            <div :class="`${prefixCls}__author`">
              作者：{{ formatNickName(item.author) }}
            </div>
          </div>
          <div>
            <span :class="`${prefixCls}__time`">{{
              formatTISOToDate(item.UpdatedAt || item.CreatedAt)
            }}</span>
          </div>
        </template>
        <MarkdownViewer v-if="item.releaseNote" :value="item.releaseNote" />
        <AEmpty v-else :image="emptyImg" description="暂无发版公告" />
      </a-collapse-panel>
    </a-collapse>
    <AEmpty v-else :image="emptyImg" description="暂无该平台版本信息" />
  </div>
</template>

<script lang="ts" setup>
import { Empty as AEmpty, Tag as ATag } from 'ant-design-vue';
import { ref, unref } from 'vue';
import { useRouter } from 'vue-router';
import type { ToolkitVersionListItem } from '/@/api/page/model/systemModel';
import { getToolkitVersionListByPage } from '/@/api/page/system';
import { MarkdownViewer } from '/@/components/Markdown';
import { formatNickName } from '/@/hooks/system/useUserList';
import { useDesign } from '/@/hooks/web/useDesign';
import { formatTISOToDate } from '/@/utils/dateUtil';

defineOptions({
  prefixCls: 'ToolkitVersion',
});

const props = defineProps({
  toolID: {
    type: Number,
    default: () => null,
  },
  curVersionID: {
    type: Number,
    default: () => null,
  },
});

const { prefixCls } = useDesign('toolkit-detail-version');
const versionList = ref([] as ToolkitVersionListItem[]);
const { currentRoute, replace } = useRouter();
const versionId = Number(unref(currentRoute).query.v);
const platform = Number(unref(currentRoute).query.platform) || undefined;
const emptyImg = AEmpty.PRESENTED_IMAGE_SIMPLE;
const collapseActiveKey = ref<number[]>([]);

async function getToolkitVersionList(id: number) {
  const { list } = await getToolkitVersionListByPage(id, {
    page: 1,
    pageSize: 999,
    platform,
  });

  if (list?.length > 0) {
    versionList.value = list;

    collapseActiveKey.value = [versionId || (list?.[0].ID as number)];
  }
}

function handleVersionClick(ID?: number) {
  if (ID) {
    replace({ query: { tab: 'Version', v: ID, platform } });
  }
}

getToolkitVersionList(unref(props.toolID));
</script>

<style lang="less">
@prefix-cls: ~'hypergryph-toolkit-detail-version';
.@{prefix-cls} {
  margin: 0 20px;
  border-bottom: 1px solid;
  border-bottom-color: @table-border-color;

  &__collapse {
    &-panel {
      display: block;
    }
  }

  &__item {
    position: relative;
  }

  &__title {
    font-size: 18px;
    margin-right: 8px;

    &:hover {
      color: @FO-Brand-Primary-Hover;
      text-decoration: underline;
    }
  }

  &__action {
    display: inline-block;
    padding: 0 16px;

    &:nth-child(1),
    &:nth-child(2) {
      border-right: 1px solid rgb(206 206 206 / 40%);
    }

    &-icon {
      margin-right: 3px;
    }
  }

  &__author {
    margin-top: 5px;
  }

  &__time {
    position: absolute;
    right: 40px;
    bottom: 28px;
  }
}

html[data-theme='light'] .@{prefix-cls} {
  &__time,
  &__author {
    color: rgb(0 0 0 / 45%);
  }
}
</style>
