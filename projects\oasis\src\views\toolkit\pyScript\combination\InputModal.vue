<template>
  <BasicModal @register="registerModal" @ok="handleSubmit">
    <template #title>
      <div class="flex items-end">
        <div>输入参数: {{ name }}</div>
        <div class="ml-3 c-FO-Brand-Primary-Default text-xs"> 类型: {{ type }} </div>
        <div></div>
      </div>
    </template>
    <BasicForm @register="registerForm" />
  </BasicModal>
</template>
<script lang="ts">
  import { defineComponent, ref } from 'vue';
  import { choiceFormSchema, inputFormSchema, inputFormTypeOptions } from './combination.data';
  import { PyScriptComponentListItem } from '/@/api/page/model/pyScriptModel';
  import { BasicForm, useForm } from '/@/components/Form';
  import { BasicModal, useModalInner } from '/@/components/Modal';

  export default defineComponent({
    name: 'PythonScriptCombinationInputModal',
    components: { BasicModal, BasicForm },
    emits: ['success', 'register'],
    setup(_, { emit }) {
      const name = ref();
      const type = ref();
      const item = ref<PyScriptComponentListItem>();
      const index = ref<number>(0);
      const list = ref<PyScriptComponentListItem[]>([]);

      const [registerForm, { resetFields, setFieldsValue, validate, updateSchema, setProps }] =
        useForm({
          labelWidth: 120,
          schemas: inputFormSchema,
          showActionButtonGroup: false,
          baseColProps: {
            span: 20,
          },
        });

      const [registerModal, { setModalProps, closeModal }] = useModalInner(async (data) => {
        await resetFields();
        name.value = data.name;
        type.value = data.type;
        item.value = data.item;
        index.value = data.index;
        list.value = data.list;
        if (type.value === 'choice') {
          await setProps({
            schemas: choiceFormSchema,
          });
        } else {
          await setProps({
            schemas: inputFormSchema,
          });
        }
        if (index.value === 0) {
          await updateSchema({
            field: 'showType',
            dynamicDisabled: ['pathfile', 'pathfolder'].includes(type.value),
            componentProps: {
              options: inputFormTypeOptions.filter((item) => !item.value.includes('Result')),
            },
          });
        } else {
          let preOutList: any[] = [
            { label: '【序号.元件名】输出名', value: 'msg', disabled: true },
          ];
          const preScript = list.value[index.value - 1];
          const preOut = preScript.configJson?.output;
          for (const key in preOut) {
            if (preOut[key]?.type === type.value) {
              preOutList.push({
                label: `【${index.value}.${preScript.name}】${key}`,
                value: `@${index.value - 1}@${key}`,
              });
            }
          }
          let allPreOutList: any[] = [
            { label: '【序号.元件名】输出名', value: 'msg', disabled: true },
          ];
          for (let i = 0; i < index.value; i++) {
            const preScript = list.value[i];
            const preOut = preScript.configJson?.output;
            for (const key in preOut) {
              if (preOut[key]?.type === type.value) {
                allPreOutList.push({
                  label: `【${i + 1}.${preScript.name}】${key}`,
                  value: `@${i}@${key}`,
                });
              }
            }
          }
          if (allPreOutList.length === 1) {
            allPreOutList = [];
          }
          await updateSchema([
            {
              field: 'showType',
              dynamicDisabled: ['pathfile', 'pathfolder'].includes(type.value),
              componentProps: {
                options: inputFormTypeOptions,
              },
            },
            {
              field: 'prevResult',
              componentProps: {
                options: preOutList,
                placeholder:
                  preOutList.length > 1 ? '请选择上一个元件的输出' : '上一元件输出类型不匹配!',
                disabled: preOutList.length <= 1,
              },
              defaultValue: preOutList.length > 1 ? preOutList[1].value : null,
            },
            {
              field: 'allPrevResult',
              componentProps: {
                options: allPreOutList,
                placeholder:
                  allPreOutList.length > 1
                    ? '请选择一个前序元件的输出'
                    : '所有前序元件输出类型均不匹配!',
                disabled: allPreOutList.length <= 1,
              },
            },
          ]);
        }
        if (['listint', 'liststr'].includes(type.value)) {
          await updateSchema({
            field: 'static',
            component: 'Select',
            componentProps: {
              mode: 'tags',
              placeholder: '请输入参数值, 回车添加',
            },
          });
        } else if (['int', 'float'].includes(type.value)) {
          await updateSchema({
            field: 'static',
            component: 'InputNumber',
            componentProps: {
              precision: type.value === 'int' ? 0 : undefined,
            },
          });
        } else if (type.value === 'bool') {
          await updateSchema({
            field: 'static',
            component: 'RadioButtonGroup',
            componentProps: {
              options: [
                { label: 'True', value: true },
                { label: 'False', value: false },
              ],
            },
          });
        } else {
          await updateSchema({
            field: 'static',
            component: 'Input',
            componentProps: {
              placeholder: '请输入指定参数值',
            },
          });
        }
        await setFieldsValue({
          ...data.item.inputObject?.[data.name]?.data,
        });

        setModalProps({ confirmLoading: false });
      });

      async function handleSubmit() {
        try {
          const values = await validate();
          setModalProps({ confirmLoading: true });
          emit('success', { name: name.value, type: type.value }, values);
          closeModal();
          await resetFields();
        } catch (error) {
        } finally {
          setModalProps({ confirmLoading: false });
        }
      }

      return {
        name,
        type,
        registerModal,
        registerForm,
        handleSubmit,
      };
    },
  });
</script>
