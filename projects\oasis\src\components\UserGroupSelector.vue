<template>
  <TreeSelect
    v-model:value="value"
    treeCheckable
    showCheckedStrategy="SHOW_PARENT"
    :treeData="renderNode"
    :loading="loadingUserGroupList"
    showSearch
    allowClear
    showArrow
    mode="multiple"
    placeholder="请选择干员组"
  >
    <template #tagRender="{ label, closable, onClose }">
      <GroupUsersPopover :groupName="label">
        <Tag :closable="closable" class="!text-sm" @close="onClose">
          {{ label }}
        </Tag>
      </GroupUsersPopover>
    </template>
  </TreeSelect>
</template>

<script setup lang="ts">
import { Tag, TreeSelect } from 'ant-design-vue';
import GroupUsersPopover from './GroupUsersPopover';
import { useSysUserGroupList } from '../hooks/useUserList.ts';
import { computed } from 'vue';
import { asyncComputed } from '@vueuse/core';
import { useUserStore } from '../store/modules/user.ts';
import { getP4ServerList } from '../hooks/useP4.ts';

const value = defineModel<string[]>('value');

const userStore = useUserStore();
const serverList = asyncComputed(() => getP4ServerList(userStore.getProjectId));
const { userGroupList, loadingUserGroupList } = useSysUserGroupList(computed(() => serverList.value?.[0]?.ID));
const renderNode = computed(() => userGroupList.value?.map((e) => ({
  label: e.name,
  value: e.name,
})));
</script>
