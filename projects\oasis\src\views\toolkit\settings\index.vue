<template>
  <div :class="prefixCls">
    <div :class="`${prefixCls}__left`">
      <div :class="`${prefixCls}__left-back-btn`" @click="goBack()">
        <Icon icon="ph:caret-left-bold" :size="16" />
        返回工具商店
      </div>

      <div :class="`${prefixCls}__left-tabs`">
        <div
          v-for="tab in tabList"
          :key="tab.name"
          :class="`${prefixCls}__left-tabs-item`"
          :active="curTab === tab.name"
          @click="curTab = tab.name"
        >
          <Icon :icon="tab.icon" class="mr-1" />
          {{ tab.name }}
        </div>
      </div>
    </div>
    <div :class="`${prefixCls}__right`">
      <component :is="currentComponent" />
    </div>
  </div>
</template>

<script lang="ts" setup name="GamePackageSettings">
import { computed, ref } from 'vue';
import { useRoute } from 'vue-router';
import BranchSetting from './branch/index.vue';
import ClassificationSetting from './classification/index.vue';
import VersionSetting from './version/index.vue';
import Icon from '/@/components/Icon';
import { useDesign } from '/@/hooks/web/useDesign';
import { useGo } from '/@/hooks/web/usePage';
import { useUserStore } from '../../../store/modules/user.ts';

const { prefixCls } = useDesign('toolkit-package-settings');
const go = useGo();
const route = useRoute();

const userStore = useUserStore();
const tabList = computed(() => (userStore.isSuperAdmin
  ? [
    {
      name: '工具配置',
      component: BranchSetting,
      icon: 'icon-park-outline:tool',
    },
    {
      name: '版本管理',
      component: VersionSetting,
      icon: 'icon-park-outline:tag-one',
    },
    {
      name: '分类配置',
      component: ClassificationSetting,
      icon: 'tabler:layout-grid',
    },
  ]
  : [
    {
      name: '工具配置',
      component: BranchSetting,
      icon: 'icon-park-outline:tool',
    },
    {
      name: '版本管理',
      component: VersionSetting,
      icon: 'icon-park-outline:tag-one',
    },
  ]));

const curTab = ref<string>(tabList.value[0].name);

const currentComponent = computed(() => {
  return tabList.value.find((tab) => tab.name === curTab.value)!.component;
});

function goBack() {
  go({ name: 'Toolkit', query: { cardList: route.query.cardList } });
}
</script>

<style lang="less">
@prefix-cls: ~'hypergryph-toolkit-package-settings';
.@{prefix-cls} {
  display: flex;
  position: relative;
  overflow: auto;

  &__left {
    display: flex;
    position: sticky;
    top: 0;
    flex-direction: column;
    width: 180px;
    margin-left: 16px;
    padding: 16px 16px 16px 0;

    &-back-btn {
      margin-bottom: 16px;
      padding: 8px;
      border-radius: 8px;
      background-color: @FO-Container-Fill1;
      font-size: 16px;
      font-weight: bold;
      cursor: pointer;
    }

    &-tabs {
      &-item {
        display: flex;
        align-items: center;
        padding: 12px;
        border-radius: 16px;
        font-weight: bold;
        cursor: pointer;
        user-select: none;

        &[active='true'],
        &:hover {
          background-color: @FO-Container-Fill1;
        }

        &:not(:last-child) {
          margin-bottom: 16px;
        }
      }
    }
  }

  &__right {
    flex: 1;
    width: 0;
    margin-right: 16px;
    padding: 16px 0;
  }
}
</style>
