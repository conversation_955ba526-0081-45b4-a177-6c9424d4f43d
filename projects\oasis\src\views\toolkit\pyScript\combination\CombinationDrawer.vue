<template>
  <BasicDrawer
    :title="getTitle"
    width="100%"
    :class="prefixCls"
    showFooter
    okText="保存"
    @register="registerDrawer"
    @ok="handleSubmit"
  >
    <template #title>
      <div class="flex items-center justify-center text-lg">
        {{ getTitle }}
        <APopconfirm v-if="isUpdate" title="确认删除该指令吗?" @confirm="handleDelete">
          <a-button shape="round" danger class="ml-2" size="small">
            删除该指令
          </a-button>
        </APopconfirm>
      </div>
    </template>
    <div class="flex flex-col lg:flex-row">
      <div class="min-w-[400px] w-full lg:w-2/5 xl:w-1/2">
        <BasicForm @register="registerForm">
          <template #name="{ model, field }">
            <div :class="`${prefixCls}__name-input`" :disabled="isUpdate">
              <a-input
                v-model:value="model[field]"
                placeholder="请输入指令名称"
                :bordered="false"
                class="!w-9/10"
                :disabled="isUpdate"
              />
              <ColorPopover
                v-model:value="model.color"
                title="指令"
                class="absolute right-[6px] top-[6px]"
              />
            </div>
          </template>
          <template #version="{ model, field }">
            v{{ model[field] }}.0
          </template>
          <template #toAllProjects="{ model, field }">
            <ARadioGroup
              v-model:value="model[field]"
              optionType="button"
              :options="[
                { label: '全员可获取', value: true },
                { label: '仅选择的项目组可获取', value: false },
              ]"
            />
            <AFormItemRest>
              <div class="mt-2 -mb-4">
                <ACheckboxGroup
                  v-if="!model.toAllProjects"
                  v-model:value="model.projectIDs"
                  :options="projectListOptions"
                />
              </div>
            </AFormItemRest>
          </template>
        </BasicForm>
      </div>
      <div class="w-full flex lg:w-3/5 xl:w-1/2">
        <div class="w-1/2">
          <div class="ml-3 flex items-center line-height-[24px]">
            指令内容
            <div v-if="canUpgradeList?.length" class="ml-4 flex items-center text-xs">
              {{ canUpgradeList?.length }}个元件可升级
              <a-button
                size="small"
                class="ml-1 border-solid !border-[#FFDA55] !bg-[#FFDA55] !text-xs !c-FO-Content-Text1"
                @click="handleUpgrade(undefined, true)"
              >
                全部升级
              </a-button>
            </div>
          </div>
          <div :class="`${prefixCls}__combination-list`">
            <PythonScriptComponentItem
              v-for="(item, i) in newComponentList"
              :key="item.tempID"
              :item="item"
              :data-id="item.ID"
              :index="i"
              :list="newComponentList"
              showManage
              @delete="handleComponentDelete"
              @input-change="handleInputChange"
              @upgrade="handleUpgrade"
            />
          </div>
        </div>
        <div class="w-1/2">
          <div class="ml-3 line-height-[24px]">
            元件库
          </div>
          <div :class="`${prefixCls}__component-list`">
            <PythonScriptComponentItem
              v-for="(item, i) in showComponentList"
              :key="item.ID"
              :item="item"
              :data-id="item.ID"
              :index="i"
              :list="newComponentList"
            />
          </div>
        </div>
      </div>
    </div>
  </BasicDrawer>
</template>

<script lang="ts" setup name="CombinationDrawer">
import {
  type CheckboxOptionType,
  Checkbox as ACheckbox,
  Form as AForm,
  Popconfirm as APopconfirm,
  Radio as ARadio,
} from 'ant-design-vue';
import { cloneDeep, has, intersection, sortedUniq } from 'lodash-es';
import { computed, nextTick, ref, unref, watchEffect } from 'vue';
import { usePyScriptComponent } from '../component/hook';
import PythonScriptComponentItem from './ComponentItem.vue';
import { environTypeOptions, formSchema } from './combination.data';
import type {
  PyScriptCombinationListItem,
  PyScriptComponentListItem,
  PyScriptListItem,
  PyScriptListItemModel,
} from '/@/api/page/model/pyScriptModel';
import {
  addPyScriptCombination,
  deletePyScriptCombination,
  editPyScriptCombination,
} from '/@/api/page/pyScript';
import { ColorPopover, defaultColorList } from '/@/components/ColorPopover';
import { BasicDrawer, useDrawerInner } from '/@/components/Drawer';
import { BasicForm, useForm } from '/@/components/Form';
import { formatNickName } from '/@/hooks/system/useUserList';
import { useDesign } from '/@/hooks/web/useDesign';
import { useMessage } from '/@/hooks/web/useMessage';
import { useSortable } from '/@/hooks/web/useSortable';
import { useUserStore } from '/@/store/modules/user';
import { buildNumberUUID } from '/@/utils/uuid';
import { useCachedProjectList } from '../../../../hooks/useProjects.ts';

const emit = defineEmits(['success', 'register']);

const ARadioGroup = ARadio.Group;
const AFormItemRest = AForm.ItemRest;
const ACheckboxGroup = ACheckbox.Group;

const { prefixCls } = useDesign('python-script-combination-drawer');
const { createMessage, createConfirm } = useMessage();
const userStore = useUserStore();
const isUpdate = ref(false);
const isCopy = ref(false);
const editId = ref();
const curPlatform = ref<string[]>([]);
const { componentList, getComponentList } = usePyScriptComponent();
const newComponentList = ref<PyScriptComponentListItem[]>([]);
const showComponentList = ref<PyScriptComponentListItem[]>([]);
const projectListOptions = ref<CheckboxOptionType[]>([]);
const hasInputCleared = ref(false);
const { data: projectList } = useCachedProjectList({ loadImmediately: true });

const canUpgradeList = computed(() => {
  return newComponentList.value.filter((e) => e.isHistory);
});

watchEffect(() => {
  showComponentList.value = componentList.value.filter(
    (item) => intersection(curPlatform.value, item.platform).length > 0,
  );
});

const getTitle = computed(
  () => `${!unref(isUpdate) ? (!unref(isCopy) ? '添加' : '复制') : '配置'}指令`,
);

const [registerForm, { resetFields, setFieldsValue, validate, updateSchema, scrollToField }]
    = useForm({
      labelWidth: 120,
      schemas: formSchema,
      showActionButtonGroup: false,
      baseColProps: {
        span: 22,
      },
    });

// 初始化元件数据
function initItemData(item?: PyScriptComponentListItem, index?: number, originInput?: PyScriptListItem) {
  if (!item) {
    return;
  }
  item!.tempID = buildNumberUUID();
  if (!item.inputObject) {
    item!.inputObject = {};
    if (item?.configJson?.input) {
      for (const key in item.configJson.input) {
        const findEnvirons = environTypeOptions.find((e) => e.value === key);
        // 有原先值则不初始化
        item!.inputObject[key] = originInput?.[key] ?? {
          input: {
            name: key,
            type: findEnvirons ? 'environ' : item?.configJson?.input?.[key]?.type,
          },
          data: {
            showType: findEnvirons ? 'environ' : 'input',
            input: findEnvirons ? undefined : '',
            environ: findEnvirons ? findEnvirons.value : undefined,
          },
          index,
        };
      }
    }
  } else {
    for (const key in item.inputObject) {
      const data = item.inputObject[key]?.data;
      if (data && ['allPrevResult', 'prevResult'].includes(data.showType!)) {
        hasInputCleared.value = true;
      }
    }
  }
  return item;
}

function clearAllPrevResult() {
  createConfirm({
    title: '提示',
    iconType: 'warning',
    content: '元件中存在 包含前序输出 的输入, 是否需要清空?',
    onOk: () => {
      newComponentList.value.forEach((e) => {
        for (const key in e.inputObject) {
          const data = e.inputObject[key]?.data;
          if (data && ['allPrevResult', 'prevResult'].includes(data.showType!)) {
            data[data.showType!] = '';
            data.showType = 'input';
          }
        }
      });
    },
    okText: '清空',
  });
}

function initDrag() {
  nextTick(() => {
    const el = document.querySelector(`.${prefixCls}__combination-list`) as HTMLElement;
    const { initSortable, sortableRef } = useSortable(el, {
      group: {
        name: 'combination',
      },
      animation: 150,
      dataIdAttr: 'data-id',
      onSort: async (event) => {
        newComponentList.value
            = sortableRef.value.toArray()?.map((ID: string, index: number) => {
            const item
                = cloneDeep(newComponentList.value.find((item) => item.ID === Number(ID)))
                || cloneDeep(componentList.value.find((item) => item.ID === Number(ID)));

            return initItemData(item, index);
          }) || [];
        if (hasInputCleared.value) {
          hasInputCleared.value = false;
          clearAllPrevResult();
        }
        if (event.pullMode === 'clone') {
          event.to.removeChild(event.item);
        }
      },
    });
    initSortable();

    const componentEl = document.querySelector(`.${prefixCls}__component-list`) as HTMLElement;
    const { initSortable: initComponentSortable } = useSortable(componentEl, {
      group: {
        name: 'combination',
        pull: 'clone',
        put: false,
      },
      dataIdAttr: 'data-id',
      animation: 150,
      sort: false,
    });
    initComponentSortable();
  });
}

function handleComponentDelete(item) {
  newComponentList.value = newComponentList.value.filter((e) => e.tempID !== item.tempID);
  newComponentList.value.forEach((e) => {
    for (const key in e.inputObject) {
      const data = e.inputObject[key]?.data;
      if (data && ['allPrevResult', 'prevResult'].includes(data.showType!)) {
        hasInputCleared.value = true;
      }
    }
  });
  if (hasInputCleared.value) {
    hasInputCleared.value = false;
    clearAllPrevResult();
  }
}

function getInputObject(record: PyScriptCombinationListItem, item: PyScriptComponentListItem, index: number) {
  const inputObject = {};
  const sc = record.configJson?.scripts?.[index];
  for (const key in sc?.[1]) {
    if (has(sc[1], key)) {
      if (has(sc[1][key], 'options')) {
        const element = sc[1][key];
        inputObject[key] = {
          input: {
            name: key,
            type: 'choice',
          },
          data: {
            input: element.type.split('@')[1],
            options: element.options,
            value: element.value,
          },
          index,
        };
      } else {
        const element = sc[1][key];
        const showType = element.type.startsWith('input')
          ? 'input'
          : element.type === 'result' && element.value.split('@')[0] === (index - 1).toString()
            ? 'prevResult'
            : element.type === 'result'
              ? 'allPrevResult'
              : element.type;
        inputObject[key] = {
          input: {
            name: key,
            type: item.configJson?.input?.[key]?.type,
          },
          data: {
            showType,
            input: showType === 'input' ? element.type.split('@')[1] : undefined,
            environ: showType === 'environ' ? element.value : undefined,
            static: showType === 'static' ? element.value : undefined,
            prevResult: showType === 'prevResult' ? element.value : undefined,
            allPrevResult: showType === 'allPrevResult' ? element.value : undefined,
          },
          index,
        };
      }
    }
  }
  return inputObject;
}

const [registerDrawer, { setDrawerProps, closeDrawer }] = useDrawerInner(async (data) => {
  await resetFields();
  isUpdate.value = !!data?.isUpdate;
  isCopy.value = !!data?.isCopy;
  editId.value = data?.record?.ID;
  curPlatform.value = [];
  await getComponentList();
  projectListOptions.value
      = (projectList.value?.map((e) => ({
      label: e.name,
      value: e.ID,
    })) as CheckboxOptionType[]) || [];
  initDrag();
  updateSchema({
    field: 'platform',
    componentProps: {
      onChange: (val: string[]) => {
        curPlatform.value = val;
      },
    },
  });
  if (unref(isUpdate) || unref(isCopy)) {
    await setFieldsValue({
      ...data.record,
      version: unref(isUpdate) ? data.record.version : 1,
      color: defaultColorList[data.record.marker > 0 ? data.record.marker - 1 : 0],
      nickName: formatNickName(unref(isCopy) ? userStore.getUserInfo : data.record?.author),
      projectIDs: data.record?.projects?.map((e) => e.ID),
    });
    curPlatform.value = data.record.platform;
    newComponentList.value = data.record.allComponents.map((item, index) => {
      item.tempID = buildNumberUUID();
      item.inputObject = getInputObject(data.record, item, index);
      return item;
    }) as PyScriptComponentListItem[];
  } else {
    await setFieldsValue({
      nickName: formatNickName(userStore.getUserInfo),
      toAllProjects: true,
    });
    newComponentList.value = [];
  }

  setDrawerProps({ confirmLoading: false });
});

function getInputVal(type: string) {
  switch (type) {
    case 'bool':
      return false;
    case 'listint':
    case 'liststr':
      return [];
    default:
      return '';
  }
}

function getInputData(input: PyScriptListItemModel, data: PyScriptListItemModel) {
  if (input.type === 'choice') {
    return {
      type: `input@${data.input || ''}`,
      options: data.options || [],
      value: data.value || '',
    };
  } else {
    switch (data.showType) {
      case 'input':
        return {
          type: `input@${data.input || ''}`,
          value: getInputVal(input.type!),
        };
      case 'prevResult':
      case 'allPrevResult':
        return {
          type: 'result',
          value: data[data.showType],
        };
      default:
        return {
          type: data.showType,
          value: data[data.showType!],
        };
    }
  }
}

function handleInputChange(record: Recordable) {
  if (!newComponentList.value[record.index].inputObject) {
    newComponentList.value[record.index].inputObject = {};
  }
  newComponentList.value[record.index].inputObject![record.input.name] = record;
}

async function handleSubmit() {
  try {
    const values = await validate();
    if (!newComponentList.value?.length) {
      createMessage.warning('请至少添加一个元件');
      return;
    }
    const tempComponentList = cloneDeep(newComponentList.value);
    setDrawerProps({ confirmLoading: true });
    const markerIndex = defaultColorList.findIndex((e) => e === values.color);
    const submitData = {
      ...values,
      marker: markerIndex > -1 ? markerIndex + 1 : 0,
      color: undefined,
      projects: values.projectIDs?.map((e) => ({
        ID: e,
      })),
      projectIDs: undefined,
      components: sortedUniq(
        tempComponentList.map((item) => ({
          ID: item.ID,
        })),
      ),

      configJson: {
        scripts: tempComponentList.map((e) => {
          for (const key in e.inputObject) {
            if (has(e.inputObject, key)) {
              const element = e.inputObject[key];
              e.inputObject[key] = getInputData(
                element.input as PyScriptListItemModel,
                element.data!,
              );
            }
          }
          return [e.name, e.inputObject || {}];
        }),
      },
      version: unref(isUpdate) ? values.version + 1 : 1,
    };
    let hasError = false;
    if (!unref(isUpdate)) {
      const res = await addPyScriptCombination(submitData);
      hasError = res?.code === 7;
      !hasError && createMessage.success(isCopy.value ? '复制成功' : '添加成功');
    } else if (unref(editId)) {
      const res = await editPyScriptCombination(submitData, unref(editId));
      hasError = res?.code === 7;
    }
    if (!hasError) {
      emit('success');
      closeDrawer();
      await resetFields();
    } else {
    }
  } catch (error: any) {
    if (error?.errorFields?.length) {
      scrollToField(error?.errorFields[0]?.name[0], {
        behavior: 'smooth',
        block: 'center',
      });
    }
  } finally {
    setDrawerProps({ confirmLoading: false });
  }
}

async function handleDelete() {
  await deletePyScriptCombination(editId.value!);
  closeDrawer();
  emit('success');
}

function handleUpgrade(curItem?: PyScriptComponentListItem, isAll = false) {
  newComponentList.value
      = newComponentList.value?.map((e, index) => {
      if (!e.isHistory) {
        return e;
      }
      const isInCanUpgradeList = canUpgradeList.value.find((item) => item.ID === e.ID);
      if ((isAll && isInCanUpgradeList) || e.ID === curItem?.ID) {
        const item = cloneDeep(
          componentList.value.find(
            (item) => item.ID === (isAll ? e.sourceCompID : curItem!.sourceCompID),
          ),
        );
        return initItemData(item, index, e.inputObject)!;
      } else {
        return e;
      }
    }) || [];
  createMessage.success('升级成功, 保存后生效');
}
</script>

<style lang="less">
@prefix-cls: ~'hypergryph-python-script-combination-drawer';
.@{prefix-cls} {
  &__combination-list,
  &__component-list {
    height: calc(100vh - 200px);
    border: 1px solid @FO-Container-Stroke1;
    border-radius: 4px;
    padding: 10px;
    overflow-y: auto;
    margin: 8px;

    & > div {
      margin-bottom: 16px;

      & > .ant-card {
        cursor: move !important;
      }
    }
  }

  &__combination-list {
    & > div:not(:last-of-type) {
      position: relative;

      &::after {
        content: '';
        position: absolute;
        bottom: -16px;
        left: 50%;
        width: 3px;
        height: 16px;
        background-color: @FO-Brand-Primary-Default;
        z-index: 1;
      }
    }
  }

  &__name-input {
    padding: 0;
    margin: 0;
    flex: 1;
    position: relative;
    background-color: @FO-Container-Fill0;
    border-radius: 6px;
    border: 1px solid @FO-Container-Stroke2;
    box-sizing: border-box;
    transition: all 0.2s;

    &:not([disabled='true']):hover {
      border-color: #4096ff;
      border-inline-end-width: 1px;
      filter: drop-shadow(0 0 2px #9ac5f9);
    }

    &[disabled='true'] {
      background-color: rgba(0, 0, 0, 0.04);
    }
  }
}
</style>
