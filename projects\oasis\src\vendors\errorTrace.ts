import { ModalError } from '@hg-tech/utils-vue';
import { message } from 'ant-design-vue';
import { getDefaultErrorMsg } from '@hg-tech/oasis-common';
import type { AxiosError } from 'axios';
import axios from 'axios';
import type { App } from 'vue';
import type { BasicResult } from '../api/model/baseModel';
import { ExpectedError } from '../constants/error';
import * as Sentry from '@sentry/vue';

export function initSentry(app: App<Element>) {
  if (import.meta.env.PROD) {
    Sentry.init({
      app,
      environment: import.meta.env.VITE_BUILD_MODE,
      release: import.meta.env.VITE_BUILD_VERSION,
      dsn: 'https://<EMAIL>/142',
      integrations: [
        Sentry.browserTracingIntegration(),
        Sentry.replayIntegration({
          /**
           * FIXME 在 @sentry/vue@9.32.0及之前 + Chrome138版本之后会出现严重的卡顿，暂时限制 mutationLimit 以规避卡死问题
           */
          mutationLimit: 150,
          maskAllText: false,
          blockAllMedia: false,
        }),
        Sentry.browserApiErrorsIntegration({
          setTimeout: true,
          setInterval: true,
          requestAnimationFrame: true,
          XMLHttpRequest: true,
          eventTarget: true,
        }),
      ],
      replaysSessionSampleRate: 0.1,
      replaysOnErrorSampleRate: 1.0,
      tracesSampleRate: 0.2,

      beforeSend(event, hint) {
        const error = hint.originalException as Error | undefined;
        if (!error || error instanceof ExpectedError || (error as ExpectedError)?.isExpectedError) {
          return null;
        }

        event.tags = {
          UserAgent: navigator.userAgent,
          ...event.tags,
        };

        if (typeof (error as Error).message === 'string' && (error as Error).message.includes('Failed to fetch dynamically imported module')) {
          message.warning(`平台已发布新版本，请刷新页面以获取最新版本吧~`);
          return null;
        }

        // 弹窗的报错，判断error是否是ModalError类型，不用上报
        if (error instanceof ModalError) {
          return null;
        }
        // ---- is message error ----
        if (typeof error === 'string') {
          return event;
        }

        // ---- is ERROR ----
        switch (error.message) {
          case 'Network Error': // offline error
            return null;
        }

        switch (error.name) {
          case 'AbortError': // video abort error
          case 'NotAllowedError': // video not allowed autoplay error
          case 'ChunkLoadError': // chunk load error
            return null;
        }

        // ---- is AxiosError ----
        if ((error as AxiosError)?.isAxiosError) {
          const axiosError = error as AxiosError<BasicResult>;
          const res = axiosError.response;

          const statusCode = res?.data?.code ?? res?.status;
          if (statusCode === 401) {
          // 不上报未登录
            return null;
          }
          if (res) {
          // 根据返回值格式化上报
            return {
              ...event,
              fingerprint: ['AxiosError', String(statusCode)],
              message: statusCode ? `[${statusCode}] ${getDefaultErrorMsg(res.data)}` : event.message,
              tags: {
                ...event.tags,
                url: axiosError.config?.url,
              },
            } as Sentry.ErrorEvent;
          }

          // 保底上报
          return event;
        }
        // ---- is CancelError ----
        if (axios.isCancel(error)) {
          return null;
        }
        return event;
      },
    });
  }
}
