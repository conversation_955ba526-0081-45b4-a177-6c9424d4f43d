import type {
  FormItemRule,
  SelectOption,
} from 'naive-ui';
import type { IFormImageGroupInstance } from './form-image-group';
import type {
  AiGenerImageForm,
  RadioType,
} from '@/models/ai-image';
import {
  NAlert,
  NButton,
  NCollapse,
  NCollapseItem,
  NForm,
  NFormItem,
  NH3,
  NIcon,
  NInput,
  NInputNumber,
  NRadio,
  NRadioGroup,
  NSelect,
  NText,
  useThemeVars,
} from 'naive-ui';
import { defineComponent, nextTick, onMounted, ref, watch } from 'vue';
import { RadioArray } from '../../config';
import { useImageForm } from '../../hook';
import { ImageFormFooter } from './form-footer';
import { ImageFormHeader } from './form-header';
import { FormImageGroup } from './form-image-group';
import { FormPreset } from './form-preset';
import { FormPrompt } from './form-prompt';
import {
  InformationCircleOutline,
  Random,
  ReloadOutline,
  ScaleLocked,
  ScaleUnlock,
} from '@/common/components/svg-icons';
import { TooltipIcon } from '@/common/components/tooltip-icon';
import { GuideType, useGuideHook } from '@/common/hooks';
import { isNumber } from '@/common/utils/types';
import {
  getRandomInteger,
  getWidthAndHeightFromRadio,
  imageUrlToBase64,
} from '@/common/utils/utils';
import {
  GenrateMode,
  ImageType,
} from '@/models/ai-image';
import CustomError from '@/common/utils/custom-error';
import deepEqual from 'fast-deep-equal';
import { cloneDeep } from 'lodash';

const ImageForm = defineComponent({
  emits: ['formChange'],
  setup(_, { emit }) {
    const themeVars = useThemeVars();
    const { formRef, loading, formValue, formHookInit } = useImageForm();
    const imageGroupRef = ref<IFormImageGroupInstance>();
    const { addStep } = useGuideHook();
    const imageModuleOpen = ref<string[]>([]);
    let prevSnapshot: AiGenerImageForm;

    const rule: FormItemRule = {
      validator: () => {
        if (!formValue.value.customRadio.every(isNumber)) {
          return new CustomError('宽高比数值必须为数字', false);
        } else {
          const [width, height] = formValue.value.customRadio.map(Number);
          if (width === 0 || height === 0) {
            return new CustomError('宽高比例不能为0', false);
          } else if (width < 0 || height < 0) {
            return new CustomError('宽高比例输入值不能小于0', false);
          } else if (width / height < 1 / 3 || width / height > 3) {
            return new CustomError('宽高比范围为1:3~3:1', false);
          } else {
            return true;
          }
        }
      },
      trigger: 'input',
    };

    const renderSizeLabel = (option: SelectOption) => {
      const { width, height } = getWidthAndHeightFromRadio(
        option.label as RadioType,
        12,
        'min',
      );
      return option
        ? (
          <div class="inline-block flex-c-start">
            <div class="mr-8px w-30px flex-c-center">
              <div
                class="h-[var(--height)] w-[var(--width)] b-2px rd-3px"
                style={{
                  '--width': `${width}px`,
                  '--height': `${height}px`,
                  'border-color': themeVars.value.textColor3,
                }}
              />
            </div>
            <NText class="w-50px text-center">{option.label}</NText>
          </div>
        )
        : null;
    };

    const handleSizeLock = () => {
      formValue.value.sizeLock = !formValue.value.sizeLock;
    };

    const handleCustomRadio = () => {
      nextTick(() => {
        formRef.value?.validate().then(() => {
          const [w, h] = formValue.value.customRadio;
          const { width, height } = getWidthAndHeightFromRadio(
            `${w}:${h}` as RadioType,
            1024,
          );
          formValue.value._width = width;
          formValue.value._height = height;
        });
      });
    };

    watch(
      () => formValue.value,
      (value) => {
        if (prevSnapshot && !deepEqual(value, prevSnapshot)) {
          emit('formChange');
        }
        prevSnapshot = cloneDeep(value);

        if (formValue.value.genrateMode === GenrateMode.img) {
          formValue.value.hrScaleConfig = 1;
        }
      },
      { deep: true, immediate: false },
    );

    onMounted(() => {
      formHookInit();
      addStep({
        sort: 10,
        type: GuideType.AI_IMAGE_BASE,
        element: '.ai-image-form-image-module',
        beforeHandle: () => {
          imageModuleOpen.value = ['imageModule'];
        },
        popover: {
          title: '图片信息',
          description:
            '“一张图胜过千言万语”，可以在这里通过图片，来进一步说明自己的想法。',
        },
      });
      addStep({
        sort: 11,
        type: GuideType.AI_IMAGE_BASE,
        element: '.ai-image-form-image-module',
        beforeHandle: () => {
          formValue.value.imageRowOpenState = [1, 0, 1, 0, 0];
        },
        popover: {
          title: '图片信息',
          description: '这里使用深度图和风格参考，补充描述“种子”的外形和风格。',
        },
      });
      addStep({
        sort: 12,
        type: GuideType.AI_IMAGE_BASE,
        element: '.ai-image-input-card:nth-child(1)',
        beforeHandle: async () => {
          formValue.value.styleReferenceRow.url
            = await imageUrlToBase64('/style.jpg');
          formValue.value.styleReferenceRow.styleWeight = 0.7;
          formValue.value.styleReferenceRow.compositionWeight = 0.3;
        },
        popover: {
          title: '风格参考',
          description:
            '期望“种子”能够呈现绿色玻璃+金属的质感，所以上传一张源石锭做为风格参考。',
        },
      });
      addStep({
        sort: 13,
        type: GuideType.AI_IMAGE_BASE,
        element: '.ai-image-input-card:nth-child(3)',
        beforeHandle: async () => {
          formValue.value.depthRow.url = await imageUrlToBase64('/depth.png');
          formValue.value.depthRow.weight = 0.6;
        },
        popover: {
          title: '深度图',
          description:
            '期望“种子”能够有一个新奇的外形，所以上传了一个深度的参考来约束结构。（常规图片上传后自动通过“深度提取”产生深度图）',
        },
      });
      addStep({
        sort: 14,
        type: GuideType.AI_IMAGE_BASE,
        element: '.upload-container',
        beforeHandle: async () => {
          imageGroupRef.value?.onFileUploadModalProxy(
            ImageType.Depth,
            'create',
          );
        },
        popover: {
          title: '深度提取',
          description: '图片上传后，在弹窗内自动进行“提取深度”得到深度图。',
        },
      });
      addStep({
        sort: 15,
        type: GuideType.AI_IMAGE_BASE,
        element: '.modal-footer-btn-group',
        popover: {
          title: '深度提取',
          description:
            '点击这里选择应用提取结果，如果上传的已经是深度图，也可以点击使用原图。',
        },
      });
      addStep({
        sort: 16,
        type: GuideType.AI_IMAGE_BASE,
        element: '.ai-image-input-card:nth-child(3)',
        beforeHandle: () => {
          imageGroupRef.value?.handleSubmit();
        },
        popover: {
          title: '深度提取',
          description:
            '生成结果将会有与当前深度图相似的结构（相似度可以通过参数调整）。',
        },
      });
      addStep({
        sort: 20,
        type: GuideType.AI_IMAGE_BASE,
        element: '.ai-image-form-size',
        beforeHandle: () => {
          formValue.value.batchSize = 4;
        },
        popover: {
          title: '规格设置',
          description: '在这里修改宽高比和单次生成的数量。',
        },
      });
      addStep({
        sort: 30,
        type: GuideType.AI_IMAGE_BASE,
        element: '.ai-image-form-seed',
        popover: {
          title: '随机种子',
          description: '在这里修改结果的随机性，通常使用完全随机（-1）即可。',
        },
      });
    });

    return () => (
      <div class="form-container pos-relative pr-[16px]">
        <ImageFormHeader />

        <NForm disabled={loading.value} label-width={80} ref={formRef}>
          <NAlert class="ai-image-form-scenarios mb-20px" show-icon={false}>
            <FormPreset />
          </NAlert>

          {/* 文本信息 */}
          <NAlert class="ai-image-form-prompt mb-20px" show-icon={false}>
            <FormPrompt />
          </NAlert>

          {/* 图像信息 */}
          <NAlert class="ai-image-form-image-module mb-20px" show-icon={false}>
            <NCollapse
              arrowPlacement="right"
              displayDirective="show"
              expanded-names={imageModuleOpen.value}
              on-update:expanded-names={(value: string[]) => {
                imageModuleOpen.value = value;
              }}
            >
              <NCollapseItem
                name="imageModule"
                v-slots={{
                  header: () => (
                    <div class="w-full flex-c-start">
                      <NText class="mr-4px font-size-18px">图像信息</NText>
                      <TooltipIcon depth={3} size={18}>
                        {{
                          default: () =>
                            '用图片来传达你想要的画面内容，对结果的构图、风格等方面进行控制',
                          trigger: () => (
                            <InformationCircleOutline />
                          ),
                        }}
                      </TooltipIcon>
                    </div>
                  ),
                }}
              >
                <FormImageGroup ref={imageGroupRef} />
              </NCollapseItem>
            </NCollapse>
          </NAlert>

          {/* 输出规格设置 */}
          <NAlert class="ai-image-form-size mb-20px" show-icon={false}>
            <NH3>输出规格设置</NH3>
            <NFormItem
              v-slots={{
                label: () => (
                  <div class="flex-c-start">
                    <NText class="mr-4px">宽高比</NText>

                    <TooltipIcon depth={3}>
                      {{
                        default: () => (
                          <>
                            默认固定为1024px的短边，并根据所选比例在下方图片尺寸处展示生成宽高信息。
                          </>
                        ),
                        trigger: () => (
                          <InformationCircleOutline />
                        ),
                      }}
                    </TooltipIcon>
                  </div>
                ),
              }}
            >
              <NSelect
                class="max-w-355px"
                onUpdate:value={(value: RadioType) => {
                  formValue.value.sizeLock = true;
                  const { width, height } = getWidthAndHeightFromRadio(
                    value,
                    1024,
                    'min',
                  );
                  formValue.value._width = width;
                  formValue.value._height = height;
                  formValue.value.customRadio = value.split(':').map(Number);
                  formRef.value?.restoreValidation();
                }}
                options={RadioArray.map((item) => ({
                  label: item,
                  value: item,
                }))}
                placeholder="输出规格"
                renderLabel={renderSizeLabel}
                v-model:value={formValue.value.imageRadio}
              />
            </NFormItem>
            <NFormItem label="自定义宽高比" path="customRadio" rule={rule}>
              <NInput
                class="max-w-355px"
                clearable
                maxlength={5}
                onUpdateValue={handleCustomRadio}
                pair
                placeholder={['宽度', '高度']}
                separator=":"
                v-model:value={formValue.value.customRadio}
              />
            </NFormItem>

            <NText class="mb-12px flex-c-start">
              <NText class="mr-4px">图片尺寸</NText>
              <TooltipIcon depth={3}>
                {{
                  default: () =>
                    // '可输入范围为512~2048(px)，默认根据宽高比进行计算，也可以点击中间按钮进行解锁，进行高宽自定义',
                    '目前仅作为比例预览结果，默认会根据短边1024px(最长边不超过2048)进行等比缩放。',
                  trigger: () => (
                    <InformationCircleOutline />
                  ),
                }}
              </TooltipIcon>
            </NText>
            <div class="flex">
              <NFormItem label="宽度">
                <NInputNumber
                  button-placement="both"
                  class="w-156px text-center"
                  placeholder="宽度"
                  // disabled={true}
                  readonly={true}
                  show-button={false}
                  size="large"
                  v-model:value={formValue.value.width}
                />
              </NFormItem>
              <NButton
                class="mx-8px"
                disabled={true}
                onClick={handleSizeLock}
                text
              >
                <NIcon size={20}>
                  {formValue.value.sizeLock ? <ScaleLocked /> : <ScaleUnlock />}
                </NIcon>
              </NButton>
              <NFormItem label="高度">
                <NInputNumber
                  button-placement="both"
                  class="w-156px text-center"
                  placeholder="高度"
                  // disabled={true}
                  readonly={true}
                  show-button={false}
                  size="large"
                  v-model:value={formValue.value.height}
                />
              </NFormItem>
            </div>
            <NFormItem
              labelAlign="left"
              labelPlacement="left"
              labelWidth={100}
              path="name"
              v-slots={{
                label: () => (
                  <div class="flex-c-start">
                    <NText class="mr-4px">高清模式</NText>
                    <TooltipIcon depth={3}>
                      {{
                        default: () =>
                          '高清模式仅在【编辑底图】为空或未开启时可用，开启后会生成高清结果，同时只能计算一张。结果尺寸将在常规结果的基础上放大对应倍数。',
                        trigger: () => (
                          <InformationCircleOutline />
                        ),
                      }}
                    </TooltipIcon>
                  </div>
                ),
              }}
            >
              <NSelect
                disabled={
                  formValue.value.genrateMode === GenrateMode.img
                  || loading.value
                }
                onUpdateValue={() => (formValue.value.batchSize = 1)}
                options={[
                  { label: '关闭', value: 1 },
                  { label: '1.5倍', value: 1.5 },
                ]}
                v-model:value={formValue.value.hrScaleConfig}
              />
            </NFormItem>

            <NFormItem
              label="单批数量"
              labelAlign="left"
              labelPlacement="left"
              path="name"
            >
              <NRadioGroup v-model:value={formValue.value.batchSize}>
                {[1, 2, 4, 6].map((count) => (
                  <NRadio
                    class="mr-12px"
                    disabled={formValue.value.isHRRedraw && count > 1}
                    label={` ${count} `}
                    size="large"
                    value={count}
                  />
                ))}
              </NRadioGroup>
            </NFormItem>
          </NAlert>

          {/* 结果随机性设置 */}
          <NAlert class="ai-image-form-seed mb-20px" show-icon={false}>
            <NH3>结果随机性设置</NH3>
            <NFormItem label="随机种子" labelAlign="left" labelPlacement="left">
              <NInputNumber
                max={Number.MAX_SAFE_INTEGER}
                min={-1}
                placeholder="请设置随机种子"
                precision={0}
                showButton={false}
                v-model:value={formValue.value.seed}
              />
              <TooltipIcon
                depth={3}
                onClick={() => (formValue.value.seed = -1)}
                secondary
                size={18}
                text={false}
                triggerClass="mx-8px px-6px"
                triggerContent="重置为完全随机（-1），每次使用不同的随机数进行生成。"
              >
                {{
                  trigger: () => <ReloadOutline />,
                }}
              </TooltipIcon>
              <TooltipIcon
                depth={3}
                onClick={() =>
                  (formValue.value.seed = getRandomInteger(
                    0,
                    Number.MAX_SAFE_INTEGER,
                  ))}
                secondary
                size={25}
                text={false}
                triggerClass="px-4px"
                triggerContent="立刻产生一个随机数，作为固定种子。"
              >
                {{
                  trigger: () => <Random />,
                }}
              </TooltipIcon>
            </NFormItem>
          </NAlert>
        </NForm>

        <ImageFormFooter />
      </div>
    );
  },
});

export { ImageForm };
