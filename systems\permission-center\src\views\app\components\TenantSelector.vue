<template>
  <Select
    v-model:value="tenantId"
    :options="tenantOptions"
    placeholder="请选择项目"
    class="w-full"
  />
</template>

<script setup lang="ts">
import { Select } from 'ant-design-vue';
import { computed } from 'vue';
import type { PermissionTenantInfo } from '../../../api/app.ts';

const props = defineProps<{
  appId?: number;
  tenantList?: PermissionTenantInfo[];
}>();

const tenantId = defineModel<PermissionTenantInfo['id']>('tenantId');
const tenantOptions = computed(() => {
  return props.tenantList?.map((item) => ({
    label: item.tenant,
    value: item.id,
  }));
});
</script>
