import type { NavigateOptions } from '@tanstack/react-router';
import React, { useEffect } from 'react';
import { Link, Outlet } from '@tanstack/react-router';
import HeaderFrame from '../../../../components/Frame/Header.tsx';
import { themeColors } from '../../../../constants/theme.ts';
import UserMenu from '../../../../components/Frame/UserMenu.tsx';
import { useProject } from '../../../../hooks/useProject.ts';
import { useUserInfo } from '../../../../hooks/useUserInfo.ts';
import { feedbackConfigRoute } from '../../_route.tsx';
import IconArrowLeft from '../../../../components/Icons/IconArrowLeft.tsx';
import { router } from '../../../../routers';

const configNavs: { label: string; to: NavigateOptions['to'] }[] = [
  { label: '反馈收集配置', to: '/feedback/$projectId/configs/collections' },
  { label: 'BUG处理提醒配置', to: '/feedback/$projectId/configs/issue' },
  { label: '需求处理提醒配置', to: '/feedback/$projectId/configs/story' },
];

const BaseConfigFrame: React.FC = () => {
  const { currentProjectId, currentProject, setCurrentProjectId } = useProject();
  const { userInfo } = useUserInfo();
  const { projectId } = feedbackConfigRoute.useParams<{ projectId: string }>();

  useEffect(() => {
    if (currentProjectId != null && currentProjectId !== Number(projectId)) {
      // 校正当前项目 ID
      setCurrentProjectId(Number(projectId)).catch(() => router.navigate({ to: '/' }));
    }
  }, [currentProjectId, projectId, setCurrentProjectId]);

  return (
    <div className="h-full w-full flex flex-col">
      <HeaderFrame
        widgetLeft={(
          <span className="flex items-center">
            <Link to="/feedback" className="mr-[12px] flex items-center">
              <IconArrowLeft size={24} fills={[themeColors.primary]} className="mr-[9px]" />
              <div
                className="select-none text-align-center font-size-[24px] c-FO-Brand-Primary-Default font-bold line-height-[32px]"
              >
                配置：{currentProject?.name}
              </div>
            </Link>
          </span>
        )}
        widgetRight={(
          <div className="flex items-center gap-[16px]">
            <UserMenu userInfo={userInfo} />
          </div>
        )}
      />
      <div className="relative h-full w-full flex flex-auto gap-[16px] overflow-auto p-x-[16px] pb-[16px]">
        <div className="w-[258px] flex flex-none flex-col overflow-auto border-[2px] border-lightGray border-rd-[10px] border-style-solid font-bold">
          {configNavs.map((nav) => (
            <Link
              key={nav.label}
              className="truncate px-[20px] py-[15px] font-size-[16px] hover:bg-lightBlue"
              to={nav.to}
              params={(param) => ({ projectId: param.projectId! })}
              resetScroll={false}
            >
              {({ isActive }) => <span className={isActive ? 'c-FO-Brand-Primary-Default' : ''}>{nav.label}</span>}
            </Link>
          ))}
        </div>
        <div className="flex-auto border-[2px] border-lightGray border-rd-[10px] border-style-solid p-[4px]">
          <div className="h-full overflow-auto p-[36px]">
            <Outlet />
          </div>
        </div>
      </div>
    </div>
  );
};

export default BaseConfigFrame;
