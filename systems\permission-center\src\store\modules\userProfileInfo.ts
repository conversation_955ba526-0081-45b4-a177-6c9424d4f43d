import { defineStore } from 'pinia';
import { useMicroAppInject, useUserProfileInfoCtx } from '@hg-tech/oasis-common';
import { computed } from 'vue';

export enum UserAuthority {
  SuperAdmin = '1024',
  User = '888',
}

/**
 * 从父应用获取用户信息
 */
export const useUserProfileInfoStore = defineStore('userProfileInfo', () => {
  const { data, loading } = useMicroAppInject(useUserProfileInfoCtx);

  const isSuperAdmin = computed(() => {
    return data.value?.authorityId === UserAuthority.SuperAdmin;
  });

  return {
    userProfileInfo: data,
    isSuperAdmin,
    loading,
  };
});
