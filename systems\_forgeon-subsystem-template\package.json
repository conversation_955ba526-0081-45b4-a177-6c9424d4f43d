{"name": "@todo/forgeon-subsystem-template", "type": "module", "version": "1.0.2", "private": true, "scripts": {"dev": "vite dev", "build": "vite build", "build:rnd": "cross-env NODE_ENV=production vite build --mode rnd", "build:pre": "cross-env NODE_ENV=production vite build --mode pre", "build:analyze": "vite build -- --analyze", "test": "run-p test:*", "test:type": "vue-tsc --noEmit --skipLib<PERSON><PERSON><PERSON>"}, "dependencies": {"@ant-design/icons-vue": "catalog:", "@hg-tech/forgeon-style": "workspace:*", "@hg-tech/forgeon-uno-config": "workspace:^", "@hg-tech/oasis-common": "workspace:*", "@hg-tech/request-api": "workspace:*", "@hg-tech/utils-vue": "workspace:*", "@micro-zoe/micro-app": "catalog:", "@vueuse/core": "catalog:", "ant-design-vue": "catalog:", "dayjs": "catalog:", "lodash": "catalog:", "pinia": "catalog:", "unocss": "catalog:", "vue": "catalog:", "vue-router": "catalog:"}, "devDependencies": {"@hg-tech/configs": "workspace:^", "@types/lodash": "catalog:", "cross-env": "catalog:", "vite": "catalog:", "vue-tsc": "catalog:"}}