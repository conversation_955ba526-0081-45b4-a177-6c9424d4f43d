export interface RoleInfo {
  roleName: string;
  value: string;
}

export interface RoleListItem {
  authorityName?: string;
  authorityId?: string;
  CreatedAt?: string;
  UpdatedAt?: string;
  parentId?: string;
  children?: RoleListItem[];
}

export interface UserInfoModel {
  ID?: number;
  userName?: string;
  moniker?: string;
  displayName?: string;
  nickName?: string;
  headerImg?: string;
  desc?: string;
  homePath?: string;
  baseColor?: string;
  activeColor?: string;
  phone?: string;
  email?: string;
  uuid?: string;
  openID?: string;
  roles?: RoleInfo[];
  disabled?: boolean;
  authorities?: RoleListItem[];
  authority?: RoleListItem;
  authorityId?: string;
  lastProjectId?: number;
  isOutSourcing?: boolean;
}
