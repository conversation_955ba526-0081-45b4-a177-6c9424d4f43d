import { Button } from 'ant-design-vue';
import { h } from 'vue';
import { platformOptions } from '../../configs.tsx';
import type { BasicColumn, FormSchema } from '/projects/oasis/src/components/Table';
import { formatNickName } from '/projects/oasis/src/hooks/system/useUserList';
import { formatKBSize } from '/projects/oasis/src/utils/file/size';

export const UPDATETYPES = {
  OPTIONALUPDATE: 1,
  FORCEUPDATE: 2,
  FORCENOWUPDATE: 3,
};

export const updateTypeOptions = [
  {
    label: '可选更新',
    value: UPDATETYPES.OPTIONALUPDATE,
  },
  {
    label: '强制更新',
    value: UPDATETYPES.FORCEUPDATE,
  },
  {
    label: '强制立即更新',
    value: UPDATETYPES.FORCENOWUPDATE,
  },
];

export const columns: BasicColumn[] = [
  {
    title: '版本',
    dataIndex: 'version',
    width: 150,
  },
  {
    title: '平台',
    dataIndex: 'platform',
    width: 120,
  },
  {
    title: '作者',
    dataIndex: 'author',
    format: (_, record) => formatNickName(record.author),
    width: 150,
  },
  {
    title: '文件',
    dataIndex: 'downloadLink',
    customRender: ({ record }) => {
      const link = record.downloadLink;
      const name = link.substring(link.lastIndexOf('/') + 1);
      return h(
        Button,
        {
          href: link,
          type: 'link',
          download: name,
        },
        {
          default: () => {
            return name + (record.sizeKB ? ` (${formatKBSize(record.sizeKB)})` : '');
          },
        },
      );
    },
  },
];

export const formSchema: FormSchema[] = [
  {
    field: 'BasicSetting',
    label: '基本配置',
    component: 'BorderBox',
    children: [{
      field: 'version',
      label: '版本',
      required: true,
      component: 'Input',

      helpMessage: [
        '示例：v1.4.0-beta.2',
        '规则参考：',
        'https://go.dev/doc/modules/version-numbers',
      ],
    }, {
      field: 'releaseNote',
      label: '发版公告',
      helpMessage: ['Windows emoji快捷键:【 win + . 】', 'macOS emoji快捷键:【 ctrl + cmd + 空格 】'],
      component: 'Input',
      slot: 'mdContent',
    }, {
      field: 'downloadLink',
      label: '文件',
      component: 'Upload',
      valueField: 'singleValue',
      rules: [{ required: true, message: '请上传打包文件' }],
      componentProps: {
        valueFormat: 'string',
        maxNumber: 1,
        multiple: false,
        reject: ['.md'],
      },
    }, {
      field: 'platform',
      label: '平台',
      helpMessage: '会根据上传的文件格式自动判断平台',
      required: true,
      component: 'RadioGroup',
      defaultValue: 1,
      componentProps: {
        options: platformOptions,
      },
    }, {
      label: '文件大小',
      show: false,
      field: 'sizeKB',
      component: 'InputNumber',
    }],
  },
  {
    field: 'updateSetting',
    label: '更新配置',
    component: 'BorderBox',

    children: [{
      field: 'updateType',
      label: '本版本更新方式',
      component: 'Select',
      componentProps: {
        options: updateTypeOptions,
      },
    }, {
      field: 'enableGray',
      label: '是否灰度',
      component: 'Switch',

      componentProps: {
        checkedChildren: '启用',
        unCheckedChildren: '关闭',
      },
    }, {
      field: 'enableHot',
      label: '是否热更',
      component: 'Switch',
      componentProps: {
        checkedChildren: '启用',
        unCheckedChildren: '关闭',
      },
    }, {
      field: 'minHotCompatibleVersionID',
      label: '热更最低兼容版本ID',
      required: true,
      component: 'Select',
      ifShow: ({ values }) => {
        return !!values.enableHot;
      },
    }, {
      field: 'hotDownloadLink',
      label: '热更包',
      component: 'Upload',
      valueField: 'singleValue',
      rules: [{ required: true, message: '请上传热更包文件' }],
      ifShow: ({ values }) => {
        return !!values.enableHot;
      },
      componentProps: {
        valueFormat: 'string',
        maxNumber: 1,
        multiple: false,
        accept: ['.zip'],
      },
    }],
  },
];
