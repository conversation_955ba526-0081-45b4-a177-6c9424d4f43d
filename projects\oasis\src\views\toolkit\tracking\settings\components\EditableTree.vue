<template>
  <div :class="prefixCls">
    <EditableNode
      v-model:value="nodeData.toolFuncName"
      v-model:isEdit="nodeData.isEditing"
      width="200px"
      @change="handleEditFunc(nodeData)"
      @cancel="handleEditCancel(nodeData)"
    >
      <div
        v-if="!nodeData.isEditing"
        :class="`${prefixCls}__content`"
        class="relative"
      >
        <div :class="`${prefixCls}__content-name`" class="flex items-center justify-center">
          <div class="relative">
            <div>
              {{ nodeData.toolFuncName }}
            </div>

            <Icon icon="icon-park-outline:edit" :class="{ 'hover-edit-name-icon': isEditMode }" class="edit-name-icon absolute right-[-22px] top-0 ml-2" @click="handleDblClick(nodeData)" />
          </div>
        </div>

        <Icon v-if="hasExtInfo(nodeData)" icon="icon-park-outline:dot" class="absolute right-0 top-0" />
        <div
          :class="`${prefixCls}__content-id`"
          class="relative"
          @click.stop="copyFuncID(nodeData.funcUUID!)"
          @dblclick.stop
        >
          {{ nodeData.funcUUID }}
          <div class="absolute right-[1px] top-[3px] float-right">
            <Icon v-if="(!nodeData.children || !nodeData.children.length) && isEditMode" icon="icon-park-outline:editor" size="12" class="mr-2" @click.stop="handleClick(nodeData)" />
            <Icon icon="ant-design:copy-outlined" size="12" title="复制ID" />
          </div>
        </div>
      </div>
    </EditableNode>
    <div v-if="nodeData.children?.length" :class="`${prefixCls}__children`">
      <EditableTree
        v-for="child in nodeData.children"
        :key="child.ID"
        v-model:isAdding="isAddingFunc"
        v-model:isEditing="isEditingFunc"
        :item="child"
        :isEditMode="isEditMode"
        @add="handleAddFunc"
        @edit="handleEditFunc"
      />
    </div>
    <template v-if="isEditMode">
      <!--  子节点操作 begin -->
      <div v-if="!nodeData.children?.length" :class="`${prefixCls}__child-btn`">
        <EditableNode
          v-model:value="newAddFuncName"
          v-model:isEdit="nodeData.isAdding"
          width="200px"
          @change="handleAddFunc(nodeData)"
          @cancel="handleAddCancel(nodeData)"
        >
          <div :class="`${prefixCls}__child-btn-group`">
            <APopconfirm
              v-if="!nodeData.isAdding"
              :title="`确定删除【${nodeData.toolFuncName}】吗？`"
              @confirm="handleDeleteFunc(nodeData)"
            >
              <Icon icon="ant-design:minus-outlined" title="删除当前节点" />
            </APopconfirm>

            <Icon
              v-if="!nodeData.isAdding"
              icon="ant-design:plus-outlined"
              title="新增子节点"
              @click="handleAddChildClick(nodeData)"
            />
          </div>
        </EditableNode>
      </div>
      <!--  子节点操作 end -->
      <!--  兄弟节点操作 begin -->
      <div :class="`${prefixCls}__bro-btn`" title="新增兄弟节点">
        <EditableNode
          v-model:value="newAddFuncName"
          v-model:isEdit="nodeData.isAddingBro"
          width="200px"
          @change="handleAddFunc(nodeData, false)"
          @cancel="handleAddCancel(nodeData, false)"
        >
          <a-button
            v-if="!nodeData.isAddingBro"
            shape="circle"
            @click="handleAddChildClick(nodeData, false)"
          >
            <Icon icon="ant-design:plus-outlined" />
          </a-button>
        </EditableNode>
      </div>
      <!--  兄弟节点操作 end -->
    </template>
    <AdditionalInfoEditModal @register="infoEditModal" @success="AdditionalInfoEditModalSuccess" />
  </div>
</template>

<script lang="ts" setup name="EditableTree">
import { Popconfirm as APopconfirm } from 'ant-design-vue';
import { assign, cloneDeep } from 'lodash-es';
import { computed, ref, watchEffect } from 'vue';
import EditableNode from './EditableNode.vue';
import type { TrackingToolFunctionsListItem } from '/@/api/page/model/trackingModel';
import Icon from '/@/components/Icon';
import { useDesign } from '/@/hooks/web/useDesign';
import { useMessage } from '/@/hooks/web/useMessage';
import { copyText } from '/@/utils/copyTextToClipboard';
import AdditionalInfoEditModal from './AdditionalInfoEditModal.vue';
import { useModal } from '/@/components/Modal';

const props = defineProps({
  item: {
    type: Object as PropType<TrackingToolFunctionsListItem>,
    required: true,
  },
  // 是否编辑模式
  isEditMode: {
    type: Boolean,
    default: false,
  },
  // 是否正在新增
  isAdding: {
    type: Boolean,
    required: true,
  },
  // 是否正在编辑
  isEditing: {
    type: Boolean,
    required: true,
  },
});
const emit = defineEmits(['add', 'edit', 'update:item', 'update:isAdding', 'update:isEditing', 'getData']);
const { prefixCls } = useDesign('editable-tree');
const { createMessage } = useMessage();
const [infoEditModal, { openModal }] = useModal();
const nodeData = ref<TrackingToolFunctionsListItem>(cloneDeep(props.item));

watchEffect(() => {
  nodeData.value = cloneDeep(props.item);
});

const isAddingFunc = computed({
  get() {
    return props.isAdding;
  },
  set(val) {
    emit('update:isAdding', val);
  },
});

const isEditingFunc = computed({
  get() {
    return props.isEditing;
  },
  set(val) {
    emit('update:isEditing', val);
  },
});

function hasExtInfo(nodeData) {
  if (!nodeData.extInfo) {
    return false;
  }

  if (nodeData.children && nodeData.children.length) {
    return false;
  }

  const extInfoList = JSON.parse(nodeData.extInfo);

  if (extInfoList && extInfoList.length > 0) {
    return true;
  } else {
    return false;
  }
}

const newAddFuncName = ref<string>('');

function handleAddFunc(item: TrackingToolFunctionsListItem, isChild = true) {
  if (item.ID === nodeData.value.ID && !newAddFuncName.value) {
    createMessage.warning('请输入名称');
  } else {
    if (item.ID !== nodeData.value.ID) {
      emit('add', item);

      return;
    }

    emit('add', {
      toolFuncName: newAddFuncName.value,
      parentFuncID: isChild ? item.ID : item.parentFuncID,
    });
  }

  newAddFuncName.value = '';
  isAddingFunc.value = false;

  if (isChild) {
    item.isAdding = false;
  } else {
    item.isAddingBro = false;
  }
}

function handleAddCancel(item: TrackingToolFunctionsListItem, isChild = true) {
  newAddFuncName.value = '';
  isAddingFunc.value = false;

  if (isChild) {
    item.isAdding = false;
  } else {
    item.isAddingBro = false;
  }
}

function handleDblClick(item: TrackingToolFunctionsListItem) {
  if (!props.isEditMode) {
    return;
  }

  if (isEditingFunc.value || isAddingFunc.value) {
    createMessage.warning('只能同时新增/编辑一个');

    return;
  }

  isEditingFunc.value = true;
  item.isEditing = true;
}

function handleClick(nodeData) {
  if (!props.isEditMode) {
    return;
  }

  openModal(true, { toolFuncName: nodeData.toolFuncName, ID: nodeData.ID, usable: nodeData.usable, extInfo: JSON.parse(nodeData.extInfo || '[]') });
}

function handleAddChildClick(item: TrackingToolFunctionsListItem, isChild = true) {
  if (isAddingFunc.value || isEditingFunc.value) {
    createMessage.warning('只能同时新增/编辑一个');

    return;
  }

  isAddingFunc.value = true;

  if (isChild) {
    item.isAdding = true;
  } else {
    item.isAddingBro = true;
  }
}

function handleEditFunc(item: TrackingToolFunctionsListItem) {
  emit('edit', item);
  isEditingFunc.value = false;
  item.isEditing = false;
}

function handleEditCancel(item: TrackingToolFunctionsListItem) {
  nodeData.value = props.item;
  isEditingFunc.value = false;
  item.isEditing = false;
}

function handleDeleteFunc(item: TrackingToolFunctionsListItem) {
  item.usable = -1;
  emit('edit', item);
}

function copyFuncID(UUID: string) {
  if (!UUID) {
    return;
  }

  copyText(UUID);
}

function AdditionalInfoEditModalSuccess(extInfo) {
  emit('edit', assign(nodeData.value, { extInfo: JSON.stringify(extInfo) }));
}
</script>

<style lang="less">
@prefix-cls: ~'hypergryph-editable-tree';
@prefix-cls-node: ~'hypergryph-editable-node';
.@{prefix-cls} {
  position: relative;
  display: flex;
  align-items: center;
  padding-left: 20px;

  & > .@{prefix-cls-node} {
    padding: 20px 0;
  }

  &::before {
    width: 20px;
    height: 50%;
    content: '';
    position: absolute;
    top: 0;
    left: 0;
  }

  &::after {
    width: 20px;
    height: 50%;
    content: '';
    position: absolute;
    top: 50%;
    left: 0;
    border-top: 1px solid @FO-Container-Stroke1;
  }

  &:not(:only-child) {
    &::after {
      border-top: 1px solid @FO-Container-Stroke1;
    }
  }

  &:not(:first-child) {
    &::before {
      border-left: 1px solid @FO-Container-Stroke1;
    }
  }

  &:not(:last-child) {
    &::after {
      border-left: 1px solid @FO-Container-Stroke1;
    }
  }

  &:last-child {
    & > .@{prefix-cls-node} {
      padding-bottom: 40px;
    }

    & > .@{prefix-cls}__bro-btn {
      display: block;
    }

    &::before {
      height: 45%;
    }

    &::after {
      top: 45%;
    }

    & > .@{prefix-cls}__children {
      &::before {
        top: 45%;
      }
    }

    & > .@{prefix-cls}__child-btn {
      height: 40px;

      &::after {
        top: 30%;
      }
    }
  }

  &__content {
    border-radius: 4px;
    overflow: hidden;
    border: 1px solid @FO-Container-Stroke1;
    width: 200px;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease-in-out;

    &:hover {
      border-color: @FO-Brand-Primary-Default;
      color: @FO-Brand-Primary-Default;
    }
    .edit-name-icon {
      display: none !important;
    }
    &:hover .hover-edit-name-icon {
      display: inline-flex !important;
    }

    &-name {
      padding: 6px 12px;
    }

    &-id {
      font-size: 12px;
      color: @FO-Content-Text2;
      background-color: @FO-Container-Stroke1;
      line-height: 12px;
      padding: 2px;

      &:hover {
        color: @FO-Brand-Primary-Default;
      }
    }
  }

  &__child-btn {
    margin-left: 20px;
    transition: all 0.3s ease-in-out;
    position: relative;

    &-group {
      border-radius: 16px;
      border: 1px solid @FO-Container-Stroke1;
      padding: 2px 8px;
      display: flex;
      justify-content: space-between;
      text-align: center;
      transition: all 0.3s ease-in-out;
      cursor: pointer;

      &:hover {
        border-color: @FO-Brand-Primary-Default;
        color: @FO-Brand-Primary-Default;
      }
    }

    &::after {
      content: '';
      position: absolute;
      top: 50%;
      left: -19px;
      width: 19px;
      height: 0;
      border-top: 1px dashed @FO-Container-Stroke1;
    }
  }

  &__bro-btn {
    display: none;
    position: absolute;
    width: 200px;
    bottom: -10px;
    text-align: center;
    left: 0;
    margin-left: 20px;
    transition: all 0.3s ease-in-out;
    z-index: 1;

    &::before {
      content: '';
      position: absolute;
      top: -19px;
      left: 50%;
      width: 0;
      height: 19px;
      border-left: 1px dashed @FO-Container-Stroke1;
    }
  }

  &__children {
    padding-left: 20px;
    position: relative;

    &::before {
      content: '';
      position: absolute;
      top: 50%;
      left: 0;
      width: 20px;
      height: 0;
      border-left: 0;
      border-top: 1px solid @FO-Container-Stroke1;
    }
  }
}
</style>
