@import (reference) './vars.less';

*,
::before,
::after {
  box-sizing: border-box;
  border-width: 0;
  border-style: solid;
  border-color: initial;
}

html,
body {
  height: 100%;
  overflow: hidden;
  margin: 0;
  padding: 0;
  text-size-adjust: auto;
  background-color: @FO-Container-Background;
  color: @FO-Content-Text1;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, 'Noto Sans', sans-serif,
    'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji';
}

:autofill {
  transition: background-color 5000s ease-in-out 0s;

  &,
  &:hover,
  &:active,
  &:focus {
    box-shadow: 0 0 0 1000px transparent inset;
    -webkit-text-fill-color: @FO-Content-Text1;
  }
}

::-webkit-scrollbar {
  height: 12px;
  width: 12px;
  background: transparent;

  &-corner {
    background: transparent;
  }

  &-track {
    background-color: transparent;
  }

  &-thumb {
    border-radius: 100px;
    border: 3px solid transparent !important; // 边框减小
    background-clip: padding-box !important;
    background: rgba(0, 0, 0, 0.25);

    &:hover,
    &:active {
      border: none !important;
      background: rgba(0, 0, 0, 0.4);
    }
  }
}

[data-theme='dark'] {
  ::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.25);

    &:hover,
    &:active {
      background: rgba(255, 255, 255, 0.4);
    }
  }
}
