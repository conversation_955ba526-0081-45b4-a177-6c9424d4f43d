<template>
  <Grid
    :ref="tableForwardRef"
    class="basic-vxe-table"
    v-bind="mergedOptions"
    :style="{
      ...cssVars,
      ...props.cssVarOverrides,
    }"
    v-on="events ?? {}"
  />
</template>

<script setup lang="ts">
import { type VxeGridInstance, type VxeGridListeners, type VxeGridProps, Grid } from 'vxe-table';
import { computed, getCurrentInstance } from 'vue';
import { merge } from 'lodash';
import { cssVars } from './defaultCssVars';

const props = withDefaults(defineProps<{
  options: VxeGridProps;
  cssVarOverrides?: Partial<Record<keyof typeof cssVars, string>>;
  events?: VxeGridListeners;
}>(), {
  events: () => ({}),
});

const instance = getCurrentInstance()!;

/**
 * 不使用 defineExpose 是因为 vue-tsc 不能正确导出类型
 */
const tableForwardRef = function (ref: VxeGridInstance) {
  instance.exposed = ref;
  instance.exposeProxy = ref;
};

const defaultOptions: VxeGridProps = {
  border: 'inner',
  minHeight: 48,
  loadingConfig: {
    text: '加载中...',
  },
  columnConfig: {
    resizable: true,
  },
};
const mergedOptions = computed<VxeGridProps>(() => ({
  ...merge({}, defaultOptions, props.options),
  data: props.options.data || [],
  columns: props.options.columns || [],
}));
</script>

<style lang="less" scoped>
@import (reference) '@hg-tech/forgeon-style/vars.less';
.basic-vxe-table :deep(.vxe-grid--layout-body-wrapper) {
  .vxe-cell--title {
    font-size: 12px;
    line-height: 16px;
  }
  .vxe-body--column {
    transition: background-color 0.2s ease-in-out;
  }
  .vxe-cell--drag-handle {
    opacity: 0;
    transition: opacity 0.2s ease-in-out;
    font-size: 12px;
  }
  .vxe-cell:has(.vxe-cell--drag-handle) {
    padding: 0;
    .vxe-cell--wrapper {
      transform: rotate(90deg);
      transform-origin: center center;
      display: flex;
      justify-content: center;
      align-items: center;
    }
    .vxe-cell--label {
      display: none;
    }
  }
  .vxe-tree-cell {
    padding-left: 0;
  }
  .vxe-cell--tree-btn {
    margin-left: -20px;
  }
  .vxe-body--row.row--hover .vxe-cell--drag-handle {
    opacity: 1;
  }
}
</style>
