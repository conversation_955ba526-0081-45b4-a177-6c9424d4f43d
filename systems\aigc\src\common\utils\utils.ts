import type { ImageRenderToolbarProps } from 'naive-ui';
import type { Component } from 'vue';
import type { RadioType } from '@/models/ai-image';
import { NButton, NIcon } from 'naive-ui';
import qs from 'qs';
import { sad } from 'random-jpn-emoji';
import { h } from 'vue';

import { CloudDownloadOutline } from '../components/svg-icons';

import { Logger } from './logger';

import { getFileUrl, UploadScene } from '@/apis/oss.api';
import CustomError from './custom-error';

function renderIcon(
  icon: Component | SVGAElement | undefined,
  props?: {
    size?: number | string;
    color?: string;
  },
) {
  return () =>
    (icon ? h(NIcon, { ...props }, { default: () => h(icon) }) : null);
}

function safeParse<T>(string: string): T | undefined {
  try {
    return JSON.parse(string) as T;
  } catch (error) {
    return undefined;
  }
}

function fileToBase64(blob: File, callback: (res: string) => void) {
  const reader = new FileReader();
  reader.addEventListener('load', async () => {
    const res = await convertImageToJpgBase64(reader.result as string);
    callback(res);
  });
  try {
    reader.readAsDataURL(blob);
  } catch (e) {
    throw new CustomError('Error converting file to base64');
  }
}

function file2BlobUrl(blob: File, callback: (res: string) => void) {
  const reader = new FileReader();
  reader.onload = (e) => {
    const fileContent = e.target?.result as ArrayBuffer; // 获取文件内容
    const newBlob = new Blob([fileContent], { type: blob.type }); // 创建Blob对象
    const blobUrl = URL.createObjectURL(newBlob); // 获取Blob URL
    callback(blobUrl); // 调用回调函数传递Blob URL
  };
  reader.readAsArrayBuffer(blob);
}

function file2Blob(blob: File, callback: (res: Blob) => void) {
  const reader = new FileReader();
  reader.onload = (e) => {
    const fileContent = e.target?.result as ArrayBuffer; // 获取文件内容
    const newBlob = new Blob([fileContent], { type: blob.type }); // 创建Blob对象
    callback(newBlob); // 调用回调函数传递Blob
  };
  reader.readAsArrayBuffer(blob);
}

function file2Buffer(blob: File, callback: (res: ArrayBuffer) => void) {
  const reader = new FileReader();
  reader.onload = (e) => {
    const fileContent = e.target?.result as ArrayBuffer; // 获取文件内容
    callback(fileContent); // 调用回调函数传递ArrayBuffer
  };
  reader.readAsArrayBuffer(blob);
}

function fileToBase64Promise(file: File): Promise<string> {
  return new Promise((resolve, reject) => {
    fileToBase64(file, (base64) => {
      if (base64) {
        resolve(base64);
      } else {
        reject(new CustomError('Error converting file to base64', true));
      }
    });
  });
}

function file2BlobUrlPromise(file: File): Promise<string> {
  return new Promise((resolve, reject) => {
    file2BlobUrl(file, (blobUrl) => {
      if (blobUrl) {
        resolve(blobUrl);
      } else {
        reject(new CustomError('Error converting file to Blob URL', true));
      }
    });
  });
}

function file2BlobPromise(file: File): Promise<Blob> {
  return new Promise((resolve, reject) => {
    file2Blob(file, (blob) => {
      if (blob) {
        resolve(blob);
      } else {
        reject(new CustomError('Error converting file to Blob', true));
      }
    });
  });
}

function file2BufferPromise(file: File): Promise<ArrayBuffer> {
  return new Promise((resolve, reject) => {
    file2Buffer(file, (buffer) => {
      if (buffer) {
        resolve(buffer);
      } else {
        reject(new CustomError('Error converting file to buffer', true));
      }
    });
  });
}

async function convertImageToJpgBase64(pngBase64: string): Promise<string> {
  return new Promise((resolve, reject) => {
    const img = new Image();
    img.onload = function () {
      const canvas = document.createElement('canvas');
      canvas.width = img.width;
      canvas.height = img.height;
      const ctx = canvas.getContext('2d');
      if (ctx) {
        ctx.fillStyle = 'white';
        ctx.fillRect(0, 0, canvas.width, canvas.height);
      }

      ctx?.drawImage(img, 0, 0);
      canvas.toBlob((blob) => {
        if (!blob) {
          reject(new CustomError('Failed to create JPEG blob.', true));
          return;
        }
        const reader = new FileReader();
        reader.onload = function () {
          if (typeof reader.result === 'string') {
            resolve(`data:image/jpeg;base64,${reader.result.split(',')[1]}`); // Extract base64 data
          } else {
            reject(new CustomError('Failed to read JPEG base64 data.', true));
          }
        };
        reader.onerror = function (error) {
          reject(error);
        };
        reader.readAsDataURL(blob);
      }, 'image/jpeg');
    };
    img.onerror = function (error) {
      reject(error);
    };
    img.src = pngBase64;
  });
}

function downloadFileFromString(
  imageString: string,
  fileName: string = 'file',
) {
  if (!imageString) {
    Logger.error(`[Util]: download src is not exist${sad()}`);
    return;
  }
  const isDataUrl = imageString.startsWith('data:');
  const isBlobUrl = imageString.startsWith('blob:');

  if (isDataUrl || isBlobUrl) {
    // 对于Blob URL和Base64数据，直接创建下载链接并触发下载
    const link = document.createElement('a');
    link.href = imageString;
    link.download = fileName;

    document.body.appendChild(link);
    link.click();

    document.body.removeChild(link);
  } else {
    // 对于普通URL，使用fetch获取图片并转换为Blob对象后再下载
    fetch(imageString)
      .then((response) => response.blob())
      .then((blob) => {
        const blobUrl = URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = blobUrl;
        link.download = fileName;

        document.body.appendChild(link);
        link.click();

        URL.revokeObjectURL(blobUrl); // 释放Blob URL

        document.body.removeChild(link);
      })
      .catch((error) => {
        throw new CustomError(`Error fetching file: ${error}`, true);
      });
  }
}

function downloadCommonFileByUrl(url: string, name: string = '文件') {
  const iframe = document.createElement('iframe');
  iframe.style.display = 'none';
  iframe.src = url;
  document.body.appendChild(iframe);
  // 可选：稍后移除 iframe
  setTimeout(() => {
    document.body.removeChild(iframe);
  }, 5000);
}

function base64ToBlobUrl(base64: string): string {
  const binaryStr = atob(base64);
  const len = binaryStr.length;
  const buffer = new ArrayBuffer(len);
  const view = new Uint8Array(buffer);
  for (let i = 0; i < len; i++) {
    view[i] = binaryStr.charCodeAt(i);
  }

  // 创建 Blob 对象
  const blob = new Blob([view], { type: 'image/png' });

  // 创建 Blob URL
  const blobUrl = URL.createObjectURL(blob);

  return blobUrl;
}

/**
 * 将图片 URL 转换为 Base64 编码
 * @param url 图片的 URL
 * @returns Base64 编码的字符串
 */
async function imageUrlToBase64(url: string): Promise<string> {
  return new Promise((resolve, reject) => {
    const img = new Image();
    img.crossOrigin = 'Anonymous';

    img.onload = () => {
      const canvas = document.createElement('canvas');
      canvas.width = img.width;
      canvas.height = img.height;
      const ctx = canvas.getContext('2d');
      if (!ctx) {
        return reject(new CustomError('无法获取 Canvas 上下文', true));
      }
      ctx.drawImage(img, 0, 0);
      const dataURL = canvas.toDataURL('image/png');
      resolve(dataURL);
    };

    img.onerror = (error) => reject(error);
    img.src = url;
  });
}

type KeyOfType<T, K extends keyof T> = K;

/* 根据base数字和宽高比计算高宽数据，最长边为base */
function getWidthAndHeightFromRadio(radio: RadioType, base: number = 1024, type: 'max' | 'min' | 'mix' = 'mix'): { width: number; height: number } {
  let height = base;
  let width = base;
  const [x, y] = radio.split(':').map((i) => Number(i));
  if (type === 'max') {
    if (x < y) {
      width = Math.floor((height / y) * x);
    } else {
      height = Math.floor((width / x) * y);
    }
  } else if (type === 'min') {
    if (x > y) {
      width = Math.floor((height / y) * x);
    } else {
      height = Math.floor((width / x) * y);
    }
  } else {
    if (x > y) {
      width = Math.floor((height / y) * x);
    } else {
      height = Math.floor((width / x) * y);
    }
    if (width > 2048) {
      width = 2048;
      height = Math.floor((width / x) * y);
    }
    if (height > 2048) {
      height = 2048;
      width = Math.floor((height / y) * x);
    }
  }

  return {
    width,
    height,
  };
}

/* 获取min-max的随机整数 */
function getRandomInteger(min: number, max: number) {
  min = Math.ceil(min);
  max = Math.floor(max);
  return Math.floor(Math.random() * (max - min + 1)) + min;
}

async function urlToFile(url: string): Promise<File> {
  return new Promise((resolve, reject) => {
    const img = new Image();
    img.crossOrigin = 'Anonymous';
    img.onload = () => {
      const canvas = document.createElement('canvas');
      canvas.width = img.width;
      canvas.height = img.height;
      const ctx = canvas.getContext('2d');
      ctx?.drawImage(img, 0, 0);
      canvas.toBlob((blob) => {
        if (blob) {
          const fileName = url.split('/').pop() || 'untitled';
          const file = new File([blob], fileName, { type: blob.type });
          resolve(file);
        } else {
          reject(new CustomError('Conversion to Blob failed', true));
        }
      }, 'image/jpeg');
    };

    // Set up the onerror handler
    img.onerror = () => {
      reject(new CustomError(`Failed to load image from ${url}`, true));
    };

    img.src = url;
  });
}

/**
 * 从 URL 中提取查询字符串的值
 */
function getQueryFromUrl(url: string, key: string) {
  // 提取查询字符串部分
  const queryString = url.split('?')[1];

  // 解析查询字符串
  const parsed = qs.parse(queryString);

  // 返回 img url 参数的值
  return parsed[key] ?? '';
}

// image组件preview tool bar 自定义方法（添加base64下载）
function renderToolbar({ nodes }: ImageRenderToolbarProps, fileList: { url?: string; name?: string; scene?: UploadScene }[], showGroupBtn: boolean = false) {
  return [
    showGroupBtn && nodes.prev,
    showGroupBtn && nodes.next,
    nodes.rotateCounterclockwise,
    nodes.rotateClockwise,
    nodes.resizeToOriginalSize,
    nodes.zoomIn,
    nodes.zoomOut,
    h(
      NButton,
      {
        circle: true,
        text: true,
        focusable: false,
        color: 'white',
        class: 'mx-8px',
        onClick: async () => {
          const { url, name } = fileList[0];
          if (url) {
            downloadFileFromString(url ?? '', name);
          } else {
            const { data } = await getFileUrl({
              name: name ?? '',
              scene: fileList[0].scene ?? UploadScene.Aigc,
            });
            downloadFileFromString(data?.url ?? '', name);
          }
        },
      },
      {
        icon: renderIcon(CloudDownloadOutline, { size: 24 }),
      },
    ),
    nodes.close,
  ];
}

/**
 * 获取图片的宽度和高度
 */
function getImageDimensions(
  input: File | string,
): Promise<{ width: number; height: number }> {
  return new Promise((resolve, reject) => {
    const img = new Image();

    // Handle File input
    if (input instanceof File) {
      const reader = new FileReader();
      reader.onload = (e) => {
        img.src = e.target?.result as string;
      };
      reader.onerror = reject;
      reader.readAsDataURL(input);
    } else {
      // Handle URL or Base64 input
      img.src = input;
    }

    img.onload = () => {
      resolve({
        width: img.width,
        height: img.height,
      });
    };

    img.onerror = reject;
  });
}

/**
 * 压缩图片
 * @param file 要压缩的图片文件
 * @returns 压缩后的图片文件
 */
function compressImage(file: File): Promise<File> {
  return new Promise((resolve, reject) => {
    if (!file.type.match(/image/)) {
      return resolve(file);
    }
    const img = new Image();
    const reader = new FileReader();

    reader.onload = (event) => {
      if (event.target?.result) {
        img.src = event.target.result as string;
      }
    };

    img.onload = () => {
      const MAX_DIMENSION = 4096;
      let width = img.width;
      let height = img.height;

      // 检查图片的最长边是否超过 4096 像素
      if (width > MAX_DIMENSION || height > MAX_DIMENSION) {
        // 根据原始比例调整宽度和高度
        if (width > height) {
          height = Math.round((height * MAX_DIMENSION) / width);
          width = MAX_DIMENSION;
        } else {
          width = Math.round((width * MAX_DIMENSION) / height);
          height = MAX_DIMENSION;
        }
      } else {
        // 如果图片不需要压缩，直接返回
        return resolve(file);
      }

      const canvas = document.createElement('canvas');
      canvas.width = width;
      canvas.height = height;

      const ctx = canvas.getContext('2d');
      if (!ctx) {
        return reject(new CustomError('无法获取Canvas上下文', true));
      }

      // 使用 requestAnimationFrame 分段处理图片绘制，防止阻塞UI
      function drawImageInChunks() {
        ctx?.drawImage(img, 0, 0, width, height);

        canvas.toBlob(
          (blob) => {
            if (blob) {
              const compressedFile = new File([blob], file.name, {
                type: file.type,
                lastModified: Date.now(),
              });
              resolve(compressedFile);
            } else {
              reject(new CustomError('无法生成Blob对象', true));
            }
          },
          'image/jpeg',
          0.9, // 设置JPEG格式压缩比例
        );
      }
      requestAnimationFrame(drawImageInChunks); // 在下一个重绘周期绘制图片
    };

    reader.onerror = (error) => reject(error);
    reader.readAsDataURL(file); // 读取文件为DataURL以便加载图片
  });
}

export {
  base64ToBlobUrl,
  compressImage,
  convertImageToJpgBase64,
  downloadCommonFileByUrl,
  downloadFileFromString,
  file2BlobPromise,
  file2BlobUrl,
  file2BlobUrlPromise,
  file2BufferPromise,
  fileToBase64,
  fileToBase64Promise,
  getImageDimensions,
  getQueryFromUrl,
  getRandomInteger,
  getWidthAndHeightFromRadio,
  imageUrlToBase64,
  KeyOfType,
  renderIcon,
  renderToolbar,
  safeParse,
  urlToFile,
};
