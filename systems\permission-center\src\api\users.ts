import type { PagedQueryParam, PermissionBaseRes, PermissionPagedRes } from './_common.ts';
import { apiService } from '../services/req.ts';

export interface SysUserInfo {
  id?: number;
  /**
   * 鹰角id
   */
  hgId?: string;
  /** 鹰角账号 */
  hgAccount?: string;
  name?: string;
  nickname?: string;
  /**
   * 组织id
   */
  organization?: number;
  /**
   * 组织编码
   */
  organizationNo?: string;
  /**
   * 组织路径
   */
  orgPath?: string;
  /**
   * 企业邮箱
   */
  enterpriseEmail?: string;
  userStatus?: SysUserStatus;
  avatar?: string;
  feishuUnionId?: string;
  feishuOpenId?: string;
  /**
   * 是否外包
   */
  outsourceFlag?: boolean;
  /**
   * 创建时间
   */
  createdAt?: string;
  /**
   * 更新时间
   */
  updatedAt?: string;
}

/**
 * 用户在职状态
 */
export enum SysUserStatus {
  /**
   * 待入职(流转中)
   */
  Processing = 'PROCESSING',
  /**
   * 待入职(已结束)
   */
  Finished = 'FINISHED',
  /**
   * 待入职(已取消)
   */
  Canceled = 'CANCELED',
  /**
   * 在职
   */
  Onboard = 'ONBOARD',
  /**
   * 离职中
   */
  Resigning = 'RESIGNING',
  /**
   * 已离职
   */
  Resigned = 'RESIGNED',
}

/**
 * 获取当前用户信息
 */
export const getCurrentUserInfo = apiService.GET<
  Record<string, never>,
  Record<string, never>,
  PermissionBaseRes<SysUserInfo>
>(`/api/auth/v1/user/me`);

/**
 * 搜索用户
 */
export const queryUserList = apiService.GET<
  {
    /**
     * 必填，姓名/昵称/邮箱/拼音复合搜索
     */
    query?: string;
  },
  Record<string, never>,
  PermissionBaseRes<SysUserInfo[]>
>(`/api/auth/v1/user/search`);

export interface SysLdapGroupListItem {
  id?: number;
  no?: string;
  name?: string;
  createdAt?: string;
  updatedAt?: string;
  category?: string;
}

export interface SysLdapGroupDetail extends SysLdapGroupListItem {
  createdAt: string;
  updatedAt: string;
  member: SysUserInfo[];
}

/**
 * 获取 LDAP 干员组列表
 */
export const fetchLdapGroupList = apiService.GET<
  PagedQueryParam & { query?: string },
  Record<string, never>,
  PermissionPagedRes<SysLdapGroupListItem>
>(`/api/auth/v1/ldap/search`);

/**
 * 获取 LDAP 干员组详情
 */
export const getLdapGroupDetail = apiService.GET<
  { no?: string },
  Record<string, never>,
  PermissionBaseRes<SysLdapGroupDetail>
>(`/api/auth/v1/ldap/{no}`);
