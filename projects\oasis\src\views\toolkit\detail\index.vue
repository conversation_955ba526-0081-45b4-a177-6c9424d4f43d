<template>
  <div v-track:v="'8sarzmzigd'" :class="prefixCls">
    <div :class="`${prefixCls}-back-btn rounded-[8px] mx-[16px]`" @click="goBack()">
      <span class="cursor-pointer font-size-[18px] font-bold">
        <Icon icon="ph:caret-left-bold" :size="16" /> 返回工具商店
      </span>
    </div>
    <div :class="`${prefixCls}-content flex w-[100%]`">
      <div :class="`${prefixCls}-left`">
        <a-tabs :active-key="tabActiveKey" @change="handleTabChange">
          <template v-for="item in achieveList" :key="item.key">
            <a-tab-pane :tab="item.name">
              <component
                :is="item.component" :tool-i-d="toolId" :cur-readme-link="toolDetail.readme"
                :cur-version-i-d="curVerDetail.ID || versionDetail.ID"
              />
            </a-tab-pane>
          </template>
        </a-tabs>
      </div>
      <div :class="`${prefixCls}-right`">
        <div :class="`${prefixCls}-right-top`">
          <div :class="`${prefixCls}-right__share`">
            <Icon
              v-tippy="{ content: '分享该工具', placement: 'bottom' }" :size="25" icon="ant-design:share-alt-outlined" color="#6b6"
              @click="sharePage"
            />
          </div>
          <div>
            <div>
              <div :class="`${prefixCls}-right__avatar`">
                <AImage
                  v-if="toolDetail.icon" :src="toolDetail.icon" :fallback="defaultImg" :preview="false" alt="icon"
                  class="!h-[60px] !w-[60px] !rounded-2xl"
                />
              </div>
            </div>
            <div>
              <div :class="`${prefixCls}-right__head`">
                <div :class="`${prefixCls}-right__head-up`">
                  <span :class="`${prefixCls}-right__title`">{{ toolDetail.name }}</span>
                  <ATag :class="`${prefixCls}-right__tag`">
                    {{ filterToolkitType(toolDetail.workTypeID!) }}
                  </ATag>
                </div>
                <div :class="`${prefixCls}-right__head-bottom`">
                  {{ toolDetail.description }}
                </div>
              </div>
            </div>
            <div>
              <a-tabs v-model:active-key="curPlatform" centered @change="handlePlatformChange">
                <a-tab-pane v-for="platform in platformList" :key="platform.value">
                  <template #tab>
                    <Icon :icon="platform.icon" class="!mr-0" />
                    {{ platform.label }}
                  </template>
                </a-tab-pane>
              </a-tabs>
            </div>
            <div>
              <div :class="`${prefixCls}-right__detail`">
                <p>
                  <span class="h-[100%] border rounded-1 p-[2px]">
                    {{ toolDetail?.project?.name ?? '通用' }}
                  </span>
                </p>

                <p>
                  <Icon icon="icon-park-outline:user" />
                  作者：{{
                    curVerDetail.author
                      ? formatNickName(curVerDetail.author)
                      : versionDetail.author
                        ? formatNickName(versionDetail.author)
                        : ''
                  }}
                </p>
                <p>
                  <Icon icon="icon-park-outline:flash-payment" />
                  最新版本：
                  {{ versionDetail.version }}
                </p>
                <p>
                  <Icon icon="icon-park-outline:home" />
                  当前版本：
                  <span
                    :class="{
                      'c-FO-Functional-Error1-Default':
                        curVerDetail.version && curVerDetail.version !== versionDetail.version,
                    }"
                  >
                    {{ curVerDetail.version || versionDetail.version }}
                  </span>
                </p>
                <p>
                  <Icon icon="icon-park-outline:file-search-one" />
                  文件大小：
                  <span>
                    {{
                      curVerDetail.sizeKB || versionDetail.sizeKB
                        ? formatKBSize(curVerDetail.sizeKB || versionDetail.sizeKB!)
                        : ''
                    }}
                  </span>
                </p>
              </div>
            </div>
            <div v-if="curVerDetail.downloadLink || versionDetail.downloadLink">
              <a-button
                v-track="'lwxdjfwggf'" v-tippy="{ content: '下载当前版本', placement: 'bottom' }"
                :class="`${prefixCls}-right__download`" :icon-size="20" type="primary" size="large" shape="circle"
                pre-icon="charm:download" :href="curVerDetail.downloadLink || versionDetail.downloadLink" download
              />
              <APopover placement="bottom">
                <template #content>
                  <QrCode :value="qrCodeUrl" tag="img" />
                  <div class="w-full text-center">
                    手机扫码下载
                  </div>
                </template>
                <a-button
                  :class="`${prefixCls}-right__qrcode`" :icon-size="18" type="warning" size="large"
                  shape="circle" pre-icon="carbon:qr-code"
                />
              </APopover>
            </div>
          </div>
        </div>
        <div v-show="tabActiveKey !== 'Version'" :class="`${prefixCls}-right-bottom`">
          <div :class="`${prefixCls}-right-bottom__title`">
            当前版本发版公告:
          </div>
          <MarkdownViewer
            v-if="curVerDetail.releaseNote || versionDetail.releaseNote"
            :value="curVerDetail.releaseNote || versionDetail.releaseNote"
            :class="`${prefixCls}-right-bottom__content`"
          />
          <AEmpty v-else :image="emptyImg" description="暂无发版公告" />
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { Empty as AEmpty, Image as AImage, Popover as APopover, Tag as ATag } from 'ant-design-vue';
import { computed, onBeforeMount, ref, unref } from 'vue';
import Icon from '/@/components/Icon/index';

import { useRouter } from 'vue-router';
import { achieveList } from './detail.data';
import type {
  CommonTypeListItem,
  ToolkitListItem,
  ToolkitVersionListItem,
} from '/@/api/page/model/systemModel';
import {
  getToolkitByID,
  getToolkitTypeListByPage,
  getToolkitVersionByID,
  getToolkitVersionListByPage,
} from '/@/api/page/system';
import defaultImg from '/@/assets/images/default-img.png';
import { MarkdownViewer } from '/@/components/Markdown';
import { QrCode } from '/@/components/Qrcode';
import { useTrack } from '/@/hooks/system/useTrack';
import { formatNickName } from '/@/hooks/system/useUserList';
import { useDesign } from '/@/hooks/web/useDesign';
import { useMessage } from '/@/hooks/web/useMessage';
import { useGo } from '/@/hooks/web/usePage';
import { copyText } from '/@/utils/copyTextToClipboard';
import { downloadByUrl } from '/@/utils/file/download';
import { formatKBSize } from '/@/utils/file/size';
import { isMacOS, isWindows } from '/@/utils/is';
import type {
  platformOptionType,
} from '/@/views/toolkit/settings/toolkitSettings.data';
import {
  platformOptions,
} from '/@/views/toolkit/settings/toolkitSettings.data';
import { useGlobSetting } from '/@/hooks/setting';

const go = useGo();
const { prefixCls } = useDesign('toolkit-detail');
const { currentRoute, replace } = useRouter();
const emptyImg = AEmpty.PRESENTED_IMAGE_SIMPLE;
const toolId = Number(unref(currentRoute).params.id) || undefined;
const versionId = Number(unref(currentRoute).query.v) || undefined;
const tabActiveKey = unref(currentRoute).query.tab;
const toolDetail = ref({} as ToolkitListItem);
const versionDetail = ref({} as ToolkitVersionListItem);
const curVerDetail = ref({} as ToolkitVersionListItem);
const curPlatform = ref<number | undefined>(
  Number(unref(currentRoute).query.platform) || undefined,
);
const fs = Number(currentRoute.value.query?.fs);
const downloadNow = Number(currentRoute.value.query?.downloadNow);

const platformList = ref<platformOptionType[]>([]);
const { beOrigin } = useGlobSetting();

const commonLink = computed(() => {
  if (curVerDetail.value.downloadLink || versionDetail.value.downloadLink) {
    return `${beOrigin}/${curVerDetail.value.downloadLink || versionDetail.value.downloadLink}`;
  }

  return undefined;
});
const iosLink = computed(() => `itms-services://?action=download-manifest&url=${encodeURIComponent(`${beOrigin}/${curVerDetail.value.plistLink || versionDetail.value.plistLink}`)}`);
const qrCodeUrl = computed(() => (curPlatform.value === 2 ? iosLink.value : commonLink.value));

const { createMessage } = useMessage();
const typeList = ref<CommonTypeListItem[]>([]);

// 获取工具商店列表
async function getToolkitTypeList() {
  const { list } = await getToolkitTypeListByPage({ page: 1, pageSize: 999 });

  if (list?.length > 0) {
    typeList.value = list;
  } else {
    typeList.value = [];
  }
}

// 获取工具信息
async function getToolkitDetail() {
  if (!toolId) {
    return;
  }

  const { retool } = await getToolkitByID(toolId);

  toolDetail.value = retool;
  platformList.value = platformOptions.filter((e) => retool.platforms?.includes(e.value));
}

// 获取工具最新版本信息
async function getLatestToolkitVersionDetail() {
  if (!toolId) {
    return;
  }

  const { list } = await getToolkitVersionListByPage(toolId, {
    page: 1,
    pageSize: curPlatform.value ? 1 : 999,
    platform: curPlatform.value,
  });

  if (list?.length > 0) {
    if (!curPlatform.value) {
      if (isMacOS) {
        versionDetail.value = list.find((e) => e.platform === 4) || list[0];
      } else if (isWindows) {
        versionDetail.value = list.find((e) => e.platform === 3) || list[0];
      } else {
        versionDetail.value = list[0];
      }
    } else {
      versionDetail.value = list[0];
    }

    curPlatform.value = versionDetail.value.platform;
  }
}

// 获取当前版本信息
async function getCurVerDetail() {
  if (versionId && toolId) {
    const { retoolVersion } = await getToolkitVersionByID(
      toolId,
      versionId,
    );

    curVerDetail.value = retoolVersion;
  }
}

// 对应工具商店名称
function filterToolkitType(type: number) {
  return type ? unref(typeList).find((e) => e.ID === type)?.name : '';
}

// 分享页面
function sharePage() {
  copyText(location.href, '工具链接已经复制成功，快去分享吧！');
}

async function handleDownload() {
  downloadByUrl({ url: `${beOrigin}/${curVerDetail.value.downloadLink}` });

  const { setTrack } = useTrack();

  await setTrack('lwxdjfwggf');
  createMessage.success(fs === 1 ? '已唤起Oasis' : '已开始下载工具...');
}

onBeforeMount(async () => {
  getToolkitTypeList();
  getToolkitDetail();
  getLatestToolkitVersionDetail();
  await getCurVerDetail();

  if (downloadNow === 1) {
    handleDownload();
  }
});

function handleTabChange(tabName: string) {
  replace({ query: { tab: tabName, v: versionId, platform: curPlatform.value, cardList: currentRoute.value.query?.cardList } });
}

function handlePlatformChange(val: number) {
  replace({ query: { tab: tabActiveKey, platform: val, cardList: currentRoute.value.query?.cardList } });
}

function goBack() {
  go({ name: 'Toolkit', query: { cardList: currentRoute.value.query?.cardList } });
}
</script>

<style lang="less">
@prefix-cls: ~'hypergryph-toolkit-detail';
.@{prefix-cls} {
  display: flex;
  flex-flow: column wrap-reverse;

  &-back-btn {
    margin-top: 10px;
    margin-left: 10px;
    padding: 20px;
    border-radius: 8px;
    background: @FO-Container-Fill1;
  }

  &-col:not(:last-child) {
    padding: 0 10px;

    &:not(:last-child) {
      border-right: 1px dashed rgb(206 206 206 / 50%);
    }
  }

  &-right {
    display: flex;
    flex: 2;
    flex-direction: column;
    min-width: 400px;

    &-top {
      position: relative;
      min-height: 300px;
      margin: 10px 10px 10px 5px;
      padding: 30px;
      overflow: hidden;
      border-radius: 8px;
      background-color: @FO-Container-Fill1;
      text-align: center;
    }

    &-bottom {
      min-height: 200px;
      margin: 0 10px 30px;
      padding: 30px;
      border-radius: 8px;
      background-color: @FO-Container-Fill1;

      &__title {
        padding-bottom: 10px;
        border-bottom: 1px solid;
        border-bottom-color: @table-border-color;
        font-size: 16px;
        font-weight: bold;
      }

      &__content {
        transform: scale(0.95);
      }
    }

    &__ribbon {
      /* top left corner */
      position: absolute;
      top: 20px;
      right: -45px;
      overflow: hidden;

      /* 45 deg ccw rotation */
      transform: rotate(45deg);
      background-color: @FO-Brand-Primary-Default;

      /* shadow */
      box-shadow: 0 0 10px #888;
      white-space: nowrap;

      &-text {
        display: block;
        width: 160px;
        margin: 1px 0;
        padding: 3px 40px;
        border: 1px solid #82aede;
        color: @FO-Content-Components1;
        font-weight: bold;
        text-align: center;
        text-decoration: none;
        text-shadow: 0 0 5px #444;
      }
    }

    &__share {
      position: absolute;
      z-index: 1;
      top: 20px;
      left: 20px;

      :hover {
        cursor: pointer !important;
      }
    }

    &__head {
      &-up {
        position: relative;
        margin-top: 25px;
        padding: 0 20px;
      }

      &-bottom {
        margin-top: 3px;
        font-size: 14px;
      }
    }

    &__title {
      margin-right: 5px;
      font-size: 20px;
      font-weight: bold;
    }

    &__tag {
      position: absolute;
      bottom: 3px;
    }

    &__detail {
      margin-top: 10px;
    }

    &__qrcode {
      margin-left: 16px;

      span.app-iconify {
        margin-top: 3px;
      }
    }

    &__team {
      &-item {
        display: inline-block;
        padding: 4px 24px;
      }

      span {
        margin-left: 3px;
      }
    }
  }

  &-left {
    flex: 3;
    min-width: 600px;
    margin: 10px 5px 10px 10px;
    padding: 20px;
    border-radius: 8px;
    background-color: @FO-Container-Fill1;
  }
}

@media screen and (width <= 960px) {
  .@{prefix-cls}-content {
    flex-direction: column;
  }
}
</style>
