import { toCssVarDeclares } from './helper';
import { type ForgeonTheme, ColorPresetToken, ForgeonThemeMap } from '../tokens';
import { FigmaThemeVars } from '../tokens/figma/figmaThemeVars';

/**
 * 获取Forgeon Design基础颜色
 * @param name 颜色名称
 * @returns 颜色值
 */
function getBasicColor(name: keyof typeof ColorPresetToken): string {
  return ColorPresetToken[name];
}

/**
 * 获取Forgeon Design业务颜色
 * @param name 颜色名称
 * @param theme 主题
 * @returns 颜色值
 */
function getForgeonColor<T extends ForgeonTheme>(name: keyof typeof ForgeonThemeMap[T], theme: T) {
  const colorKey = ForgeonThemeMap[theme][name] as keyof typeof ColorPresetToken;
  if (colorKey === undefined) {
    throw new Error(`The color key ${colorKey} is not found in the color preset.`);
  }

  if (colorKey in ColorPresetToken) {
    return ColorPresetToken[colorKey];
  } else {
    return colorKey;
  }
}

/**
 * 生成对应主题的 css 覆盖，用于处理非全局的样式覆盖
 * @description 将结果挂载至指定元素的 style 属性上
 */
function getCssDeclareOverrides(theme: ForgeonTheme): string {
  return toCssVarDeclares(FigmaThemeVars[theme]).join('');
}

export {
  getBasicColor,
  getCssDeclareOverrides,
  getForgeonColor,
};
