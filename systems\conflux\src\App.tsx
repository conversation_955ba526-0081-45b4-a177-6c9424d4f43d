import { defineComponent } from 'vue';
import { ConfigProvider } from 'ant-design-vue';
import zhCN from 'ant-design-vue/es/locale/zh_CN';
import { ForgeonThemeProvider } from '@hg-tech/oasis-common';
import { MergeHeader } from './components/header.tsx';
import { useAppThemeStore } from './store/modules/appTheme.ts';
import { store } from './store/pinia.ts';

const AppLayout = defineComponent({
  setup() {
    const appThemeStore = useAppThemeStore(store);
    return () => (
      <ForgeonThemeProvider theme={appThemeStore.theme}>
        <ConfigProvider locale={zhCN} theme={appThemeStore.antdToken}>
          <div class="h-100vh flex flex-col overflow-hidden">
            <MergeHeader class="flex-shrink-0" />
            <div class="flex flex-grow-1 flex-col overflow-auto p-20px">
              <router-view />
            </div>
          </div>
        </ConfigProvider>
      </ForgeonThemeProvider>
    );
  },
});

export {
  AppLayout,
};
