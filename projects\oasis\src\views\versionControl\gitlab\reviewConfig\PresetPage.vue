<template>
  <PageWrapper
    class="gitlab-gitlab-group"
    :title="`Git审查配置: ${branchInfo?.name}`"
    headerSticky
    @back="() => emit('back')"
  >
    <div class="m-4 rounded-md bg-FO-Container-Fill1 p-4">
      <div class="my-10 w-full flex flex-col items-center justify-center">
        <template v-if="isCloning">
          <div class="mb-[24px] flex items-center gap-[24px]">
            <Spin :spinning="branchOptionsLoading">
              <div class="text-xl">
                从已存在分支中克隆 Git 审查配置:
              </div>
              <div class="mt-6 text-lg">
                分支:
                <Select
                  v-model:value="selectedCloneBranch"
                  class="w-400px pl-3"
                  placeholder="请选择源分支"
                  showSearch
                  optionFilterProp="name"
                  :options="branchOptions"
                  allowClear
                />
              </div>
            </Spin>
          </div>
          <div class="w-full flex justify-center gap-[6px]">
            <Button type="primary" :disabled="!selectedCloneBranch" :loading="cloneLoading" @click="doClone">
              克隆
            </Button>
            <Button :disabled="cloneLoading" @click="() => isCloning = false">
              取消
            </Button>
          </div>
        </template>
        <template v-else>
          <div class="text-lg c-FO-Content-Text2">
            该 Git 分支配置未配置
          </div>
          <div class="mt-6">
            <Button type="primary" :disabled="cloneLoading" :loading="configLoading" @click="doConfig">
              配置
            </Button>
            <Button class="ml-3" type="primary" :disabled="configLoading" :loading="cloneLoading" @click="() => isCloning = true">
              克隆其他分支的配置
            </Button>
          </div>
        </template>
      </div>
    </div>
  </PageWrapper>
</template>

<script setup lang="ts">
import { useLatestPromise } from '@hg-tech/utils-vue';
import { Button, Select, Spin } from 'ant-design-vue';
import { ref, watch } from 'vue';
import { PageWrapper } from '../../../../components/Page';
import type { BranchesBaseItemListItem } from '../../../../api/page/model/gitlabModel.ts';
import { useUserStore } from '../../../../store/modules/user.ts';
import { addBranchExtras, copyBranches, getBranchesByRepoID } from '../../../../api/page/gitlab.ts';
import { getAllPaginationList } from '../../../../hooks/web/usePagination.ts';

const props = defineProps<{
  repoID: number;
  branchInfo: BranchesBaseItemListItem;
  loading?: boolean;
}>();

const emit = defineEmits<{
  (e: 'update'): void;
  (e: 'back'): void;
}>();

const isCloning = ref(false);
const selectedCloneBranch = ref<number>();
const userStore = useUserStore();
const { data: branchOptions, execute: fetchOptions, loading: branchOptionsLoading } = useLatestPromise(async (repoID: number, mrFormatEnabled = false) => {
  const rawList = await getAllPaginationList((p) => getBranchesByRepoID(userStore.getProjectId, { ...p, repoID, mrFormatEnabled }));
  return (rawList?.list || []).filter((i) => i.reviewEnabled).map((item) => ({
    label: item.description,
    value: item.ID,
  }));
});
const { execute: doConfig, loading: configLoading } = useLatestPromise(async () => {
  const res = await addBranchExtras(userStore.getProjectId, {
    repoID: props.repoID,
    branchID: props.branchInfo?.ID,
  });

  if (res?.code !== 7) {
    emit('update');
  }
});
const { execute: doClone, loading: cloneLoading } = useLatestPromise(async () => {
  if (!selectedCloneBranch.value || !props.branchInfo?.ID) {
    return;
  }

  const res = await copyBranches(userStore.getProjectId, {
    repoID: props.repoID,
    srcBranchID: selectedCloneBranch.value,
    dstBranchID: props.branchInfo?.ID,
  });

  if (res?.code !== 7) {
    emit('update');
  }
});

watch(() => props.repoID, () => {
  fetchOptions(props.repoID);
}, { immediate: true });
</script>
