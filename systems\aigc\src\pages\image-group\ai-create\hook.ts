import type { FormInst } from 'naive-ui';
import type {
  ImageResultInfo,
  ImageTask,
} from './config';
import type {
  PromptConfig,
} from '@/models/ai-image';
import type {
  ControlNet,
  ImageExtraParams,
  SdImageExtractParams,
  SdProgressResponse,
  SdResponse,
} from '@/models/stable-diffusion';
import dayjs from 'dayjs';
import { merge } from 'lodash';
import { NButton } from 'naive-ui';
import { happy, sad, shock } from 'random-jpn-emoji';
import { h, nextTick, ref } from 'vue';
import { useRoute } from 'vue-router';

import {
  AIImagePresets,
  promptPresetWeight,
  TaskProgressState,
} from './config';

import { getPromptConfig } from '@/apis/settings.api';
import {
  checkSDQueueProgress,
  sdExtractImage,
  sdExtraSingleImage,
  sdImg2Img,
  sdTxt2Img,
} from '@/apis/stable-diffusion.api';
import {
  useApiRequest,
  useNaiveUIA<PERSON>,
  useTransHook,
  useUserBase,
} from '@/common/hooks';
import { NumberStepComputer } from '@/common/utils/compute-num-step';
import { base64ToBlobUrl } from '@/common/utils/utils';
import { PageName } from '@/configs/page-config';
import {
  AiGenerImageForm,
  AIStyleTagValue,
  GenrateMode,
  getIndexByImageType,
  ImageEditType,
  ImageType,
  promptPrefix,
} from '@/models/ai-image';
import {
  BaseModel,
  HRAlgorithm,
  Img2ImgRequestParams,
  Txt2ImgRequestParams,
} from '@/models/stable-diffusion';
import { router } from '@/plugins/router';
import CustomError from '@/common/utils/custom-error';

const { userInfo } = useUserBase();

const formRef = ref<FormInst>();
const loading = ref<boolean>(false);
const progressShow = ref<boolean>(false);
const formValue = ref<AiGenerImageForm>(new AiGenerImageForm());
const resultList = ref<ImageResultInfo[]>([]);
const taskList = ref<ImageTask[]>([]);
const currentImageResult = ref<ImageResultInfo | null>();
const currentTask = ref<ImageTask | null>();
const currentImageIndex = ref<number>(0);
const progressTaskState = ref<TaskProgressState>(TaskProgressState.Wait);
const currentTaskProgress = ref<number>(0);
const waitTaskCount = ref<number>(0);
const stepComputer = ref<NumberStepComputer>();
const taskCollapsed = ref<boolean>(true);
const promptConfig = ref<PromptConfig[]>([]);
const skipGuide = ref<boolean>(false);
const hasRendered = ref(false);

function useImageForm() {
  const { data: configData, mutate: getConfig } = useApiRequest({
    request: getPromptConfig,
    errorText: '【Error】获取提示词配置发生错误了',
  });
  const { data: extraImageData, mutate: sendExtraSingleImage } = useApiRequest({
    request: sdExtraSingleImage,
    errorText: '【Error】超分发生错误了',
  });
  const {
    data: extractImageData,
    error: extractImageError,
    mutate: sendExtractImage,
  } = useApiRequest({
    request: sdExtractImage,
    errorText: '【Error】图片提取发生错误了',
  });
  const {
    data: progressData,
    error: progressError,
    mutate: getProgress,
  } = useApiRequest<SdProgressResponse, { taskId: string }>({
    request: checkSDQueueProgress,
    errorText: '【Error】获取生图进度错误了',
  });
  const {
    data: txt2ImgData,
    error: txt2imgError,
    mutate: mutateTxt2Img,
  } = useApiRequest<SdResponse, Txt2ImgRequestParams>({
    request: sdTxt2Img,
    errorText: '【Error】文生图发生错误了',
  });
  const {
    data: img2ImgData,
    error: img2imgError,
    mutate: mutateImg2Img,
  } = useApiRequest<SdResponse, Img2ImgRequestParams>({
    request: sdImg2Img,
    errorText: '【Error】图生图发生错误了',
  });
  const route = useRoute();
  const { translateText } = useTransHook();
  const { message, notification } = useNaiveUIApi();

  /* 生成结果维护队列 */
  const handleImageResult = (
    fileList: ImageResultInfo[],
    capacity: number = 20,
  ) => {
    currentTask.value = {
      list: fileList,
      taskId: fileList[0]?.taskId ?? '',
      createTime: new Date().getTime(),
    };
    taskList.value = [currentTask.value, ...taskList.value].slice(0, capacity);
    resultList.value = currentTask.value.list;
    currentImageResult.value = resultList.value[0];
    currentImageIndex.value = 0;
  };

  /* txt/img to img参数格式化 */
  const formatFormData2params = async (
    formData: AiGenerImageForm,
    type: GenrateMode,
  ): Promise<Txt2ImgRequestParams | Img2ImgRequestParams> => {
    const result
      = formData.genrateMode === GenrateMode.txt
        ? new Txt2ImgRequestParams()
        : new Img2ImgRequestParams();
    const [promptRes, nPromptRes] = await Promise.all([
      translateText(formData.prompt),
      translateText(formData.nPrompt),
    ]);
    if (!promptRes.error && !nPromptRes.error) {
      result.prompt
        = promptPrefix[formData.styleTag].replace(
          '{{styleWeight}}',
          `${formData.styleWeight}`,
        ) + promptRes.prompt;
      formData.nPrompt = nPromptRes.prompt;
      result.negativePrompt = formData.negativePrompt;
    } else {
      throw new CustomError(
        `【翻译请求异常】:${promptRes.message || nPromptRes.message}`,
        true,
      );
    }

    result.batchSize = formData.batchSize;
    result.nIter = formData.nIter;
    result.width = formData.width;
    result.height = formData.height;
    result.seed = formData.seed;
    result.baseModel = formData.baseModel;
    result.overrideSettings.sdModelCheckpoint = formData.baseModel;
    // 是否开启高清模式
    if (formData.isHRRedraw && result instanceof Txt2ImgRequestParams) {
      result.hrPrompt = result.prompt;
      result.hrNegativePrompt = result.negativePrompt;
      result.enableHr = true;
      result.hrScale = formData.hrScaleConfig;
      result.denoisingStrength = 0.3;
      result.hrUpscaler = result.baseModel === BaseModel.Animagine ? HRAlgorithm.animate : HRAlgorithm.common;
    }

    if (result.baseModel === BaseModel.Animagine) {
      result.samplerName = 'Euler a';
      result.steps = 30;
    }

    result.styles = [
      dayjs().format('YYYY-MM-DD_HH-mm-ss'),
      userInfo.value?.nickName ?? '无姓名',
      userInfo.value?.cName ?? '无昵称',
      `${userInfo.value?.ID}` || '无ID',
      result.forceTaskId ?? '无任务ID',
    ];

    // 多个controlnet处理
    result.alwaysonScripts.controlNet.args
      = formData.controlNets as ControlNet[];

    // Mask/底图 特殊处理
    if (type === 'img' && result instanceof Img2ImgRequestParams) {
      if (formData.imageRowOpenState[getIndexByImageType(ImageType.EditBase)] && formData.editBaseRow.url) {
        result.initImages = [formData.editBaseRow.url];
        result.denoisingStrength = formData.editBaseRow.denoisingStrength;
      }
      if (formData.maskRow.url) {
        result.mask = formData.maskRow.url;
        result.maskBlurX = formData.maskRow.maskBlur;
      }
    }
    return result;
  };

  /* 检查排队信息 */
  const checkQueueProgress = async (taskId: string) => {
    if (
      ![TaskProgressState.Progress, TaskProgressState.Queue].includes(
        progressTaskState.value,
      )
    ) {
      return;
    }
    await getProgress({ taskId });
    const data = progressData.value!;
    if (!progressError.value && progressData.value) {
      if (data.queued && data.textinfo && !data.progress) {
        progressTaskState.value = TaskProgressState.Queue;
        waitTaskCount.value = Number.parseInt(
          data.textinfo.match(/In queue: (\d+)\/(\d+)/)?.[1] ?? '0',
          10,
        );
      } else if (!data.queued && data.progress) {
        progressTaskState.value = TaskProgressState.Progress;
      }
      data.progress && stepComputer.value?.updateTarget(data.progress * 100);
    }
  };

  const handleFormValidate = async (): Promise<boolean> => {
    if (!formRef.value) {
      return false;
    }
    return new Promise((resolve) => {
      formRef.value
        ?.validate()
        .then(() => resolve(true))
        .catch(() => resolve(false));
    });
  };

  /* 提交函数 */
  const submit = async (formData: AiGenerImageForm) => {
    if (loading.value) {
      return;
    }
    const isValid = await handleFormValidate();
    if (!isValid) {
      message.error(`表单中存在异常信息哦${sad()}`);
      return;
    }
    if (!formData.prompt && !formData.editBaseRow.url) {
      message.warning(`请至少输入文本信息或者上传编辑底图捏 ${shock()}`);
      return;
    }
    loading.value = true;
    currentTaskProgress.value = 0;
    progressShow.value = true;
    progressTaskState.value = TaskProgressState.Progress;
    let params: Txt2ImgRequestParams | Img2ImgRequestParams;
    try {
      params = await formatFormData2params(formData, formData.genrateMode);
    } catch (error) {
      message.error((error as Error).message);
      loading.value = false;
      progressShow.value = false;
      return;
    }
    const batchSizePercent = (params.batchSize / 8) * 5;
    stepComputer.value = new NumberStepComputer({
      start: 0,
      target: 0,
      durationMs:
        batchSizePercent > 1 ? Math.floor(batchSizePercent * 1000) : 2000,
      intervalMs: 200,
      updateCallback: (value) => {
        currentTaskProgress.value = value;
      },
    });
    stepComputer.value.startIncrement();

    const processInterval = setInterval(
      async () => await checkQueueProgress(params.forceTaskId),
      batchSizePercent > 1 ? Math.floor(batchSizePercent * 1000) : 2000,
    );

    formData.genrateMode === GenrateMode.img && params instanceof Img2ImgRequestParams
      ? await mutateImg2Img(params)
      : await mutateTxt2Img(params as Txt2ImgRequestParams);

    const data = (
      formData.genrateMode === GenrateMode.img
        ? img2ImgData.value
        : txt2ImgData.value
    ) as SdResponse;
    const error = txt2imgError.value || img2imgError.value;

    if (!error) {
      progressTaskState.value = TaskProgressState.Done;
      stepComputer.value?.doneIncrement(currentTaskProgress.value);
      loading.value = false;
      setTimeout(() => {
        progressShow.value = false;
      }, 1000);
      const n = notification.create({
        title: `图片生成已全部完成啦${happy()}`,
        meta: dayjs().format('YYYY-MM-DD HH:mm:ss'),
        action: () =>
          (route.name !== PageName.AIImagePage
            ? h(
              NButton,
              {
                text: true,
                type: 'primary',
                onClick: () => {
                  n.destroy();
                  router.push({
                    name: PageName.AIImagePage,
                  });
                },
              },
              {
                default: () => '前往查看',
              },
            )
            : null),
      });
      if (route.name === PageName.AIImagePage) {
        n.duration = 3000;
      }
      clearInterval(processInterval);
    } else {
      progressTaskState.value = TaskProgressState.Failed;
      stepComputer.value?.doneIncrement(currentTaskProgress.value);
      loading.value = false;
      clearInterval(processInterval);
    }

    const images: ImageResultInfo[]
      = data?.images.map((item, index) => ({
        image: {
          url: `data:image/png;base64,${item}`,
          preview: base64ToBlobUrl(item),
          height: params.height * (params.enableHr ? params.hrScale : 1),
          width: params.width * (params.enableHr ? params.hrScale : 1),
        },
        taskId: params.forceTaskId + (index ? `|${index}` : ''),
        isTaskStart: index === 0,
        isTaskEnd: index === data?.images.length - 1,
        params: data?.info ?? '',
      })) ?? [];

    if (images.length > 0) {
      handleImageResult(images);
    } else {
      throw new CustomError(`【Error】生成图片失败了: ${images}`, true);
    }
  };

  /* 切换当前图片 */
  const switchCurrentImage = (taskId: string, index: number) => {
    if (!resultList.value.some((item) => item.taskId === taskId)) {
      return;
    }
    currentImageResult.value = resultList.value.find(
      (item) => item.taskId === taskId,
    );
    currentImageIndex.value = index;
  };

  /* 切换上一张图片 */
  const switchPrevImage = () => {
    if (currentImageIndex.value <= 0) {
      message.warning(`已经到第一张了喔 ${happy()}`);
      return;
    }
    currentImageIndex.value = currentImageIndex.value - 1;
    currentImageResult.value = resultList.value[currentImageIndex.value];
  };

  /* 切换下一张图片 */
  const switchNextImage = () => {
    if (currentImageIndex.value >= resultList.value.length - 1) {
      message.warning(`已经到最后一张了喔 ${happy()}`);
      return;
    }
    currentImageIndex.value = currentImageIndex.value + 1;
    currentImageResult.value = resultList.value[currentImageIndex.value];
  };

  /* 超分 */
  const extraImage = async (
    params: ImageExtraParams,
  ): Promise<ImageResultInfo | null> => {
    loading.value = true;
    await sendExtraSingleImage(params);
    loading.value = false;

    return {
      image: {
        url: `data:image/png;base64,${extraImageData.value?.image}`,
        preview: base64ToBlobUrl(extraImageData.value?.image ?? ''),
        height: params.upscalingResizeH,
        width: params.upscalingResizeH,
      },
      isTaskStart: true,
      isTaskEnd: true,
      taskId: currentImageResult.value?.taskId ?? '',
      params: '',
    };
  };

  /* 提取 */
  const extractImage = async (
    params: SdImageExtractParams,
  ): Promise<ImageResultInfo | null> => {
    loading.value = true;
    await sendExtractImage(params);
    loading.value = false;
    if (extractImageError.value) {
      return null;
    }
    return {
      image: {
        url: `data:image/jpeg;base64,${extractImageData.value?.images[0]}`,
        preview: base64ToBlobUrl(extractImageData.value?.images[0] ?? ''),
        height: 1024,
        width: 1024,
      },
      isTaskStart: true,
      isTaskEnd: true,
      taskId: '',
      params: '',
    };
  };

  const closeProgressPanel = () => {
    progressShow.value = false;
  };

  const switchTask = (id: string) => {
    currentTask.value = taskList.value.find((item) => item.taskId === id);
    resultList.value = currentTask.value?.list ?? [];
    currentImageResult.value = resultList.value[0];
    currentImageIndex.value = 0;
  };

  // 设置图片编辑组合的预设值
  const handleImageCombSet = (
    type: ImageEditType,
    value = formValue.value.styleTag,
  ) => {
    formValue.value.imageCombSet = type;
    const defaultPreset = AIImagePresets[formValue.value.scenarios].find(
      (item) => item.value === value,
    )?.default as Record<ImageEditType, Record<string, unknown>>;

    merge(formValue.value, defaultPreset?.[type]);

    nextTick(() => {
      formValue.value.styleWeight
        = promptPresetWeight[formValue.value.styleTag];
      // 切换其他标签情况下，重置高清模式按钮状态
      formValue.value.hrScaleConfig = 1;
    });
  };

  const formHookInit = async () => {
    handleImageCombSet(ImageEditType.new);
    await getConfig();
    promptConfig.value = configData.value ?? [];
    const { query } = route;
    // 签名生成逻辑
    if (query?.prompt && query.create === '1') {
      formValue.value.prompt = query.prompt as string;
      formValue.value.styleTag = AIStyleTagValue.AnimateCommon;
      submit(formValue.value);
      router.replace({ name: PageName.AIImagePage });
    }
  };

  return {
    formRef,
    loading,
    formValue,
    resultList,
    currentImageResult,
    taskList,
    currentTask,
    progressTaskState,
    progressShow,
    currentTaskProgress,
    currentImageIndex,
    waitTaskCount,
    taskCollapsed,
    promptConfig,
    skipGuide,
    hasRendered,
    submit,
    extraImage,
    extractImage,
    closeProgressPanel,
    switchCurrentImage,
    switchPrevImage,
    switchNextImage,
    switchTask,
    handleImageCombSet,
    formHookInit,
  };
}

export { useImageForm };
