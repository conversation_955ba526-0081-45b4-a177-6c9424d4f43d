.themeSwitch {
  position: relative;
}

:root {
  overflow: hidden;
  text-size-adjust: 100%;
  line-height: normal;
  &::view-transition-old(root),
  &::view-transition-new(root) {
    animation: none;
    mix-blend-mode: normal;
  }

  &::view-transition-old(root) {
    z-index: 99999;
  }

  &::view-transition-new(root) {
    z-index: 1;
  }

  &[data-theme='dark'] {
    &::view-transition-old(root) {
      z-index: 1;
    }

    &::view-transition-new(root) {
      z-index: 99999;
    }
  }
}
