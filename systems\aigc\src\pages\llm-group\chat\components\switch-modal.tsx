import { NButton, NCheckbox, NText } from 'naive-ui';
import { type PropType, defineComponent, ref } from 'vue';

const SwitchModal = defineComponent({
  props: {
    onConfirm: {
      type: Function as PropType<(isCheck: boolean) => void>,
      default: () => {},
    },
  },
  setup(props) {
    const isCheck = ref(false);
    return () => (
      <div>
        <div class="mb-12px FO-Font-R14">
          <NText class="FO-Font-B14">⚠️安全提示：</NText>
          您即将切换至
          <NText class="FO-Font-B14">外部-通用模式</NText>
          （公开版Deepseek接口），相关数据会被传输至
          <NText class="FO-Font-B14">非鹰角服务器!!!</NText>
          请勿用于公司敏感数据的处理或使用公司内部敏感信息进行提问。如有相关需求，可以切换到
          {' '}
          {'>'}
          {'>'}
          {' '}
          <NText class="FO-Font-B14">内部-安全模式</NText>
          进行使用。
        </div>
        <div class="flex items-center justify-between">
          <NCheckbox label="下次不再提醒" v-model:checked={isCheck.value} />
          <NButton onClick={() => props.onConfirm(isCheck.value)} type="primary">切换</NButton>
        </div>
      </div>
    );
  },
});

export {
  SwitchModal,
};
