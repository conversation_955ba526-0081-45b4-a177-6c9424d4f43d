import type { BasicColumn, FormSchema } from '/@/components/Table';

export enum DevicePlatform {
  Android = 1,
  iOS = 2,
  HarmonyOS = 11,
  Windows = 3,
  MacOS = 4,
  MacOSIntel = 5,
  PS = 6,
  Switch = 7,
  Xbox = 8,
  DS = 9,
  GS = 10,
}

export interface platformOptionType {
  label: string;
  value: DevicePlatform;
  icon?: string;
  fullIcon?: string;
  color?: string;
}

// 平台
export const platformOptions: platformOptionType[] = [
  {
    label: 'Android',
    value: DevicePlatform.Android,
    icon: 'ant-design:android-outlined',
  },
  {
    label: 'iOS',
    value: DevicePlatform.iOS,
    icon: 'ant-design:apple-outlined',
  },
  {
    label: 'Windows',
    value: DevicePlatform.Windows,
    icon: 'ant-design:windows-outlined',
  },
  {
    label: 'macOS',
    value: DevicePlatform.MacOS,
    icon: 'mdi:apple-finder',
  },
  {
    label: 'macOS (Intel)',
    value: DevicePlatform.MacOSIntel,
    icon: 'mdi:apple-finder',
  },
  /*
   * {
   *   label: 'PS',
   *   value: DevicePlatform.PS,
   *   icon: 'mdi:playstation',
   * },
   * {
   *   label:'Switch',
   *   value:DevicePlatform.Switch,
   *   icon:'mdi:nintendo-switch',
   * },
   * {
   *   label: 'Xbox',
   *   value: DevicePlatform.Xbox,
   *   icon: 'mdi:xbox',
   * }
   */
];

export const columns: BasicColumn[] = [
  {
    title: '序号',
    dataIndex: 'ID',
    width: 60,
  },
  {
    title: '名称',
    dataIndex: 'name',
    width: 250,
  },
  {
    title: '工具类型',
    dataIndex: 'workTypeID',
    width: 80,
  },
  {
    title: '支持平台',
    dataIndex: 'platforms',
    width: 300,
  },
  {
    title: '适用项目',
    dataIndex: 'projectID',
    width: 150,
  },
  {
    title: '图标',
    dataIndex: 'icon',
    width: 70,
  },
  {
    title: '描述',
    dataIndex: 'description',
  },
  {
    title: '工具页面',
    dataIndex: 'showPage',
    width: 100,
  },
];

// 搜索静态数据
export const searchFormSchema: FormSchema[] = [
  {
    field: 'projectIDs',
    label: '适用项目',
    component: 'Select',
    slot: 'project-id',
  },
  {
    field: 'name',
    label: '名称',
    component: 'Input',
    componentProps: {
      placeholder: '请输入工具名称',
    },
  },
  {
    field: 'workTypeID',
    label: '工具类型',
    component: 'Select',
    slot: 'tool-type',
  },
];

export const formSchema: FormSchema[] = [
  {
    field: 'BasicSetting',
    label: '基本配置',
    component: 'BorderBox',
    children: [
      {
        field: 'name',
        label: '名称',
        required: true,
        component: 'Input',
      },

      {
        field: 'workTypeID',
        label: '工具类型',
        component: 'RadioButtonGroup',
        slot: 'toolType',
        required: true,
      },
      {
        field: 'adminIds',
        component: 'UserSelect',
        label: '可管理者',
        componentProps: {
          isMultiple: true,
        },
        required: true,
      },
      {
        field: 'projectID',
        label: '适用项目',
        component: 'Select',
        slot: 'projectID',
      },
      {
        field: 'platforms',
        label: '支持平台',
        component: 'CheckboxGroup',
        required: true,
        componentProps: {
          options: platformOptions,
        },
      },
      {
        field: 'bundleID',
        label: 'Bundle ID',
        helpMessage: 'iOS平台需提供',
        ifShow: ({ values }) => {
          return values.platforms?.includes(2);
        },
        required: true,
        component: 'Input',
      },
      {
        label: '图标',
        field: 'icon',
        component: 'Upload',
        valueField: 'singleValue',
        rules: [{ required: true, message: '请上传图标' }],
        colProps: { span: 24 },
        componentProps: {
          valueFormat: 'string',
          maxSize: 20,
          maxNumber: 1,
          multiple: false,
          isPicture: true,
          accept: ['.jpg', '.jpeg', '.gif', '.png', '.webp'],
        },
      },
      /*
       * {
       *   field: 'visible',
       *   label: '是否可见',
       *   component: 'Switch',
       *   componentProps: {
       *     checkedChildren: '显示',
       *     unCheckedChildren: '隐藏',
       *   },
       *   defaultValue: true,
       *   required: true,
       * },
       */
      {
        label: '描述',
        field: 'description',
        component: 'InputTextArea',
        componentProps: {
          placeholder: '请输入描述',
          maxLength: 100,
        },
      },
    ],
  },
  {
    field: 'README',
    label: 'README',
    component: 'BorderBox',
    children: [
      {
        field: 'readme',
        label: 'README',
        helpMessage: [
          'Windows emoji快捷键:【 win + . 】',
          'macOS emoji快捷键:【 ctrl + cmd + 空格 】',
        ],
        component: 'Input',
        slot: 'mdContent',
      },
    ],
  },
];

export const commonFieldNames = {
  label: 'name',
  value: 'ID',
};
