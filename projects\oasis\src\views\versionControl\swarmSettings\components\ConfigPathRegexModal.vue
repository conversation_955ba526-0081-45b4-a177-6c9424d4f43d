<template>
  <Modal
    wrapClassName="swarm-group-path-regex-modal"
    :width="750"
    :open="show"
    centered
    class="swarm-group-path-regex-modal"
    :maskClosable="false"
    destroyOnClose
    :afterClose="modalDestroy"
    @cancel="() => modalCancel()"
  >
    <template #title>
      <div class="relative text-center">
        <span class="vertical-mid text-xl">
          填写路径
        </span>
        <Button
          class="custom-rounded-btn absolute top-[50%] ml-[8px] transform-translate-y-[-50%]"
          size="small"
          @click="handleClear"
        >
          清空配置
        </Button>
      </div>
    </template>
    <div class="mb-[12px] text-center font-size-[16px]">
      配置文件路径需要 包含 的正则表达式
    </div>
    <div v-for="(regexGroup, idx) in regexList" :key="idx">
      <div class="flex items-center">
        <div class="flex-auto">
          <Input
            v-model:value="regexGroup.regex"
            class="border-rd-b-none"
            placeholder="请填写正则表达式"
            @change="() => validErrors[idx] = undefined"
          >
            <template #prefix>
              <span class="mr-[36px]">正则表达式</span>
            </template>
            <template #suffix />
          </Input>
          <Input
            v-model:value="regexGroup.validate"
            class="mt-[-1px] border-rd-t-none"
            placeholder="请填写任意符合该正则表达式的字符串"
            @change="() => validErrors[idx] = undefined"
          >
            <template #prefix>
              <span class="mr-[36px]">验证字符串</span>
            </template>
            <template #suffix>
              <Button size="small" type="link" @click="() => validate(idx)">
                验证
              </Button>
            </template>
          </Input>
        </div>
        <div class="flex-none px-[24px]">
          <div class="flex cursor-pointer border-rd-md bg-[#545454] p-[4px] c-FO-Content-Components1" @click="() => handleRemove(idx)">
            <Icon :size="24" icon="ant-design:minus-outlined" />
          </div>
        </div>
      </div>
      <div class="ml-[4px] min-h-[24px] truncate">
        <template v-if="validErrors[idx] != null">
          <div v-if="validErrors[idx]" class="c-#FF5100">
            {{ validErrors[idx] }}
          </div>
          <div v-else class="c-#008A0E">
            验证通过
          </div>
        </template>
      </div>
    </div>
    <Button class="mb-[24px] w-full" @click="handleAdd">
      <Icon icon="icon-park-outline:plus" :size="26" class="my-[-2px]" />
    </Button>
    <template #footer>
      <div class="sticky bottom-0 mb-2 w-full flex items-center justify-center bg-FO-Container-Fill1">
        <Button
          type="primary"
          class="mr-6 !rounded"
          @click="handleSubmit"
        >
          保存
        </Button>
        <Button class="!rounded" @click="modalCancel()">
          取消
        </Button>
      </div>
    </template>
  </Modal>
</template>

<script lang="ts" setup>
import { Button, Input, Modal } from 'ant-design-vue';
import { ref } from 'vue';
import { Icon } from '../../../../components/Icon';
import type { RegexCustomPath } from '../../../../api/page/model/p4Model.ts';
import type { ModalBaseProps } from '@hg-tech/utils-vue';
import type { NullableBasicResult } from '/@/api/model/baseModel.ts';

const props = defineProps<ModalBaseProps<{ updatedItem?: NullableBasicResult }> & {
  regexList: RegexCustomPath[];
  sentReq?: (regexList: RegexCustomPath[]) => Promise<NullableBasicResult>;
}>();

const regexList = ref(props.regexList);
const validErrors = ref<(string | undefined)[]>([]);
function handleAdd() {
  regexList.value.push({
    regex: '',
    validate: '',
  });
}

async function validate(idx: number) {
  const regexGroup = regexList.value[idx];
  return validErrors.value[idx] = (function () {
    if (!regexGroup.regex) {
      return '正则表达式不能为空';
    }
    if (!regexGroup.validate) {
      return '验证字符串不能为空';
    }
    try {
      if (!new RegExp(regexGroup.regex).test(regexGroup.validate)) {
        return '字符串不符合正则表达式，请修改';
      }
    } catch {
      return '正则表达式不合法，请修改';
    }

    // 验证通过
    return '';
  })();
}

function handleRemove(idx: number) {
  regexList.value.splice(idx, 1);
  validErrors.value.splice(idx, 1);
}

function handleClear() {
  regexList.value = [];
  validErrors.value = [];
}

async function handleSubmit() {
  // 过滤掉空的正则表达式
  regexList.value = regexList.value.filter((item) => item.regex);
  validErrors.value = [];

  // 校验
  regexList.value.forEach((_, idx) => validate(idx));

  if (validErrors.value.some((e) => e)) {
    // 校验失败
    return;
  }
  const updatedItem = await props.sentReq?.(regexList.value);
  return props.modalConfirm({ updatedItem });
}
</script>

<style lang="less">
.swarm-group-path-regex-modal {
  &__radio {
    display: flex;
    margin-top: 8px;
    font-size: 18px;
    font-weight: bold !important;
  }
}
</style>
