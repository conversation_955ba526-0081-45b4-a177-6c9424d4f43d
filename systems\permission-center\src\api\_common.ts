import type { CommonBaseRes } from '@hg-tech/oasis-common';

/**
 * 权限中心报错码
 * @see https://hypergryph.feishu.cn/wiki/J0MFwWgWtig77JkR3QQcN9vLnFf
 */
export enum PermissionErrorCode {
  /**
   * 请求资源不存在
   */
  ErrBadRequestNotFound = 10004,
  /**
   * 内部错误
   */
  ErrServiceInternalUnknown = 10010,
  /**
   * 配置错误
   */
  ErrServiceInternalConfigEmpty = 10011,
  /**
   * 重复操作
   */
  ErrServiceInternalRepeatOperator = 10013,
  /**
   * 代理 stable diffusion 错误
   */
  ErrServiceInternalProxySD = 10100,
  /**
   * 代理 chat_gpt 错误
   */
  ErrServiceInternalProxyGPT = 10101,
  /**
   * DB 错误
   */
  ErrServiceInternalDB = 10110,
  /**
   * Redis 错误
   */
  ErrServiceInternalRedis = 10111,
  /**
   * OSS 错误
   */
  ErrServiceInternalOSS = 10120,
}

export type PermissionBaseRes<T = never> = CommonBaseRes<T, PermissionErrorCode>;

export interface PagedQueryParam {
  page: number;
  pageSize: number;
}

export interface PagedRes<T> {
  list?: T[];
  page?: number;
  pageSize?: number;
  total?: number;
}

export type PermissionPagedRes<T> = CommonBaseRes<PagedRes<T>, PermissionErrorCode>;
