import type { UserConfig } from 'unocss';
import { kebabCase, mapKeys } from 'lodash-es';
import { ForgeonThemeCssVar } from '@hg-tech/forgeon-style';

export const unocssTheme = {
  colors: mapKeys(ForgeonThemeCssVar, (_, key) => {
    // FunctionalSuccess1Default -> FO-Functional-Success1-Default
    // ContentText0 -> FO-Content-Text0
    return `FO-${kebabCase(key)}`
      .replace(/-\w/g, (match) => match.toUpperCase())
      .replace(/-(\d+)/g, '$1');
  }) as Record<string, string>,
  breakpoints: {
    'xs': '320px',
    'sm': '640px',
    'md': '768px',
    'lg': '960px',
    'xl': '1280px',
    '2xl': '1536px',
    '3xl': '1920px',
    '4xl': '2560px',
  },
  borderRadius: {
    none: '0px',
    sm: '4px',
    md: '8px',
    lg: '12px',
    half: '50%',
    full: '9999px',
  },
} satisfies UserConfig['theme'];
