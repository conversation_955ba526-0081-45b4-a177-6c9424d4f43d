import type { Router } from 'vue-router';
import { PlatformEnterPoint } from '@hg-tech/oasis-common';

/**
 * 兼容旧有 hash 路由
 */
export function withHashCompatible(router: Router) {
  router.beforeEach((to, _, next) => {
    let hashPath = to.hash.slice(1);

    /**
     * 兼容 OASIS 内跳转链接解析异常问题
     * @see https://applink.feishu.cn/client/message/link/open?token=AmduMNh5AQAEZ3JHz8X8wAE%3D
     */
    hashPath = hashPath.replace(/state=((\/\w+)+\?fs=\d&oasis=\d)/, (match, p1) => {
      return `state=${encodeURIComponent(p1)}`;
    });

    if (hashPath && to.name === PlatformEnterPoint.Home) {
      return next(hashPath);
    }
    next();
  });
}
