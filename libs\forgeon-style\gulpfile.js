import gulp, { parallel, series } from 'gulp';
import less from 'gulp-less';
import ts from 'gulp-typescript';
import fs from 'node:fs/promises';
import path from 'node:path';
import { fileURLToPath } from 'node:url';
import gulpEsbuild from 'gulp-esbuild';

const SRC_DIR = path.resolve(path.dirname(fileURLToPath(import.meta.url)), 'src');
const DIST_DIR = path.resolve(path.dirname(fileURLToPath(import.meta.url)), 'es');
const STYLES_DIR = path.resolve(path.dirname(fileURLToPath(import.meta.url)), 'dist');

export async function compileLess() {
  gulp
    .src(path.join(SRC_DIR, 'styles/index.less'))
    .pipe(less())
    .pipe(gulp.dest(path.join(STYLES_DIR, 'styles')));
}

export async function copyLess() {
  gulp
    .src(path.join(SRC_DIR, 'styles/**/vars.less'))
    .pipe(gulp.dest(path.join(STYLES_DIR, 'styles')));
}

export async function compileTs() {
  gulp.src([
    'src/**/*.ts',
    '!src/**/*.spec.ts',
  ])
    .pipe(gulpEsbuild({
      format: 'esm',
      target: 'esnext',
    }))
    .pipe(gulp.dest(DIST_DIR));
}

export async function genTypes() {
  const tsResult = gulp.src([
    'src/**/*.ts',
    '!src/**/*.spec.ts',
  ])
    .pipe(ts({
      target: 'esnext',
      module: 'esnext',
      moduleResolution: 'node',
      declaration: true,
      resolveJsonModule: true,
      noEmit: false,
      strict: true,
      skipLibCheck: true,
      allowSyntheticDefaultImports: true,
      rootDir: '.',
      lib: [
        'ESNext',
        'DOM',
        'DOM.Iterable',
      ],
    }));
  return tsResult.dts.pipe(gulp.dest(DIST_DIR));
}

export async function clean() {
  return Promise.all([
    fs.rm(DIST_DIR, { force: true, recursive: true }),
    fs.rm(STYLES_DIR, { force: true, recursive: true }),
  ]);
}

export default series(
  clean,
  [parallel([
    compileLess,
    copyLess,
    compileTs,
    genTypes,
  ])],
);
