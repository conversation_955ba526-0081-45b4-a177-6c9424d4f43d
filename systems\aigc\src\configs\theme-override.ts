import { type ForgeonThemeTokens, ForgeonTheme, getForgeonColor } from '@hg-tech/forgeon-style';
import type { GlobalThemeOverrides } from 'naive-ui';
import { mapValues, merge } from 'lodash';

const commonComponentColors = {
  common: {
    primaryColor: 'BrandPrimaryDefault',
    primaryColorHover: 'BrandPrimaryHover',
    primaryColorPressed: 'BrandPrimaryActive',
    primaryColorSuppl: 'BrandPrimaryActive',
  },
} as {
  [P in keyof NonNullable<GlobalThemeOverrides>]: {
    [T in keyof NonNullable<NonNullable<GlobalThemeOverrides>[P]>]: ForgeonThemeTokens
  }
};
function toThemedComponentColors(mode: ForgeonTheme) {
  return mapValues(commonComponentColors, (component) => {
    return mapValues(component, (value) => getForgeonColor(value, mode));
  });
}

const lightThemeOverrides = merge(toThemedComponentColors(ForgeonTheme.Light), {
  common: {
    fontFamily: "'Oswald-Medium'",
    hoverColor: '#5e45ff15',
    bodyColor: '#ffffff',
    infoColor: '#4e35db',
    errorColor: '#f24f44',
    successColor: '#22bd4b',
    warningColor: '#fa8611',
  },
  Layout: {
    siderColor: '#F5F7FC',
  },
  Button: {
    borderRadiusLarge: '8px',
    borderRadiusMedium: '8px',
    borderRadiusSmall: '6px',
    borderRadiusTiny: '6px',
    heightLarge: '48px',
    heightMedium: '40px',
    heightSmall: '32px',
    heightTiny: '24px',
    border: '1px solid #d3d8e0',
    borderHover: '1px solid #d3d8e0',
    borderPressed: '1px solid #d3d8e0',
    borderFocus: '1px solid #d3d8e0',
    textColor: '#1c1f26',
    textColorHover: '#1c1f26',
    textColorPressed: '#1c1f26',
    textColorFocus: '#1c1f26',
    waveOpacity: 0,
    colorHover: '#f5f6f7',
    colorPressed: '#edeff2',
    colorQuaternaryHover: '#f5f6f7',
    colorQuaternaryPressed: '#edeff2',
    paddingSmall: '8px 24px',
    textColorError: '#ffffff',
    textColorHoverError: '#ffffff',
    textColorFocusError: '#ffffff',
    textColorPressedError: '#ffffff',
    textColorDisabledError: '#ffffff',
  },
  Menu: {
    itemTextColor: '#686A7A',
    itemIconColor: '#686A7A',
    itemTextColorHover: '#404251',
    itemIconColorHover: '#404251',
    itemColorHover: '#E3EAFF',
    itemIconColorCollapsed: '#686A7A',
  },
  Empty: {
    color: '#252527',
  },
  Image: {
    toolbarColor: 'rgba(0, 0, 0, 0.65)',
    toolbarIconColor: '#fff',
    toolbarBorderRadius: '12px',
  },
  Alert: {
    color: '#f7f7fa',
    borderRadius: '8px',
  },
  Divider: {
    color: 'rgb(224, 224, 230)',
  },
  Tabs: {
    colorSegment: '#f7f7fa',
  },
  Dialog: {
    borderRadius: '12px',
    padding: '24px',
    contentMargin: '20px 0 10px 0',
    iconSize: '20px',
  },
  Dropdown: {
    optionColorHover: '#f5f6f7',
    borderRadius: '6px',
  },
  InternalSelectMenu: {
    borderRadius: '6px',
    paddingMedium: '8px',
    optionPaddingMedium: '8px',
  },
} as GlobalThemeOverrides);

const darkThemeOverrides = merge(toThemedComponentColors(ForgeonTheme.Dark), {
  common: {
    fontFamily: "'Oswald-Medium'",
    primaryColor: '#6f6ff7',
    primaryColorHover: '#5f63d8',
    primaryColorPressed: '#5157b9',
    primaryColorSuppl: '#5f63d8',
    hoverColor: '#5f667515',
    bodyColor: '#222730',
    tabColor: '#2c2c2c',
    modalColor: '#29323f',
    popoverColor: '#29323f',
    infoColor: '#3b84f1',
    errorColor: '#db665a',
    successColor: '#36a358',
    warningColor: '#de873c',
  },
  Layout: {
    siderColor: '#202731',
  },
  Progress: {
    fillColor: '#5f63d8',
  },
  Alert: {
    color: '#202731',
    borderRadius: '8px',
    colorInfo: '#5f667515',
    iconColorInfo: '#5f63d8',
  },
  Slider: {
    fillColor: '#5f63d8',
    fillColorHover: '#5f63d8',
  },
  Card: {
    color: '#29323f',
  },
  Input: {
    color: '#2b303b',
  },
  Button: {
    textColorPrimary: '#fff',
    textColorHoverInfo: '#fff',
    textColorHoverPrimary: '#fff',
    textColorFocusPrimary: '#fff',
    textColorDisabledPrimary: '#fff',
    textColorTertiaryHover: '#1CBBFF',
    borderRadiusLarge: '8px',
    borderRadiusMedium: '8px',
    borderRadiusSmall: '6px',
    borderRadiusTiny: '6px',
    heightLarge: '48px',
    heightMedium: '40px',
    heightSmall: '32px',
    heightTiny: '24px',
    border: '1px solid #3f4654',
    borderHover: '1px solid #3f4654',
    borderPressed: '1px solid #3f4654',
    borderFocus: '1px solid #3f4654',
    textColor: '#ebebf0',
    textColorHover: '#ebebf0',
    textColorPressed: '#ebebf0',
    textColorFocus: '#ebebf0',
    waveOpacity: 0,
    colorHover: '#353a47',
    colorPressed: '#3f4654',
    colorQuaternaryHover: '#353a47',
    colorQuaternaryPressed: '#3f4654',
    textColorError: '#660910',
    textColorHoverError: '#660910',
    textColorFocusError: '#660910',
    textColorPressedError: '#660910',
    textColorDisabledError: '#5157b9',
    paddingSmall: '8px 24px',
  },
  Image: {
    toolbarColor: 'rgba(0, 0, 0, 0.65)',
    toolbarIconColor: '#fff',
    toolbarBorderRadius: '12px',
  },
  Divider: {
    color: 'rgba(255, 255, 255, 0.4)',
  },
  DataTable: {
    thColor: '#29323f',
    tdColor: '#202731',
    tdColorStriped: '#29323f',
    tdColorHover: '#191f28',
  },
  Tabs: {
    colorSegment: '#29323f',
  },
  Dialog: {
    borderRadius: '12px',
    padding: '24px',
    contentMargin: '20px 0 10px 0',
    iconSize: '20px',
  },
  Dropdown: {
    optionColorHover: '#353a47',
    borderRadius: '6px',
  },
  InternalSelectMenu: {
    borderRadius: '6px',
    paddingMedium: '8px',
    optionPaddingMedium: '8px',
  },
} as GlobalThemeOverrides);

const themeOverrides = {
  dark: darkThemeOverrides,
  light: lightThemeOverrides,
};

export { themeOverrides };
