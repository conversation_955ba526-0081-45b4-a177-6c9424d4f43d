import type {
  BasicFetchResult,
  BasicPageParams,
  BasicResult,
  CommonTypeItemParams,
  CommonTypeListGetResultModel,
  CommonTypeListItem,
  CommonTypePageParams,
  ProjectPageParams,
  ToolkitItemParams,
  ToolkitListItem,
  ToolkitVersionItemParams,
  ToolkitVersionListItem,
  ToolkitVersionPageListGetResultModel,
  ToolkitVersionPageParams,
} from './model.ts';
import { requestService } from '../services/req.ts';

export * from './model.ts';

/**
 * 获取工具类型列表
 */
export const getToolkitTypeListByPage = requestService.GET<
  CommonTypePageParams,
  Record<string, never>,
  BasicResult<CommonTypeListGetResultModel>
>('/api/v1/workTypes');

/**
 * 根据id获取工具类型信息
 */
export const getToolkitTypeByID = requestService.GET<
  { id: number },
  Record<string, never>,
  BasicResult<CommonTypeItemParams>
>(`/api/v1/workTypes/:id`);

/**
 * 创建工具类型
 */
export const addToolkitType = requestService.POST<
  Record<string, never>,
  CommonTypeListItem,
  BasicResult<never>
>('/api/v1/workTypes');

/**
 * 编辑工具类型
 */
export const editToolkitType = requestService.PUT<
  { editId: number },
  CommonTypeListItem,
  BasicResult<never>
>(`/api/v1/workTypes/:editId`);

/**
 * 删除工具类型
 */
export const deleteToolkitType = requestService.DELETE<
  { editId: number },
  Record<string, never>,
  BasicResult<never>
>(`/api/v1/workTypes/:editId`);

/**
 * 获取工具商店列表
 */
export const getToolkitListByPage = requestService.GET<
  BasicPageParams & Partial<ToolkitListItem>,
  Record<string, never>,
  BasicResult<BasicFetchResult<ToolkitListItem>>
>('/api/v1/tools');

/**
 * 根据id获取工具信息
 */
export const getToolkitByID = requestService.GET<
  { ID: number },
  Record<string, never>,
  BasicResult<ToolkitItemParams>
>(`/api/v1/tools/:ID`);

/**
 * 创建工具
 */
export const addToolkit = requestService.POST<
  Record<string, never>,
  ToolkitListItem,
  BasicResult<never>
>('/api/v1/tools');

/**
 * 编辑工具
 */
export const editToolkit = requestService.PUT<
  { editId: number },
  ToolkitListItem,
  BasicResult<never>
>(`/api/v1/tools/:editId`);

/**
 * 删除工具
 */
export const deleteToolkit = requestService.DELETE<
  { editId: number },
  Record<string, never>,
  BasicResult<never>
>(`/api/v1/tools/:editId`);

/**
 * 获取工具版本列表
 */
export const getToolkitVersionListByPage = requestService.GET<
  ToolkitVersionPageParams & { toolID: string },
  Record<string, never>,
  BasicResult<ToolkitVersionPageListGetResultModel>
>(`/api/v1/tools/:toolID/versions`);

/**
 * 根据id获取工具版本信息
 */
export const getToolkitVersionByID = requestService.GET<
  { toolID: number; ID: number },
  Record<string, never>,
  BasicResult<ToolkitVersionItemParams>
>(`/api/v1/tools/:toolID/versions/:ID`);

/**
 * 创建工具版本
 */
export const addToolkitVersion = requestService.POST<
  { toolID: number },
  ToolkitVersionListItem,
  BasicResult<never>
>(`/api/v1/tools/:toolID/versions`);

/**
 * 编辑工具版本
 */
export const editToolkitVersion = requestService.PUT<
  { toolID: number; editId: number },
  ToolkitVersionListItem,
  BasicResult<never>
>(`/api/v1/tools/:toolID/versions/:editId`);

/**
 * 删除工具版本
 */
export const deleteToolkitVersion = requestService.DELETE<
  { toolID: number; editId: number },
  Record<string, never>,
  BasicResult<never>
>(`/api/v1/tools/:toolID/versions/:editId`);

/**
 * 获取项目列表
 * @param params 筛选条件
 */
export const getProjectListByPage = requestService.GET<
  CommonTypePageParams,
  Record<string, never>,
  BasicResult<BasicFetchResult<ProjectPageParams>>
>(`/api/v1/projects`);

/**
 * 上传文件
 */
export const uploadFile = requestService.POST<
  Record<string, never>,
  FormData,
  BasicResult<{ url: string }>
>(`/api/auth/v2/file/upload`);
