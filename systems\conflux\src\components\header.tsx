import { computed, defineComponent } from 'vue';
import { ForgeonHeader, ForgeonProjectSelector, useMicroAppInject, usePlatformConfigCtx } from '@hg-tech/oasis-common';
import { useForgeonConfigStore } from '../store/modules/forgeonConfig';
import { store } from '../store/pinia';
import { Breadcrumb, Tooltip } from 'ant-design-vue';
import { RouterLink, useRouter } from 'vue-router';
import Icon from '@ant-design/icons-vue';

import ChatHelp24Regular from '../assets/svg/ChatHelp24Regular.svg?component';

const MergeHeader = defineComponent({
  setup() {
    const forgeonConfig = useForgeonConfigStore(store);
    const platformConfig = computed(() => {
      if (window.__MICRO_APP_ENVIRONMENT__) {
        return useMicroAppInject(usePlatformConfigCtx);
      }
    });
    const router = useRouter();

    const breadcrumbData = computed(() => {
      const { meta } = router.currentRoute.value;
      return meta.breadcrumb || [];
    });

    const handleHelp = () => {
      window.open(
        'https://hypergryph.feishu.cn/wiki/UA9qwnWQDigkcZkWYaYc8MF6n3F',
      );
    };

    return () => (
      <ForgeonHeader
        onHandleMenuExpand={() => platformConfig.value?.data.value?.changeMenuExpendStatus(true)}
        showUnfoldIcon={!platformConfig.value?.data.value?.isMenuExpanded}
      >{{
          actions: () => (
            <>
              <ForgeonProjectSelector
                class="w-160px"
                containerClass=" bg-FO-Container-Fill2"
                onSelect={(id) => {
                  if (id === undefined) {
                    return;
                  }
                  forgeonConfig.setCurrentProjectId(id);
                }}
                options={forgeonConfig.projectList}
                value={forgeonConfig.currentProjectId}
              />
              <Tooltip placement="bottomRight" title="【Conflux平台】帮助手册汇总">
                <Icon
                  class="cursor-pointer font-size-[18px]"
                  component={<ChatHelp24Regular />}
                  onClick={handleHelp}
                />
              </Tooltip>
            </>
          ),
          title: () => (
            <Breadcrumb class="h-32px flex items-center">
              {breadcrumbData.value?.map((item, index) => (
                <Breadcrumb.Item class="FO-Font-B16" key={item.name}>
                  {
                    index === breadcrumbData.value.length - 1
                      ? <span class="text-primary-default">{item.label}</span>
                      : (
                        <RouterLink to={{
                          name: item.name,
                        }}
                        >{item.label}
                        </RouterLink>
                      )
                  }

                </Breadcrumb.Item>
              ))}
            </Breadcrumb>
          ),
        }}
      </ForgeonHeader>
    );
  },
});
export { MergeHeader };
