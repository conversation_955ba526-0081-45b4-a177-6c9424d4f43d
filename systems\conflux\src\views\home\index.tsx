import { Button } from 'ant-design-vue';
import { computed, defineComponent, ref, watch } from 'vue';
import { MergeConfigDrawer } from './config';
import { PlatformEnterPoint } from '@hg-tech/oasis-common';
import Icon from '@ant-design/icons-vue';
import { useRouter } from 'vue-router';
import { useMergeHome } from './use-merge-home';
import { MergeRuleTable } from './table';
import type { EdgeData } from './utils';
import { BranchGraph } from './graph/branch-graph';
import { PermissionProvider } from '../../components/PermissionProvider';
import { MergePermission } from '../../constants/premission';

import Add from '../../assets/svg/Add.svg?component';

const MergeHome = defineComponent({
  setup() {
    const router = useRouter();
    const { initHomeData, currentProjectId, ruleList, currentBranchMap } = useMergeHome();
    const defaultForm = ref();

    const isShowMergeConfig = ref(false);
    const branchItems = computed(() => {
      const branchPathSet = new Set<number>();
      ruleList.value.forEach((rule) => {
        branchPathSet.add(rule.sourceStreamId ?? 0);
        branchPathSet.add(rule.targetStreamId ?? 0);
      });

      return Array.from(branchPathSet).filter((id) => currentBranchMap.value.has(id)).map((id) => ({
        id,
        name: currentBranchMap.value.get(id)?.path ?? '--',
        status: 'Merged',
      }));
    });

    const edgeItems = computed(() => {
      const edges: EdgeData[] = [];
      ruleList.value.forEach((rule) => {
        if (
          !rule.sourceStream
          || !rule.targetStream
          || !rule.sourceStreamId
          || !rule.targetStreamId
          || !currentBranchMap.value.has(rule.sourceStreamId)
          || !currentBranchMap.value.has(rule.targetStreamId)
        ) {
          return;
        }
        edges.push({
          source: rule.sourceStreamId,
          target: rule.targetStreamId,
          triggerType: rule.mergeTrigger!,
          status: !!rule.enable,
          resolveRule: rule.defaultResolveRule!,
          ruleId: rule.id,
          taskCount: rule.pendingTaskCount,
        });
      });
      return edges;
    });

    watch(() => currentProjectId.value, async () => {
      currentProjectId.value && initHomeData();
    }, { immediate: true });

    return () => (
      <div class="merge-home rd-12px bg-FO-Container-Fill1 p-20px">
        <div class="mb-20px flex items-center justify-between">
          <div class="FO-Font-B16">分支合并配置图</div>
          <div class="actions flex gap-12px">
            <PermissionProvider permission={{ any: [MergePermission.ViewMergeHistory] }}>
              <Button class="btn-fill-secondary" onClick={() => router.push({ name: PlatformEnterPoint.ConfluxHistory })}>
                自动合并历史
              </Button>
            </PermissionProvider>
            <PermissionProvider permission={{ any: [MergePermission.ViewTask] }}>
              <Button class="btn-fill-secondary" onClick={() => router.push({ name: PlatformEnterPoint.ConfluxTask })}>
                待处理任务
              </Button>
            </PermissionProvider>
            <PermissionProvider permission={{ any: [MergePermission.AddRule] }}>
              <Button
                icon={<Icon class="font-size-16px" component={<Add />} />}
                onClick={() => {
                  defaultForm.value = null;
                  isShowMergeConfig.value = true;
                }}
                type="primary"
              >
                新增自动合并配置
              </Button>
            </PermissionProvider>
          </div>
        </div>
        <div class="overflow-hidden">
          <BranchGraph
            class="mb-20px"
            edges={edgeItems.value}
            nodes={branchItems.value}
          />
        </div>
        <div class="merge-list-container mb-20px">
          <MergeRuleTable onEditRule={(rule) => {
            isShowMergeConfig.value = true;
            defaultForm.value = rule;
          }}
          />
        </div>
        <MergeConfigDrawer
          defaultForm={defaultForm.value}
          id={defaultForm.value?.id}
          v-model:visible={isShowMergeConfig.value}
        />
      </div>
    );
  },
});
export { MergeHome };
