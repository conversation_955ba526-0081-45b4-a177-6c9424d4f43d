import type { PagedQuery } from '../../apis/common.ts';
import React, { useCallback, useRef } from 'react';
import { Button, Empty, Flex, message, Spin } from 'antd';
import { Link } from '@tanstack/react-router';
import AutoSizer from 'react-virtualized-auto-sizer';
import { useMount } from 'ahooks';
import HeaderFrame from '../../components/Frame/Header.tsx';
import UserMenu from '../../components/Frame/UserMenu.tsx';
import { themeColors } from '../../constants/theme.ts';
import IconArrowLeft from '../../components/Icons/IconArrowLeft.tsx';
import { useUserInfo } from '../../hooks/useUserInfo.ts';
import { useInfinityLoad } from '../../hooks/useInfinityLoad.ts';
import { createBugBot, fetchBugBotList } from '../../apis/bugBot.ts';
import { useModalShow } from '../../components/useModalShow.tsx';
import BugBotList from './Content/BugBotList.tsx';
import BugBotModal from './Content/BugBotModal.tsx';

const Entry: React.FC = () => {
  const { userInfo } = useUserInfo();
  const [msg, msgHolder] = message.useMessage();
  const { show, modalHolder } = useModalShow(BugBotModal);

  const fetchBugBots = useCallback(async (query: PagedQuery) => {
    const res = await fetchBugBotList({
      ...query,
    }, {});

    return res.data.data ?? undefined;
  }, []);
  const virtualContainerRef = useRef<HTMLDivElement | null>(null);
  const { currentList, updateListItem, pageInfo, loadMore, reloading, reload, end } = useInfinityLoad(fetchBugBots, {
    getKey: (item) => item.id?.toString(),
  });

  useMount(() => {
    reload();
  });

  const handleNew = useCallback(async () => {
    await show({
      title: '新增',
      async onSubmit(formValue) {
        return createBugBot({}, formValue);
      },
    });
    msg.success('添加成功');
    return reload();
  }, [msg, reload, show]);

  return (
    <div className="h-full w-full flex flex-col">
      {msgHolder}
      {modalHolder}
      <HeaderFrame
        widgetLeft={(
          <span className="flex items-center">
            <Link to="/" className="mr-[12px] flex items-center">
              <IconArrowLeft size={24} fills={[themeColors.primary]} className="mr-[9px]" />
              <div
                className="select-none text-align-center font-size-[24px] c-FO-Brand-Primary-Default font-bold line-height-[32px]"
              >
                提单机器人
              </div>
            </Link>
          </span>
        )}
        widgetRight={(
          <div className="flex items-center gap-[16px]">
            <Button className="border-rd-full" onClick={handleNew}>新增</Button>
            <UserMenu userInfo={userInfo} />
          </div>
        )}
      />
      <div className="h-full w-full flex flex-auto flex-col overflow-auto p-x-[16px] pb-[16px]">
        <div className="flex-auto overflow-hidden">
          <AutoSizer disableWidth={false}>
            {({ height, width }) => (
              <Flex align="center" justify="center" className="overflow-hidden border-[2px] border-lightGray border-rd-[10px] border-style-solid" style={{ width, height }}>
                <Spin size="large" spinning={reloading} delay={200}>
                  {currentList.length > 0
                    ? (
                      <BugBotList
                        ref={virtualContainerRef}
                        height={height}
                        width={width}
                        currentList={currentList}
                        updateListItem={updateListItem}
                        loadMore={loadMore}
                        reload={reload}
                        itemCount={end ? currentList.length : currentList.length + 1}
                        end={end}
                      />
                    )
                    : <Empty />}
                </Spin>
              </Flex>
            )}
          </AutoSizer>
        </div>
      </div>
    </div>
  );
};

export default Entry;
