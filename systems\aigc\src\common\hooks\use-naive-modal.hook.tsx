import { computed, createVNode, defineComponent, reactive, render } from 'vue';
import { type ModalOptions, NConfigProvider, NModal } from 'naive-ui';
import { useAppTheme } from './use-theme.hook';
import { themeOverrides } from '@/configs/theme-override';
import { createSharedComposable } from '@vueuse/core';

const useNaiveModal = createSharedComposable(() => {
  const modals: (() => void)[] = [];
  const { appTheme, currentTheme } = useAppTheme();

  const create = (options: ModalOptions) => {
    const container = document.createElement('div');
    document.body.appendChild(container);

    const state = reactive({ show: true });

    const close = () => {
      state.show = false;
      setTimeout(() => {
        render(null, container);
        modals.splice(modals.indexOf(close), 1);
      }, 300); // 延迟等待动画结束
      options.onClose?.();
    };

    const vNode = createVNode(
      defineComponent(() => () => (
        <NConfigProvider
          inline-theme-disabled
          theme={appTheme.value}
          themeOverrides={themeOverrides[currentTheme.value]}
        >
          <NModal
            class={options.class}
            closable={options.closable ?? true}
            maskClosable={options.maskClosable ?? true}
            onUpdate:show={(value) => !value && close()}
            show={state.show}
            {...options}
          >
            {typeof options.content === 'function' ? options.content() : options.content}
          </NModal>
        </NConfigProvider>
      )),
    );

    render(vNode, container);
    modals.push(close);

    return close;
  };

  const closeAll = () => {
    modals.forEach((close) => close());
    modals.length = 0;
  };

  const isModalOpen = computed(() => {
    return modals.length > 0;
  });

  return { create, closeAll, isModalOpen };
});

export {
  useNaiveModal,
};
