<template>
  <a
    :href="data?.mrLink" target="_blank"
    class="c-FO-Content-Text1 bg-FO-Container-Fill1 hover:c-FO-Content-Text1 flex items-center gap-[12px] overflow-auto b-rd-[10px] p-4"
  >
    <Icon
      icon="carbon:dot-mark"
      :class="match([isPending, isRejected])
        .with([false, false], () => 'c-FO-Functional-Success1-Default')
        .with([true, false], () => 'c-FO-Functional-Warning1-Default')
        .with([P.any, true], () => 'c-FO-Functional-Error1-Default')
        .exhaustive()"
    />
    <div class="flex flex-auto flex-col overflow-auto">
      <ReviewCell class="mb-[12px]" label="描述">
        {{ data?.title }}
      </ReviewCell>
      <div class="flex gap-x-[36px]">
        <ReviewCell label="时间" class="flex-none">
          {{ formatDateString(data?.CreatedAt) }}
        </ReviewCell>
        <ReviewCell label="改动" class="flex-none">
          <span>{{ data?.fileChangedList?.length || 0 }}个文件</span>
          <Tooltip placement="top">
            <template #title>
              <div v-for="(fileChanged, fileIndex) in data?.fileChangedList" :key="fileIndex">
                <div v-if="fileChanged.action === ReviewStatus.Ready">
                  {{ fileChanged.newPath }}
                </div>
                <div v-else-if="fileChanged.action === ReviewStatus.Approve">
                  {{ fileChanged.oldPath }}
                </div>
                <div v-else-if="fileChanged.action === ReviewStatus.Refuse">
                  {{ fileChanged.newPath }}
                </div>
                <div v-else-if="fileChanged.action === ReviewStatus.NeedRevision">
                  {{ fileChanged.oldPath }}
                  <Icon icon="icon-park-outline:arrow-right">
                    {{ fileChanged.newPath }}
                  </Icon>
                </div>
              </div>
            </template>
            <Icon icon="icon-park-solid:info" class="ml-1" />
          </Tooltip>
        </ReviewCell>
        <ReviewCell
          label="请求人"
          :content="formatNickName(data?.author)"
          class="flex-none"
        />
        <ReviewCell
          v-if="data?.approvers?.length"
          label="Reviewer"
          :content="formatUserList(data?.approvers)"
          class="min-w-[100px] flex-shrink-1"
        />
        <ReviewCell
          v-if="data?.reviewers?.length"
          label="审查人"
          :content="formatUserList(data?.reviewers)"
          class="min-w-[100px] flex-shrink-1"
        />
      </div>
    </div>
    <div class="flex flex-none items-center gap-[12px] truncate font-bold">
      <div class="inline-flex gap-[12px]">
        <span v-if="passedApprover" class="c-#0067C7">{{ formatNickName(passedApprover?.approver) }}已通过review</span>
        <span v-if="passedReviewer" class="c-FO-Functional-Success1-Default">{{ formatNickName(passedReviewer?.reviewer) }}已通过审批</span>
        <span v-else-if="refusedReviewer" class="c-FO-Functional-Error1-Default">审批已被{{ formatNickName(refusedReviewer?.reviewer) }}拒绝</span>
      </div>
      <template v-if="isPending && !isRejected">
        <div v-if="readyForApprove" class="c-FO-Functional-Warning1-Default">正在等待Review</div>
        <template v-else-if="readyForReview">
          <span v-if="data?.pipelineStatusInfo?.enable && data.pipelineStatusInfo?.status !== 10" class="c-FO-Functional-Warning1-Default">正在等待流水线通过</span>
          <template v-else-if="allowToReview">
            <Popconfirm
              title="确认通过审批?"
              :disabled="disabled || refusing"
              @confirm.stop="() => void handleReview(ReviewStatus.Approve)"
            >
              <Button
                :loading="updatingReview"
                :disabled="disabled || refusing"
                class="b-FO-Functional-Success1-Default border-2 font-bold !rounded-[12px]"
                size="small"
              >
                通过审批
              </Button>
            </Popconfirm>
            <Popconfirm
              title="确认拒绝?"
              :disabled="disabled || updatingReview"
              @confirm.stop="() => void handleRefuse()"
            >
              <Button
                :disabled="disabled || updatingReview"
                :loading="refusing"
                class="b-FO-Functional-Error1-Default border-2 font-bold !rounded-[12px]"
                size="small"
              >
                拒绝
              </Button>
            </Popconfirm>
          </template>
          <span v-else class="c-FO-Functional-Warning1-Default">正在等待审批</span>
        </template>
      </template>
      <Icon icon="icon-park-outline:arrow-right-up" class="c-FO-Brand-Primary-Default" />
    </div>
  </a>
</template>

<script setup lang="ts">
import { Button, message, Popconfirm, Tooltip } from 'ant-design-vue';
import { useLatestPromise } from '@hg-tech/utils-vue';
import { match, P } from 'ts-pattern';
import { computed } from 'vue';
import dayjs from 'dayjs';
import { updateReviewStatus } from '../../../../api/page/gitlab.ts';
import { formatNickName, formatUserList } from '../../../../hooks/system/useUserList.ts';
import Icon from '../../../../components/Icon';
import { type GitlabReviewItem, ApproveStatus, ReviewStatus } from '../../../../api/page/model/gitlabModel.ts';
import { useUserStore } from '../../../../store/modules/user.ts';
import ReviewCell from './ReviewCell.vue';
import { useAdmin } from '../../../../hooks/useProjects.ts';

const props = defineProps<{
  data: GitlabReviewItem | undefined;
  disabled?: boolean;
}>();
const emit = defineEmits<{
  (e: 'update'): void;
}>();

const userStore = useUserStore();
const { isSuperAdminOrProjectAdmin } = useAdmin();

const readyForApprove = computed(() => (Boolean(props.data?.needApprove) && props.data?.approveStatus === ApproveStatus.Ready));
const readyForReview = computed(() => (Boolean(props.data?.needReview) && props.data?.reviewStatus === ReviewStatus.Ready));
const isPending = computed(() => readyForApprove.value || readyForReview.value);
const isRejected = computed(() => (props.data?.approveStatus === ApproveStatus.Refuse) || (props.data?.reviewStatus === ReviewStatus.Refuse));

const passedApprover = computed(() => props.data?.reviewerStatusList.find((i) => i.approver != null));

const getReviewerByStatus = (status: ReviewStatus) => props.data?.reviewerStatusList.find((i) => i.reviewStatus === status);
const passedReviewer = computed(() => getReviewerByStatus(ReviewStatus.Approve));
const refusedReviewer = computed(() => getReviewerByStatus(ReviewStatus.Refuse));

const allowToReview = computed(() => {
  return props.data?.reviewStatus === ReviewStatus.Ready && (isSuperAdminOrProjectAdmin.value || props.data?.reviewers.some((i) => i.ID === userStore.getUserInfo.ID));
});

function formatDateString(isoDateStr?: string) {
  const dObj = dayjs(isoDateStr);
  if (isoDateStr && dObj.isValid()) {
    return dObj.format('YYYY-MM-DD HH:mm:ss');
  }
}

const { execute: updateReview, loading: updatingReview } = useLatestPromise(updateReviewStatus);
async function handleReview(reviewStatus: ReviewStatus) {
  const res = await updateReview(userStore.getProjectId, props.data?.ID, { reviewStatus });
  if (res?.code !== 7) {
    message.success('已通过');
    emit('update');
  }
}

const { execute: handleRefuse, loading: refusing } = useLatestPromise(async () => {
  const res = await updateReview(userStore.getProjectId, props.data?.ID, { reviewStatus: ReviewStatus.Refuse });

  if (res?.code !== 7) {
    message.success('已拒绝');
    emit('update');
  }
});
</script>
