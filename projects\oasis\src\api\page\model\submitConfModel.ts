import type { UserInfoModel } from '../../sys/model/userModel';
import type { RegexCustomPath } from './p4Model';
import type { BaseItem, BasicFetchResult, BasicPageParams } from '/@/api/model/baseModel';

// TAG列表 Model
export interface TagListItem extends BaseItem {
  name?: string;
  description?: string;
  streamIDs?: number[];
  configType?: number;
  tagPathItems?: RegexCustomPath[];
}

// id获取TAG信息 接口参数
export interface TagItemParams {
  retag: TagListItem;
}

// TAG列表 接口参数
export type TagPageParams = BasicPageParams & TagListItem;

// TAG列表 接口返回数据
export type TagListGetResultModel = BasicFetchResult<TagListItem>;

// 批量更新Tag排序接口参数 Model
export interface BatchTagParams {
  idList: number[];
}

// Trigger事件类型列表 Model
export interface TriggerTypeListItem extends BaseItem {
  name?: string;
  typeTrigger?: number;
  disabled?: boolean;
}

// id获取Trigger事件类型信息 接口参数
export interface TriggerTypeItemParams {
  retype: TriggerTypeListItem;
}

// Trigger事件类型列表 接口参数
export type TriggerTypePageParams = BasicPageParams & TriggerTypeListItem;

// Trigger事件类型列表 接口返回数据
export type TriggerTypeListGetResultModel = BasicFetchResult<TriggerTypeListItem>;

// Trigger可用参数列表 Model
export interface TriggerArgListItem extends BaseItem {
  name?: string;
  eventID?: number;
  eventIds?: number[];
  eventTypes?: TriggerTypeListItem[];
}

// id获取Trigger可用参数信息 接口参数
export interface TriggerArgItemParams {
  rearg: TriggerArgListItem;
}

// Trigger可用参数列表 接口参数
export type TriggerArgPageParams = BasicPageParams & TriggerArgListItem;

// Trigger可用参数列表 接口返回数据
export type TriggerArgListGetResultModel = BasicFetchResult<TriggerArgListItem>;

// Trigger网络钩子列表 Model
export interface TriggerWebhookListItem extends BaseItem {
  name?: string;
  eventType?: TriggerTypeListItem;
  eventTypeID?: number;
  path?: string;
  isRaw?: boolean;
  raw?: string;
  url?: string;
  token?: string;
  enable?: boolean;
  args?: TriggerArgListItem[];
  argIds?: number[];
  projectID?: number;
  serverID?: number;
  depotID?: number;
  isDefault?: boolean;
  headers?: { [key: string]: string }[];
  paths?: string[];
  pathList?: { path: string; prefix?: string }[];
  createdBy?: UserInfoModel;
  sort?: number;
  withStream?: boolean;
  body?: string;
  bodyObj?: object;
}

// id获取Trigger网络钩子信息 接口参数
export interface TriggerWebhookItemParams {
  rearg: TriggerWebhookListItem;
}

// Trigger网络钩子列表 接口参数
export type TriggerWebhookPageParams = BasicPageParams &
  TriggerWebhookListItem & { fuzzy?: string };

// Trigger网络钩子列表 接口返回数据
export type TriggerWebhookListGetResultModel = BasicFetchResult<TriggerWebhookListItem>;

// 预览Trigger网络钩子 接口返回数据
export interface PreviewTriggerWebhookGetResultModel {
  content: string;
}

// 预览Trigger网络钩子 接口返回数据
export interface DiffTriggerWebhookGetResultModel {
  diff: {
    newText: string;
    oldText: string;
  };
}

// p4超管用户列表 Model
export interface PerforcesListItem {
  ID?: string | number;
  server?: string;
  username?: string;
  password?: string;
  description?: string;
  CreatedAt?: string;
  timestamp?: number;
}

// id获取p4超管用户信息 接口参数
export interface PerforcesItemParams {
  reperforce: PerforcesListItem;
}

// p4超管用户列表 接口参数
export type PerforcesPageParams = BasicPageParams & PerforcesListItem;

// p4超管用户列表 接口返回数据
export type PerforcesListGetResultModel = BasicFetchResult<PerforcesListItem>;
