<template>
  <Modal
    width="700px"
    :open="show"
    :maskClosable="false"
    :destroyOnClose="true"
    :centered="true"
    :afterClose="modalDestroy"
    @cancel="() => modalCancel()"
  >
    <template #title>
      <div class="relative text-center">
        <span class="vertical-mid text-xl">
          勾选路径
        </span>
        <Button
          class="custom-rounded-btn absolute top-[50%] ml-[8px] transform-translate-y-[-50%]"
          size="small"
          @click="resetAll"
        >
          清空勾选
        </Button>
      </div>
    </template>
    <AuditAuditTree
      ref="p4TreeRef"
      :branchInfo="branchInfo"
      :permissionList="groupInfo?.reviewPaths"
    />
    <template #footer>
      <Button type="primary" class="mr-6" @click="handleSubmit">
        保存
      </Button>
      <Button @click="modalCancel">
        取消
      </Button>
    </template>
  </Modal>
</template>

<script setup lang="ts">
import { Button, message, Modal } from 'ant-design-vue';
import { type ModalBaseProps, useLatestPromise } from '@hg-tech/utils-vue';
import type { BranchesBaseItemListItem, SelectReviewGroupsItem } from '../../../../../api/page/model/gitlabModel.ts';
import { updatePathConfig } from '../../../../../api/page/gitlab.ts';
import { useUserStore } from '../../../../../store/modules/user.ts';
import AuditAuditTree from './AuditTree.vue';
import { ref } from 'vue';
import type { FilePathNode } from '../../../../../api/model/baseModel.ts';

const props = defineProps<ModalBaseProps & {
  branchInfo?: BranchesBaseItemListItem;
  groupInfo?: SelectReviewGroupsItem;
}>();

const userStore = useUserStore();
const { execute: updateConfig } = useLatestPromise(updatePathConfig);
const p4TreeRef = ref();

async function resetAll() {
  p4TreeRef.value?.treeRef?.setCheckedKeys([]);
  p4TreeRef.value?.setHasChangeCheck(true);
}

async function handleSubmit() {
  const reviewPaths: FilePathNode[] = p4TreeRef.value?.getOptimalList() ?? props.groupInfo?.reviewPaths;
  if (!reviewPaths?.length) {
    message.warning('请至少选择一个路径');
    return;
  }

  await updateConfig(userStore.getProjectId, {
    id: props.groupInfo?.ID,
    reviewPaths,
  });

  props.modalConfirm();
}
</script>
