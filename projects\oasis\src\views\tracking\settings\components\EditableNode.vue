<template>
  <div :class="prefixCls" v-bind="$attrs">
    <slot v-if="!showIsEdit" name="default">
      <div :style="{ width }">
        {{ showValue }}
      </div>
    </slot>
    <a-input
      v-else
      v-model:value="showValue"
      placeholder="请输入名称"
      class="!rounded"
      :style="{ width }"
      @press-enter="handleChange()"
    >
      <template #suffix>
        <a-button type="text" class="!px-2 !py-0 !c-FO-Functional-Success1-Default" @click="handleChange()">
          <Icon icon="charm:tick" />
        </a-button>
        <a-button type="text" class="!px-2 !py-0 !c-FO-Functional-Error1-Default" @click="handleCancel()">
          <Icon icon="charm:cross" />
        </a-button>
      </template>
    </a-input>
  </div>
</template>

<script lang="ts" setup name="EditableNode">
import { computed } from 'vue';
import Icon from '/@/components/Icon';
import { useDesign } from '/@/hooks/web/useDesign';

const props = defineProps({
  value: {
    type: String,
    default: '',
  },
  isEdit: {
    type: Boolean,
    default: false,
  },
  width: {
    type: String,
    default: '100%',
  },
});

const emit = defineEmits(['update:value', 'update:isEdit', 'change', 'cancel']);

const { prefixCls } = useDesign('editable-node');

const showValue = computed({
  get() {
    return props.value;
  },
  set(val) {
    emit('update:value', val);
  },
});

const showIsEdit = computed({
  get() {
    return props.isEdit;
  },
  set(val) {
    emit('update:isEdit', val);
  },
});

function handleChange() {
  emit('change', showValue.value);
  showIsEdit.value = false;
}

function handleCancel() {
  emit('cancel');
  showIsEdit.value = false;
}
</script>

<style lang="less">
@prefix-cls: ~'hypergryph-editable-node';
//  .@{prefix-cls} {
//  }
</style>
