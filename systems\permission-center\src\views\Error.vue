<template>
  <Result class="w-full" :status="status" :title="renderInfo.title" :subTitle="renderInfo.subTitle">
    <template #extra>
      <Button type="primary">
        返回首页
      </Button>
    </template>
  </Result>
</template>

<script setup lang="ts">
import { type ResultProps, Button, Result } from 'ant-design-vue';
import { computed } from 'vue';

const props = withDefaults(defineProps<{
  status?: ResultProps['status'];
  title?: string;
  subTitle?: string;
}>(), {
  status: 404,
});

const renderInfo = computed(() => {
  switch (Number(props.status)) {
    case 403:
      return {
        title: props.title ?? '403',
        status: 403,
        subTitle: props.subTitle ?? '抱歉，您无权访问此页面。',
      };
    case 500:
      return {
        title: props.title ?? '500',
        status: 500,
        subTitle: '抱歉，服务器报告错误。',
      };
    case 404:
    default:
      return {
        title: props.title ?? '404',
        status: 404,
        subTitle: props.subTitle ?? '抱歉，您访问的页面不存在, 或您无权访问此页面, 请联系管理员核实。',
      };
  }
});
</script>
