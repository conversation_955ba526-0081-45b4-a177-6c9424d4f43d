<template>
  <BasicTree
    v-bind="$attrs"
    :key="renderRootKey"
    ref="treeRef"
    :class="prefixCls"
    :treeData="treeData"
    checkable
    :loadData="getNodeData"
    :defaultExpandLevel="1"
    :selectable="false"
    :fieldNames="defaultFieldNames"
    @check="checkEvent"
  >
    <template #title="item">
      <div
        :class="{ 'font-bold': item.hasChange, [`${prefixCls}__half-checked`]: item.halfChecked }"
      >
        <Icon :icon="getTypeIcon(item.type)" :color="getTypeIcon(item.type, true)" class="mr-1" />
        {{ item.name }}
      </div>
      <div v-if="hasImport" class="ml-4px">
        <div v-if="item.importType === importTypeMenu.includeImport" class="FO-Font-R12 b-1 b-FO-Content-Text2 b-rd-4px p-2px c-FO-Content-Text2">
          包含import+
        </div>
        <div v-else-if="item.importType === importTypeMenu.fromImport" class="FO-Font-R12 b-1 b-rd-4px bg-FO-Content-Text2 p-2px c-FO-Container-Fill1">
          来自import+
        </div>
        <div v-else-if="item.importType === importTypeMenu.point" class="h-8px w-8px b-rd-4px bg-FO-Content-Text2" />
      </div>
    </template>
  </BasicTree>
</template>

<script lang="ts" setup>
import type { TreeDataItem } from 'ant-design-vue/es/tree/Tree';
import { cloneDeep, difference, map, pullAll, union } from 'lodash-es';
import { defaultFieldNames, getTypeIcon, importTypeMenu } from '../../p4PermissionManage/p4StateTree/p4StateTree.data';
import { type ComputedRef, type PropType, type Ref, nextTick, ref, unref, watchEffect } from 'vue';
import type { P4PermissionListItem, UpdateP4PermissionItem } from '../../../../api/page/model/p4Model';
import { getP4TreeCurNodeList } from '../../../../api/page/p4';
import { Icon } from '../../../../components/Icon';
import { type FieldNames, type TreeActionType, BasicTree } from '../../../../components/Tree';
import { useTree } from '../../../../components/Tree/src/hooks/useTree';
import { useDesign } from '../../../../hooks/web/useDesign';
import { useP4StoreWithOut } from '../../../../store/modules/p4';
import { useUserStore } from '../../../../store/modules/user';
import { findNode } from '../../../../utils/helper/treeHelper';

const props = defineProps({
  // 初始权限列表
  permissionList: {
    type: Array as PropType<P4PermissionListItem[]>,
    default: () => [],
  },
  hasImport: {
    type: Boolean,
    default: true,
  },
});
const emit = defineEmits<{
  (e: 'stateChange'): void;
}>();
const { prefixCls } = useDesign('three-state-tree');
// 树ref
const treeRef = ref<Nullable<TreeActionType>>(null);
// 树数据
const treeData = ref<TreeDataItem[]>([]);
// 初始选中的节点key
const initCheckedKeys = ref<string[]>([]);
// 所有选中的节点key
const checkedKeys = ref<string[]>([]);
// 所有半选中的节点key
const halfCheckedKeys = ref<string[]>([]);

// 判断是否有修改过勾选状态
const hasChangeCheck = ref(false);

const userStore = useUserStore();
const p4Store = useP4StoreWithOut();

const { getAllKeys } = useTree(
  treeData as Ref<TreeDataItem[]>,
  defaultFieldNames as unknown as ComputedRef<FieldNames>,
);

// 是否初次加载
const isFirstLoad = ref<boolean>(true);

/**
 * FIXME 临时解决 root 更新但底层节点不更新的问题
 * @see https://project.feishu.cn/test-techcenter/issue/detail/5254865625
 */
const renderRootKey = ref(Math.random());

watchEffect(async () => {
  if (p4Store.getCurStream) {
    const path = `${p4Store.getCurStream.path}/`;
    const find = props.permissionList?.find((e) => e.path === path);
    treeData.value = [
      {
        path,
        name: p4Store.getCurStream.path?.split('/')?.pop(),
        permit: find?.permit || 0,
        type: 1,
        isLeaf: false,
      },
    ] as unknown as TreeDataItem[];
    if (!isFirstLoad.value) {
      renderRootKey.value = Math.random();
      await nextTick();
      getNodeData(treeData.value[0]);
    } else {
      isFirstLoad.value = false;
    }
  }
});

async function getNodeData(treeNode: P4PermissionListItem) {
  if (!userStore.getProjectId || !p4Store.getCurStream || treeNode.isLoaded) {
    return;
  }
  const { items } = await getP4TreeCurNodeList(userStore.getProjectId, p4Store.getCurStream.ID!, {
    currentPath: treeNode.path,
  });
  const children = items
    ? map(items, (e) => {
      props.permissionList?.forEach((per) => {
        if (per.path === e.path) {
          e.permit = per.permit;
        } else if (e.path.endsWith('/') && per.path?.startsWith(e.path)) {
          e.halfChecked = true;
        }
      });
      // 父节点为允许,当前节点不是拒绝,则当前节点为允许
      if (treeNode.permit === 1 && e.permit !== 2) {
        e.permit = 1;
      }
      // 是初次加载, 当前节点为允许且不是半选状态,则加入选中列表
      if (!hasChangeCheck.value && e.permit === 1 && !e.halfChecked) {
        initCheckedKeys.value.push(e.path);
      }
      // 半选中的节点需要从服务器获取子节点
      if (e.halfChecked) {
        getNodeData(e);
      }
      return {
        path: e.path,
        name: e.name,
        permit: e.permit,
        type: e.kind === 'file' ? 2 : 1,
        isLeaf: e.kind === 'file',
        importType: e.importType,
      };
    })
    : [];
  treeRef.value?.updateNodeByKey(treeNode.path!, {
    halfChecked: undefined,
    isLoaded: true,
    children,
  });
  nextTick(() => {
    !hasChangeCheck.value && treeRef.value?.setCheckedKeys(initCheckedKeys.value);
  });
}

// 过滤掉列表里所有子节点
function removeAllChildren(list: string[]) {
  // 列表中的所有子节点列表
  const childrenList = new Set<string>([]);
  list.forEach((a) => {
    if (a.endsWith('/')) {
      list.forEach((b) => {
        if (b !== a && b.includes(a)) {
          childrenList.add(b);
        }
      });
    }
  });
  // 过滤掉列表里所有子节点
  pullAll(list, [...childrenList]);
}

// 树选中事件
function checkEvent(cKeys, e) {
  if (!hasChangeCheck.value) {
    hasChangeCheck.value = true;
    removeAllChildren(initCheckedKeys.value);
  }
  checkedKeys.value = cloneDeep(cKeys);

  halfCheckedKeys.value = e.halfCheckedKeys;
  const findNodeItem = findNode(unref(treeData), (n) => n.path === e.node.path);
  if (findNodeItem) {
    const newCheckedKeys = cloneDeep(cKeys);
    removeAllChildren(newCheckedKeys);
    changeState(findNodeItem, newCheckedKeys);
    emit('stateChange');
  }
}

// 切换状态
function changeState(item: P4PermissionListItem, checkedKeys: string[]) {
  item.hasChange
      = initCheckedKeys.value.includes(item.path!) !== checkedKeys.includes(item.path!);
  if (item.children?.length) {
    item.children.forEach((e) => {
      changeState(e, checkedKeys);
    });
  }
}

// 获取最优权限列表
function getOptimalList(onlyCheckedList = false) {
  if (!unref(hasChangeCheck)) {
    return undefined;
  }
  // 选中列表
  const checkedKeyList = treeRef.value?.getCheckedKeys() as string[];
  removeAllChildren(checkedKeyList);
  // 未选中列表
  const uncheckedKeyList = difference(
    getAllKeys(),
    union(unref(checkedKeys), unref(halfCheckedKeys)),
  ) as string[];
  removeAllChildren(uncheckedKeyList);

  // 是否是选中列表更短
  const isCheckListShort
      = onlyCheckedList || checkedKeyList.length <= uncheckedKeyList.length + 1;
    // 最优列表
  const optimalList = isCheckListShort ? checkedKeyList : uncheckedKeyList;
  const formatList: UpdateP4PermissionItem[] = [];
  optimalList?.forEach((e) => {
    formatList.push({
      path: e,
      permit: isCheckListShort ? 1 : 2,
    });
  });
  // 若 未选中列表更优 则需要将 根节点选中并加入
  if (!isCheckListShort) {
    formatList.unshift({
      path: unref(treeData)[0].path,
      permit: 1,
    });
  }
  return formatList;
}

function setHasChangeCheck(check: boolean) {
  hasChangeCheck.value = check;
}
// 抛出ref, data 和 获取变化列表的方法
defineExpose({
  treeRef,
  treeData,
  getOptimalList,
  setHasChangeCheck,
});
</script>

<style lang="less">
@prefix-cls: ~'hypergryph-three-state-tree';
.@{prefix-cls} {
  .ant-tree-node-content-wrapper {
    background: none !important;

    &:hover {
      background-color: #f5f6f5 !important;
    }
  }
}

html[data-theme='dark'] .@{prefix-cls} {
  .ant-tree-node-content-wrapper {
    &:hover {
      background-color: #272727 !important;
    }
  }
}
</style>
