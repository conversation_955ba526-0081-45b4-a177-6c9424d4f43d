import { RuleV1MergeTrigger } from '@hg-tech/api-schema-merge';
import { type PropType, defineComponent } from 'vue';
import Icon from '@ant-design/icons-vue';
import BasicStrokeInterface from '../../../assets/svg/BasicStrokeInterface.svg?component';
import BasicStrokeMerge from '../../../assets/svg/BasicStrokeMerge.svg?component';
import BasicStrokeTag from '../../../assets/svg/BasicStrokeTag.svg?component';
import BasicFillCheck2 from '../../../assets/svg/BasicFillCheck2.svg?component';
import BasicFillBlock from '../../../assets/svg/BasicFillBlock.svg?component';
import BasicFillWarning from '../../../assets/svg/BasicFillWarning.svg?component';

const TriggerEdgeLabel = defineComponent({
  props: {
    triggerType: {
      type: String as PropType<RuleV1MergeTrigger>,
      default: RuleV1MergeTrigger.AUTO,
    },
  },
  setup(props) {
    const renderTriggerType = () => {
      switch (props.triggerType) {
        case RuleV1MergeTrigger.AUTO:
          return <Icon class="c-FO-Datavis-Blue1" component={<BasicStrokeMerge />} />;
        case RuleV1MergeTrigger.TAG:
          return <Icon class="c-FO-Datavis-Blue1" component={<BasicStrokeTag />} />;
        case RuleV1MergeTrigger.API:
          return <Icon class="c-FO-Datavis-Blue1" component={<BasicStrokeInterface />} />;
        default:
          return null;
      }
    };

    const triggerLabelMap = {
      [RuleV1MergeTrigger.AUTO]: '自动触发',
      [RuleV1MergeTrigger.TAG]: '标签触发',
      [RuleV1MergeTrigger.API]: 'API触发',
      [RuleV1MergeTrigger.MERGE_TRIGGER_INVALID]: '未知触发器',
    };

    return () => (
      <div class="edge-label FO-Font-R12 h-full w-full flex items-center justify-center b-1px b-FO-Datavis-Blue2 rd-4px bg-FO-Container-Fill1">
        {renderTriggerType()}
        <div class="truncate">
          {triggerLabelMap[props.triggerType] || 'Unknown'}
        </div>
      </div>
    );
  },
});

const StatusEdgeLabel = defineComponent({
  props: {
    status: {
      type: Boolean as PropType<boolean>,
      default: true,
    },
    taskCount: {
      type: Number as PropType<number>,
      default: 0,
    },
    ruleId: {
      type: String as PropType<string>,
      default: '',
    },
    onTagClick: {
      type: Function as PropType<() => void>,
      default: () => {},
    },
  },
  setup(props) {
    const renderStatusIcon = () => {
      if (!props.status) {
        return <Icon class="font-size-16px c-FO-Functional-Error1-Default" component={<BasicFillBlock />} />;
      } else if (props.status && props.taskCount <= 0) {
        return <Icon class="font-size-16px c-FO-Datavis-Green1" component={<BasicFillCheck2 />} />;
      } else {
        return <Icon class="font-size-16px c-FO-Functional-Warning1-Default" component={<BasicFillWarning />} />;
      }
    };

    const onHandleTagClick = () => {
      if (props.ruleId && props.status && props.taskCount > 0) {
        props.onTagClick();
      }
    };

    return () => (
      <div
        class={[
          'edge-label',
          props.status && props.taskCount > 0 ? 'cursor-pointer' : '',
          'h-full w-full flex items-center justify-center gap-4px b-1px rd-4px b-FO-Datavis-Blue2 rd-4px bg-FO-Container-Fill1 FO-Font-R12',
        ]}
        onClick={onHandleTagClick}
      >
        {renderStatusIcon()}
      </div>
    );
  },
});

export {
  StatusEdgeLabel,
  TriggerEdgeLabel,
};
