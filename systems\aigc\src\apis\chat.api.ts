import { type PromptDescRequest, ChatMessageResponse, ChatModelResponse, ChatSessionListResponse, ChatSessionResponse } from '@/models/chat';
import { GlobalEnv } from '@/configs/global-env';
import { PromptDescResponse } from '@/models/chat';
import { fetcher } from '@/plugins/fetcher';

const API_PREFIX = `${GlobalEnv.APIPrefixMap[GlobalEnv.HGEnv]}/aigc${GlobalEnv.HGEnv === 'dev' ? '-api' : ''}`;

async function submitChatContent(params: PromptDescRequest) {
  return fetcher.requestAPI<PromptDescResponse>({
    url: `${API_PREFIX}/v1/create_polish_description`,
    method: 'POST',
    data: { ...params },
    type: PromptDescResponse,
  });
}

/**
 * 创建chat会话
 */
async function createChatSession() {
  return fetcher.requestAPI<ChatSessionResponse>({
    url: `${API_PREFIX}/v1/deepseek/session`,
    method: 'POST',
    type: ChatSessionResponse,
  });
}

/**
 * 获取支持的模型
 */
async function getChatModel() {
  return fetcher.requestAPI<ChatModelResponse>({
    url: `${API_PREFIX}/v1/deepseek/models`,
    method: 'GET',
    type: ChatModelResponse,
  });
}

/**
 * 获取历史会话list
 */
async function getChatSessionList(params: { page: number; pageSize: number }) {
  return fetcher.requestAPI<ChatSessionListResponse>({
    url: `${API_PREFIX}/v1/deepseek/histories`,
    method: 'GET',
    data: { page: params.page, pageSize: params.pageSize },
    type: ChatSessionListResponse,
  });
}

/**
 * 获取历史会话详情
 */
async function getChatSessionDetail(params: { sessionId: string }) {
  return fetcher.requestAPI<ChatMessageResponse>({
    url: `${API_PREFIX}/v1/deepseek/session/${params.sessionId}`,
    method: 'GET',
    type: ChatMessageResponse,
  });
}

async function deleteSession(params: { sessionId: string }) {
  return fetcher.requestAPI<ChatSessionResponse>({
    url: `${API_PREFIX}/v1/deepseek/session/${params.sessionId}`,
    method: 'DELETE',
    type: ChatSessionResponse,
  });
}

async function updateSessionDetail(params: { sessionId: string; title: string }) {
  return fetcher.requestAPI<ChatSessionResponse>({
    url: `${API_PREFIX}/v1/deepseek/session/${params.sessionId}`,
    method: 'PUT',
    data: { title: params.title },
    type: ChatSessionResponse,
  });
}

export {
  createChatSession,
  deleteSession,
  getChatModel,
  getChatSessionDetail,
  getChatSessionList,
  submitChatContent,
  updateSessionDetail,
};
