<template>
  <BasicModal
    :class="prefixCls"
    :title="`埋点额外信息配置:${toolFuncName}`"
    :width="800"
    @ok="ok"
    @register="register"
  >
    <div>
      <div class="c-FO-Content-Text2 mb text-center">
        配置额外埋点信息，触发埋点时可选提交这些信息，以在埋点分析页面中查看
      </div>

      <div :key="keyNum" :class="`${prefixCls}__additional-Info-${ID}`" class="max-h-400px overflow-scroll">
        <div v-for="(item, index) in extInfo" :key="index" class="mb-2 flex items-center gap-8 b-rd-4px bg-FO-Container-Fill3 p-2">
          <div class="i-ic:round-drag-handle h-20px w-20px cursor-grab" :class="`${prefixCls}__drag-btn`" />
          <div class="flex flex-1 items-center gap-4">
            <div>
              信息名称
            </div>
            <a-input v-model:value="item.name" class="flex-1" :maxlength="30" />
          </div>
          <div class="flex flex-1 items-center gap-4">
            <div>标识id</div>
            <a-input v-model:value="item.key" class="flex-1" placeholder="仅支持英文标识" :maxlength="30" @change="(e) => keyChange(e, index)" />
          </div>
          <div class="i-icon-park-outline:delete h-20px w-20px cursor-pointer" @click="handleDelete(index)" />
        </div>
      </div>

      <div class="flex cursor-pointer justify-center gap-2 b-rd-4px bg-FO-Container-Fill3 py-2" @click="addExtInfo">
        <Icon icon="icon-park-outline:plus" />
        <div>添加</div>
      </div>
      <div
        v-if="hasError"
        class="my text-center c-#AD0000"
      >
        {{ errorFont }}
      </div>
    </div>
  </BasicModal>
</template>

<script lang="ts" setup>
import { useDesign } from '/@/hooks/web/useDesign';
import { BasicModal, useModalInner } from '/@/components/Modal';
import { nextTick, ref, watch } from 'vue';
import { Icon } from '/@/components/Icon';

import { useSortable } from '/@/hooks/web/useSortable';
import { isNullOrUnDef } from '/@/utils/is';

defineOptions({
  name: 'AdditionalInfoEditModal',
});

const emit = defineEmits(['success']);
const { prefixCls } = useDesign('additional-info-edit-modal');
const toolFuncName = ref<string>('');
const ID = ref<number>(0);
const usable = ref<number>(1);
const keyNum = ref<number>(1);
const hasError = ref<boolean>(false);
const errorFont = ref<string>('');
const extInfo = ref<{ name: string, key: string }[]>([]);
const [register, { closeModal, redoModalHeight }] = useModalInner((data) => {
  toolFuncName.value = data.toolFuncName;
  ID.value = data.ID;
  usable.value = data.usable;
  extInfo.value = data.extInfo;

  initDrag();
});

function handleDelete(index) {
  extInfo.value.splice(index, 1);
}

function keyChange(e, index) {
  // 判断e.data是否是大小写字母
  if (!/^[A-Z]+$/i.test(e.data)) {
    // 删除非字母字符
    extInfo.value[index].key = extInfo.value[index].key.replace(/[^a-z]/g, '');
    setTimeout(() => {
      keyNum.value++;
    });
  }
}

async function ok() {
  for (let i = 0; i < extInfo.value.length; i++) {
    if (extInfo.value[i].name === '' || extInfo.value[i].key === '') {
      errorFont.value = '请完整填写每一行，或删除空值行';
      hasError.value = true;

      return;
    }

    if (['CreatedAt', 'ip', 'user'].includes(extInfo.value[i].key)) {
      errorFont.value = `标识id{${extInfo.value[i].key}}无法使用，请更改`;
      hasError.value = true;

      return;
    }
  }

  const keys = extInfo.value.map((obj) => obj.key);
  const hasSome = keys.filter((item, index) => keys.indexOf(item) !== index);

  if (hasSome.length) {
    errorFont.value = `重复的标识id{${hasSome[0]}}`;
    hasError.value = true;

    return;
  }

  emit('success', extInfo.value);
  closeModal();
  hasError.value = false;
  errorFont.value = '';
}

function initDrag() {
  nextTick(() => {
    const el = document.querySelector(`.${prefixCls}__additional-Info-${ID.value}`) as HTMLElement;

    const { initSortable } = useSortable(el, {
      handle: `.${prefixCls}__drag-btn`,
      onEnd: async ({ oldIndex, newIndex }) => {
        if (isNullOrUnDef(oldIndex) || isNullOrUnDef(newIndex) || oldIndex === newIndex) {
          return;
        }

        // 排序接口
        const currentGroup = extInfo.value[oldIndex];

        extInfo.value.splice(oldIndex, 1);
        extInfo.value.splice(newIndex, 0, currentGroup);
        setTimeout(() => {
          keyNum.value++;
        });
      },
    });

    initSortable();
  });
}

function addExtInfo() {
  extInfo.value = [...extInfo.value, { name: '', key: '' }];
  redoModalHeight();
}

watch(() => keyNum.value, () => {
  initDrag();
});
</script>

<style lang="less">
@prefix-cls: ~'hypergryph-additional-info-edit-modal';
.@{prefix-cls} {
  height: 50%;
}
</style>
