/* eslint-disable no-console */
import type { RouteLocationNormalized } from 'vue-router';
import { GlobalEnv } from '@/configs/global-env';
import { type SDKCarrier, initWebJS } from '@hg/event-log';
import type { TrackEventName, TrackEventParams } from './event';
import { findItemInMenuByPath, ModulesMenuConfig, useForgeonTrackCtx, useMicroAppInject } from '@hg-tech/oasis-common';
import { useAppTheme, useUserBase } from '@/common/hooks';

interface CommonParams {
  /**
   * 当前路由路径
   */
  route_path: string;
  /**
   * 前路由路径
   */
  prv_route_path: string;
  /**
   * 前路由所属业务层级
   */
  business_level_prv: string;
  /**
   * 所属业务层级
   */
  business_level_cur: string;
  /**
   * 主题
   */
  theme: string;
  /**
   * 当前route_name
   */
  route_name?: string;
  /**
   * 前route_name
   */
  prv_route_name?: string;
}

let commonParams: CommonParams = {
  route_path: '',
  prv_route_path: '',
  business_level_cur: '',
  business_level_prv: '',
  theme: '',
  prv_route_name: '',
  route_name: '',
};

function setTrackCommonParams(params: Partial<CommonParams>) {
  commonParams = { ...commonParams, ...params };
}

function getTrackCommonParams() {
  return { ...commonParams };
}

function beforeLog() {
  const { currentTheme } = useAppTheme();
  const { route_path, prv_route_path } = getTrackCommonParams();
  const { path: prvPath } = findItemInMenuByPath(ModulesMenuConfig, prv_route_path);
  const { path } = findItemInMenuByPath(ModulesMenuConfig, route_path);

  setTrackCommonParams({
    business_level_cur: path.join('/') || '',
    business_level_prv: prvPath.join('/'),
    route_name: path[path.length - 1],
    prv_route_name: prvPath[prvPath.length - 1] || '',
    theme: currentTheme.value,
  });
}

let webEventTracker: {
  sendPV: (params: {
    data: { route_name: string; route_path: string; prv_route_name?: string; prv_route_path?: string };
  }) => void;
  sendEvent: (eventName: TrackEventName, params: Record<string, any>) => void;
} | undefined;

// 处理微应用埋点
// 微应用埋点使用主应用的埋点能力
if (window.__MICRO_APP_ENVIRONMENT__) {
  const { data: track } = useMicroAppInject(useForgeonTrackCtx);
  webEventTracker = track.value;
} else {
  // 子应用独立启动时增加埋点能力
  if (GlobalEnv.isProd) {
  /**
   * @see https://hypergryph.feishu.cn/wiki/PbPrw5HJMiLy05k2GOjceRL5nxc
   */
    const HGEventTracker = initWebJS({
    /**
     * @see https://datalake.hypergryph.net/area/11/app/app_manager/app_list_manager
     */
      appId: '7l6k0y2vw4rksevp6bibwuht',
      regionTag: 'cn',
      enableRealTimeSend: true,
    }) as SDKCarrier;

    function setUserInfo() {
      const { userInfo } = useUserBase();
      HGEventTracker.setUser(userInfo.value?.email || '');
    }

    webEventTracker = {
      sendPV: (params) => {
        beforeLog();
        setUserInfo();
        HGEventTracker?.pageViewEvent?.({
          ...getTrackCommonParams(),
          ...params.data,
        });
      },
      sendEvent: (eventName, params) => {
        beforeLog();
        setUserInfo();
        HGEventTracker?.event(eventName, {
          ...getTrackCommonParams(),
          ...params,
        });
      },
    };
  }
}

function sendLog(eventName: TrackEventName, eventData: Record<string, any>) {
  try {
    const dataWithCommonParams = { ...eventData };
    if (!GlobalEnv.isProd) {
      beforeLog();
      console.log(`[event-report] ${eventName} eventData:`, {
        ...getTrackCommonParams(),
        ...eventData,
      });
    }
    webEventTracker?.sendEvent(eventName, dataWithCommonParams);
  } catch (e) {
    console.error(`[sendLog] Failed to send ${eventName} event:`, e);
  }
}

/**
 * 记录路由跳转
 */
export function traceRouteChange(cur: RouteLocationNormalized, from?: RouteLocationNormalized) {
  try {
    setTrackCommonParams({
      route_path: cur.path,
      prv_route_path: from?.path || '',
    });

    if (!GlobalEnv.isProd) {
      beforeLog();
      console.log(`[event-report] pv eventData:`, {
        ...getTrackCommonParams(),
        route_name: cur.name as string,
        route_path: cur.path,
        prv_route_name: from?.name as string,
        prv_route_path: from?.name ? from?.path : undefined,
      });
    }
    webEventTracker?.sendPV({
      data: {
        route_name: cur.name as string,
        route_path: cur.path,
        prv_route_name: from?.name as string,
        prv_route_path: from?.name ? from?.path : undefined,
      },
    });
  } catch (e) {
    console.error('[traceRouteChange]路由埋点失败：', e);
  }
}

/**
 * 记录点击事件
 */
export function traceClickEvent<T extends TrackEventName>(eventName: T, eventData?: TrackEventParams<T>) {
  try {
    sendLog(eventName, {
      ...eventData,
    });
  } catch (e) {
    console.error('[traceClickEvent]点击埋点失败：', e);
  }
}
