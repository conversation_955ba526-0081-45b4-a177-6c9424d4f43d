<template>
  <div :class="prefixCls">
    <ACard class="!mb-4">
      <template #title>
        <span v-if="!isChild">使用总量</span>
        <span v-else>
          <div v-for="name in curParentNames" :key="name" class="mr-1 inline-block">
            <Icon icon="ep:arrow-right-bold" />
            {{ name }}
          </div>
        </span>
      </template>
      <div v-if="!isChild" class="flex">
        <div>
          <span class="mr-3">周期</span>
          <a-select
            v-model:value="curPeriod"
            placeholder="请选择周期"
            :options="periodOptions"
            @change="() => init()"
          />
        </div>
        <div class="ml-4">
          <span class="mr-3">范围</span>
          <a-select
            v-model:value="curRange"
            placeholder="请选择范围"
            :options="showRangeOptions"
            @change="() => init()"
          />
        </div>
      </div>
      <div v-if="!isNoData" class="relative">
        <div
          ref="chartRef"
          :class="`${prefixCls}__chart`"
          :style="{ height: '400px', width: '100%' }"
        />
        <div :class="`${prefixCls}__chart-chart`">
          <div
            v-for="(func, i) in [...functionList].reverse()"
            :key="func.ID"
            :class="`${prefixCls}__chart-legend`"
          >
            <div class="flex items-center truncate text-xs">
              <div
                v-if="func.hasChild && parentID !== func.ID"
                :class="`${prefixCls}__chart-legend__label cursor-pointer`"
                :style="{ backgroundColor: getCurReverseColor(i, functionList.length) }"
                @click="() => getFuncChild(func)"
              >
                <Icon icon="ep:arrow-right-bold" :size="11" />
              </div>
              <div
                v-else-if="!func.hasChild && func.hasExtInfo && parentID !== func.ID"
                :class="`${prefixCls}__chart-legend__label cursor-pointer`"
                :style="{ backgroundColor: getCurReverseColor(i, functionList.length) }"
                @click="() => getinfoTable(func)"
              >
                <Icon icon="icon-park-outline:dot" :size="11" />
              </div>
              <div
                v-else
                :class="`${prefixCls}__chart-legend__label`"
                :style="{ backgroundColor: getCurReverseColor(i, functionList.length) }"
              />
              <span class="mx-[6px]">{{ func.name }}</span>
            </div>
            <div
              v-if="func.avgTime"
              :class="`${prefixCls}__chart-legend__time`"
              @click="() => handleTime(func.ID)"
            >
              平均耗时:{{ func.avgTime }}s
            </div>
          </div>
        </div>
      </div>
      <AEmpty v-if="isNoData" :image="emptyImg" class="h-400px flex items-center justify-center" />
    </ACard>

    <FunctionCard
      v-if="!isNoData && curParentID !== -1"
      ref="childCardRef"
      :parent-i-d="curParentID"
      :tool-i-d
      :project-i-d
      is-child
      :period="curPeriod"
      :range="curRange"
      :parent-names="curParentNames"
    />
    <TimeConsumingModal @register="registerModal" />
    <AdditionalInfoTableModal @register="infoTableModal" />
  </div>
</template>

<script lang="ts" setup>
import type { BarSeriesOption } from 'echarts';
import type { ComponentPublicInstance, Ref } from 'vue';
import { Card as ACard, Empty as AEmpty } from 'ant-design-vue';
import { isEmpty } from 'lodash-es';
import { computed, nextTick, ref, watch, watchEffect } from 'vue';
import { periodOptions, rangeOptions } from '../analysis.data';
import TimeConsumingModal from './TimeConsumingModal.vue';
import AdditionalInfoTableModal from './AdditionalInfoTableModal.vue';
import type {
  TrackingToolChartsFunctionResultModel,
  TrackingToolChartsItemResultModel,
} from '/@/api/page/model/trackingModel';
import { getTrackingToolFunctions } from '/@/api/page/tracking';
import { Icon } from '/@/components/Icon';
import { useModal } from '/@/components/Modal';
import { useDesign } from '/@/hooks/web/useDesign';
import { useECharts } from '/@/hooks/web/useECharts';
import { ECHARTS_COLOR_LIST } from '/@/settings/designSetting';
import { isNullOrUnDef } from '/@/utils/is';

defineOptions({
  name: 'FunctionCard',
});

const props = defineProps({
  projectID: {
    type: Number,
    default: 0,
  },
  toolID: {
    type: Number,
    required: true,
  },
  parentID: {
    type: Number,
    default: -1,
  },
  isChild: {
    type: Boolean,
    default: false,
  },
  period: {
    type: Number,
    default: 1,
  },
  range: {
    type: Number,
    default: 5,
  },
  parentNames: {
    type: Array as PropType<string[]>,
    default: () => [],
  },
});

const { prefixCls } = useDesign('tracking-analysis-function-card');

const chartRef = ref<HTMLDivElement | null>(null);
const childCardRef = ref<ComponentPublicInstance | null>(null);
const { setOptions } = useECharts(chartRef as Ref<HTMLDivElement>);
const emptyImg = AEmpty.PRESENTED_IMAGE_SIMPLE;
const [registerModal, { openModal }] = useModal();
const [infoTableModal, { openModal: openInfoTableModal }] = useModal();
const ToolParentFunction = ref<TrackingToolChartsItemResultModel[]>([]);
const functionList = ref<TrackingToolChartsFunctionResultModel[]>([]);
const isNoData = ref(true);
const curParentID = ref(props.parentID);
const curParentNames = ref([...props.parentNames]);
const curFunc = ref<TrackingToolChartsFunctionResultModel>();

const curPeriod = ref(props.period);
const curRange = ref(props.range);

const showRangeOptions = computed(() => {
  return rangeOptions.map((item) => {
    const curPeriodTemp = periodOptions.find((e) => e.value === curPeriod.value);

    return { label: item.label + curPeriodTemp?.label || '', value: item.value };
  });
});

function getCurReverseColor(index: number, sum: number) {
  return ECHARTS_COLOR_LIST.slice(0, sum).reverse()[index];
}

watchEffect(() => {
  if (props.period) {
    curPeriod.value = props.period;
  }

  if (props.range) {
    curRange.value = props.range;
  }
});

async function getToolFunctions(ID?: number) {
  if (isNullOrUnDef(props.projectID)) {
    return;
  }

  curParentID.value = -1;

  const { list, function: funcs } = await getTrackingToolFunctions({
    toolID: props.toolID,
    period: curPeriod.value,
    range: curRange.value,
    parentID: ID || props.parentID || curParentID.value,
    projectID: props.projectID,
  });

  ToolParentFunction.value = list || [];

  if (!ID) {
    functionList.value = funcs || [];

    if (props.isChild) {
      curFunc.value = functionList.value?.[0];
      curParentNames.value = [...props.parentNames];
      curParentNames.value.push(curFunc.value?.name);
    }
  }

  isNoData.value = isEmpty(funcs);
}

async function init(ID?: number) {
  await getToolFunctions(ID);

  if (isNoData.value) {
    return;
  }

  const xAxisData: string[] = [];
  const series: BarSeriesOption[] = [];

  functionList.value.forEach((item, i) => {
    series.push({
      color: ECHARTS_COLOR_LIST[i],
      name: item.name,
      type: 'bar',
      stack: '总量',
      emphasis: {
        focus: 'series',
      },
      data: [],
    });
  });
  ToolParentFunction.value.forEach((i) => {
    xAxisData.push(i.time);
    functionList.value.forEach((item) => {
      series.forEach((s) => {
        if (s.name === item.name) {
          s.data!.push(i.data[item.name] || null);
        }
      });
    });
  });
  setOptions({
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow',
      },
      formatter: (params: any) => {
        const { name } = params[0];
        let str = `${name}<br/>`;
        const reParams = params.reverse();

        reParams.forEach((item: any) => {
          str += `${item.marker}${item.seriesName}: ${item.value || '-'}<br/>`;
        });

        return str;
      },
    },
    toolbox: {
      itemSize: 20,
      feature: {
        magicType: {
          type: ['stack'],
        },
      },
    },
    grid: {
      right: '250px',
      left: '2%',
      containLabel: true,
    },
    xAxis: [
      {
        type: 'category',
        data: xAxisData,
      },
    ],
    yAxis: [
      {
        type: 'value',
      },
    ],
    series,
  });
}

async function getFuncChild(func: TrackingToolChartsFunctionResultModel) {
  if (!func.hasChild) {
    return;
  }

  // 点击假图例显示子图
  const res = functionList.value.find((item) => item.name === func.name);

  if (res && res.ID !== props.parentID) {
    curParentID.value = res.ID;
    await nextTick(); // wait for childCard rendered
    childCardRef.value?.$el?.scrollIntoView({
      behavior: 'smooth',
      block: 'center',
    });
  }
}

function getinfoTable(func: TrackingToolChartsFunctionResultModel) {
  openInfoTableModal(true, { projectID: props.projectID, functionName: func.name, functionID: func.ID });
}

// 打开耗时统计弹窗
function handleTime(functionID: number) {
  openModal(true, {
    functionName: functionList.value.find((item) => item.ID === functionID)?.name || '',
    functionID,
    range: curRange.value,
    period: curPeriod.value,
    toolID: props.toolID,
    projectID: props.projectID,
  });
}

watch(
  () => props.toolID,
  (val, oldVal) => {
    if (val !== oldVal) {
      init();
    }
  },
  {
    immediate: true,
  },
);
watch(
  () => props.parentID,
  (val, oldVal) => {
    if (val !== oldVal) {
      init();
    }
  },
);
watch(
  () => props.projectID,
  (val, oldVal) => {
    if (val !== oldVal && !props.isChild) {
      init();
    }
  },
);
</script>

<style lang="less">
@prefix-cls: ~'hypergryph-tracking-analysis-function-card';
.@{prefix-cls} {
  &__dropdown {
    & .ant-dropdown-menu-item-disabled {
      cursor: default !important;
      color: rgb(0 0 0 / 85%) !important;
      background-color: #e5e5e5;
    }
  }

  &__chart {
    &-legend {
      display: flex;
      align-items: center;
      user-select: none;

      &__label {
        display: flex;
        align-items: center;
        height: 14px;
        width: 28px;
        border-radius: 6px;
        padding: 0 8px;
      }

      &__time {
        cursor: pointer;
        user-select: none;
        border: 1px solid #d9d9d9;
        padding: 2px 4px;
        line-height: 14px;
        font-size: 12px;
        border-radius: 4px;
      }
    }

    &-chart {
      position: absolute;
      top: 20.5%;
      left: calc(100% - 233px);
      display: flex;
      flex-direction: column;
      gap: 4px;
    }
  }
}

html[data-theme='dark'] .@{prefix-cls} {
  &__dropdown {
    & .ant-dropdown-menu-item-disabled {
      color: #c9d1d9 !important;
      background-color: #313131;
    }
  }
}
</style>
