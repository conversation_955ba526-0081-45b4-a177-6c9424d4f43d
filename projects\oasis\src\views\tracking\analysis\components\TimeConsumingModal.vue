<template>
  <BasicModal
    :class="prefixCls"
    :title="`耗时统计: ${functionName}`"
    :footer="null"
    :width="800"
    @register="registerModal"
  >
    <div
      ref="chartRef"
      :class="`${prefixCls}__chart`"
      :style="{ height: '250px', width: '100%' }"
    />
    <template v-if="curRange.length">
      <div class="mt-3 font-bold">
        {{ curParams?.name ? `【${curParams?.name}】` : '' }}
        {{ curParams?.seriesName || '' }} 详情:
      </div>
      <div
        ref="chartDetailRef"
        :class="`${prefixCls}__chart`"
        :style="{ height: '250px', width: '100%' }"
      />
    </template>
  </BasicModal>
</template>

<script lang="ts" setup>
import type { BarSeriesOption } from 'echarts';
import { isEqual, map, omit } from 'lodash-es';
import type { Ref } from 'vue';
import { ref } from 'vue';
import type {
  TrackingToolFunctionTimeLegendItem,
  TrackingToolFunctionTimeListItem,
} from '/@/api/page/model/trackingModel';
import {
  getTrackingToolFunctionTimeDetail,
  getTrackingToolFunctionTimes,
} from '/@/api/page/tracking';
import { BasicModal, useModalInner } from '/@/components/Modal';
import { useDesign } from '/@/hooks/web/useDesign';
import { useECharts } from '/@/hooks/web/useECharts';
import { getGradientColor } from '/@/utils/color';

defineOptions({
  name: 'TrackingAnalysisTimeConsumingModal',
});

const { prefixCls } = useDesign('time-consuming-modal');
const chartRef = ref<HTMLDivElement | null>(null);
const chartDetailRef = ref<HTMLDivElement | null>(null);
const { setOptions, getInstance } = useECharts(chartRef as Ref<HTMLDivElement>);
const { setOptions: setDetailOptions, getInstance: getDetailInstance } = useECharts(
  chartDetailRef as Ref<HTMLDivElement>,
);

const curRange = ref<number[]>([]);
const functionName = ref('');
const curParams = ref();
const timeData = ref<TrackingToolFunctionTimeLegendItem[]>([]);
const listData = ref<TrackingToolFunctionTimeListItem[]>([]);
const [registerModal, { setModalProps }] = useModalInner(async (data) => {
  setModalProps({ confirmLoading: true });
  getInstance()?.clear();
  functionName.value = data.functionName;

  const { time, list } = await getTrackingToolFunctionTimes(omit(data, ['functionName']));

  timeData.value = time || [];
  listData.value = list || [];

  const xAxisData: string[] = [];
  const series: BarSeriesOption[] = [];
  const legendData: string[] = [];

  time.forEach((item, i) => {
    legendData.push(item.ts);
    series.push({
      color: getGradientColor(i, time.length),
      name: item.ts,
      type: 'bar',
      stack: '总量',
      emphasis: {
        focus: 'series',
      },
      data: [],
    });
  });
  list.forEach((item) => {
    xAxisData.push(item.time);
    time.forEach((ts) => {
      series.forEach((s) => {
        if (s.name === ts.ts) {
          s.data!.push(item.data[ts.ts] * 100);
        }
      });
    });
  });
  setOptions({
    animation: false,
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow',
      },
      appendToBody: true,
      formatter: (params: any) => {
        const { name } = params[0];
        let str = `${name}<br/>`;
        const reParams = params.reverse();

        reParams.forEach((item: any) => {
          str += `${item.marker}${item.seriesName}: ${item.value ? `${item.value}%` : 0}<br/>`;
        });

        return str;
      },
    },

    legend: {
      orient: 'vertical',
      right: '5%',
      top: '20%',
      data: legendData.reverse(),
    },
    grid: {
      right: '20%',
      left: '2%',
      top: '5%',
      bottom: '5%',
      containLabel: true,
    },
    xAxis: [
      {
        type: 'category',
        data: xAxisData,
        axisTick: {
          alignWithLabel: true,
        },
      },
    ],
    yAxis: [
      {
        type: 'value',
        axisLabel: {
          formatter: '{value} %',
        },
      },
    ],
    series,
  });
  getInstance()?.on('click', (params) => {
    const { seriesName, name } = params;

    // 点击图例显示子图
    curParams.value = params;

    const range = timeData.value.find((item) => item.ts === seriesName)?.range || [];
    const timeRange = listData.value.find((item) => item.time === name)?.range || [];

    if (range.length > 0 && !isEqual(curRange.value, range)) {
      curRange.value = range;
      getTrackingToolFunctionTimeDetail({
        functionID: data.functionID,
        projectID: data.projectID,
        timeRange,
        elapsedTime: curRange.value,
      }).then((res) => {
        const detailData = res?.list;

        getDetailInstance()?.clear();

        if (detailData?.length) {
          setDetailOptions({
            animation: false,
            tooltip: {
              trigger: 'axis',
              axisPointer: {
                type: 'shadow',
              },
              appendToBody: true,
              formatter: (params: any) => {
                const { name } = params[0];
                let str = `${name}<br/>`;
                const reParams = params.reverse();

                reParams.forEach((item: any) => {
                  str += `${item.marker}次数: ${item.value || 0}<br/>`;
                });

                return str;
              },
            },
            grid: {
              right: '20%',
              left: '2%',
              top: '20%',
              bottom: '5%',
              containLabel: true,
            },
            xAxis: [
              {
                type: 'category',
                data: map(detailData, 'ts'),
                axisTick: {
                  alignWithLabel: true,
                },
              },
            ],
            yAxis: [
              {
                type: 'value',
                name: '次数',
              },
            ],
            series: {
              data: map(detailData, 'count'),
              type: 'bar',
            },
          });
        } else {
          setDetailOptions({});
        }
      });
    } else if (!range?.length) {
      curRange.value = [];
      curParams.value = null;
    }
  });

  setModalProps({ confirmLoading: false });
});
</script>

<style lang="less">
@prefix-cls: ~'hypergryph-time-consuming-modal';
//  .@{prefix-cls} {
//  }
</style>
