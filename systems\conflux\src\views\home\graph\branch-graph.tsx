import { type PropType, computed, defineComponent, h, ref, render, shallowRef, watch } from 'vue';
import { Graph, Markup } from '@antv/x6';
import { ForgeonTheme, getForgeonColor } from '@hg-tech/forgeon-style';
import { RuleV1MergeTrigger } from '@hg-tech/api-schema-merge';
import { useElementSize } from '@vueuse/core';
import { Button, Popover, Tooltip } from 'ant-design-vue';
import { getTeleport, register } from '@antv/x6-vue-shape';
import { type EdgeData, type NodeData, addOpacityToColor, groupConnectedNodes } from '../utils';
import { store } from '../../../store/pinia';
import { useAppThemeStore } from '../../../store/modules/appTheme';
import { BranchItem } from './branch-item';
import { StatusEdgeLabel, TriggerEdgeLabel } from './edge-labels';
import { MergeGraphHelpTips, ResolveRuleArrowPath } from '../../../models/config.model';
import { useRouter } from 'vue-router';
import { PlatformEnterPoint } from '@hg-tech/oasis-common';
import style from './branch-graph.module.less';
import Icon from '@ant-design/icons-vue';

import BasicStrokeHelp from '../../../assets/svg/BasicStrokeHelp.svg?component';
import BasicStrokeExpend from '../../../assets/svg/BasicStrokeExpend.svg?component';
import BasicStrokeCollapse from '../../../assets/svg/BasicStrokeCollapse.svg?component';
import BasicStrokeReset from '../../../assets/svg/BasicStrokeReset.svg?component';

register({
  shape: 'branch-node',
  width: 200,
  height: 40,
  component: BranchItem,
});

const BranchGraph = defineComponent({
  props: {
    nodes: {
      type: Array as PropType<NodeData[]>,
      required: true,
    },
    edges: {
      type: Array as PropType<EdgeData[]>,
      required: true,
    },
  },
  setup(props) {
    const containerRef = ref<HTMLDivElement>();
    const defaultHeight = Math.min(document.body.clientHeight / 2, 524);
    const graphHeight = ref(defaultHeight);
    const isExpend = ref(false);

    const appThemeStore = useAppThemeStore(store);
    const theme = computed(() => (appThemeStore.theme === 'dark' ? ForgeonTheme.Dark : ForgeonTheme.Light));
    const TeleportContainer = getTeleport();
    const router = useRouter();
    const { width, height } = useElementSize(containerRef);

    const graphRef = shallowRef<Graph>();

    const renderGraph = async () => {
      if (!containerRef.value) {
        return null;
      }
      if (!graphRef.value?.disposed) {
        graphRef.value?.dispose(true);
      }

      const width = containerRef.value.clientWidth;
      const height = graphHeight.value;
      graphRef.value = new Graph({
        container: containerRef.value,
        width,
        height,
        panning: true,
        mousewheel: {
          enabled: true,
          modifiers: ['ctrl', 'alt'],
          factor: 1.1,
        },
        connecting: {
          anchor: 'orth',
          connectionPoint: 'boundary',
        },
        background: {
          color: getForgeonColor('ContainerFill2', theme.value),
        },
        onEdgeLabelRendered: (args) => {
          const { selectors, label } = args;
          const content = selectors.foContent as HTMLDivElement;
          let vNode;
          if (content) {
            switch (label.attrs?.data.type) {
              case 'trigger':
                vNode = h(TriggerEdgeLabel, {
                  triggerType: label.attrs?.data.triggerType as RuleV1MergeTrigger,
                });
                break;
              case 'status':
                vNode = h(StatusEdgeLabel, {
                  status: label.attrs?.data.status as boolean,
                  ruleId: label.attrs?.data.ruleId as string,
                  taskCount: label.attrs?.data.taskCount as number,
                  onTagClick: () => {
                    router.push({
                      name: PlatformEnterPoint.ConfluxTask,
                      query: {
                        ruleId: label.attrs?.data.ruleId as string,
                      },
                    });
                  },
                });
                break;
            }
            vNode && render(vNode, content);
          }
          return undefined;
        },
      });
      const groups = groupConnectedNodes(props.nodes, props.edges);
      // 计算每个 group 的最大节点数（用于宽度计算）
      const groupNodeCounts = groups.map((group) => {
        // 统计每个 level 的节点数，取最大值
        const levelMap: Record<number, NodeData[]> = {};
        group.nodes.forEach((node) => {
          const level = node.level ?? 0;
          if (!levelMap[level]) {
            levelMap[level] = [];
          }
          levelMap[level].push(node);
        });
        return Math.max(...Object.values(levelMap).map((nodes) => nodes.length));
      });
        // 每个节点的宽度
      const nodeWidth = 200;
      // 节点之间的最小间距
      const nodeXSpacing = 150;
      // group 之间的最小间距
      const groupSpacing = 40;
      // 每个 group 的 y 间距
      const groupYSpacing = 180;
      // 计算每个 group 的宽度
      const groupWidths = groupNodeCounts.map((count) => {
        if (count <= 1) {
          return nodeWidth;
        }
        return nodeWidth + (count - 1) * (nodeWidth + nodeXSpacing);
      });
        // 计算每个 group 的起始 x 坐标
      const groupStartXs: number[] = [];
      let currentX = 40;
      for (let i = 0; i < groupWidths.length; i++) {
        groupStartXs.push(currentX);
        // 下一个 group 的起点 = 当前 group 终点 + groupSpacing
        currentX += groupWidths[i] + groupSpacing;
      }
      groups.forEach((group, colIndex) => {
        // 按 level 分组
        const levelMap: Record<number, NodeData[]> = {};
        group.nodes.forEach((node) => {
          const level = node.level ?? 0;
          if (!levelMap[level]) {
            levelMap[level] = [];
          }
          levelMap[level].push(node);
        });
        Object.entries(levelMap).forEach(([levelStr, nodesAtLevel]) => {
          const level = Number(levelStr);
          const count = nodesAtLevel.length;
          // 该 group 的起始 x
          const groupStartX = groupStartXs[colIndex];
          // 该 group 的宽度
          const groupWidth = groupWidths[colIndex];
          // 计算该 level 的中心线
          const centerX = groupStartX + groupWidth / 2;

          nodesAtLevel.forEach((node, nodeIdx) => {
            const label = node.name;
            // 居中分布
            const offset = (nodeIdx - (count - 1) / 2) * (nodeWidth + nodeXSpacing);
            graphRef.value?.addNode({
              id: `${node.id}`,
              x: centerX + offset,
              y: level * groupYSpacing,
              shape: 'branch-node',
              data: {
                id: node.id,
                title: label,
                status: node.status || '进行中',
              },
            });
          });
        });
      });

      props.edges.forEach((edge) => {
        graphRef.value?.addEdge({
          source: `${edge.source}`,
          target: `${edge.target}`,
          attrs: {
            line: {
              stroke: addOpacityToColor(getForgeonColor('FunctionalInfo1Default', theme.value), edge.status ? 1 : 0.3),
              strokeDasharray: edge.triggerType === RuleV1MergeTrigger.AUTO ? '0' : '5',
              // 虚线动画
              // style: {
              //   animation: `${style.antLine} 30s infinite linear`,
              // },
              targetMarker: {
                name: 'path',
                stroke: addOpacityToColor(getForgeonColor('FunctionalInfo1Default', theme.value), edge.status ? 1 : 0.5),
                d: ResolveRuleArrowPath[edge.resolveRule],
                strokeWidth: 2,
                offsetX: -5,
                fill: getForgeonColor('ContainerFill2', theme.value),
              },
            },
          },
          router: {
            name: 'er',
            args: {
              padding: 20,
            },
          },
          labels: [
            {
              markup: Markup.getForeignObjectMarkup(),
              attrs: {
                fo: {
                  width: 72,
                  height: 20,
                  x: -36,
                  y: -10,
                },
                data: {
                  type: 'trigger',
                  triggerType: edge.triggerType,
                },
              },
              position: {
                distance: 0.4,
              },
            },
            {
              markup: Markup.getForeignObjectMarkup(),
              attrs: {
                fo: {
                  width: 24,
                  height: 24,
                  x: -12,
                  y: -12,
                },
                data: {
                  type: 'status',
                  status: edge.status,
                  ruleId: edge.ruleId,
                  taskCount: edge.taskCount,
                },
              },
              position: {
                distance: 0.65,
              },
            },
          ],
        });
      });
      graphRef.value?.zoomToFit({ maxScale: 1, padding: 50 });
      graphRef.value?.centerContent();
    };

    const triggerCanvasSize = () => {
      isExpend.value = !isExpend.value;
      graphHeight.value = !isExpend.value ? defaultHeight : document.body.clientHeight - 200;
    };

    watch([width, height, graphHeight, () => props.nodes, () => props.edges, theme], () => renderGraph(), { immediate: true });

    return () => (
      <div class={[style.branchGraph, 'pos-relative w-full']}>
        <div class="pos-absolute right-0 top-0 z-1 z-10 flex flex-col gap-12px p-12px">

          <Tooltip placement="right" title={isExpend.value ? '收起' : '展开'}>
            <Button
              class="btn-fill-basic"
              icon={<Icon class="c-FO-Content-Icon2" component={isExpend.value ? <BasicStrokeCollapse /> : <BasicStrokeExpend />} />}
              onClick={triggerCanvasSize}
            />
          </Tooltip>
          <Popover placement="rightTop" trigger="click">
            {{
              content: () => (
                <div class="flex flex-col gap-8px">
                  {
                    MergeGraphHelpTips.map((item) => (
                      <div class="flex items-center">
                        {item.icon()}
                        <span class="FO-Font-R12 ml-8px">{item.label}</span>
                      </div>
                    ))
                  }
                </div>
              ),
              default: () => (
                <Tooltip placement="right" title="提示">
                  <Button class="btn-fill-basic" icon={<Icon class="c-FO-Content-Icon2" component={<BasicStrokeHelp />} />} />
                </Tooltip>
              ),
            }}
          </Popover>
          <Tooltip placement="right" title="重置">
            <Button
              class="btn-fill-basic"
              icon={<Icon class="c-FO-Content-Icon2" component={<BasicStrokeReset />} />}
              onClick={renderGraph}
            />
          </Tooltip>
        </div>
        <div
          class="h-524px b-1px b-FO-Container-Stroke1 rd-12px w-full!"
          ref={containerRef}
        />
        <TeleportContainer />
      </div>

    );
  },
});

export {
  BranchGraph,
};
