import type {
  IEditorContext,
  IEditorErasePointPlugin,
} from '@/common/components/editor';
import type {
  FileUploaderExpose,
} from '@/common/components/file-uploader';
import {
  NButton,
  NButtonGroup,
  NForm,
  NFormItem,
  NH1,
  NIcon,
  NInputNumber,
  NP,
  NPopover,
  NScrollbar,
  NText,
  NTooltip,
  useMessage,
  useThemeVars,
} from 'naive-ui';
import {
  computed,
  defineComponent,
  onActivated,
  onDeactivated,
  ref,
  watchEffect,
} from 'vue';

import { useBgRemoval } from './hook';
import { RemovalResult } from './remove-result';

import tipImage from '@/assets/images/tips.png';
import {
  Editor,
  EditorAction,
  ErasePointPlugin,
} from '@/common/components/editor';
import {
  FileUploader,
} from '@/common/components/file-uploader';
import {
  ArrowBack,
  ArrowForward,
  ControlPointFilled,
  DragDrop,
  InformationCircleOutline,
  ScaleFit16Regular,
} from '@/common/components/svg-icons';
import { useAppTheme } from '@/common/hooks';
import { TrackEventName } from '@/plugins/event';
import { traceClickEvent } from '@/plugins/track';

const BgRemoval = defineComponent({
  setup() {
    const editorRef = ref<
      IEditorContext<{
        erasePointPlugin: IEditorErasePointPlugin;
      }>
    >();
    const editorErasePlugin = computed(
      () => editorRef.value?.plugins.erasePointPlugin,
    );
    const editorEventPlugin = computed(
      () => editorRef.value?.plugins.eventPlugin,
    );
    const editorInstancePlugin = computed(
      () => editorRef.value?.plugins.instancePlugin,
    );
    const editorIns = computed(() => editorRef.value?.editor);

    const FileUploaderRef = ref<FileUploaderExpose>();
    const {
      tipPopoverRef,
      loading,
      formValue,
      defaultTipsVisible,
      submit,
    } = useBgRemoval();
    const themeVars = useThemeVars();
    const { currentTheme } = useAppTheme();
    const message = useMessage();

    watchEffect(() => {
      if (formValue.value.image.length > 0) {
        editorErasePlugin.value?.startAddErasePoint();
      } else {
        editorErasePlugin.value?.stopAddErasePoint();
        editorIns.value?.history?.clear();
      }
    });

    onActivated(() => {
      if (formValue.value.image.length > 0) {
        editorErasePlugin.value?.startAddErasePoint();
      } else {
        editorErasePlugin.value?.stopAddErasePoint();
      }
    });

    onDeactivated(() => {
      defaultTipsVisible.value = false;
    });

    const onSubmit = () => {
      traceClickEvent(TrackEventName.AI_BG_REMOVAL_GENERATE);
      const res = editorErasePlugin.value?.getErasePointPosition() ?? [];
      formValue.value.negativePoints = res
        .filter((item) => item.color === 'red')
        .map((item) => [item.x, item.y]);
      formValue.value.positivePoints = res
        .filter((item) => item.color === 'black')
        .map((item) => [item.x, item.y]);

      if (formValue.value.positivePoints.length === 0 && formValue.value.negativePoints.length === 0) {
        message.create('请至少添加一个正向或反向标记点', { type: 'warning' });
        return;
      }
      submit();
    };

    const renderTips = () => {
      return (
        <>
          <div class="flex p-8px">
            <div class="flex-shrink-0">
              <img height={80} src={tipImage} width={80} />
            </div>

            <div class="ml-12px flex-grow-1">
              <div class="tips-content">
                <NText class="mb-4px block">
                  - 左键点击图像添加一个黑色的正向标记点（想提取的部分）。
                </NText>
                <NText class="mb-4px block">
                  - 右键点击图像添加一个红色的反向标记点（不想提取的部分）。
                </NText>
                <NText class="mb-4px block">
                  - 左键再次点击可以删除标记点。
                </NText>
                <NText>- 按住点可以进行拖拽操作。</NText>
              </div>
            </div>
          </div>
        </>
      );
    };

    return () => (
      <div class="bg-removal-container h-[calc(100vh-64px)] p-[20px]">
        <div class="h-full flex">
          <NScrollbar
            class="h-full max-w-600px min-w-400px flex-shrink-0 md:w-500px xl:w-600px xs:w-400px"
            contentClass="h-full"
          >
            <div
              class="left h-full flex flex-col b-r-2px b-[var(--border-color)] pr-20px"
              style={{
                '--border-color': themeVars.value.borderColor,
              }}
            >
              <NH1 class="mb-[24px]" prefix="bar">
                快速抠图
                <NP class="flex items-center">
                  <NText class="mr-4px">上传图片并标记提取/去除区域</NText>
                  <NPopover
                    placement="right"
                    ref={tipPopoverRef}
                    trigger="hover"
                  >
                    {{
                      default: () => renderTips(),
                      trigger: () => (
                        <NIcon class="cursor-pointer" depth={3} size={20}>
                          <InformationCircleOutline />
                        </NIcon>
                      ),
                    }}
                  </NPopover>
                </NP>
              </NH1>

              <div class="input-wrapper flex-shrink-0">
                <NForm>
                  <NFormItem label="图片：" labelAlign="center">
                    <FileUploader
                      containerStyle={{
                        width: '100%',
                        height: '150px',
                      }}
                      placeholder="点击或者拖拽图片到该区域"
                      ref={FileUploaderRef}
                      v-model:fileList={formValue.value.image}
                    />
                  </NFormItem>
                </NForm>
              </div>
              <div class="flex-c-between mb-12px">
                <NButtonGroup
                  class={`overflow-hidden bg-FO-Container-Fill1`}
                >
                  <NTooltip>
                    {{
                      default: () => <>拖拽模式</>,
                      trigger: () => (
                        <NButton
                          disabled={
                            loading.value || formValue.value.image.length === 0
                          }
                          onClick={() =>
                            editorEventPlugin.value?.switchAction(
                              EditorAction.Mover,
                            )}
                          tertiary
                          type={
                            editorEventPlugin.value?.currentAction
                            === EditorAction.Mover
                              ? 'primary'
                              : 'default'
                          }
                        >
                          <NIcon size={20}>
                            <DragDrop />
                          </NIcon>
                        </NButton>
                      ),
                    }}
                  </NTooltip>
                  <NTooltip>
                    {{
                      default: () => <>标点模式</>,
                      trigger: () => (
                        <NButton
                          disabled={
                            loading.value || formValue.value.image.length === 0
                          }
                          onClick={() =>
                            editorEventPlugin.value?.switchAction(
                              EditorAction.DrawErasePoint,
                            )}
                          tertiary
                          type={
                            editorEventPlugin.value?.currentAction
                            === EditorAction.DrawErasePoint
                              ? 'primary'
                              : 'default'
                          }
                        >
                          <NIcon size={20}>
                            <ControlPointFilled />
                          </NIcon>
                        </NButton>
                      ),
                    }}
                  </NTooltip>
                  <NTooltip>
                    {{
                      default: () => <>适配画布</>,
                      trigger: () => (
                        <NButton
                          disabled={
                            loading.value || formValue.value.image.length === 0
                          }
                          onClick={() =>
                            editorInstancePlugin.value?.imageFitKonva()}
                          tertiary
                        >
                          <NIcon size={20}>
                            <ScaleFit16Regular />
                          </NIcon>
                        </NButton>
                      ),
                    }}
                  </NTooltip>
                  <NTooltip>
                    {{
                      default: () => <>撤销（Ctrl+Z）</>,
                      trigger: () => (
                        <NButton
                          disabled={editorIns.value?.history?.isUndoEmpty()}
                          onClick={() => editorIns.value?.history?.undo()}
                          tertiary
                        >
                          <NIcon size={20}>
                            <ArrowBack />
                          </NIcon>
                        </NButton>
                      ),
                    }}
                  </NTooltip>
                  <NTooltip>
                    {{
                      default: () => <>前进（Ctrl+Shift+Z）</>,
                      trigger: () => (
                        <NButton
                          disabled={editorIns.value?.history?.isRedoEmpty()}
                          onClick={() => editorIns.value?.history?.redo()}
                          tertiary
                        >
                          <NIcon size={20}>
                            <ArrowForward />
                          </NIcon>
                        </NButton>
                      ),
                    }}
                  </NTooltip>
                </NButtonGroup>

                <NTooltip>
                  {{
                    default: () => (
                      <>
                        缩放范围在1-300%之间，可以直接
                        {' '}
                        <strong>滚轮</strong>
                        进行缩放
                      </>
                    ),
                    trigger: () => (
                      <NInputNumber
                        button-placement="both"
                        class={`w-120px text-center bg-op-65 bg-FO-Container-Fill1`}
                        disabled={
                          loading.value || formValue.value.image.length === 0
                        }
                        format={(val: number | null) =>
                          `${Number(val?.toFixed(2))}%`}
                        max={300}
                        min={1}
                        onUpdateValue={(num: number | null) =>
                          editorInstancePlugin.value?.setImageRatio(num! / 100 || 0)}
                        parse={(val: string) => Number(val.replace('%', ''))}
                        step={1}
                        value={(editorInstancePlugin.value?.radio ?? 0) * 100}
                      />
                    ),
                  }}
                </NTooltip>
              </div>
              <div class="editor-wrapper w-full flex-grow-1">
                <Editor
                  class="h-full w-full"
                  image={formValue.value.image[0]}
                  onMessage={(msgInfo) =>
                    message.create(msgInfo.msg, { type: 'warning' })}
                  ref={editorRef}
                >
                  <ErasePointPlugin />
                </Editor>
              </div>

              <div
                class="pos-sticky bottom-0 mt-10px flex flex-row-reverse b-t-1 pt-10px"
                style={{
                  backgroundColor: themeVars.value.bodyColor,
                  borderColor: themeVars.value.borderColor,
                }}
              >
                <NButton
                  loading={loading.value}
                  onClick={onSubmit}
                  type="primary"
                >
                  立即生成
                </NButton>
              </div>
            </div>
          </NScrollbar>
          <div class="right h-full flex-grow-1">
            <RemovalResult />
          </div>
        </div>
      </div>
    );
  },
});

export { BgRemoval };
