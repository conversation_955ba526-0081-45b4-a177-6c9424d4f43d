import { PyScriptPlatformOptions } from '../component/component.data';
import { defaultColorList } from '/@/components/ColorPopover';
import type { FormSchema } from '/@/components/Table';
import { isUrlReg } from '/@/utils/is';

export const inputFormTypeOptions = [
  {
    label: '由用户输入',
    value: 'input',
  },
  {
    label: '指定参数值',
    value: 'static',
  },
  {
    label: '系统参数',
    value: 'environ',
  },
  {
    label: '上一个元件的输出',
    value: 'prevResult',
  },
  {
    label: '所有前序输出',
    value: 'allPrevResult',
  },
];

export const environTypeOptions = [
  {
    label: 'Oasis登录用户名 (username)',
    value: 'username',
  },
  {
    label: '当前p4用户名 (p4user)',
    value: 'p4user',
  },
  {
    label: '当前p4密码 (p4password)',
    value: 'p4password',
  },
  {
    label: '当前p4服务器 (p4port)',
    value: 'p4port',
  },
];

export const oasisArgOptions = [
  {
    label: '刷新启动器工程列表',
    value: 'refresh_launcher_project',
  },
  {
    label: '刷新启动器引擎列表',
    value: 'refresh_launcher_engine',
  },
];

export const formSchema: FormSchema[] = [
  {
    field: 'name',
    label: '指令名称',
    component: 'Input',
    slot: 'name',
    required: true,
    colProps: {
      span: 11,
      lg: 22,
    },
  },
  {
    field: 'nickName',
    label: '作者',
    component: 'Input',
    componentProps: {
      placeholder: '获取失败',
      disabled: true,
    },
    colProps: {
      span: 11,
      lg: 22,
    },
  },
  {
    field: 'color',
    label: '颜色',
    component: 'Input',
    defaultValue: defaultColorList[0],
    show: false,
  },
  {
    field: 'docURL',
    label: '文档网址',
    component: 'Input',
    componentProps: {
      placeholder: '请输入文档网址',
    },
    required: true,
    colProps: {
      span: 11,
      lg: 22,
    },
    rules: [{ pattern: isUrlReg, message: '请输入正确的网址' }],
  },
  {
    field: 'type',
    label: '指令类型',
    component: 'Input',
    componentProps: {
      placeholder: '请输入指令类型',
    },
    required: true,
    colProps: {
      span: 11,
      lg: 22,
    },
  },
  {
    field: 'toAllProjects',
    label: '指令获取',
    component: 'Switch',
    slot: 'toAllProjects',
  },
  {
    field: 'projectIDs',
    label: '项目列表',
    component: 'Select',
    show: false,
  },
  {
    field: 'platform',
    label: '支持平台',
    component: 'CheckboxGroup',
    componentProps: {
      options: PyScriptPlatformOptions,
    },
    required: true,
  },
  {
    field: 'oasisArgs',
    label: 'Oasis参数',
    component: 'TreeSelect',
    componentProps: {
      placeholder: '请选择Oasis参数',
      mode: 'multiple',
      getPopupContainer: () => document.body,
      treeData: oasisArgOptions,
      treeCheckable: true,
      showCheckedStrategy: 'SHOW_PARENT',
    },
  },

  {
    field: 'version',
    label: '版本',
    component: 'InputNumber',
    slot: 'version',
    defaultValue: 1,
  },
];

export const inputFormSchema: FormSchema[] = [
  {
    field: 'showType',
    label: '显示类型',
    component: 'Select',
    componentProps: {
      getPopupContainer: () => document.body,
      placeholder: '请选择显示类型',
    },
    defaultValue: 'input',
    required: true,
  },
  {
    field: 'input',
    label: '提示文本',
    component: 'Input',
    componentProps: {
      placeholder: '请输入提示文本',
    },
    ifShow: ({ values }) => {
      return values.showType === 'input';
    },
  },
  {
    field: 'static',
    label: '指定参数值',
    component: 'Input',
    componentProps: {
      placeholder: '请输入指定参数值',
    },
    ifShow: ({ values }) => {
      return values.showType === 'static';
    },
    required: true,
  },
  {
    field: 'environ',
    label: '系统参数',
    component: 'Select',
    componentProps: {
      options: environTypeOptions,
      placeholder: '请选择系统参数',
      getPopupContainer: () => document.body,
    },
    ifShow: ({ values }) => {
      return values.showType === 'environ';
    },
    required: true,
  },
  {
    field: 'prevResult',
    label: '选择输出',
    component: 'Select',
    componentProps: {
      placeholder: '请选择上一个元件的输出',
      getPopupContainer: () => document.body,
    },
    ifShow: ({ values }) => {
      return values.showType === 'prevResult';
    },
    required: true,
  },
  {
    field: 'allPrevResult',
    label: '选择输出',
    component: 'Select',
    componentProps: {
      placeholder: '请选择一个前序元件的输出',
      getPopupContainer: () => document.body,
    },
    ifShow: ({ values }) => {
      return values.showType === 'allPrevResult';
    },
    required: true,
  },
];

export const choiceFormSchema: FormSchema[] = [
  {
    field: 'input',
    label: '提示文本',
    component: 'Input',
    componentProps: {
      placeholder: '请输入提示文本',
    },
  },
  {
    field: 'options',
    label: '选项',
    component: 'Select',
    componentProps: ({ formModel, formActionType }) => ({
      placeholder: '请输入选项, 回车添加',
      getPopupContainer: () => document.body,
      mode: 'tags',
      onChange: async (val) => {
        const updateSchema = formActionType?.updateSchema;
        if (!updateSchema) {
          return;
        }
        formModel.value = '';
        await updateSchema({
          field: 'value',
          componentProps: {
            options: val.map((item) => ({
              label: item,
              value: item,
            })),
          },
        });
      },
    }),
    required: true,
  },
  {
    field: 'value',
    label: '默认值',
    component: 'Select',
    componentProps: {
      placeholder: '请选择默认值',
      getPopupContainer: () => document.body,
    },
    ifShow: ({ values }) => {
      return values.options && values.options.length > 0;
    },
  },
];
