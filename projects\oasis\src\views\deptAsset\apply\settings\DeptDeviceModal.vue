<template>
  <BasicModal
    title="部门设备管理" :width="1000" destroyOnClose :footer="null" :afterClose="handleAfterClose"
    @register="registerModal"
  >
    <div class="flex gap-4">
      <div class="border-r-FO-Container-Stroke1 w-160px flex flex-col items-center gap-2 border-r-1 px-4">
        <div
          v-for="tab in tabList" :key="tab.value"
          class="hover:bg-FO-Container-Fill2 w-full cursor-pointer rounded-md p-2"
          :class="{ 'c-FO-Brand-Primary-Default !bg-FO-Brand-Tertiary-Active': activeTab === tab.value }"
          @click="() => handleTabClick(tab.value)"
        >
          {{ tab.label }}
        </div>
      </div>

      <div v-if="activeTab === 'recycle'" class="h-600px min-w-0 flex flex-1 flex-col gap-4">
        <AAlert message="对于未回收的部门公共资产，请联系设备归属人回收后统一管理，收到设备后请点击“确认回收” ，设备归属人将更新为部门管理员" type="info" showIcon />
        <div class="flex items-center justify-between gap-2">
          <div class="flex items-center gap-6">
            <AInput v-model:value="keyword" placeholder="搜索设备名称或资产编号" class="w-240px" @change="() => reload()">
              <template #suffix>
                <Icon :icon="SearchIcon" />
              </template>
            </AInput>
            <ACheckbox v-model:checked="onlyShowNotRecycled" @change="() => reload()">
              仅看未回收
            </ACheckbox>
          </div>
          <AButton v-if="selectedRowKeys.length > 0" type="primary" @click="() => handleBatchConfirm()">
            批量确认 ({{ selectedRowKeys.length }})
          </AButton>
        </div>
        <BasicTable @register="registerTable">
          <template #bodyCell="{ column, record }">
            <template v-if="column.dataIndex === 'deviceName'">
              <EllipsisText class="c-FO-Brand-Primary-Default block cursor-pointer" @click="() => handleDeviceClick(record)">
                {{ record.deviceName }}
              </EllipsisText>
            </template>
            <template v-else-if="column.dataIndex === 'assetNo'">
              <EllipsisText class="block">
                {{ record.assetNo }}
              </EllipsisText>
            </template>
            <template v-else-if="column.dataIndex === 'ownerID'">
              <span
                v-tippy="`点击联系：${getUserById(record.ownerID)?.displayName || ''}`"
                class="c-FO-Brand-Primary-Default cursor-pointer" @click="() => handleUserClick(record.ownerID)"
              >
                {{ getUserById(record.ownerID)?.displayName || '' }}
              </span>
            </template>
            <template v-else-if="column.dataIndex === 'deptID'">
              <div class="flex items-center gap-2">
                <EllipsisText class="block">
                  {{ getDeptName(record.deptID) }}
                </EllipsisText>
              </div>
            </template>
            <template v-else-if="column.dataIndex === 'action'">
              <div class="flex items-center gap-2">
                <AButton
                  v-if="!record.deptAdminDevice?.isRecycled"
                  v-tippy="isApplyOrReturning(record.fsmState) ? '请联系原所属人完成审批后回收设备' : ''" type="link"
                  :class="{ '!c-FO-Content-Text2 cursor-not-allowed opacity-50': isApplyOrReturning(record.fsmState) }"
                  @click="() => handleConfirm(record, true)"
                >
                  确认回收
                </AButton>
                <AButton
                  v-else v-tippy="isApplyOrReturning(record.fsmState) ? '请完成审批后进行操作' : ''" type="link"
                  class="!c-FO-Content-Text2"
                  :class="{ 'cursor-not-allowed opacity-50': isApplyOrReturning(record.fsmState) }"
                  @click="() => handleConfirm(record, false)"
                >
                  取消回收
                </AButton>
              </div>
            </template>
          </template>
        </BasicTable>
      </div>
      <div v-else class="h-600px min-w-0 flex flex-1 flex-col gap-4 overflow-auto">
        <div v-for="item in showAdminList" :key="item.ID" class="flex flex-col gap-2">
          <div class="flex flex-col gap-2 px-4">
            <EllipsisText class="block font-bold">
              {{ item.dept?.orgPath?.replace('鹰角>', '') }}
            </EllipsisText>
            <DefaultAccessLevel :item="item" :deptList="deptList" :editingId="editingId" />
            <div class="flex items-center justify-between">
              <div>已回收设备管理</div>
              <AButton v-if="item.ID !== editingId" :disabled="deptAssetStore.isEdit && (deptAssetStore.getEditingId !== item.ID || deptAssetStore.getEditType !== 'recover')" @click="() => handleEdit(item)">
                <Icon :icon="EditIcon" />
                编辑
              </AButton>
              <template v-else>
                <div class="flex items-center gap-2">
                  <AButton @click="() => handleCancel()">
                    取消
                  </AButton>
                  <AButton type="primary" @click="() => handleSave(item)">
                    保存
                  </AButton>
                </div>
              </template>
            </div>
            <div class="mt flex flex-col gap-4 rounded-md bg-FO-Container-Fill2 p-4">
              <div class="flex gap-2 rounded-md">
                <div class="w-120px">
                  候补审批人
                </div>
                <div class="min-w-0 flex flex-1 flex-col gap-2">
                  <template v-if="item.ID === editingId">
                    <UserSelect
                      v-model:value="editSubAdminIds" class="w-300px" placeholder="请选择候补审批人" isMultiple
                      :maxTagCount="3"
                    />
                  </template>
                  <EllipsisText v-else :lines="3">
                    {{ item.subAdmins?.map((item) => formatNickName(item)).join('; ') || '无' }}
                  </EllipsisText>
                  <div class="c-FO-Content-Text2">
                    候补审批人将拥有设备审批权限，可以通过飞书卡片或网页端进行审批操作。
                  </div>
                </div>
              </div>
              <div class="flex gap-2">
                <div class="w-120px">
                  最长借用天数
                </div>
                <div v-if="item.ID === editingId">
                  <ARadioGroup v-model:value="isIndefinitely" class="flex flex-col gap-2">
                    <ARadio :value="true">
                      不限制
                    </ARadio>
                    <ARadio :value="false">
                      <div class="flex items-center gap-2">
                        <AInputNumber
                          v-model:value="editMaxDay" :min="1" :max="99" :defaultValue="5" :precision="0"
                          class="w-80px"
                        />天
                      </div>
                    </ARadio>
                  </ARadioGroup>
                </div>
                <div v-else>
                  {{ item.maxDay === -1 || !item.maxDay ? '不限制' : `${item.maxDay}天` }}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </BasicModal>
</template>

<script lang="ts" setup>
import { Alert as AAlert, Button as AButton, Checkbox as ACheckbox, Input as AInput, InputNumber as AInputNumber, Radio as ARadio, RadioGroup as ARadioGroup, message } from 'ant-design-vue';
import { BasicModal, useModalInner } from '/@/components/Modal';
import type { ModalMethods } from '/@/components/Modal';
import SearchIcon from '@iconify-icons/icon-park-outline/search';
import { Icon } from '/@/components/Icon';
import { computed, ref } from 'vue';
import { getDeviceAdminDeviceList, getDeviceAdminList, recycleDevice, updateDeviceAdminMaxDay } from '/@/api/page/deptAsset';
import { getAllPaginationList } from '/@/hooks/web/usePagination';
import { type BasicColumn, useTable } from '/@/components/Table';
import { BasicTable } from '/@/components/Table';
import type { DeptListItem } from '/@/api/page/model/systemModel';
import { findNode } from '/@/utils/helper/treeHelper';
import { type DeviceAdminListItem, type DeviceListItem, DeviceFsmStateEnum } from '/@/api/page/model/deptAssetModel';
import { useRoute, useRouter } from 'vue-router';
import { useDeptAssetApply } from '../hook';
import { PlatformEnterPoint } from '@hg-tech/oasis-common';
import { ResultEnum } from '/@/enums/httpEnum';
import { useUserStore } from '/@/store/modules/user';
import { UserSelect } from '/@/components/Form';
import { formatNickName } from '/@/hooks/system/useUserList';
import DefaultAccessLevel from './DefaultAccessLevel.vue';
import EditIcon from '@iconify-icons/icon-park-outline/edit';
import { usedeptAssetStoreWithOut } from '/@/store/modules/deptAsset';

const emit = defineEmits<{
  register: [methods: ModalMethods, uuid: number];
  success: [];
}>();
const deptAssetStore = usedeptAssetStoreWithOut();
const route = useRoute();
const { replace, resolve } = useRouter();
const userStore = useUserStore();
const { handleUserClick, getUserById } = useDeptAssetApply();

const keyword = ref('');
const deptAdminID = ref<number>();
const deptList = ref<DeptListItem[]>([]);
const onlyShowNotRecycled = ref(false);
const selectedRowKeys = ref<(string | number)[]>([]);
const selectedRows = ref<DeviceListItem[]>([]);
const userID = ref<number>();
const hasRecycleFailed = ref(false);
const activeTab = ref<'recycle' | 'settings'>('recycle');
const deviceAdminList = ref<DeviceAdminListItem[]>([]);
const editingId = ref<number>();
const isIndefinitely = ref(true);
const editMaxDay = ref(5);
const editSubAdminIds = ref<number[]>([]);

const tabList: { label: string; value: 'recycle' | 'settings' }[] = [
  {
    label: '设备回收',
    value: 'recycle',
  },
  {
    label: '管理配置',
    value: 'settings',
  },
];

const columns: BasicColumn[] = [
  {
    title: '设备名称',
    dataIndex: 'deviceName',
    width: 150,
  },
  {
    title: '资产编号',
    dataIndex: 'assetNo',
    width: 150,
  },
  {
    title: '归属人(原)',
    dataIndex: 'ownerID',
    width: 150,
  },
  {
    title: '归属部门',
    dataIndex: 'deptID',
    align: 'left',
  },
];

async function getAdminList() {
  const { list } = await getAllPaginationList(getDeviceAdminList);
  deviceAdminList.value = list || [];
}

const showAdminList = computed(() => deviceAdminList.value.filter((item) => {
  if (deptAdminID.value) {
    return item.ID === deptAdminID.value;
  } else {
    return item.userID === userStore.getUserInfo?.ID;
  }
}));

function isApplyOrReturning(fsmState: DeviceFsmStateEnum) {
  return [DeviceFsmStateEnum.APPLYING, DeviceFsmStateEnum.RETURNING].includes(fsmState);
}

function initSelected() {
  selectedRows.value = [];
  selectedRowKeys.value = [];
}

function isDisabled(record: DeviceListItem) {
  return record.deptAdminDevice?.isRecycled || isApplyOrReturning(record.fsmState as DeviceFsmStateEnum);
}

function handleTabClick(tab: 'recycle' | 'settings') {
  activeTab.value = tab;
  if (tab === 'settings') {
    getAdminList();
  }
}

const [registerTable, { reload }] = useTable({
  api: () => getAllPaginationList((p) => getDeviceAdminDeviceList({
    deptAdminID: deptAdminID.value,
    keyword: keyword.value,
    isRecycled: onlyShowNotRecycled.value ? false : undefined,
    userID: userID.value,
    ...p,
  })),
  columns,
  inset: true,
  useSearchForm: false,
  showIndexColumn: false,
  bordered: true,
  pagination: false,
  canResize: false,
  scroll: {
    y: 400,
  },
  rowSelection: {
    type: 'checkbox',
    onChange: (keys: (string | number)[], rows: DeviceListItem[]) => {
      selectedRowKeys.value = keys;
      selectedRows.value = rows;
    },
    getCheckboxProps: (record: DeviceListItem) => ({
      disabled: isDisabled(record),
    }),
  },
  afterFetch: () => {
    if (hasRecycleFailed.value) {
      // 过滤掉应该被禁用的行项目
      const filteredRows = selectedRows.value.filter((record) => !isDisabled(record));
      selectedRows.value = filteredRows;
      selectedRowKeys.value = filteredRows.map((row) => row.ID as number);
      hasRecycleFailed.value = false;
    } else {
      initSelected();
    }
  },
  clickToRowSelect: false,
  actionColumn: {
    width: 130,
    title: '操作',
    dataIndex: 'action',
    fixed: 'right',
  },
});

const [registerModal, { setModalProps }] = useModalInner(async (data) => {
  setModalProps({ confirmLoading: true });
  activeTab.value = 'recycle';
  keyword.value = '';
  deptAdminID.value = data.record?.ID;
  deptList.value = data.deptList || [];
  userID.value = data.userID;
  onlyShowNotRecycled.value = Number(route.query.recycle) === 1;
  editingId.value = undefined;
  setModalProps({ confirmLoading: false });
});

/**
 * 获取部门名称
 * @param deptID 部门ID
 * @returns 部门名称
 */
function getDeptName(deptID: number) {
  return findNode(deptList.value, (item) => item.ID === deptID)?.orgPath?.replace('鹰角>', '');
}

/**
 * 点击设备名称打开设备详情
 * @param record 设备信息
 */
function handleDeviceClick(record: DeviceListItem) {
  const { fullPath } = resolve({
    name: PlatformEnterPoint.DeptAssetApplyManagement,
    query: {
      editId: record.ID,
    },
  });
  window.open(fullPath, '_blank');
}

/**
 * 确认回收
 * @param record 设备信息
 * @param recycle 是否回收
 */
async function handleConfirm(record: DeviceListItem, recycle: boolean) {
  if (isApplyOrReturning(record.fsmState as DeviceFsmStateEnum)) {
    return;
  }

  const res = await recycleDevice({
    deviceIds: [record.ID as number],
    recycle,
  });
  if (res?.code === ResultEnum.API_ERROR) {
    hasRecycleFailed.value = true;
  }

  reload();
  emit('success');
}

/**
 * 批量确认回收
 */
async function handleBatchConfirm() {
  if (!selectedRows.value.length) {
    return;
  }

  // 获取选中行中未回收的设备ID
  const deviceIds = selectedRows.value.map((row) => row.ID as number);

  if (deviceIds.length) {
    const res = await recycleDevice({
      deviceIds,
      recycle: true,
    });
    if (res?.code === ResultEnum.API_ERROR) {
      hasRecycleFailed.value = true;
    }
    reload();
    emit('success');
  }
}

/** 关闭模态框 */
async function handleAfterClose() {
  if (route.query.recycle) {
    await replace({ query: { ...route.query, recycle: undefined } });
  }
  emit('success');
}

/**
 * 编辑
 * @param item 部门管理员信息
 */
function handleEdit(item: DeviceAdminListItem) {
  if (item.maxDay === -1) {
    isIndefinitely.value = true;
    editMaxDay.value = 5;
  } else {
    isIndefinitely.value = !item.maxDay;
    editMaxDay.value = item.maxDay || 5;
  }
  editSubAdminIds.value = item.subAdmins?.map((item) => item.ID as number) || [];
  editingId.value = item.ID;
  deptAssetStore.setIsEdit(true);
  deptAssetStore.setEditingId(undefined);
  deptAssetStore.setEditType('recover');
}

/**
 * 取消编辑
 */
function handleCancel() {
  editingId.value = undefined;
  deptAssetStore.setIsEdit(false);
  deptAssetStore.setEditingId(undefined);
}

/**
 * 保存
 * @param item 部门管理员信息
 */
async function handleSave(item: DeviceAdminListItem) {
  if (!item.ID) {
    return;
  }
  if (!isIndefinitely.value && !editMaxDay.value) {
    message.warning('最长借用天数不能为空');
    return;
  }
  try {
    const res = await updateDeviceAdminMaxDay(item.ID, { maxDay: isIndefinitely.value ? -1 : editMaxDay.value, subAdminIds: editSubAdminIds.value || [] });
    if (res?.code === ResultEnum.API_ERROR) {
      return;
    }
    await getAdminList();
    editingId.value = undefined;
    deptAssetStore.setIsEdit(false);
    deptAssetStore.setEditingId(undefined);
  } catch (error) {
    console.error(error);
  }
}
</script>
