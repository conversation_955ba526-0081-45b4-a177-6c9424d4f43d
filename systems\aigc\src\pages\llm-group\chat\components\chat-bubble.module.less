@import (reference) '@hg-tech/forgeon-style/vars.less';

.chatBubble {
  max-width: 100%;
  .markdownContent {
    color: @FO-Content-Text1;
    /* 标题 */
    h1 {
      font-size: 28px;
      line-height: 40px;
      margin: 20px 0;
    }

    h2 {
      font-size: 24px;
      line-height: 36px;
      margin: 18px 0;
    }

    h3 {
      font-size: 22px;
      line-height: 32px;
      margin: 16px 0;
    }

    h4 {
      font-size: 20px;
      line-height: 28px;
      margin: 14px 0;
    }

    h5 {
      font-size: 18px;
      line-height: 26px;
      margin: 12px 0;
    }

    h6 {
      font-size: 16px;
      line-height: 24px;
      margin: 10px 0;
    }

    /* 段落 */
    p {
      margin: 12px 0;
    }

    /* 链接 */
    a {
      color: @FO-Content-Link-Default;
      text-decoration: none;
    }

    a:hover {
      text-decoration: underline;
    }

    /* 列表 */
    ul,
    ol {
      padding-left: 20px;
      margin: 12px 0;
    }

    li {
      list-style: auto;
      margin: 6px 0;
      &::marker {
        color: @FO-Brand-Primary-Default;
      }
    }

    .codeBlock {
      background-color: @FO-Container-Fill2;
      font-size: 15px;
      line-height: 22px;
      overflow: hidden;
      margin: 12px 0;
      border-radius: 12px;

      .codeBlockHeader {
        color: @FO-Content-Text2;
        border-bottom: 1px solid @FO-Container-Stroke1;
        padding: 12px;
      }

      .codeBlockCopyBtn {
        padding: 0 4px;
        border-radius: 4px;
        background-color: @FO-Container-Fill1;
        font-size: 12px;
        color: @FO-Content-Text1;
        &:hover {
          color: @FO-Content-Text2;
        }
      }
    }

    /* 代码块 */
    pre {
      background-color: @FO-Container-Fill2;
      padding: 12px;
      border-radius: 4px;
      line-height: 22px;
      overflow-x: auto;
      margin-bottom: 0;
    }

    code {
      background-color: transparent;
      text-shadow: none;
      color: @FO-Content-Text1;
      * {
        background-color: transparent;
      }
    }

    /* 表格 */
    table {
      width: 100%;
      border-collapse: collapse;
      margin: 16px 0;
    }

    th,
    td {
      border: 1px solid @FO-Container-Stroke1;
      padding: 8px;
      text-align: left;
    }

    th {
      background-color: @FO-Container-Fill2;
    }

    /* 引用块 */
    blockquote {
      border-left: 3px solid @FO-Container-Stroke1;
      color: @FO-Content-Text3;
      padding: 0 12px;
      margin-bottom: 0;
      code,
      li {
        color: inherit;
        &::marker {
          color: inherit;
        }
      }
    }

    /* 分割线 */
    hr {
      border: none;
      border-top: 1px solid @FO-Container-Stroke1;
      margin: 24px 0;
    }
    .katexBlock {
      /* Reset styles inside .katex-container to avoid conflicts */
      all: unset !important;
      display: block !important;
      font-size: 1.2em !important;
      line-height: 1.5 !important;
      margin: 1em 0 !important;
      padding: 0 !important;
      max-width: 100%;
      overflow-x: auto !important;
      * {
        font-family: 'KaTeX_Typewriter', serif !important;
      }
    }
    .katexInline {
      /* Reset styles inside .katex-container to avoid conflicts */
      all: unset !important;
      display: inline !important;
      font-size: 1.2em !important;
      line-height: normal !important;
      margin: 1em 0 !important;
      padding: 0 !important;
      max-width: 100%;
      overflow-x: auto !important;
      * {
        font-family: 'KaTeX_Typewriter', serif !important;
      }
    }
  }
}
