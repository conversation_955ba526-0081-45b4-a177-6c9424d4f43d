{"name": "@hg-tech/toolkit-management", "type": "module", "version": "1.0.0", "private": true, "scripts": {"dev": "vite dev", "build": "tsc -b && vite build", "build:rnd": "tsc -b && cross-env NODE_ENV=production vite build --mode rnd", "build:pre": "tsc -b && cross-env NODE_ENV=production vite build --mode pre", "build:analyze": "vite build -- --analyze", "test": "run-p test:*", "test:type": "tsc -b"}, "dependencies": {"@ant-design/icons-vue": "^7.0.1", "@hg-tech/forgeon-style": "workspace:*", "@hg-tech/forgeon-uno-config": "workspace:^", "@hg-tech/oasis-common": "workspace:*", "@hg-tech/request-api": "workspace:*", "@hg-tech/utils-vue": "workspace:*", "@iconify-icons/ant-design": "^1.2.7", "@iconify-icons/carbon": "^1.2.20", "@iconify-icons/charm": "^1.2.8", "@iconify-icons/clarity": "^1.2.8", "@iconify-icons/icon-park-outline": "^1.2.11", "@iconify-icons/mdi": "^1.2.48", "@iconify-icons/ph": "^1.2.5", "@iconify/types": "^2.0.0", "@iconify/vue": "^4.1.2", "@micro-zoe/micro-app": "^1.0.0-rc.20", "@vueuse/core": "^11.0.3", "@vueuse/router": "^11.0.3", "ant-design-vue": "^4.2.3", "dayjs": "^1.11.13", "lodash": "^4", "lodash-es": "^4.17.21", "lz-string": "^1.5.0", "pinia": "2.2.2", "unocss": "^0.62.4", "uuid": "^11.1.0", "vue": "^3.4.38", "vue-router": "^4.4.3", "vxe-table": "^4.7.75"}, "devDependencies": {"@hg-tech/configs": "workspace:^", "@types/lodash": "^4.17.7", "cross-env": "^7.0.3", "vite": "^5.4.14", "vue-tsc": "^2.1.6"}}