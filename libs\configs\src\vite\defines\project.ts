import type { PluginOption, UserConfig } from 'vite';
import { mergeConfig } from 'vite';
import { analyzer } from 'vite-bundle-analyzer';
import { dirname } from 'node:path';
import { defineViteConfig, setProcessEnv } from '../helper.js';
import { fileURLToPath } from 'node:url';

export const defineViteConfigProject = defineViteConfig((fn, configs) => {
  return async (env) => {
    setProcessEnv(env.mode);

    const userConfig = await fn(env);

    return mergeConfig({
      build: {
        sourcemap: userConfig.build?.sourcemap || configs?.analyze,
        cssCodeSplit: false, // 禁用 css 代码拆分以解决可能导致的样式不稳定问题
      },
      resolve: {
        alias: [
          {
            find: 'lodash',
            replacement: dirname(fileURLToPath(import.meta.resolve('lodash-es'))),
          },
          ...Array.isArray(userConfig.resolve?.alias) ? userConfig.resolve.alias : Object.entries(userConfig.resolve?.alias || {}).map(([find, replacement]) => ({ find, replacement })),
        ],
      },
      plugins: [
        ...configs?.analyze
          ? [analyzer({
            analyzerMode: 'static',
            fileName: 'report',
          })]
          : [],
      ] as PluginOption[],
    } satisfies UserConfig, userConfig);
  };
});
