@import (reference) '@hg-tech/forgeon-style/vars.less';

// project-select
@forgeon-project-select-bg-color: var(--forgeon-project-select-bg-color);
@forgeon-outside-project-select-bg-color: var(--forgeon-outside-project-select-bg-color);

// forgeon btn
@forgeon-btn-normal-bg-color: var(--forgeon-btn-normal-bg-color);
@forgeon-btn-normal-text-color: var(--forgeon-btn-normal-text-color);
@forgeon-btn-selected-bg-color: var(--forgeon-btn-selected-bg-color);
@forgeon-btn-border-color: var(--forgeon-btn-border-color);

// =================================
// ==========border-color===========
// =================================

// Dark-light
@border-color-shallow-dark: #cececd;

// Light-dark
@border-color-light: @FO-Container-Stroke1;

// =================================
// ============message==============
// =================================

// success-bg-color
@success-background-color: #f1f9ec;
// info-bg-color
@info-background-color: #e8eff8;
// warn-bg-color
@warning-background-color: #fdf6ed;
// danger-bg-color
@danger-background-color: #fef0f0;

// =================================
// ==============Header=============
// =================================
@header-bg-color: var(--header-bg-color);
@header-dark-bg-color: var(--header-bg-color);
@header-dark-bg-hover-color: var(--header-bg-hover-color);
@header-light-bg-hover-color: var(--border-hover-color);
@header-light-desc-color: #7c8087;
@header-light-bottom-border-color: #eee;
// top-menu
@top-menu-active-bg-color: var(--header-active-menu-bg-color);

// =================================
// ==============Menu===============
// =================================

// let -menu
@sider-dark-bg-color: var(--sider-dark-bg-color);
@sider-dark-darken-bg-color: var(--sider-dark-darken-bg-color);
@sider-dark-lighten-bg-color: var(--sider-dark-lighten-bg-color);

// =================================
// ==============tree===============
// =================================
// tree item hover background
@tree-hover-background-color: #f5f7fa;
// tree item hover font color
@tree-hover-font-color: #f5f7fa;

// =================================
// ==============link===============
// =================================
@link-hover-color: @FO-Brand-Primary-Default;

// Label color
@text-color-call-out: var(--text-color-call-out);

// Auxiliary information color-dark
@text-color-help-dark: #909399;

// =================================
// ============breadcrumb===========
// =================================
@breadcrumb-item-normal-color: #999;
// =================================
// ============ custom =============
// =================================
@report-card-background: var(--report-card-background-color);
@report-card-content-background: var(--report-card-content-background-color);
@member-card-background: var(--member-card-background-color);
@set-card-background: var(--set-card-background-color);
@set-card-hover-background: var(--set-card-hover-background-color);
@task-card-background: var(--task-card-background-color);
@task-card-hover-background: var(--task-card-hover-background-color);
@table-border-color: @FO-Container-Stroke1;
@table-head-border: @FO-Container-Stroke1;
@table-background-color: @FO-Container-Fill2;
@table-disabled-background-color: var(--table-disabled-background-color);
@opacity-background-color: var(--opacity-background-color);
@python-card-background: var(--python-card-background-color);
@pkg-primary-color: var(--pkg-primary-color);
@python-script-header-background: var(--python-script-header-background-color);
@black-button-background: var(--black-button-background-color);
@input-background: var(--input-background-color);
@input-border: var(--input-border-color);
@tab-bg-color: var(--tab-bg-color);
