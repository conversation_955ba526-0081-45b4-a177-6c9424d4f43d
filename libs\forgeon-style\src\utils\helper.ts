import { capitalize } from 'lodash-es';

export const AUTO_GENERATED_COMMENT = `/* This file is automatically generated. DO NOT EDIT it manually. */`;

/**
 * 将 figma 风格变量转为字符串数组
 * @example $fo.$brand.primary-default -> ['FO', 'Brand', 'Primary', 'Default']
 */
function splitFigmaVarName(s: string) {
  return s.split(/[$.-]+/).filter(Boolean).map((i) => (i.toLocaleLowerCase() === 'fo' ? i.toLocaleUpperCase() : capitalize(i)));
}

/**
 * 标准化 figma 风格变量名
 * @example $fo.$brand.primary-default -> FO-Brand-Primary-Default
 */
export function normalizeFigmaVarName(s: string) {
  return splitFigmaVarName(s).join('-');
}

/**
 * 将 figma 风格变量转为 PascalCase 风格
 * @example $fo.$brand.primary-default -> FOBrandPrimaryDefault
 */
export function formatPascalCaseVarName(s: string) {
  return splitFigmaVarName(s).join('');
}

/**
 * 将 figma 风格变量转为 PascalCase 风格
 * @example $fo.$brand.primary-default -> BrandPrimaryDefault
 */
export function formatPascalCaseVarNameWithoutPrefix(s: string) {
  return formatPascalCaseVarName(s).replace(/^FO/i, '');
}

/**
 * 将 figma 风格变量转为 css var 风格，
 * @example $fo.$brand.primary-default -> --FO-Brand-Primary-Default (对应 figma 自动生成的 css 模板)
 */
export function formatCssVarName(s: string) {
  return `--${normalizeFigmaVarName(s)}`;
}

/**
 * 将变量对象转换为 CSS 变量声明字符串数组
 * @example --FO-Brand-Primary-Default: var(--FO-Brand-Primary-Default);
 */
export function toCssVarDeclares(vars: Record<string, string>) {
  return Object.entries(vars).map(([key, value]) => `${formatCssVarName(key)}: var(${formatCssVarName(value)});`);
}
