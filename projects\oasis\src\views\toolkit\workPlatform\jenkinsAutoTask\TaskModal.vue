<template>
  <BasicModal v-bind="$attrs" @register="registerModal" :title="getTitle" @ok="handleSubmit">
    <BasicForm @register="registerForm" />
  </BasicModal>
</template>
<script lang="ts">
  import { computed, defineComponent, ref, unref } from 'vue';
  import { formSchema } from './task.data';
  import { addJob, editJob } from '/@/api/page/jenkins';
  import { BasicForm, useForm } from '/@/components/Form';
  import { BasicModal, useModalInner } from '/@/components/Modal';
  import { useUserStoreWithOut } from '/@/store/modules/user';
  import NavigationDefaultImg from '/resource/img/navigation-default.png';

  export default defineComponent({
    name: 'TaskModal',
    components: { BasicModal, BasicForm },
    emits: ['success', 'register'],
    setup(_, { emit }) {
      const isUpdate = ref(false);
      const editId = ref();
      const userStore = useUserStoreWithOut();

      const [registerForm, { resetFields, setFieldsValue, validate }] = useForm({
        labelWidth: 120,
        schemas: formSchema,
        showActionButtonGroup: false,
        baseColProps: {
          span: 20,
        },
      });

      const [registerModal, { setModalProps, closeModal }] = useModalInner(async (data) => {
        await resetFields();
        isUpdate.value = !!data?.isUpdate;
        editId.value = data?.record?.ID;
        if (unref(isUpdate)) {
          await setFieldsValue({
            ...data.record,
          });
        } else {
          await setFieldsValue({
            icon: NavigationDefaultImg,
          });
        }
        setModalProps({ confirmLoading: false });
      });

      const getTitle = computed(() => (!unref(isUpdate) ? '添加' : '编辑') + '任务');

      async function handleSubmit() {
        try {
          const values = await validate();
          setModalProps({ confirmLoading: true });
          if (!unref(isUpdate)) {
            const { id } = await addJob(userStore.getProjectId, values);
            emit('success', 'add', id);
          } else if (unref(editId)) {
            await editJob(userStore.getProjectId, values, unref(editId));
            emit('success', 'edit', unref(editId));
          }
          closeModal();
          await resetFields();
        } finally {
          setModalProps({ confirmLoading: false });
        }
      }

      return {
        registerModal,
        registerForm,
        getTitle,
        handleSubmit,
        isUpdate,
      };
    },
  });
</script>
