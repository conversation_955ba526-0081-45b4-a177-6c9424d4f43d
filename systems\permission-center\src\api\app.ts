import type { PermissionBaseRes } from './_common.ts';
import type { SysUserInfo } from './users.ts';
import { apiService } from '../services/req.ts';

export interface PermissionAppInfoBase {
  id?: number;
  createdAt?: string;
  updatedAt?: string;
  name?: string;
  code?: string;
  description?: string;
  isMultiTenant?: number;
  isApiCheck?: number;
  tenant?: PermissionTenantInfo[];
  creator?: string;
  secret?: string;
  isAdmin?: boolean;
  category?: string;
  icon?: string;
}

export interface PermissionAppInfo extends PermissionAppInfoBase {
  member?: { admin?: SysUserInfo[]; owner?: SysUserInfo[] };
}

export interface PermissionAppInfoV2 extends PermissionAppInfoBase {
  member: SysUserInfo[];
}

/**
 * 获取权限管理应用列表
 */
export const getPermissionApps = apiService.GET<
  Record<string, never>,
  Record<string, never>,
  PermissionBaseRes<{ myApp: PermissionAppInfoV2[]; allApp: PermissionAppInfoV2[] }>
>(`/api/auth/v2/apps`);

export interface PermissionAppForm {
  name?: string;
  code?: string;
  description?: string;
  isMultiTenant?: boolean;
  isApiCheck?: boolean;
  admin?: SysUserInfo['hgId'][];
  icon?: string;
  category?: string;
}

/**
 * 获取应用
 */
export const getPermissionAppDetail = apiService.GET<
  { appId: PermissionAppInfo['id'] },
  Record<string, never>,
  PermissionBaseRes<PermissionAppInfo>
>(`/api/auth/v1/app/{appId}`);

/**
 * 创建应用
 */
export const createPermissionApp = apiService.POST<
  Record<string, never>,
  PermissionAppForm,
  PermissionBaseRes<PermissionAppInfo>
>(`/api/auth/v1/app`);

/**
 * 更新应用
 */
export const updatePermissionApp = apiService.PUT<
  { appId: PermissionAppInfo['id'] },
  PermissionAppForm,
  PermissionBaseRes<PermissionAppInfo>
>(`/api/auth/v1/app/{appId}`);

/**
 * 删除应用
 */
export const deletePermissionApp = apiService.DELETE<
  { appId: PermissionAppInfo['id'] },
  Record<string, never>,
  PermissionBaseRes<PermissionAppInfo>
>(`/api/auth/v1/app/{appId}`);

export interface PermissionTenantInfo {
  id?: number;
  appId?: number;
  tenant?: string;
  /**
   * 对应项目内 projectId
   */
  originId?: string;
  /**
   * 租户管理员
   */
  members?: SysUserInfo[];
}

/**
 * 获取应用分类列表
 */
export const getPermissionAppCategories = apiService.GET<
  Record<string, never>,
  Record<string, never>,
  PermissionBaseRes<string[]>
>(`/api/auth/v2/app/categories`);

/**
 * 获取应用租户列表
 */
export const getPermissionAppTenantList = apiService.GET<
  { appId: PermissionAppInfo['id'] },
  Record<string, never>,
  PermissionBaseRes<PermissionTenantInfo[]>
>(`/api/auth/v1/app/{appId}/tenants`);

/**
 * 应用角色枚举
 */
export enum PermissionRoleEnum {
  /**
   * 系统管理员
   */
  SystemAdmin = 'system_admin',
  /**
   * 应用管理员
   */
  AppAdmin = 'app_admin',
  /**
   * 项目管理员
   */
  ProjectAdmin = 'project_admin',
}

/**
 * 获取我的应用角色
 */
export const getPermissionRoleMe = apiService.GET<
  { appId: PermissionAppInfo['id'] },
  Record<string, never>,
  PermissionBaseRes<PermissionRoleEnum[]>
>(`/api/auth/v1/app/{appId}/role/me`);

/**
 * 获取应用租户管理员
 */
export const getPermissionTenantsMembers = apiService.GET<
  { appId: PermissionAppInfo['id'] },
  Record<string, never>,
  PermissionBaseRes<PermissionTenantInfo[]>
>(`/api/auth/v1/app/{appId}/tenants_members`);

/**
 * 更新应用租户管理员
 */
export const updatePermissionTenantMembers = apiService.PUT<
  { appId: PermissionAppInfo['id']; appTenantId: PermissionTenantInfo['id'] },
  {
    member: SysUserInfo['hgId'][];
  },
  PermissionBaseRes<PermissionTenantInfo>
>(`/api/auth/v1/app/{appId}/tenant/{appTenantId}/member`);
