<template>
  <PageWrapper
    :title="`提交tag:${streamData?.description || streamData?.path}`"
    headerSticky
    @back="handleBack"
  >
    <div class="m-4 rounded-md bg-white p-4">
      <div class="my-10 w-full flex flex-col items-center justify-center">
        <template v-if="isCloning">
          <div class="mb-[24px] flex items-center gap-[24px]">
            <Icon
              icon="devGuard-submit-tags|svg"
              class="c-[#0067C7]"
              :size="60"
            />
            <div>
              <div class="text-xl">
                从已存在的DevGuard配置中克隆提交tag配置:
              </div>
              <div class="mt-6 text-lg">
                分支:
                <Select
                  v-model:value="selectedCloneStream"
                  class="w-400px pl-3"
                  placeholder="请选择源分支"
                  showSearch
                  optionFilterProp="label"
                  :options="streamOptions"
                  allowClear
                />
              </div>
            </div>
          </div>
          <div class="w-full flex justify-center gap-[6px]">
            <BasicButton type="primary" :disabled="!selectedCloneStream" :loading="cloneLoading" @click="handleClone">
              克隆
            </BasicButton>
            <BasicButton :disabled="cloneLoading" @click="() => isCloning = false">
              取消
            </BasicButton>
          </div>
        </template>
        <template v-else>
          <div class="text-secondary text-lg">
            该DevGuard配置未配置提交tag
          </div>
          <div class="mt-6">
            <BasicButton type="primary" :disabled="cloneLoading" :loading="configLoading" @click="handleConfig">
              配置
            </BasicButton>
            <BasicButton class="ml-3" type="primary" :disabled="configLoading" :loading="cloneLoading" @click="() => isCloning = true">
              克隆其他分支的配置
            </BasicButton>
          </div>
        </template>
      </div>
    </div>
  </PageWrapper>
</template>

<script setup lang="ts">
import { useLatestPromise } from '@hg-tech/utils-vue';
import { computed, ref } from 'vue';
import { Select } from 'ant-design-vue';
import { cloneTagConfig, configCommitTag } from '../../../../api/page/p4.ts';
import { useUserStore } from '../../../../store/modules/user.ts';
import { PageWrapper } from '../../../../components/Page/index.ts';
import { PlatformEnterPoint } from '@hg-tech/oasis-common';
import { useRouter } from 'vue-router';
import type { StreamsListItem } from '../../../../api/page/model/p4Model.ts';
import { Icon } from '../../../../components/Icon/index.ts';
import { BasicButton } from '/@/components/Button';

const props = withDefaults(defineProps<{
  streamData: StreamsListItem;
  allStreamList?: StreamsListItem[];
}>(), {
  allStreamList: () => [],
});

const emit = defineEmits<{
  (e: 'update'): void;
}>();

const router = useRouter();
const userStore = useUserStore();
const { execute: doConfig, loading: configLoading } = useLatestPromise(configCommitTag);
const { execute: doClone, loading: cloneLoading } = useLatestPromise(cloneTagConfig);
const isCloning = ref(false);
const selectedCloneStream = ref<number>();
const streamOptions = computed(() => props.allStreamList
  ?.filter((i) => i.tagConfig && i.ID !== props.streamData.ID)
  ?.map((item) => ({
    label: item.description || item.path,
    value: item.ID,
  })) || []);

async function handleConfig() {
  if (!props.streamData?.ID) {
    return;
  }
  const res = await doConfig(userStore.getProjectId, { streamID: props.streamData.ID, bindTag: false, tagIDs: [] });
  if (res?.code !== 7) {
    emit('update');
  }
}

async function handleClone() {
  if (!props.streamData?.ID || !selectedCloneStream.value) {
    return;
  }

  const res = await doClone(userStore.getProjectId, {
    fromStreamID: selectedCloneStream.value,
    toStreamID: props.streamData.ID,
  });
  if (res?.code !== 7) {
    emit('update');
  }
}

function handleBack() {
  router.push({ name: PlatformEnterPoint.P4Depots });
}
</script>
