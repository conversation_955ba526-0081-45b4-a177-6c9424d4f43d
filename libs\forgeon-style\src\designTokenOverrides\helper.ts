import { type ForgeonThemeTokens, ForgeonTheme } from '../tokens';
import { mapValues, merge } from 'lodash-es';
import { getForgeonColor } from '../utils';
import type { ThemeConfig } from 'ant-design-vue/es/config-provider/context';

export type ForgeonThemeMapToAntdToken = { [P in keyof NonNullable<ThemeConfig['token']>]: ForgeonThemeTokens };

export function toAntdThemedToken(
  vars: ForgeonThemeMapToAntdToken,
  mode: ForgeonTheme,
) {
  return mapValues(vars, (value) => value && getForgeonColor(value, mode)) as ThemeConfig['token'];
}

export type ForgeonThemeMapToAntdComponentToken = {
  [P in keyof NonNullable<ThemeConfig['components']>]: {
    [T in keyof NonNullable<NonNullable<ThemeConfig['components']>[P]>]: ForgeonThemeTokens
  }
};

export function toAntdThemedComponentColors(
  vars: ForgeonThemeMapToAntdComponentToken,
  mode: ForgeonTheme,
) {
  return mapValues(vars, (component) => mapValues(component, (value) => value && getForgeonColor(value, mode))) as ThemeConfig['components'];
}

export function withCustomAntdTheme(
  tokens: {
    common?: ForgeonThemeMapToAntdToken;
    components?: ForgeonThemeMapToAntdComponentToken;
  },
  defaultTheme: {
    light: ThemeConfig;
    dark: ThemeConfig;
  },
) {
  return {
    antdThemeTokensLight: merge({}, defaultTheme.light, {
      token: toAntdThemedToken(tokens.common || {}, ForgeonTheme.Light),
      components: toAntdThemedComponentColors(tokens.components || {}, ForgeonTheme.Light),
    }),
    antdThemeTokensDark: merge({}, defaultTheme.dark, {
      token: toAntdThemedToken(tokens.common || {}, ForgeonTheme.Dark),
      components: toAntdThemedComponentColors(tokens.components || {}, ForgeonTheme.Dark),
    }),
  };
}
