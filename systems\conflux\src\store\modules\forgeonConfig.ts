import { defineStore } from 'pinia';
import { useMicroAppInject, usePlatformConfigCtx } from '@hg-tech/oasis-common';
import { computed } from 'vue';

/**
 * 获取主应用配置信息
 */
export const useForgeonConfigStore = defineStore('ForgeonConfig', () => {
  const { data } = useMicroAppInject(usePlatformConfigCtx);
  return {
    projectList: computed(() => data.value?.projectList),
    currentProjectId: computed(() => data.value?.currentProjectId),
    setCurrentProjectId: (id: number) => {
      data.value?.setCurrentProjectId(id);
    },
  };
});
