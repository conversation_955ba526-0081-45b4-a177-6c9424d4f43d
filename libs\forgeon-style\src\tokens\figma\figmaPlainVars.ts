/* This file is automatically generated. DO NOT EDIT it manually. */

/**
 * @deprecated 仅供当前仓库内部使用
 */
export const FigmaPlainVars = {
  '$light.$violet.1': 'rgba(235,235,255,1.00)',
  '$light.$violet.2': 'rgba(212,209,255,1.00)',
  '$light.$violet.3': 'rgba(182,176,255,1.00)',
  '$light.$violet.4': 'rgba(155,143,255,1.00)',
  '$light.$violet.5': 'rgba(124,106,255,1.00)',
  '$light.$violet.6': 'rgba(94,69,255,1.00)',
  '$light.$violet.7': 'rgba(78,53,219,1.00)',
  '$light.$violet.8': 'rgba(63,39,184,1.00)',
  '$light.$violet.9': 'rgba(49,27,148,1.00)',
  '$light.$violet.10': 'rgba(36,17,112,1.00)',
  '$light.$blue.1': 'rgba(224,240,255,1.00)',
  '$light.$blue.2': 'rgba(196,224,255,1.00)',
  '$light.$blue.3': 'rgba(153,200,255,1.00)',
  '$light.$blue.4': 'rgba(107,173,255,1.00)',
  '$light.$blue.5': 'rgba(61,145,255,1.00)',
  '$light.$blue.6': 'rgba(13,114,255,1.00)',
  '$light.$blue.7': 'rgba(5,91,219,1.00)',
  '$light.$blue.8': 'rgba(0,71,184,1.00)',
  '$light.$blue.9': 'rgba(0,54,148,1.00)',
  '$light.$blue.10': 'rgba(0,39,112,1.00)',
  '$light.$lightblue.1': 'rgba(219,246,255,1.00)',
  '$light.$lightblue.2': 'rgba(188,237,254,1.00)',
  '$light.$lightblue.3': 'rgba(142,223,253,1.00)',
  '$light.$lightblue.4': 'rgba(96,208,252,1.00)',
  '$light.$lightblue.5': 'rgba(50,191,251,1.00)',
  '$light.$lightblue.6': 'rgba(0,171,250,1.00)',
  '$light.$lightblue.7': 'rgba(0,139,208,1.00)',
  '$light.$lightblue.8': 'rgba(0,108,167,1.00)',
  '$light.$lightblue.9': 'rgba(0,79,125,1.00)',
  '$light.$lightblue.10': 'rgba(0,51,83,1.00)',
  '$light.$teal.1': 'rgba(216,248,239,1.00)',
  '$light.$teal.2': 'rgba(178,240,225,1.00)',
  '$light.$teal.3': 'rgba(135,225,204,1.00)',
  '$light.$teal.4': 'rgba(84,211,183,1.00)',
  '$light.$teal.5': 'rgba(39,196,165,1.00)',
  '$light.$teal.6': 'rgba(0,181,148,1.00)',
  '$light.$teal.7': 'rgba(0,151,126,1.00)',
  '$light.$teal.8': 'rgba(0,121,103,1.00)',
  '$light.$teal.9': 'rgba(0,91,79,1.00)',
  '$light.$teal.10': 'rgba(0,60,53,1.00)',
  '$light.$green.1': 'rgba(218,248,224,1.00)',
  '$light.$green.2': 'rgba(181,242,194,1.00)',
  '$light.$green.3': 'rgba(142,229,161,1.00)',
  '$light.$green.4': 'rgba(101,215,128,1.00)',
  '$light.$green.5': 'rgba(65,202,98,1.00)',
  '$light.$green.6': 'rgba(34,189,75,1.00)',
  '$light.$green.7': 'rgba(27,158,64,1.00)',
  '$light.$green.8': 'rgba(20,126,52,1.00)',
  '$light.$green.9': 'rgba(14,95,39,1.00)',
  '$light.$green.10': 'rgba(9,63,27,1.00)',
  '$light.$lightgreen.1': 'rgba(233,250,210,1.00)',
  '$light.$lightgreen.2': 'rgba(207,245,171,1.00)',
  '$light.$lightgreen.3': 'rgba(182,236,132,1.00)',
  '$light.$lightgreen.4': 'rgba(156,226,95,1.00)',
  '$light.$lightgreen.5': 'rgba(131,217,61,1.00)',
  '$light.$lightgreen.6': 'rgba(106,207,29,1.00)',
  '$light.$lightgreen.7': 'rgba(86,173,24,1.00)',
  '$light.$lightgreen.8': 'rgba(66,138,18,1.00)',
  '$light.$lightgreen.9': 'rgba(47,103,12,1.00)',
  '$light.$lightgreen.10': 'rgba(30,69,8,1.00)',
  '$light.$yellow.1': 'rgba(255,253,204,1.00)',
  '$light.$yellow.2': 'rgba(255,249,176,1.00)',
  '$light.$yellow.3': 'rgba(255,241,135,1.00)',
  '$light.$yellow.4': 'rgba(252,229,93,1.00)',
  '$light.$yellow.5': 'rgba(252,215,50,1.00)',
  '$light.$yellow.6': 'rgba(252,197,0,1.00)',
  '$light.$yellow.7': 'rgba(213,155,0,1.00)',
  '$light.$yellow.8': 'rgba(170,116,0,1.00)',
  '$light.$yellow.9': 'rgba(128,80,0,1.00)',
  '$light.$yellow.10': 'rgba(85,49,0,1.00)',
  '$light.$orange.1': 'rgba(255,240,212,1.00)',
  '$light.$orange.2': 'rgba(254,227,180,1.00)',
  '$light.$orange.3': 'rgba(253,207,139,1.00)',
  '$light.$orange.4': 'rgba(252,185,98,1.00)',
  '$light.$orange.5': 'rgba(251,162,60,1.00)',
  '$light.$orange.6': 'rgba(250,134,17,1.00)',
  '$light.$orange.7': 'rgba(208,101,12,1.00)',
  '$light.$orange.8': 'rgba(167,72,8,1.00)',
  '$light.$orange.9': 'rgba(125,47,5,1.00)',
  '$light.$orange.10': 'rgba(83,27,2,1.00)',
  '$light.$red.1': 'rgba(254,232,226,1.00)',
  '$light.$red.2': 'rgba(253,208,197,1.00)',
  '$light.$red.3': 'rgba(251,175,161,1.00)',
  '$light.$red.4': 'rgba(249,145,129,1.00)',
  '$light.$red.5': 'rgba(247,112,99,1.00)',
  '$light.$red.6': 'rgba(242,79,68,1.00)',
  '$light.$red.7': 'rgba(209,42,37,1.00)',
  '$light.$red.8': 'rgba(174,26,26,1.00)',
  '$light.$red.9': 'rgba(138,16,21,1.00)',
  '$light.$red.10': 'rgba(102,9,16,1.00)',
  '$light.$pink.1': 'rgba(253,228,239,1.00)',
  '$light.$pink.2': 'rgba(250,211,229,1.00)',
  '$light.$pink.3': 'rgba(246,168,207,1.00)',
  '$light.$pink.4': 'rgba(241,127,188,1.00)',
  '$light.$pink.5': 'rgba(237,87,172,1.00)',
  '$light.$pink.6': 'rgba(229,64,163,1.00)',
  '$light.$pink.7': 'rgba(196,36,137,1.00)',
  '$light.$pink.8': 'rgba(161,24,120,1.00)',
  '$light.$pink.9': 'rgba(125,15,92,1.00)',
  '$light.$pink.10': 'rgba(89,8,66,1.00)',
  '$light.$purple.1': 'rgba(250,231,251,1.00)',
  '$light.$purple.2': 'rgba(245,210,250,1.00)',
  '$light.$purple.3': 'rgba(235,167,247,1.00)',
  '$light.$purple.4': 'rgba(219,123,240,1.00)',
  '$light.$purple.5': 'rgba(202,83,235,1.00)',
  '$light.$purple.6': 'rgba(188,64,229,1.00)',
  '$light.$purple.7': 'rgba(140,30,183,1.00)',
  '$light.$purple.8': 'rgba(107,19,148,1.00)',
  '$light.$purple.9': 'rgba(77,11,112,1.00)',
  '$light.$purple.10': 'rgba(58,6,89,1.00)',
  '$light.$gray.0': 'rgba(255,255,255,1.00)',
  '$light.$gray.1': 'rgba(245,246,247,1.00)',
  '$light.$gray.2': 'rgba(237,239,242,1.00)',
  '$light.$gray.3': 'rgba(227,230,237,1.00)',
  '$light.$gray.4': 'rgba(211,216,224,1.00)',
  '$light.$gray.5': 'rgba(194,200,212,1.00)',
  '$light.$gray.6': 'rgba(177,183,196,1.00)',
  '$light.$gray.7': 'rgba(157,164,178,1.00)',
  '$light.$gray.8': 'rgba(138,146,161,1.00)',
  '$light.$gray.9': 'rgba(122,130,145,1.00)',
  '$light.$gray.10': 'rgba(105,112,128,1.00)',
  '$light.$gray.11': 'rgba(88,95,110,1.00)',
  '$light.$gray.12': 'rgba(72,78,92,1.00)',
  '$light.$gray.13': 'rgba(56,61,74,1.00)',
  '$light.$gray.14': 'rgba(41,46,56,1.00)',
  '$light.$gray.15': 'rgba(28,31,38,1.00)',
  '$light.$gray.16': 'rgba(0,0,0,1.00)',
  '$dark.$violet.1': 'rgba(51,56,99,1.00)',
  '$dark.$violet.2': 'rgba(60,66,126,1.00)',
  '$dark.$violet.3': 'rgba(70,77,155,1.00)',
  '$dark.$violet.4': 'rgba(81,87,185,1.00)',
  '$dark.$violet.5': 'rgba(95,99,216,1.00)',
  '$dark.$violet.6': 'rgba(111,111,247,1.00)',
  '$dark.$violet.7': 'rgba(136,134,247,1.00)',
  '$dark.$violet.8': 'rgba(166,164,251,1.00)',
  '$dark.$violet.9': 'rgba(195,194,253,1.00)',
  '$dark.$violet.10': 'rgba(222,222,255,1.00)',
  '$dark.$blue.1': 'rgba(37,60,94,1.00)',
  '$dark.$blue.2': 'rgba(44,75,122,1.00)',
  '$dark.$blue.3': 'rgba(50,90,150,1.00)',
  '$dark.$blue.4': 'rgba(54,104,180,1.00)',
  '$dark.$blue.5': 'rgba(57,118,210,1.00)',
  '$dark.$blue.6': 'rgba(59,132,241,1.00)',
  '$dark.$blue.7': 'rgba(97,158,246,1.00)',
  '$dark.$blue.8': 'rgba(133,183,249,1.00)',
  '$dark.$blue.9': 'rgba(170,208,252,1.00)',
  '$dark.$blue.10': 'rgba(209,232,255,1.00)',
  '$dark.$lightblue.1': 'rgba(31,65,89,1.00)',
  '$dark.$lightblue.2': 'rgba(37,83,115,1.00)',
  '$dark.$lightblue.3': 'rgba(42,106,145,1.00)',
  '$dark.$lightblue.4': 'rgba(47,125,173,1.00)',
  '$dark.$lightblue.5': 'rgba(50,144,201,1.00)',
  '$dark.$lightblue.6': 'rgba(53,162,229,1.00)',
  '$dark.$lightblue.7': 'rgba(90,181,237,1.00)',
  '$dark.$lightblue.8': 'rgba(128,198,242,1.00)',
  '$dark.$lightblue.9': 'rgba(166,216,250,1.00)',
  '$dark.$lightblue.10': 'rgba(204,238,255,1.00)',
  '$dark.$teal.1': 'rgba(29,71,70,1.00)',
  '$dark.$teal.2': 'rgba(32,89,87,1.00)',
  '$dark.$teal.3': 'rgba(33,107,103,1.00)',
  '$dark.$teal.4': 'rgba(33,128,121,1.00)',
  '$dark.$teal.5': 'rgba(34,148,138,1.00)',
  '$dark.$teal.6': 'rgba(33,166,152,1.00)',
  '$dark.$teal.7': 'rgba(89,186,172,1.00)',
  '$dark.$teal.8': 'rgba(129,207,192,1.00)',
  '$dark.$teal.9': 'rgba(166,227,213,1.00)',
  '$dark.$teal.10': 'rgba(201,248,235,1.00)',
  '$dark.$green.1': 'rgba(38,74,45,1.00)',
  '$dark.$green.2': 'rgba(42,90,53,1.00)',
  '$dark.$green.3': 'rgba(48,108,62,1.00)',
  '$dark.$green.4': 'rgba(50,126,71,1.00)',
  '$dark.$green.5': 'rgba(54,145,80,1.00)',
  '$dark.$green.6': 'rgba(54,163,88,1.00)',
  '$dark.$green.7': 'rgba(96,183,118,1.00)',
  '$dark.$green.8': 'rgba(131,202,147,1.00)',
  '$dark.$green.9': 'rgba(165,222,176,1.00)',
  '$dark.$green.10': 'rgba(208,240,194,1.00)',
  '$dark.$lightgreen.1': 'rgba(59,79,40,1.00)',
  '$dark.$lightgreen.2': 'rgba(69,98,45,1.00)',
  '$dark.$lightgreen.3': 'rgba(78,117,48,1.00)',
  '$dark.$lightgreen.4': 'rgba(86,136,53,1.00)',
  '$dark.$lightgreen.5': 'rgba(92,156,56,1.00)',
  '$dark.$lightgreen.6': 'rgba(97,176,58,1.00)',
  '$dark.$lightgreen.7': 'rgba(124,191,92,1.00)',
  '$dark.$lightgreen.8': 'rgba(152,207,126,1.00)',
  '$dark.$lightgreen.9': 'rgba(179,224,159,1.00)',
  '$dark.$lightgreen.10': 'rgba(208,240,194,1.00)',
  '$dark.$yellow.1': 'rgba(84,66,35,1.00)',
  '$dark.$yellow.2': 'rgba(111,87,40,1.00)',
  '$dark.$yellow.3': 'rgba(137,106,42,1.00)',
  '$dark.$yellow.4': 'rgba(163,126,42,1.00)',
  '$dark.$yellow.5': 'rgba(190,148,42,1.00)',
  '$dark.$yellow.6': 'rgba(217,169,39,1.00)',
  '$dark.$yellow.7': 'rgba(223,190,82,1.00)',
  '$dark.$yellow.8': 'rgba(230,210,121,1.00)',
  '$dark.$yellow.9': 'rgba(239,230,160,1.00)',
  '$dark.$yellow.10': 'rgba(250,248,200,1.00)',
  '$dark.$orange.1': 'rgba(87,57,40,1.00)',
  '$dark.$orange.2': 'rgba(112,72,47,1.00)',
  '$dark.$orange.3': 'rgba(140,88,53,1.00)',
  '$dark.$orange.4': 'rgba(168,104,57,1.00)',
  '$dark.$orange.5': 'rgba(196,118,59,1.00)',
  '$dark.$orange.6': 'rgba(222,135,60,1.00)',
  '$dark.$orange.7': 'rgba(229,162,90,1.00)',
  '$dark.$orange.8': 'rgba(237,188,123,1.00)',
  '$dark.$orange.9': 'rgba(245,212,159,1.00)',
  '$dark.$orange.10': 'rgba(255,236,201,1.00)',
  '$dark.$red.1': 'rgba(89,46,46,1.00)',
  '$dark.$red.2': 'rgba(112,58,56,1.00)',
  '$dark.$red.3': 'rgba(138,69,65,1.00)',
  '$dark.$red.4': 'rgba(164,81,75,1.00)',
  '$dark.$red.5': 'rgba(191,93,84,1.00)',
  '$dark.$red.6': 'rgba(219,102,90,1.00)',
  '$dark.$red.7': 'rgba(230,129,118,1.00)',
  '$dark.$red.8': 'rgba(239,155,146,1.00)',
  '$dark.$red.9': 'rgba(247,181,175,1.00)',
  '$dark.$red.10': 'rgba(254,211,206,1.00)',
  '$dark.$pink.1': 'rgba(92,50,83,1.00)',
  '$dark.$pink.2': 'rgba(116,59,103,1.00)',
  '$dark.$pink.3': 'rgba(140,66,120,1.00)',
  '$dark.$pink.4': 'rgba(164,74,138,1.00)',
  '$dark.$pink.5': 'rgba(190,80,156,1.00)',
  '$dark.$pink.6': 'rgba(217,85,173,1.00)',
  '$dark.$pink.7': 'rgba(227,121,189,1.00)',
  '$dark.$pink.8': 'rgba(237,153,205,1.00)',
  '$dark.$pink.9': 'rgba(245,185,222,1.00)',
  '$dark.$pink.10': 'rgba(253,215,238,1.00)',
  '$dark.$purple.1': 'rgba(81,53,97,1.00)',
  '$dark.$purple.2': 'rgba(99,59,121,1.00)',
  '$dark.$purple.3': 'rgba(117,67,145,1.00)',
  '$dark.$purple.4': 'rgba(137,75,170,1.00)',
  '$dark.$purple.5': 'rgba(158,82,196,1.00)',
  '$dark.$purple.6': 'rgba(177,89,222,1.00)',
  '$dark.$purple.7': 'rgba(194,123,230,1.00)',
  '$dark.$purple.8': 'rgba(210,155,238,1.00)',
  '$dark.$purple.9': 'rgba(225,186,245,1.00)',
  '$dark.$purple.10': 'rgba(239,216,251,1.00)',
  '$dark.$gray.0': 'rgba(26,30,36,1.00)',
  '$dark.$gray.1': 'rgba(34,39,48,1.00)',
  '$dark.$gray.2': 'rgba(43,48,59,1.00)',
  '$dark.$gray.3': 'rgba(53,58,71,1.00)',
  '$dark.$gray.4': 'rgba(63,70,84,1.00)',
  '$dark.$gray.5': 'rgba(79,86,99,1.00)',
  '$dark.$gray.6': 'rgba(95,102,117,1.00)',
  '$dark.$gray.7': 'rgba(116,120,135,1.00)',
  '$dark.$gray.8': 'rgba(132,138,153,1.00)',
  '$dark.$gray.9': 'rgba(148,153,168,1.00)',
  '$dark.$gray.10': 'rgba(168,172,184,1.00)',
  '$dark.$gray.11': 'rgba(187,190,199,1.00)',
  '$dark.$gray.12': 'rgba(200,201,209,1.00)',
  '$dark.$gray.13': 'rgba(214,215,219,1.00)',
  '$dark.$gray.14': 'rgba(224,225,229,1.00)',
  '$dark.$gray.15': 'rgba(235,235,240,1.00)',
  '$dark.$gray.16': 'rgba(242,243,247,1.00)',
  '$basic.$black.0': 'rgba(0,0,0,0.00)',
  '$basic.$black.5': 'rgba(0,0,0,0.05)',
  '$basic.$black.10': 'rgba(0,0,0,0.10)',
  '$basic.$black.20': 'rgba(0,0,0,0.20)',
  '$basic.$black.30': 'rgba(0,0,0,0.30)',
  '$basic.$black.40': 'rgba(0,0,0,0.40)',
  '$basic.$black.50': 'rgba(0,0,0,0.50)',
  '$basic.$black.60': 'rgba(0,0,0,0.60)',
  '$basic.$black.70': 'rgba(0,0,0,0.70)',
  '$basic.$black.80': 'rgba(0,0,0,0.80)',
  '$basic.$black.90': 'rgba(0,0,0,0.90)',
  '$basic.$black.100': 'rgba(0,0,0,1.00)',
  '$basic.$white.0': 'rgba(255,255,255,0.00)',
  '$basic.$white.5': 'rgba(255,255,255,0.05)',
  '$basic.$white.10': 'rgba(255,255,255,0.10)',
  '$basic.$white.20': 'rgba(255,255,255,0.20)',
  '$basic.$white.30': 'rgba(255,255,255,0.30)',
  '$basic.$white.40': 'rgba(255,255,255,0.40)',
  '$basic.$white.50': 'rgba(255,255,255,0.50)',
  '$basic.$white.60': 'rgba(255,255,255,0.60)',
  '$basic.$white.70': 'rgba(255,255,255,0.70)',
  '$basic.$white.80': 'rgba(255,255,255,0.80)',
  '$basic.$white.90': 'rgba(255,255,255,0.90)',
  '$basic.$white.100': 'rgba(255,255,255,1.00)',
};
