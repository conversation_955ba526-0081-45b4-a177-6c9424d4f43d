import { defineStore } from 'pinia';
import { type PermissionDeclaration, useMicroAppInject, usePermissionCtx } from '@hg-tech/oasis-common';
import { MergePermission } from '../../constants/premission';
import { computed } from 'vue';

export const usePermissionStore = defineStore('permission', () => {
  const { data, loading } = useMicroAppInject(usePermissionCtx);

  const isProjectAdmin = computed(() => {
    return data.value?.checkPermission({
      any: [MergePermission.EditMerge],
    });
  });

  return {
    checkPermission: (permission: Omit<PermissionDeclaration, 'scope'>) => data.value?.checkPermission(permission, window.__MICRO_APP_NAME__),
    isProjectAdmin,
    permissionInfo: data.value?.permissionInfo,
    loading,
  };
});
