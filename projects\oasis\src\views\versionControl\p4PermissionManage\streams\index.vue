<template>
  <div :class="prefixCls">
    <div class="flex items-center justify-between" :class="{ 'mb-4': allStreamList?.length }">
      <div class="flex items-center">
        <slot name="depotTitle" />
      </div>
      <div class="flex items-center">
        <template v-if="canAddStream">
          <div
            v-tippy="'点击跳转帮助文档'"
            class="mr-3 cursor-pointer font-bold"
            @click="() => linkToHelp()"
          >
            一键开分支
            <Icon icon="ant-design:question-circle-outlined" />
            ：
          </div>
        </template>
        <a-button
          v-if="canCreateStream"
          v-track="'1esyr3ip7n'"
          class="custom-rounded-btn mr-3"
          borderColor="success"
          @click="() => handleFastAdd()"
        >
          <Icon icon="ant-design:plus-outlined" />
          新建分支
        </a-button>
        <a-button
          v-if="canAddStream"
          v-track="'hpdsmapisy'"
          class="custom-rounded-btn"
          borderColor="warning"
          @click="() => handleCreate()"
        >
          <Icon icon="ant-design:plus-outlined" />
          添加P4已有分支
        </a-button>
      </div>
    </div>
    <div ref="streamDom" :class="`${prefixCls}__stream-${depotID}`">
      <div v-for="stream in allStreamList" :key="stream.ID" :class="`${prefixCls}__stream-item`">
        <div class="flex items-center">
          <div
            class="i-ic:round-drag-handle mr-4 hidden h-20px w-20px cursor-grab"
            :class="`${prefixCls}__drag-btn`"
          />
          <Icon :size="20" class="mr-4" :class="`${prefixCls}__stream-icon`" :icon="getStreamIcon(stream)" />
          <div>
            <div class="flex items-center font-bold">
              {{ stream.description || stream.path }}
              <a-button
                v-if="stream.step! < 4 && !isVirtualStream(stream)"
                v-tippy="{
                  content: '继续一键开分支流程',
                  placement: 'bottom',
                }"
                type="text"
                size="small"
                class="!c-FO-Functional-Warning1-Default"
                @click.stop="handleFastEdit(stream)"
              >
                <Icon icon="material-symbols:step-over-rounded" />
              </a-button>
              <a-button
                type="text"
                size="small"
                :class="`${prefixCls}__stream-item-btn`"
                @click.stop="handleEdit(stream)"
              >
                <Icon icon="icon-park-outline:edit" />
              </a-button>
              <a-button
                type="text"
                danger
                size="small"
                :class="`${prefixCls}__stream-item-btn`"
                @click.stop="handleDelete(stream)"
              >
                <Icon icon="icon-park-outline:delete" />
              </a-button>
            </div>
            <div class="flex items-center text-xs c-FO-Content-Text2">
              <span v-if="stream.description">{{ stream.path }}</span>
              <span v-if="stream.user" class="ml-4">添加人: {{ formatNickName(stream.user) }}</span>
            </div>
          </div>
        </div>
        <div class="flex">
          <div v-if="!userStore.isDM01" :class="`${prefixCls}__extra`">
            <div :class="`${prefixCls}__extra-btn`" @click="() => handleSubmitCheck(stream)">
              <Icon
                icon="devGuard-submit-check-rules|svg"
                :style="{ color: stream.commitCheckEnabled && !isVirtualStream(stream) ? '#B02C54' : '#B4B4B4' }"
                :size="20"
              />
              <span class="text-xs">提交检查</span>
            </div>
            <div :class="`${prefixCls}__extra-btn`" @click="() => goToReportPage(stream)">
              <Icon
                icon="devGuard-submit-check-report|svg"
                :style="{ color: stream.hasRescRpt && !isVirtualStream(stream) ? '#B02C54' : '#B4B4B4' }"
                :size="20"
              />
              <span class="text-xs">检查报告</span>
            </div>
            <div :class="`${prefixCls}__extra-divider`" />

            <div
              v-tippy="{
                content: '配置P4提交工具的提交权限',
                placement: 'bottom',
              }"
              :class="`${prefixCls}__extra-btn`"
              @click="() => goToPermissionPage(stream)"
            >
              <Icon
                icon="devGuard-submit-permission|svg"
                :style="{ color: stream.permissionStatus && !isVirtualStream(stream) ? '#0067C7' : '#B4B4B4' }"
                :size="20"
              />
              <span class="text-xs">提交权限</span>
            </div>
            <div
              v-tippy="{
                content: '配置可提交单号类型后，用户使用提交工具时只可以使用符合条件的单子提交',
                placement: 'bottom',
              }"
              :class="`${prefixCls}__extra-btn`"
              @click="() => goToCommitParamsPage(stream)"
            >
              <Icon
                icon="devGuard-submit-commit-params|svg"
                :style="{ color: stream.workItemConfig && !isVirtualStream(stream) ? '#0067C7' : '#B4B4B4' }"
                :size="20"
              />
              <span class="text-xs">提交单号</span>
            </div>
            <div
              :class="`${prefixCls}__extra-btn`"
              @click="() => goToCommitTagsPage(stream)"
            >
              <Icon
                icon="devGuard-submit-tags|svg"
                :style="{ color: stream.tagConfig && !isVirtualStream(stream) ? '#0067C7' : '#B4B4B4' }"
                :size="20"
              />
              <span class="text-xs">提交tag</span>
            </div>
            <div
              v-tippy="{
                content: '配置Changelist标签',
                placement: 'bottom',
              }"
              :class="`${prefixCls}__extra-btn`"
              @click="() => goToCliPage(stream)"
            >
              <Icon
                icon="icon-park-outline:tag-one"
                :size="20"
                :style="{ color: stream.hasClLabelRecord && !isVirtualStream(stream) ? '#0067C7' : '#B4B4B4' }"
              />
              <span class="text-xs">CL标签</span>
            </div>
            <div :class="`${prefixCls}__extra-divider`" />

            <div
              v-if="!isLocalDepot"
              v-tippy="{
                content: '为特定路径配置Swarm审查规则和审查员',
                placement: 'bottom',
              }"
              :class="`${prefixCls}__extra-btn`"
              @click="() => goToAuditPage(stream)"
            >
              <div class="relative flex items-center justify-center">
                <Icon
                  icon="devGuard-auditor|svg"
                  :size="20"
                  :style="{ color: getCurAudit(stream.ID!) && !isVirtualStream(stream) ? '#FFC700' : '#B4B4B4' }"
                />
                <Icon
                  v-if="stream.lockStatus === 3"
                  class="absolute top-4px -right-10px !c-#db851f"
                  icon="icon-park-solid:lock"
                  :size="12"
                />
              </div>
              <span class="text-xs">审批和审查</span>
            </div>
            <div
              v-tippy="{
                content: '配置指定路径的提交完成后发送通知',
                placement: 'bottom',
              }"
              :class="`${prefixCls}__extra-btn`"
              @click="() => goToCompleteNoticePage(stream)"
            >
              <Icon
                icon="icon-park-outline:comment"
                :size="20"
                :style="{ color: stream.hasNoticeRule && !isVirtualStream(stream) ? '#FFC700' : '#B4B4B4' }"
              />
              <span class="text-xs">提交完成通知</span>
            </div>
            <div :class="`${prefixCls}__extra-divider`" />

            <Popconfirm
              :disabled="stream.diffManageEnabled || stream.streamType === 4"
              okText="确认"
              placement="topRight"
              arrowPointAtCenter
              @confirm="handleOpenDiff(stream)"
            >
              <template #title>
                确认开启表格diff功能？<br>
                开启后该分支新提交完成的cl将自动进行比对。
              </template>
              <div :class="`${prefixCls}__extra-btn`" @click="() => goToFormDiffPage(stream)">
                <Icon
                  icon="icon-park-solid:excel"
                  :size="20"
                  :style="{ color: stream.diffManageEnabled && !isVirtualStream(stream) ? '#008A0E' : '#B4B4B4' }"
                />
                <span class="text-xs">表格Diff</span>
              </div>
            </Popconfirm>
          </div>
        </div>
      </div>
    </div>

    <StreamsDrawer @register="registerDrawer" @success="handleSuccess" />
    <FastAddStreamModal @register="registerModal" @success="handleSuccess" />

    <DeleteModal @register="registerDeleteModal" @success="handleSuccess" />
  </div>
</template>

<script lang="ts" setup>
import { Popconfirm } from 'ant-design-vue';
import { computed, nextTick, onBeforeMount, ref, unref } from 'vue';
import { cloneDeep } from 'lodash-es';
import { useP4DepotStream } from '../hook';

import DeleteModal from './DeleteModal.vue';
import StreamsDrawer from './StreamsDrawer.vue';
import FastAddStreamModal from './fastAddStream/FastAddStreamModal.vue';
import { streamTypeOptions } from './streams.data';
import type { P4SubmitCheckItem, StreamsListItem } from '/@/api/page/model/p4Model';
import type { SwarmReviewProjectsListItem } from '/@/api/page/model/swarmModel';
import { getDepotCheckInfo, getDM01StreamsListByPage, getStreamsListByPage, getUserDM01GroupList, updateStreamsSort } from '/@/api/page/p4';
import { switchStreamDiff } from '/@/api/page/p4FormDiff';
import { getSwarmReviewProjectsListByPage } from '/@/api/page/swarm';
import { useDrawer } from '/@/components/Drawer';
import Icon from '/@/components/Icon';
import { useModal } from '/@/components/Modal';
import { formatNickName } from '/@/hooks/system/useUserList';
import { useDesign } from '/@/hooks/web/useDesign';
import { useMessage } from '/@/hooks/web/useMessage';
import { useGo } from '/@/hooks/web/usePage';
import { useSortable } from '/@/hooks/web/useSortable';
import { useUserStoreWithOut } from '/@/store/modules/user';
import { isNullOrUnDef } from '/@/utils/is';
import { openWindow } from '/@/utils';
import { getAllPaginationList } from '/@/hooks/web/usePagination';
import { PlatformEnterPoint } from '@hg-tech/oasis-common';

defineOptions({
  name: 'P4StreamsManage',
});

const props = defineProps({
  depotID: {
    type: Number,
    required: true,
  },
  isLocalDepot: {
    type: Boolean,
    default: false,
  },
  serverID: {
    type: Number,
    required: true,
  },
});

const go = useGo();
const { prefixCls } = useDesign('p4-stream-manage');
const [registerDrawer, { openDrawer }] = useDrawer();
const [registerModal, { openModal }] = useModal();
const [registerDeleteModal, { openModal: openDeleteModal }] = useModal();
const streamDom = ref();
const userStore = useUserStoreWithOut();
const { createMessage } = useMessage();
const auditList = ref<SwarmReviewProjectsListItem[]>([]);

const { getAllStreamList, allStreamList } = useP4DepotStream();

// 获取审查列表
async function getAuditList() {
  const { list } = await getAllPaginationList((p) => getSwarmReviewProjectsListByPage(userStore.getProjectId, {
    ...p,
    depotID: props.depotID,
  }));

  auditList.value = list || [];
}

/**
 * 当前为虚拟分支
 */
function isVirtualStream(stream: StreamsListItem) {
  return stream.streamType === 4;
}

function getCurAudit(ID: number) {
  return unref(auditList).find((e) => e.streamID === ID) || undefined;
}

const checkObject = ref<P4SubmitCheckItem>();

async function getCheckObject() {
  const { id, check } = await getDepotCheckInfo(userStore.getProjectId, props.depotID);

  if (id) {
    checkObject.value = check;
  } else {
    checkObject.value = undefined;
  }
}

const dm01GroupList = ref<number[]>([]);

async function getDM01GroupList() {
  const { groups } = await getUserDM01GroupList({ depotID: props.depotID });
  const categoryList = groups?.map((e) => (e.authoritySort === 1 ? 1 : e.category)) || [];

  dm01GroupList.value = categoryList?.includes(1) ? [2, 3] : (categoryList as number[]);
}

const hasDM01StreamPermission = computed(() => {
  return userStore.isDM01 && (dm01GroupList.value?.length || userStore.isSuperAdmin);
});
const hasAddLocalStreamPermission = computed(() => {
  return props.isLocalDepot && allStreamList.value?.length === 0;
});

const canCreateStream = computed(() => {
  return !props.isLocalDepot && (!userStore.isDM01 || hasDM01StreamPermission.value);
});

const canAddStream = computed(() => {
  return (
    (!props.isLocalDepot || hasAddLocalStreamPermission)
    && (!userStore.isDM01 || hasDM01StreamPermission.value)
  );
});

function getStreamIcon(stream: StreamsListItem) {
  return stream.category === 2
    ? 'devGuard-stream-online|svg'
    : streamTypeOptions.find((e) => e.value === stream.streamType)?.icon;
}

function showVirtualStreamMsg() {
  createMessage.warning('请在Virtual类型分支的原分支进行配置');
}

async function getCloneStreamList() {
  const params = {
    page: 1,
    pageSize: 999,
    depotID: props.depotID,

  };
  const { list } = userStore.isDM01
    ? await getDM01StreamsListByPage(params)
    : await getStreamsListByPage(userStore.getProjectId!, { ...params, permissionOrTag: true });
  return list;
}

async function handleFastAdd() {
  const list = await getCloneStreamList();
  openModal(true, {
    depotID: props.depotID,
    serverID: props.serverID,
    streamList: list,
    auditList: unref(auditList),
    checkObject: unref(checkObject),
    dm01GroupList: unref(dm01GroupList),
  });
}

async function handleCreate() {
  const list = await getCloneStreamList();

  openModal(true, {
    isExistStream: true,
    depotID: props.depotID,
    serverID: props.serverID,
    streamList: list,
    auditList: unref(auditList),
    checkObject: unref(checkObject),
    dm01GroupList: unref(dm01GroupList),
  });
}

async function handleFastEdit(record: StreamsListItem) {
  const list = await getCloneStreamList();

  openModal(true, {
    record,
    isExistStream: true,
    depotID: props.depotID,
    serverID: props.serverID,
    streamList: list,
    auditList: unref(auditList),
    checkObject: unref(checkObject),
    dm01GroupList: unref(dm01GroupList),
    hasAudit: !!getCurAudit(record.ID!),
  });
}

function goToAuditPage(record: StreamsListItem) {
  if (isVirtualStream(record)) {
    showVirtualStreamMsg();

    return;
  }

  go({
    name: PlatformEnterPoint.SwarmSettings,
    params: { id: props.depotID, stream_id: record.ID },
    query: { sID: props.serverID },
  });
}

function goToReportPage(record: StreamsListItem) {
  if (isVirtualStream(record)) {
    showVirtualStreamMsg();

    return;
  }

  go({
    name: PlatformEnterPoint.ResourceCheckReports,
    params: { id: props.depotID, stream_id: record.ID },
    query: { sID: props.serverID },
  });
}

function goToPermissionPage(record: StreamsListItem) {
  if (isVirtualStream(record)) {
    showVirtualStreamMsg();

    return;
  }

  go({
    name: PlatformEnterPoint.P4PermissionManagement,
    params: { id: props.depotID, stream_id: record.ID },
    query: { tab: 'permission', sID: props.serverID },
  });
}

function goToCompleteNoticePage(record: StreamsListItem) {
  if (isVirtualStream(record)) {
    showVirtualStreamMsg();

    return;
  }

  go({
    name: PlatformEnterPoint.P4CompleteNoticeManagement,
    params: { id: props.depotID, stream_id: record.ID },
    query: { tab: 'completeNotice', sID: props.serverID },
  });
}

function goToCommitTagsPage(record: StreamsListItem) {
  if (isVirtualStream(record)) {
    showVirtualStreamMsg();

    return;
  }

  go({
    name: PlatformEnterPoint.P4CommitTagConfigs,
    params: { id: props.depotID, stream_id: record.ID },
    query: { sID: props.serverID },
  });
}

function goToCommitParamsPage(record: StreamsListItem) {
  if (isVirtualStream(record)) {
    showVirtualStreamMsg();

    return;
  }

  go({
    name: PlatformEnterPoint.P4CommitParamsConfigs,
    params: { id: props.depotID, stream_id: record.ID },
    query: { sID: props.serverID },
  });
}

function goToCliPage(record: StreamsListItem) {
  if (isVirtualStream(record)) {
    showVirtualStreamMsg();

    return;
  }

  go({
    name: PlatformEnterPoint.P4ClLabelManagement,
    params: { id: props.depotID, stream_id: record.ID },
    query: { sID: props.serverID },
  });
}

function goToFormDiffPage(record: Recordable) {
  if (isVirtualStream(record)) {
    showVirtualStreamMsg();

    return;
  }

  if (!record.diffManageEnabled) {
    return;
  }

  go({
    name: PlatformEnterPoint.P4FormDiff,
    params: { id: props.depotID, stream_id: record.ID },
    query: { sID: props.serverID },
  });
}

function handleSubmitCheck(record: StreamsListItem) {
  if (isVirtualStream(record)) {
    showVirtualStreamMsg();

    return;
  }

  go({
    name: PlatformEnterPoint.ResourceCheck,
    params: { id: props.depotID, stream_id: record.ID },
    query: { sID: props.serverID },
  });
}

async function handleOpenDiff(record: Recordable) {
  if (isVirtualStream(record)) {
    showVirtualStreamMsg();

    return;
  }

  const res = await switchStreamDiff(userStore.getProjectId, record.ID);

  if (res?.code === 7) {
    return;
  }

  createMessage.success('开启成功');

  record.diffManageEnabled = true;
  goToFormDiffPage(record);
}

function handleEdit(record: Recordable) {
  openDrawer(true, {
    record,
    depotID: props.depotID,
    serverID: props.serverID,
    isUpdate: true,
  });
}

async function handleDelete(record: StreamsListItem) {
  openDeleteModal(true, {
    record,
  });
}

// 初始化拖拽
function initDrag() {
  nextTick(() => {
    const el = document.querySelector(`.${prefixCls}__stream-${props.depotID}`) as HTMLElement;
    const { initSortable } = useSortable(el, {
      handle: `.${prefixCls}__drag-btn`,
      onEnd: async ({ oldIndex, newIndex }) => {
        if (isNullOrUnDef(oldIndex) || isNullOrUnDef(newIndex) || oldIndex === newIndex) {
          return;
        }

        const newList = cloneDeep(allStreamList.value);
        // 排序接口
        const currentGroup = newList[oldIndex];

        newList.splice(oldIndex, 1);
        newList.splice(newIndex, 0, currentGroup);

        await updateStreamsSort(userStore.getProjectId, { idList: newList.map((item) => item.ID!), depotID: props.depotID });
        await init();
      },
    });

    initSortable();
  });
}

function init() {
  getAllStreamList(props.depotID, userStore.isDM01);
  if (!userStore.isDM01) {
    getAuditList();
    getCheckObject();
  } else {
    getDM01GroupList();
  }

  initDrag();
}

function handleSuccess() {
  init();
}

function linkToHelp() {
  const link = userStore.isDM01
    ? 'https://hypergryph.feishu.cn/wiki/KKsFw8MchiH6QrkK3u0cQ1vVn1c'
    : 'https://hypergryph.feishu.cn/wiki/Bkr2w2nn7ifxXfkrkvfc0WIInef?from=from_copylink';

  openWindow(link);
}

onBeforeMount(() => {
  init();
});
</script>

<style lang="less">
@prefix-cls: ~'hypergryph-p4-stream-manage';

.@{prefix-cls} {
  &__stream {
    &-item {
      display: flex;
      align-items: center;
      justify-content: space-between;
      width: 100%;
      padding: 16px;
      border: 1px solid @table-head-border;
      border-radius: 8px;
      background-color: @FO-Container-Fill1;

      &-btn {
        opacity: 0;
      }

      &:hover {
        border-color: @FO-Brand-Primary-Default;

        & .@{prefix-cls}__stream-item-btn {
          opacity: 1;
        }
        & .@{prefix-cls}__drag-btn {
          display: block;
        }
        & .@{prefix-cls}__stream-icon {
          display: none;
        }
      }

      &:not(:last-child) {
        margin-bottom: 8px;
      }
    }
  }

  &__extra {
    display: flex;
    justify-content: center;
    align-items: center;

    &-btn {
      display: flex;
      position: relative;
      flex-direction: column;
      align-items: center;
      padding: 4px 8px;
      margin: 0 2px;
      transition: all 0.3s ease;
      border-radius: 6px;
      cursor: pointer;
      white-space: nowrap;

      &:hover {
        background-color: @member-card-background;
      }
    }

    &-divider {
      width: 1px;
      height: 36px;
      margin: 4px;
      background-color: @table-border-color;
    }
  }
}
</style>
