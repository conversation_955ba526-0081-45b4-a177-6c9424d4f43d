import { type RuleV1Depot, RuleV1StreamType } from '@hg-tech/api-schema-merge';
import { Popover } from 'ant-design-vue';
import { computed, defineComponent, inject, onMounted, ref } from 'vue';
import { useMergeHome } from '../use-merge-home';
import { renderBranchTypeIcon } from '../../../models/config.model';

export interface BranchItemProps {
  title: string;
  id: number;
  status: string;
  streamList: RuleV1Depot[];
}

const BranchItem = defineComponent({
  setup() {
    const { currentBranchMap } = useMergeHome();
    const getNode = inject<() => { data: BranchItemProps }>('getNode');
    const props = ref<BranchItemProps>();
    const branchInfo = computed(() => {
      const stream = currentBranchMap.value.get(props.value?.id ?? 0);
      return stream;
    });

    onMounted(() => {
      props.value = getNode?.().data;
    });

    const getBranchName = () => {
      return branchInfo.value?.name || '--';
    };

    return () => (
      <Popover placement="top">
        {{
          default: () => (
            <div class="branch-item h-full w-full flex items-center justify-center gap-4px b-2px b-FO-Datavis-Blue1 rd-8px bg-FO-Datavis-Blue3 px-12px hover:bg-FO-Container-Fill1">
              {renderBranchTypeIcon[branchInfo.value?.streamType ?? RuleV1StreamType.DEVELOPMENT]()}
              <div class="branch-item-name FO-Font-B14 truncate">{getBranchName()}</div>
            </div>
          ),
          title: () => (
            <div class="branch-info">
              <div class="branch-item-status-text FO-Font-B14 mb-4px flex items-center gap-8px">
                {renderBranchTypeIcon[branchInfo.value?.streamType ?? RuleV1StreamType.DEVELOPMENT]()}
                {getBranchName()}
              </div>
              <div class="branch-item-status-text FO-Font-R14 pl-24px c-FO-Content-Text2">
                {branchInfo.value?.path}
              </div>
            </div>
          ),
        }}
      </Popover>
    );
  },
});

export {
  BranchItem,
};
