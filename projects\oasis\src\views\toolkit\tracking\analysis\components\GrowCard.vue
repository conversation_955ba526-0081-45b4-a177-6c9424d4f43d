<template>
  <div class="md:flex">
    <template v-for="(item, index) in growCardList" :key="item.title">
      <ACard
        size="small"
        :loading="loading"
        class="w-full md:w-1/2 !md:mt-0"
        :class="{ '!md:mr-4': index + 1 < 2, '!mt-4': index > 0 }"
      >
        <template #title>
          <span class="c-FO-Content-Text1">{{ item.title }}</span>
        </template>
        <div class="flex items-center justify-between px-4 py-4">
          <span class="text-2xl">{{ item.value }}</span>
          <Icon :icon="item.icon" :size="40" />
        </div>
      </ACard>
    </template>
  </div>
</template>

<script lang="ts" setup>
import { Card as ACard } from 'ant-design-vue';
import { computed } from 'vue';
import Icon from '/@/components/Icon';

defineOptions({
  name: 'TrackingAnalysisGrowCard',
});

const props = defineProps({
  loading: {
    type: Boolean,
    default: false,
  },
  sum: {
    type: Object,
    default: () => {
      return {
        coverSum: 0,
        accessSum: 0,
      };
    },
  },
});

const growCardList = computed(() => [
  {
    title: '访问总量',
    icon: 'fluent:people-audience-20-regular',
    value: props.sum.accessSum,
  },
  {
    title: '总覆盖',
    icon: 'fluent:data-pie-20-regular',
    value: props.sum.coverSum,
  },
]);
</script>
