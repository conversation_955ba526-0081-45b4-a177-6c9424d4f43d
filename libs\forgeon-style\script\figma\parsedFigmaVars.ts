import variables from './variables.json';
import { omit } from 'lodash-es';

interface FigmaVarValue<T = any> {
  $type: string;
  $value: T;
  $description: string;
  $variable_metadata: {
    name: string;
    figmaId: string;
    modes: Record<string, T>;
  };
}

interface FigmaVar<T> {
  [K: string]: this | FigmaVarValue<T>;
}

/**
 * 转换 figma
 */
function transformFigmaVars<V, T extends FigmaVar<V> = FigmaVar<V>>(
  inputVars: T,
  options: {
    transformValue: (value?: FigmaVarValue<V>['$variable_metadata']['modes']) => V;
    transformVarName?: (keyPath: string[]) => string;
  },
  path: string[] = [],
) {
  const {
    transformVarName = (p: string[]) => p.join('.'),
    transformValue = (v?: FigmaVarValue<V>['$variable_metadata']['modes']) => v,
  } = options ?? {};
  if ('$value' in inputVars) {
    // 认为这里是值
    const vars = inputVars as unknown as FigmaVarValue;
    return {
      [transformVarName(path)]: transformValue(vars.$variable_metadata?.modes),
    };
  }

  return Object.entries(inputVars).reduce((acc, [key, value]): Record<string, any> => ({
    ...acc,
    ...transformFigmaVars(value as T, options, [...path, key]),
  }), {} as Record<string, any>);
}

/**
 * 常量色
 * @example { '$light.$violet.1': 'rgba(235,235,255,1.00)' }
 */
const plainRootName: keyof typeof variables = '@globalcolorpalette';
const plainKey = variables[plainRootName].$collection_metadata.modes[0].key;
export const plainVars = transformFigmaVars<string>(
  omit(variables[plainRootName], '$collection_metadata'),
  { transformValue: (modes) => modes?.[plainKey] ?? '' },
);

/**
 * 主题色
 * @example { 'light:': {'$fo.$brand.primary-default': '$light.$violet.6'} }
 */
export const themeVars: Record<string, Record<string, string>> = variables['@focolorvariables'].$collection_metadata.modes.reduce((acc, mode) => ({
  ...acc,
  [mode.key]: transformFigmaVars<string>(
    omit(variables['@focolorvariables'], '$collection_metadata'),
    {
      transformValue: (modes) => {
        return modes?.[mode.key]?.match(new RegExp(`^\\{${plainRootName}\\.(.+)\\}$`))?.[1] ?? '';
      },
    },
  ),
}), {});

/**
 * 主题色所有值
 * @example [ '$fo.$brand.primary-default' ]
 */
export const themeVarNames = Object.keys(Object.values(themeVars)?.[0] || {});
