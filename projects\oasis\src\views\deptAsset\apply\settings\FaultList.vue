<template>
  <Tab :curTab="PlatformEnterPoint.DeviceManagementFaultList">
    <div class="m-4 rounded-md bg-FO-Container-Fill1">
      <BasicTable :formConfig="formConfig" @register="registerTable">
        <template #toolbar>
          <span class="w-full flex c-FO-Content-Text2">共 {{ totalNum }} 条报障记录</span>
        </template>
        <template #bodyCell="{ column, record }">
          <template v-if="column.dataIndex === 'device'">
            <EllipsisText>
              {{ record.device?.deviceName }}
            </EllipsisText>
            <div class="c-FO-Content-Text2">
              {{ record.device?.assetNo }}
            </div>
          </template>
          <template v-else-if="column.dataIndex === 'installRecord'">
            <EllipsisText class="hover:(cursor-pointer c-FO-Brand-Primary-Default)" @click="() => handleAppDetail(record.installRecord)">
              {{ record.installRecord?.appName }}
              <template v-if="record.installRecord?.version">
                ({{ record.installRecord?.version }})
              </template>
            </EllipsisText>
          </template>
          <template v-else-if="column.dataIndex === 'screenshot'">
            <div v-if="record.screenshot" class="w-fit overflow-hidden border border-FO-Container-Stroke2 rounded-4px">
              <Image :src="record.screenshot" alt="截图" :width="80" :height="60" />
            </div>
          </template>
          <template v-else-if="column.dataIndex === 'desc'">
            <EllipsisText>
              {{ record.desc }}
            </EllipsisText>
          </template>
          <template v-else-if="column.dataIndex === 'status'">
            <div class="flex items-center justify-center">
              <Dropdown :trigger="['click']">
                <div class="flex cursor-pointer items-center">
                  <div class="mr-2 h-2 w-2 rounded-full" :class="getStatusColor(record.status)" />
                  <span>{{ getStatusLabel(record.status) }}</span>
                  <Icon :icon="DownIcon" class="ml-1 text-xs c-FO-Content-Text2" />
                </div>
                <template #overlay>
                  <Menu @click="(e) => handleStatusChange(e, record)">
                    <MenuItem
                      v-for="item in faultStatusOptions" :key="item.value" :value="item.value"
                      :disabled="item.value === record.status"
                    >
                      <div class="flex items-center">
                        <div class="mr-2 h-2 w-2 rounded-full" :class="getStatusColor(item.value)" />
                        <span>{{ item.label }}</span>
                      </div>
                    </MenuItem>
                  </Menu>
                </template>
              </Dropdown>
            </div>
          </template>
          <template v-else-if="column.dataIndex === 'action'">
            <a-button type="link" @click="handleDetail(record)">
              设备详情
            </a-button>
          </template>
        </template>
        <template #emptyText>
          <div class="flex items-center justify-center">
            <Empty description="暂无记录" />
          </div>
        </template>
      </BasicTable>
    </div>
  </Tab>
</template>

<script lang="ts" setup>
import type { BasicColumn, FormProps, FormSchema } from '/@/components/Table';
import { BasicTable, useTable } from '/@/components/Table';
import { getFaultListByPage, getFaultTypes, updateFaultStatus } from '/@/api/page/deptAsset';
import { useUserList } from '/@/hooks/system/useUserList';
import { type FaultListItem, type FaultTypeListItem, FaultStatusEnum } from '/@/api/page/model/deptAssetModel';
import dayjs from 'dayjs';
import { computed, onMounted, ref, watchEffect } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { Dropdown, Empty, Image, Menu, MenuItem, message } from 'ant-design-vue';
import { EllipsisText } from '/@/components/EllipsisText';
import { PlatformEnterPoint } from '@hg-tech/oasis-common';
import Icon from '/@/components/Icon';
import DownIcon from '@iconify-icons/icon-park-outline/down';
import Tab from '../components/Tab.vue';
import { type MtlInstallRecordListItem, MtlInstallRecordSource } from '/@/api/page/mtl/model/deviceModel';
import { downloadByUrl } from '/@/utils/file/download';
import { getOssDownloadPreSignedUrl } from '/@/api/page/mtl/device';
import { getGamePackagesVersionByID } from '/@/api/page/test';
import { ResultEnum } from '/@/enums/httpEnum';
import type { BasicResult } from '/@/api/model/baseModel';

const route = useRoute();
const { resolve } = useRouter();
const { getNickNameByFieldNameContainDeleted, getUserListContainDeleted } = useUserList();

const totalNum = ref(0);
const faultTypeOptions = ref<FaultTypeListItem[]>([]);

const faultStatusOptions = [
  { label: '待处理', value: FaultStatusEnum.PENDING, color: 'bg-FO-Functional-Success1-Default' },
  { label: '处理中', value: FaultStatusEnum.PROCESSING, color: 'bg-FO-Brand-Primary-Default' },
  { label: '已关闭', value: FaultStatusEnum.CLOSED, color: 'bg-FO-Content-Text2' },
];
function getStatusLabel(status: FaultStatusEnum) {
  return faultStatusOptions?.find((item) => item.value === status)?.label || '';
}

function getStatusColor(status: FaultStatusEnum) {
  return faultStatusOptions?.find((item) => item.value === status)?.color || '';
}
const columns = computed<BasicColumn[]>(() => [
  {
    title: '提交人',
    dataIndex: 'creatorId',
    format: (_, record: Partial<FaultListItem>) => {
      return getNickNameByFieldNameContainDeleted(record.creatorId, 'ID') || '';
    },
    width: 150,
  },
  {
    title: '报障设备',
    dataIndex: 'device',
    align: 'left',
    width: 150,
  },
  {
    title: '问题类型',
    dataIndex: 'faultType',
    width: 120,
    format: (faultType) => {
      return faultTypeOptions.value?.find((item: FaultTypeListItem) => item.type === faultType)?.value || '';
    },
  },
  {
    title: '应用名称',
    dataIndex: 'installRecord',
    width: 150,
  },
  {
    title: '问题描述',
    dataIndex: 'desc',
    align: 'left',
    width: 200,
  },
  {
    title: '截图',
    dataIndex: 'screenshot',
    width: 100,
  },
  {
    title: '时间',
    dataIndex: 'CreatedAt',
    format: (text) => {
      return dayjs(text as string).format('YYYY-MM-DD HH:mm:ss');
    },
    width: 160,
  },
  {
    title: 'Agent',
    dataIndex: 'agent',
    width: 100,
  },
  {
    title: '处理状态',
    dataIndex: 'status',
    width: 100,
    fixed: 'right',
  },
]);

const searchFormSchema = computed<FormSchema[]>(() => [
  {
    field: 'createAtRange',
    label: '日期',
    component: 'RangePicker',
    componentProps: {
      valueFormat: 'X',
      placeholder: ['开始时间', '结束时间'],
      presets: [
        { label: '今天', value: [dayjs(), dayjs()] },
        { label: '最近一周', value: [dayjs().subtract(6, 'day'), dayjs()] },
        { label: '当前月', value: [dayjs().startOf('month'), dayjs()] },
      ],
      class: '!max-w-260px',
    },
  },
  {
    field: 'status',
    label: '处理状态',
    component: 'Select',
    componentProps: {
      options: faultStatusOptions,
      placeholder: '请选择处理状态',
    },
  },
  {
    field: 'creatorIds',
    label: '提交人',
    component: 'UserSelect',
    componentProps: {
      isMultiple: true,
      maxTagCount: 3,
      class: '!max-w-260px',
    },
  },
  {
    field: 'faultType',
    label: '问题类型',
    component: 'Select',
    componentProps: {
      options: faultTypeOptions.value,
      fieldNames: {
        label: 'value',
        value: 'type',
      },
      optionFilterProp: 'value',
      placeholder: '请选择问题类型',
      mode: 'multiple',
      maxTagCount: 3,
      showArrow: true,
      class: '!max-w-260px',
    },
  },
  {
    field: 'device',
    label: '设备',
    component: 'Input',
    componentProps: {
      placeholder: '请输入设备名称或资产编号',
    },
  },
]);

const formConfig = computed<FormProps>(() => ({
  labelWidth: 120,
  showActionButtonGroup: false,
  layout: 'vertical',
  schemas: searchFormSchema.value,
  baseColProps: {
    span: 4,
    style: {
      marginRight: '10px',
    },
  },
}));

async function getFaultTypeOptions() {
  const { list } = await getFaultTypes();
  faultTypeOptions.value = list || [];
}
const [registerTable, { getRawDataSource, reload }] = useTable({
  api: (p) => {
    const { createAtRange, ...rest } = p;
    const formattedCreateAtRange = createAtRange?.length > 0 ? [dayjs.unix(createAtRange[0]).startOf('day').unix(), dayjs.unix(createAtRange[1]).endOf('day').unix()] : undefined;
    return getFaultListByPage({ ...rest, id: route.query.faultId, createAtRange: formattedCreateAtRange });
  },
  columns,
  inset: true,
  resizeHeightOffset: 36,
  useSearchForm: true,
  showIndexColumn: false,
  bordered: true,
  pagination: {
    pageSize: 10,
    showTotal: () => '',
  },
  actionColumn: {
    width: 130,
    title: '操作',
    dataIndex: 'action',
    fixed: 'right',
  },
});

// 处理状态变更
async function handleStatusChange(e: any, record: FaultListItem) {
  const newStatus = e.key;
  if (newStatus === record.status) {
    return;
  }

  if (!record.ID) {
    return;
  }

  try {
    await updateFaultStatus(record.ID, Number(newStatus));
  } finally {
    reload();
  }
}

/**
 * 打开应用详情
 * @param record 安装记录
 */
async function handleAppDetail(record: MtlInstallRecordListItem) {
  const downloadInfo = record.downloadInfo ? JSON.parse(record.downloadInfo) : {};
  // 包体中心安装的
  if (record.source === MtlInstallRecordSource.PackageCenter) {
    if (downloadInfo.projectID && downloadInfo.versionID && downloadInfo.packageID) {
      const res = (await getGamePackagesVersionByID(
        downloadInfo.projectID,
        downloadInfo.packageID,
        downloadInfo.versionID,
        'none',
      )) as unknown as BasicResult<any>;
      if (res?.code === ResultEnum.API_ERROR && res?.msg?.includes('不存在')) {
        message.error('应用已失效, 从游戏包体中心删除');
        return;
      }
      const { fullPath } = resolve({ name: PlatformEnterPoint.GamePackage, query: { p: downloadInfo.projectID, v: downloadInfo.versionID, b: downloadInfo.packageID } });
      window.open(fullPath, '_blank');
    } else {
      message.error('应用已失效, 从游戏包体中心删除');
    }
  } else {
    // 本地安装的
    if (downloadInfo.apk || downloadInfo.ipa) {
      const name = (downloadInfo.apk || downloadInfo.ipa).replace(/^https:\/\/[^/]+\//, '');
      const { data: { data } } = await getOssDownloadPreSignedUrl({ name }, {});
      downloadByUrl({ url: data });
    } else {
      message.error('应用已失效, 上传的应用被删除或过期');
    }
  }
}

onMounted(() => {
  watchEffect(() => {
    const { total } = getRawDataSource();
    totalNum.value = total || 0;
  });
  getUserListContainDeleted();
  getFaultTypeOptions();
});
function handleDetail(record: FaultListItem) {
  const { fullPath } = resolve({ name: PlatformEnterPoint.CloudDevice, query: { editId: record.deviceId } });
  window.open(fullPath, '_blank');
}
</script>

<style lang="less" scoped>
::v-deep(.ant-form) {
  padding: 0 !important;
  margin-bottom: 0 !important;
}
</style>
