/* eslint-disable */
import { HTMLAttributes } from 'vue';

declare global {
  namespace JSX {
    interface IntrinsicAttributes extends HTMLAttributes {
      type?: string;
      accept?: string;
      group?: string;
    }
    interface Element extends HTMLAttributes {
      [key: string]: any;
    }
  }
  
  interface Window {
    __MICRO_APP_ENVIRONMENT__?: boolean;
    mount: () => void;
    unmount: () => void;
  }
}


