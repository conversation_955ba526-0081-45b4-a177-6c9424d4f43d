import { createUseFetchPoolLRU } from '@hg-tech/utils-vue';
import type { AppChatsParams } from '../api/page/model/systemModel.ts';
import { getAppChatList } from '../api/page/system.ts';
import { computed } from 'vue';
import { type MaybeRef, get } from '@vueuse/core';

const [useCachedChatGroupList] = createUseFetchPoolLRU(async (params?: Partial<AppChatsParams>) => {
  return getAppChatList(params).then((res) => res.list || []);
});

export function useChatGroupList(robot: MaybeRef<AppChatsParams['robot']>) {
  const { data, isLoading } = useCachedChatGroupList(
    { loadImmediately: true, defaultValue: [] },
    computed(() => ({ robot: get(robot) })),
  );
  return {
    chatGroupList: data,
    loadingChatGroupList: isLoading,
  };
}
