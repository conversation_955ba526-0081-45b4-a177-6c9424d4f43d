export enum MergeLocalStorageKey {
  isOnlineResolverTipsShow = 'MERGE:isOnlineResolverTipsShow',
}

export function getLocalStorage(key: MergeLocalStorageKey): string | null {
  return localStorage.getItem(key);
}
export function setLocalStorage(key: MergeLocalStorageKey, value: string): void {
  localStorage.setItem(key, value);
}
export function removeLocalStorage(key: MergeLocalStorageKey): void {
  localStorage.removeItem(key);
}
export function clearLocalStorage(): void {
  localStorage.clear();
}
