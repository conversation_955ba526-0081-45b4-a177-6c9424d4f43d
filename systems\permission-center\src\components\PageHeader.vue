<template>
  <ForgeonHeader
    :onHandleMenuExpand="() => platformConfig?.changeMenuExpendStatus(true)"
    :showUnfoldIcon="!platformConfig?.isMenuExpanded"
  >
    <template #title>
      <slot name="title">
        {{ title }}
      </slot>
    </template>
    <template #actions>
      <slot name="actions" />
    </template>
  </ForgeonHeader>
</template>

<script setup lang="ts">
import { ForgeonHeader, useMicroAppInject, usePlatformConfigCtx } from '@hg-tech/oasis-common';

defineProps<{
  title?: string;
}>();

defineSlots<{
  title: () => any;
  actions: () => any;
}>();

const { data: platformConfig } = useMicroAppInject(usePlatformConfigCtx);
</script>
