<template>
  <div v-track:v="'qsczx6ixuk'" class="toolkit-list">
    <div class="toolkit-list__left">
      <img class="w-full" :src="RoofCurtain" alt="">
      <div class="mt-4 flex flex-col gap-2 overflow-auto">
        <Spin :spinning="loadingToolkitRes">
          <template v-if="commonTypeList?.length > 0">
            <div
              v-for="comType in commonTypeList"
              :key="comType.ID"
              class="toolkit-list__left-tab"
              :class="{ 'toolkit-list__left-tab--active': activeTab === comType.ID }"
              @click="() => handleTabChange(comType.ID)"
            >
              <EllipsisText class="toolkit-list__left-tab-title">
                {{ comType.name }}
              </EllipsisText>
            </div>
          </template>
          <Empty v-else :image="Empty.PRESENTED_IMAGE_SIMPLE" description="暂无分支数据" />
        </Spin>
      </div>
    </div>
    <div class="toolkit-list__right">
      <div class="toolkit-list__right-filter">
        <div class="w-full flex flex-col-reverse justify-start gap-y-4 2xl:flex-row 2xl:items-center 2xl:justify-between">
          <div class="flex items-center">
            <div class="ml-5 flex items-center">
              <span class="mr-3 w-[33px] self-start font-bold leading-6">项目:</span>
              <TagSelect
                v-model="searchParams.labels"
                :options="allProjectLabel"
                isMultiple
                :fieldNames="{ label: 'name', value: 'ID' }"
                class="flex-1"
              />
            </div>
          </div>
          <div class="toolkit-list__right-filter-btn-list">
            <div class="toolkit-list__right-filter-btn ml" @click="() => goToSettingsPage()">
              <Icon :icon="SettingConfig" class="mr-1" />
              上传与配置
            </div>
          </div>
        </div>
        <div class="flex items-center">
          <div class="ml-5 mt-4 flex items-center">
            <span class="mr-3 font-bold">平台:</span>
            <TagSelect v-model="searchParams.platform" :options="platformOptions" />
          </div>
        </div>
        <div class="flex items-center">
          <div class="mt-4 flex flex-1 flex-wrap items-center">
            <span class="ml-5 mr-3 font-bold">搜索:</span>
            <Input
              v-model:value="searchParams.search"
              placeholder="请输入"
              style="width: 200px"
              allowClear
            />
          </div>
        </div>
      </div>
      <div class="toolkit-list__right-content">
        <CardList :data-list="renderPackageVersionList" :loading="loadingList" />
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { useRouteQuery } from '@vueuse/router';
import { Empty, Input, Spin } from 'ant-design-vue';
import { Icon } from '@iconify/vue';
import { debounce } from 'lodash';
import { TagSelect } from '@hg-tech/oasis-common/deprecated';
import SettingConfig from '@iconify-icons/icon-park-outline/setting-config';
import { EllipsisText, PlatformEnterPoint, useMicroAppInject, useProjectInfoCtx } from '@hg-tech/oasis-common';
import { computed, reactive, ref, toRefs, watch, watchEffect } from 'vue';
import { useRouter } from 'vue-router';
import CardList from './CardList.vue';
import { type ProjectListItem, getToolkitListByPage } from '../../api';
import { getToolkitTypeListByPage } from '../../api';
import { platformOptions } from '../configs.tsx';
import RoofCurtain from './assets/roof-curtain.svg';
import { vTrack } from '../../directives/track.ts';
import { useLatestPromise } from '@hg-tech/utils-vue';
import type { SearchParams } from './type.ts';

const router = useRouter();

const urlBranch = useRouteQuery('b', 0, { transform: Number });
const activeTab = ref<number>();

const { data: projectInfo } = useMicroAppInject(useProjectInfoCtx);
const { data: toolkitTypeRes, execute: fetchToolkitRes, loading: loadingToolkitRes } = useLatestPromise(() => getToolkitTypeListByPage({ page: 1, pageSize: 999 }, {}));
const commonTypeList = computed(() => {
  const list = toolkitTypeRes.value?.data?.data?.list || [];
  if (list.length) {
    return [{ ID: 0, name: '全部' }, ...list];
  } else {
    return [];
  }
});
watchEffect(async () => fetchToolkitRes());
watchEffect(async () => {
  activeTab.value = activeTab.value || urlBranch.value || 0;

  if (!activeTab.value || !commonTypeList.value?.some((item) => item.ID === activeTab.value)) {
    activeTab.value = commonTypeList.value?.[0]?.ID;
  }
});

const searchParams = reactive<SearchParams>({
  platform: undefined,
  labels: [],
  search: '',
});
const { labels: searchLabels, search: searchKw } = toRefs(searchParams);
const { data: listRes, execute: fetchList, loading: loadingList } = useLatestPromise((search?: SearchParams['search'], projectIds?: SearchParams['labels']) => {
  return getToolkitListByPage({
    name: search,
    projectIDs: projectIds,
    page: 1,
    pageSize: 999,
  }, {});
});
const renderPackageVersionList = computed(() => {
  return (listRes.value?.data?.data?.list || [])
    .filter((e) => !activeTab.value || e.workTypeID === activeTab.value)
    .filter((e) => (!searchParams.platform || e.platforms?.includes(searchParams.platform)));
});
watch([searchKw, searchLabels], debounce(() => fetchList(searchKw?.value, searchLabels?.value), 300), { immediate: true });

const allProjectLabel = computed<ProjectListItem[]>(() => {
  return [
    { ID: 0, name: '通用' },
    ...projectInfo.value?.projectList || [],
  ];
});

function handleTabChange(ID?: number) {
  if (ID !== activeTab.value) {
    activeTab.value = ID;
  }
}

function goToSettingsPage() {
  router.push({
    name: PlatformEnterPoint.ToolkitPackageSettings,
  });
}
</script>

<style lang="less" scoped>
@import (reference) '@hg-tech/forgeon-style/vars.less';

.toolkit-list {
  display: flex;
  position: relative;
  overflow: auto;
  height: 100%;
  padding: 16px 0 16px 16px;

  &__left {
    display: flex;
    flex-direction: column;
    width: 220px;
    padding: 0 16px 16px 0;

    &-tab {
      position: relative;
      padding: 8px 12px;
      cursor: pointer;
      user-select: none;
      border-radius: 20px;
      margin-bottom: 8px;
      transition: background-color 0.3s;

      &--active,
      &:hover {
        background-color: @FO-Container-Fill1;
      }

      &-icon {
        position: absolute;
        top: 10px;
        left: 12px;
        width: 20px;
        border-radius: 6px;
      }

      &-title {
        width: 150px;
        margin-left: 26px;
        font-weight: bold;
      }
    }
  }

  &__right {
    flex: auto;
    overflow: auto;
    padding-right: 16px;
    display: flex;
    flex-direction: column;
    gap: 12px;

    &-filter {
      flex: none;
      padding: 16px;
      border-radius: 10px;
      background-color: @FO-Container-Fill1;

      &-btn {
        display: inline-flex;
        align-items: center;
        justify-content: center;
        padding: 2px 10px;
        border-radius: 16px;
        background-color: @FO-Container-Fill6;
        color: @FO-Content-Components1;
        border: 1px solid @FO-Container-Fill6;
        font-weight: bold;
        cursor: pointer;
        user-select: none;
        white-space: nowrap;
      }
    }

    &-content {
      flex: auto;
      border-radius: 10px;
      background-color: @FO-Container-Fill1;
    }
  }
}
</style>
