<template>
  <PageLayout>
    <template #headerTitle>
      <Breadcrumb class="FO-Font-B18" separator=">" :routes="routeNav">
        <BreadcrumbItem>Home</BreadcrumbItem>
        <template #itemRender="{ route: { path, breadcrumbName } }">
          <router-link v-if="path" :to="path" class="!bg-transparent !text-FO-Content-Text2">
            {{ breadcrumbName }}
          </router-link>
          <span v-else class="!text-FO-Content-Text1">
            {{ breadcrumbName }}
          </span>
        </template>
      </Breadcrumb>
    </template>
    <div class="h-full min-h-0 flex flex-1 flex-col gap-16px overflow-hidden p-20px">
      <AppToolbar
        class="flex-none" :appId="routerAppId" :appInfo="appInfo" :loading="appInfoLoading"
        @updated="refreshAppInfo"
      />
      <RoleList v-model:tenantId="tenantId" class="min-h-0 flex-1" :appInfo="appInfo" :tenantList="tenantList?.data?.data" />
    </div>
  </PageLayout>
</template>

<script setup lang="ts">
import { Breadcrumb, BreadcrumbItem } from 'ant-design-vue';
import { computed, watch } from 'vue';
import type { Route } from 'ant-design-vue/es/breadcrumb/Breadcrumb';
import { useRoute, useRouter } from 'vue-router';
import { PlatformEnterPoint } from '@hg-tech/oasis-common';
import { usePermissionAppInfo } from '../../composables/usePermissionAppInfo.ts';
import PageLayout from '../../components/PageLayout.vue';
import AppToolbar from './components/AppToolbar.vue';
import RoleList from './components/RoleList.vue';
import { useRouteQuery } from '@vueuse/router';
import { useForgeOnProjectInfo } from '../../composables/useForgeOnProjectInfo.ts';
import { useLatestPromise } from '@hg-tech/utils-vue';
import { getPermissionAppTenantList } from '../../api/app.ts';

const router = useRouter();
const route = useRoute();
const routerAppId = computed(() => Number(route.params.appId));
const routerTab = computed(() => route.query?.fromTab);

const { appInfo, refreshAppInfo, appInfoLoading } = usePermissionAppInfo(routerAppId);
const { currentProjectId } = useForgeOnProjectInfo();
const tenantId = useRouteQuery<number | undefined>('tenantId', undefined, { transform: (v) => (v != null ? Number(v) : undefined) });
const { data: tenantList, execute: fetchTenantList } = useLatestPromise(getPermissionAppTenantList);

watch(appInfo, (appInfo) => {
  if (appInfo?.isMultiTenant) {
    fetchTenantList({ appId: appInfo.id }, {});
  }
}, { immediate: true });

watch([
  () => appInfo?.value?.isMultiTenant,
  () => tenantList.value?.data?.data,
  currentProjectId,
], ([isMultiTenant, tList, cProjectId]) => {
  if (isMultiTenant && tList?.length) {
    if (cProjectId && !tenantId.value) {
      tenantId.value = tList?.find((i) => Number(i.originId) === Number(cProjectId))?.id;
    }

    if (!tList.some((i) => i.id === tenantId.value)) {
      tenantId.value = tList?.[0]?.id;
    }
  }
}, { immediate: true });

const routeNav = computed<Route[]>(() => {
  return [
    {
      path: router.resolve({ name: PlatformEnterPoint.PermissionCenterDashboard, query: { tab: routerTab.value } }).href,
      breadcrumbName: '权限管理中心',
    },
    {
      path: '',
      breadcrumbName: appInfo.value?.name || '-',
    },
  ];
});
</script>
