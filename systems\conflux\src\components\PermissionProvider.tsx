import type { PermissionDeclaration } from '@hg-tech/oasis-common';
import { message } from 'ant-design-vue';
import { usePermissionStore } from '../store/modules/permission';
import type { PropType } from 'vue';
import { computed, defineComponent, ref, watch } from 'vue';

const PermissionProvider = defineComponent({
  props: {
    permission: {
      type: Object as PropType<Omit<PermissionDeclaration, 'scope'>>,
      required: true,
    },
    mode: {
      type: String as PropType<'hide' | 'block'>,
      default: 'hide',
    },
    disabledChild: {
      type: Boolean as PropType<boolean>,
      default: true,
    },
    customCheck: {
      type: Function as PropType<() => boolean>,
    },
  },
  setup(props, { slots }) {
    const { checkPermission } = usePermissionStore();
    const hasPermission = ref(false);

    watch(() => props.permission, async (newPermission) => {
      if (newPermission) {
        try {
          hasPermission.value = props.customCheck
            ? (await checkPermission?.(newPermission)) || props.customCheck() || false
            : (await checkPermission?.(newPermission)) || false;
        } catch (error) {
          console.error('PermissionProvider checkPermission error:', error);
          hasPermission.value = false;
        }
      }
    }, { immediate: true, deep: true });

    // 控制是否渲染内容
    const shouldRender = computed(() => (props.mode === 'hide' ? hasPermission.value : true));

    // 如果是阻断模式，控制子组件的 disabled 属性
    const isDisabled = computed(() => props.mode === 'block' && props.disabledChild && !hasPermission.value);

    // 阻断点击事件并提示
    const handleClick = (event: MouseEvent) => {
      if (props.mode === 'block' && !hasPermission.value) {
        event.stopPropagation();
        event.preventDefault();
        message.warning('【Warn】当前操作没有权限，请联系管理员进行申请');
      }
    };

    return () => (
      <>
        {shouldRender.value
          ? (
            <div class="permission-provider inline-block cursor-pointer" onClick={handleClick}>
              <div
                style={{
                  'pointer-events':
                  props.mode === 'block' && !hasPermission.value
                    ? 'none'
                    : 'auto',
                  'opacity': isDisabled.value ? 0.5 : 1,
                }}
              >
                {slots.default?.({ disabled: isDisabled.value })}
              </div>
            </div>
          )
          : null}
      </>
    );
  },
});

export { PermissionProvider };
