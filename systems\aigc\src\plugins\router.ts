import { createRouter, createWebHistory } from 'vue-router';

import { usePermission, useUserBase } from '@/common/hooks';
import { Logger } from '@/common/utils/logger';
import {
  getRouterConfig,
  getTargetPage,
  PageName,
} from '@/configs/page-config';
import { findItemInMenuByPath, ModulesMenuConfig, PlatformEnterPoint, useMicroAppInject, usePlatformConfigCtx } from '@hg-tech/oasis-common';
import { traceRouteChange } from './track';
import { getItem, STORAGE_KEY } from '@/common/utils/localstorage';
import { nextTick } from 'vue';
import { showAIToolWarningModal, showAIWarningModal } from '@/common/components/warning-modal';
import { usePlatformConfig } from '@/common/hooks/platform-config.hook';
import { safeEncodeURL } from '@/common/utils/url/compressor';

const router = createRouter({
  history: createWebHistory(),
  routes: getRouterConfig(),
});

router.beforeEach(async (to, from) => {
  const { checkPermission } = usePermission();
  const { checkIsLogin } = useUserBase();
  const { isFullScreen } = usePlatformConfig();

  const targetPageName = to.name as string;
  const fromPageName = from.name as string;
  const pageConfig = getTargetPage(targetPageName);
  const isLogin = await checkIsLogin();
  isFullScreen.value = to.query.fullscreen === '1';

  Logger.debug('checkIsLogin:', isLogin);
  Logger.debug('pageConfig:', pageConfig);
  Logger.debug('from page:', fromPageName);
  Logger.debug('go to page:', targetPageName);

  // Auth.
  const isAuthNeeded = pageConfig?.needAuth;
  Logger.debug('Need auth:', isAuthNeeded);

  if (isAuthNeeded) {
    if (!isLogin) {
      return {
        name: PageName.Login,
        query: {
          redirect: to.name === PageName.Homepage ? '' : safeEncodeURL(to.fullPath),
        },
      };
    }
  }

  if (window.__MICRO_APP_ENVIRONMENT__ && !targetPageName) {
    if (to.path === '/aigc/login' || to.path === '/aigc' || to.path === '/aigc/') {
      return {
        name: PlatformEnterPoint.AIImagePage,
      };
    }
    return {
      name: PlatformEnterPoint.AINotFound,
    };
  }

  // Forbidden page.
  if (
    getTargetPage(targetPageName)?.permission
    && !checkPermission(
      getTargetPage(targetPageName)?.permission as string | string[],
      getTargetPage(targetPageName)?.permissionType,
    )
  ) {
    return {
      name: PlatformEnterPoint.AIForbidden,
      query: {
        redirect: safeEncodeURL(to.fullPath),
        target: targetPageName,
        fullscreen: to.query.fullscreen,
      },
    };
  }

  // Login page redirect.
  if (targetPageName === PageName.Login) {
    const isLogin = await checkIsLogin();
    if (isLogin) {
      return {
        name: PageName.Homepage,
      };
    }
  }

  // 404 to index.
  if (!pageConfig || !targetPageName) {
    return {
      name: PlatformEnterPoint.AINotFound,
    };
  }

  return true;
});

router.afterEach((to, from) => {
  if (window.__MICRO_APP_ENVIRONMENT__) {
    const { data } = useMicroAppInject(usePlatformConfigCtx);
    data.value?.setDocumentTitle(to.meta.title as string);
  } else {
    to.meta.title && (document.title = to.meta.title as string);
  }
  if (to.fullPath !== from.fullPath) {
    traceRouteChange(to, from);
  }
});

router.afterEach(async (to) => {
  const { path } = findItemInMenuByPath(ModulesMenuConfig, to.path);
  const secendMenu = path[1] ?? '';
  if (!secendMenu) {
    return;
  }

  // LLM Warning Modal.
  if ([PlatformEnterPoint.LLM].includes(secendMenu as PlatformEnterPoint)
    && getItem(STORAGE_KEY.LLM_WARNING_MODEL_SHOW) !== '1') {
    await nextTick();
    showAIWarningModal();
  // ai tool Warning Modal.
  } else if ([
    PlatformEnterPoint.AIImage,
    PlatformEnterPoint.Voice,
  ].includes(secendMenu as PlatformEnterPoint)
  && getItem(STORAGE_KEY.AI_TOOL_WARNING_MODEL_SHOW) !== '1') {
    await nextTick();
    showAIToolWarningModal();
  }
});

export { router };
