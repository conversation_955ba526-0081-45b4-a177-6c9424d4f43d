import type { Ref } from 'vue';
import { ref, watch } from 'vue';
import type { ICommonMenuItem, PlatformEnterPoint } from '../../configs';
import { findInTrees } from '@hg-tech/utils';
import { uniq } from 'lodash';

export function useForgeOnActiveMenu(allMenus: Ref<ICommonMenuItem[]>, curRoutePath: Ref<string>) {
  const activeMenu = ref<PlatformEnterPoint>();
  const openKeys = ref<PlatformEnterPoint[]>([]);

  watch([allMenus, curRoutePath], ([menus, routePath]) => {
    const { path, target } = findInTrees(menus, (item) => item.path != null && routePath.startsWith(item.path));
    activeMenu.value = target?.key;
    openKeys.value = uniq([...openKeys.value, ...path]);
  }, { immediate: true });

  return {
    activeMenu,
    openKeys,
  };
}
