<template>
  <Modal
    :width="600"
    :open="show"
    :maskClosable="false"
    destroyOnClose
    centered
    :afterClose="modalDestroy"
    @cancel="() => modalCancel()"
  >
    <template #title>
      <div class="mb text-center">
        <span class="FO-Font-B16">
          <span>{{ title }}</span>
        </span>
      </div>
    </template>
    <BasicForm @register="registerForm" />
    <template #footer>
      <div class="mt flex justify-end">
        <a-button @click="modalDestroy()">
          取消
        </a-button>
        <a-button type="primary" class="ml-2" @click="handleConfirm">
          确定
        </a-button>
      </div>
    </template>
  </Modal>
</template>

<script lang="ts" setup>
import { Modal } from 'ant-design-vue';
import type { ModalBaseProps } from '@hg-tech/utils-vue';
import type { NullableBasicResult } from '/@/api/model/baseModel';
import { formSchema } from './navigation.data';
import { BasicForm, useForm } from '/@/components/Form';
import type { NavigationsListItem } from '/@/api/page/model/navigtionModel';
import { onMounted } from 'vue';
import NavigationDefaultImg from '/resource/img/navigation-default.png';
import { isFeishuUrlReg, isUrl, isUrlReg } from '/@/utils/is';
import { getMainDomainName } from '/@/utils/http/axios/helper';

const props = defineProps< ModalBaseProps<{ updatedItem?: NullableBasicResult }> & {
  record: NavigationsListItem;
  sentReq?: (formValue: NavigationsListItem) => Promise<NullableBasicResult | undefined>;
  title?: string;
  type?: 'navigation' | 'groupChat';
}>();

const [registerForm, { resetFields, setFieldsValue, updateSchema, validate }] = useForm({
  labelWidth: 120,
  schemas: formSchema,
  showActionButtonGroup: false,
  baseColProps: {
    span: 20,
  },
});

onMounted(async () => {
  await resetFields();
  await updateSchema({
    field: 'url',
    rules: props.type === 'navigation'
      ? [
        {
          required: true,
          message: '请输入正确的网址, 需以http(s)开头',
          pattern: isUrlReg,
        },
      ]
      : [{
        required: true,
        message: '请输入正确的飞书项目群聊链接',
        pattern: isFeishuUrlReg,
      }],
    componentProps: props.type === 'navigation'
      ? ({ formActionType }) => {
        return {
          placeholder: '请输入网址',
          onChange: async (e) => {
            const val = e?.target?.value;
            if (isUrl(val)) {
              const { setFieldsValue } = formActionType;
              await setFieldsValue({
                title: getMainDomainName(val),
              });
            }
          },
        };
      }
      : {
        placeholder: '请输入飞书项目群聊链接',
      },
  });
  await setFieldsValue({
    ...props.record,
    icon: props.type === 'navigation' ? props.record.icon || NavigationDefaultImg : props.record.icon,
  });
});

async function handleConfirm() {
  const values = await validate();
  const updatedItem = await props.sentReq?.(values);
  return props.modalConfirm({ updatedItem });
}
</script>
