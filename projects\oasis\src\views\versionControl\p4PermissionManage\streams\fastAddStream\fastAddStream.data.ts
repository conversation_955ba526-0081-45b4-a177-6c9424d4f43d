import type { FormSchema } from '/@/components/Form';
import { streamTypeOptions } from '../streams.data';
import { useMessage } from '/@/hooks/web/useMessage';
import CloneCompleteNotice from './CloneCompleteNotice.vue';
import ClonePermission from './ClonePermission.vue';
import CloneSubmitCheck from './CloneSubmitCheck.vue';
import CloneSwarm from './CloneSwarm.vue';
import CreateBranch from './CreateBranch.vue';
import DM01CreateBranch from './DM01CreateBranch.vue';
import Finish from './Finish.vue';

export const stepTitleList = [
  '新建分支',
  '克隆提交检查',
  '克隆权限',
  '克隆提交完成通知',
  '克隆审查',
  '完成',
];

export const stepComponentList = [
  CreateBranch,
  CloneSubmitCheck,
  ClonePermission,
  CloneCompleteNotice,
  CloneSwarm,
  Finish,
];

export const DM01StepTitleList = ['新建分支', '完成'];

export const DM01StepComponentList = [DM01CreateBranch, Finish];

export const autoMergeRuleOptions = [
  {
    label: 'Safe Resolve(No Merge)',
    description: '提交分支和目标分支合并的文件出现冲突时，直接放弃解决并发送失败通知。',
    value: 'as',
  },
  {
    label: 'Safe Resolve (Allow Merge)',
    description:
      '提交分支和目标分支合并的文件出现冲突时, 如果两个文件修改的地方不同，接受合并的结果，若合并失败则发送通知。',
    value: 'am',
  },
  {
    label: 'Accept Source',
    description: '提交分支和目标分支合并的文件出现冲突时，由提交分支的文件直接覆盖。',
    value: 'at',
  },
  {
    label: 'Accept Target',
    description: '提交分支和目标分支合并的文件出现冲突时，放弃合并且保留目标分支的修改。',
    value: 'ay',
  },
];

export const createBranchFormSchema: FormSchema[] = [
  {
    label: '分支名称',
    field: 'description',
    component: 'Input',
    componentProps: {
      placeholder: '请输入分支名称',
      maxLength: 100,
    },
    required: true,
  },
  {
    field: 'name',
    label: '分支路径名',
    component: 'Input',
    componentProps: {
      placeholder: '请输入分支路径名',
    },
    rules: [
      {
        required: true,
        message: '请输入分支路径名',
      },
      {
        pattern: /^[\w.-]+$/,
        message: '根目录名只能包含字母、数字、下划线、中划线、小数点',
      },
    ],
  },
  {
    field: 'streamType',
    label: '分支类型',
    required: true,
    component: 'Select',
    defaultValue: 2,
    componentProps: {
      placeholder: '请选择分支类型',
      options: streamTypeOptions,
      getPopupContainer: () => document.body,
    },
  },
  {
    field: 'rootDirectory',
    label: '根目录名',
    component: 'Input',
    componentProps: {
      placeholder: '请输入根目录名',
    },
    rules: [
      {
        required: true,
        message: '请输入根目录名',
      },
      {
        pattern: /^[\w.-]+$/,
        message: '根目录名只能包含字母、数字、下划线、中划线、小数点',
      },
    ],
  },
  {
    field: 'workspace',
    label: '工作空间',
    component: 'Input',
    required: true,
    componentProps: {
      placeholder: '请输入工作空间',
    },
  },
  {
    field: 'parent',
    label: '父分支',
    component: 'Select',
    componentProps: {
      placeholder: '请输入父分支',
      fieldNames: {
        label: 'description',
        value: 'path',
      },
      getPopupContainer: () => document.body,
    },
    required: true,
  },
  {
    field: 'path',
    label: '分支路径',
    component: 'Input',
    componentProps: {
      placeholder: '分支路径 将根据 分支路径名和类型 自动生成',
    },
  },
];

export const DM01CreateBranchFormSchema: FormSchema[] = [
  {
    field: 'category',
    label: '分支类别',
    required: true,
    component: 'RadioButtonGroup',
    componentProps: {
      buttonStyle: 'outline',
    },
    defaultValue: 2,
  },
  {
    field: 'versionID',
    label: '版本号',
    ifShow: ({ values }) => {
      return values.category !== 1;
    },
    component: 'Select',
    slot: 'versionID',
    colProps: {
      span: 10,
    },
    required: true,
  },
  {
    field: 'bigOne',
    label: '',
    ifShow: ({ values }) => {
      return values.category === 3;
    },
    component: 'Checkbox',
    colProps: { span: 5 },
    defaultValue: false,
    renderComponentContent: '跨版本的分支',
  },
  {
    field: 'streamType',
    label: '分支属性',
    required: true,
    component: 'RadioButtonGroup',
    componentProps: {
      buttonStyle: 'outline',
    },
    ifShow: ({ values }) => {
      return values.category === 2;
    },
    defaultValue: 2,
  },
  {
    label: '分支名称',
    field: 'description',
    component: 'Input',
    componentProps: {
      placeholder: '请输入分支名称',
      maxLength: 100,
    },
    required: true,
  },
  {
    field: 'name',
    label: '分支路径名',
    component: 'Input',
    componentProps: {
      placeholder: '请输入分支路径名',
    },
    rules: [
      {
        required: true,
        message: '请输入分支路径名',
      },
      {
        pattern: /^[\w.-]+$/,
        message: '分支路径名只能包含字母、数字、下划线、中划线、小数点',
      },
    ],
  },
  {
    field: 'parent',
    label: '父分支',
    component: 'Input',
    dynamicDisabled: ({ values }) => {
      return values.streamType !== 3;
    },
    componentProps: {
      placeholder: '请输入父分支',
      fieldNames: {
        label: 'path',
        value: 'path',
      },
      getPopupContainer: () => document.body,
    },
    required: true,
  },

  {
    field: 'path',
    label: '分支路径',
    component: 'Input',
    componentProps: {
      placeholder: '分支路径 将根据 分支路径名,类别,属性和版本 自动生成',
    },
  },
];

export const DM01AddBranchFormSchema: FormSchema[] = [
  {
    field: 'path',
    label: '分支路径',
    component: 'Input',
    componentProps: {
      placeholder: '请输入分支路径',
    },
    required: true,
    colProps: {
      span: 19,
    },
  },
  {
    field: 'pathValidate',
    label: '',
    component: 'Input',
    slot: 'pathValidate',
    colProps: {
      span: 4,
      offset: 1,
    },
  },
  {
    label: '分支名称',
    field: 'description',
    component: 'Input',
    componentProps: {
      placeholder: '请输入分支名称',
      maxLength: 100,
    },
    required: true,
  },
  {
    field: 'category',
    label: '分支类别',
    component: 'RadioButtonGroup',
    componentProps: {
      buttonStyle: 'outline',
    },
    required: true,
  },

  {
    field: 'streamType',
    label: '分支属性',
    component: 'RadioButtonGroup',
    componentProps: {
      buttonStyle: 'outline',
    },
    ifShow: ({ values }) => {
      return values.category === 2;
    },
    required: true,
  },
];

export const DM01EditBranchFormSchema: FormSchema[] = [
  {
    label: '分支名称',
    field: 'description',
    component: 'Input',
    componentProps: {
      placeholder: '请输入分支名称',
      maxLength: 100,
    },
    required: true,
  },
  {
    field: 'path',
    label: '分支路径',
    component: 'Input',
    componentProps: {
      placeholder: '请输入分支路径',
      disabled: true,
    },
    required: true,
  },
  {
    field: 'category',
    label: '分支类别',
    component: 'RadioButtonGroup',
    componentProps: {
      buttonStyle: 'outline',
      disabled: true,
    },
    required: true,
  },

  {
    field: 'streamType',
    label: '分支属性',
    component: 'RadioButtonGroup',
    componentProps: {
      buttonStyle: 'outline',
    },
    ifShow: ({ values }) => {
      return values.category === 2;
    },
    required: true,
  },
];

export const configAutoMergeFormSchema: FormSchema[] = [
  {
    field: 'toStreamID',
    label: '合并到',
    component: 'Select',
    slot: 'toStreamID',
    rules: [{ required: true, message: '请选择合入分支', type: 'number' }],
  },
  {
    field: 'tagID',
    label: 'Tag',
    component: 'Select',
    slot: 'tagID',
    required: true,
  },
  {
    field: 'webhook',
    label: 'Webhook',
    component: 'Input',
    helpMessage: '用以发送swarm状态至飞书群',
    componentProps: ({ formModel, formActionType }) => ({
      placeholder: '请输入飞书机器人 Webhook, 如：abc-123-456-789-xyz',
      onchange: (event: MouseEvent) => {
        const val = (event.target as HTMLInputElement).value;

        if (val.includes('/')) {
          const { createMessage } = useMessage();

          formModel.webhook = val.split('/').pop();
          createMessage.info('webhook不需要包含 / 前的内容，已自动截取');
          formActionType.clearValidate('webhook');
        }
      },
    }),
    rules: [
      {
        required: true,
        message: '请输入Webhook，如：abc-123-456-789-xyz',
      },
      {
        pattern: /^[a-z0-9-]+$/i,
        message: 'Webhook格式不正确，只能包含字母、数字、-',
      },
    ],
  },
  {
    field: 'rule',
    label: '冲突处理',
    component: 'Select',
    slot: 'rule',
    required: true,
  },
];

export const cloneCompleteNoticeFormSchema: FormSchema[] = [
  {
    field: 'copyFromStreamID',
    label: '',
    component: 'Select',
    componentProps: {
      fieldNames: {
        label: 'description',
        value: 'ID',
      },
      showSearch: true,
      optionFilterProp: 'description',
      placeholder: '请选择源分支',
      allowClear: true,
      getPopupContainer: () => document.body,
    },
    required: true,
  },
];
