<template>
  <BasicModal
    @register="registerModal"
    :title="getTitle"
    @ok="handleSubmit"
    :width="600"
    :wrapClassName="prefixCls"
  >
    <BasicForm @register="registerForm" />
  </BasicModal>
</template>
<script lang="ts">
  import { omit } from 'lodash-es';
  import { computed, defineComponent, ref, unref } from 'vue';
  import { formSchema } from './param.data';
  import { addJobParam, editJobParam } from '/@/api/page/jenkins';
  import { BasicForm, useForm } from '/@/components/Form';
  import { BasicModal, useModalInner } from '/@/components/Modal';
  import { useDesign } from '/@/hooks/web/useDesign';
  import { useUserStoreWithOut } from '/@/store/modules/user';
  import NavigationDefaultImg from '/resource/img/navigation-default.png';

  export default defineComponent({
    name: 'JenkinsAutoTaskParamModal',
    components: { BasicModal, BasicForm },
    emits: ['success', 'register'],
    setup(_, { emit }) {
      const { prefixCls } = useDesign('jenkins-auto-task-param-modal');

      const isUpdate = ref(false);
      const editId = ref();
      const userStore = useUserStoreWithOut();
      const jobID = ref();

      const [registerForm, { resetFields, setFieldsValue, validate, updateSchema }] = useForm({
        labelWidth: 120,
        schemas: formSchema,
        showActionButtonGroup: false,
        baseColProps: {
          span: 22,
        },
      });

      const [registerModal, { setModalProps, closeModal }] = useModalInner(async (data) => {
        await resetFields();
        isUpdate.value = !!data?.isUpdate;
        editId.value = data?.record?.ID;
        jobID.value = data?.jobID;

        if (unref(isUpdate)) {
          const { type, defaults } = data.record;
          await updateSchema({
            field: 'defaults',
            component: type === 1 ? 'Input' : type === 2 ? 'RadioGroup' : 'CheckboxGroup',
          });
          // 重置默认值, 防止vue warn
          await setFieldsValue({
            defaults: undefined,
          });
          await setFieldsValue({
            ...omit(data.record, 'defaults'),
            defaults: type === 3 ? defaults : defaults?.[0],
          });
        } else {
          await setFieldsValue({
            icon: NavigationDefaultImg,
          });
        }
        setModalProps({ confirmLoading: false });
      });

      const getTitle = computed(() => (!unref(isUpdate) ? '添加' : '编辑') + '参数');

      const getFirstData = (data) => {
        if (Array.isArray(data)) {
          return data[0];
        }
        return data || '';
      };
      async function handleSubmit() {
        try {
          const values = await validate();
          const submitData = {
            ...values,
            jobID: unref(jobID),
            defaults: values.type === 3 ? values.defaults : [getFirstData(values.defaults)],
          };
          setModalProps({ confirmLoading: true });
          if (!unref(isUpdate)) {
            await addJobParam(userStore.getProjectId, submitData);
            emit('success', 'add');
          } else if (unref(editId)) {
            await editJobParam(userStore.getProjectId, submitData, unref(editId));
            emit('success', 'edit');
          }
          closeModal();
          await resetFields();
        } finally {
          setModalProps({ confirmLoading: false });
        }
      }

      return {
        prefixCls,
        registerModal,
        registerForm,
        getTitle,
        handleSubmit,
        isUpdate,
      };
    },
  });
</script>
<style lang="less">
@prefix-cls: ~'hypergryph-jenkins-auto-task-param-modal';
  .@{prefix-cls} {
    & .ant-checkbox-group,
    & .ant-radio-group:not(.ant-radio-group-solid) {
      width: 100%;
      min-height: 32px;
      padding: 4px 8px;
      border: 1px solid @FO-Container-Stroke1;

      & .ant-checkbox-wrapper-checked,
      & .ant-radio-wrapper-checked {
        color: @FO-Brand-Primary-Default;
        font-weight: bold;
      }
    }
  }
</style>
