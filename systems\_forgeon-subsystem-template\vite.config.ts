import { defineViteConfigProjectVueSys } from '@hg-tech/configs';
import UnoCSS from 'unocss/vite';
import process from 'node:process';

// https://vitejs.dev/config/
export default defineViteConfigProjectVueSys(() => {
  return {
    plugins: [
      UnoCSS(),
    ],
    build: {
      rollupOptions: {
        // TODO ---- 配置 external 仅为了加速模板构建测试，实际生成请移除 ----
        external: [
          /@hg-tech\/.*$/,
          /node_modules\/.*$/,
        ],
        // TODO ---- 移除以上配置 ----
      },
    },
    server: {
      host: true,
      port: 1024 + Math.random() * 8192, // TODO 替换为项目内没有的固定端口
      proxy: {
        // TODO 根据需要修改或删除
        '/api': {
          ws: true,
          secure: false,
          changeOrigin: true,
          target: 'be.hypergryph.net/api',
        },
      },
    },
  };
}, {
  analyze: process.argv.includes('--analyze'),
});
