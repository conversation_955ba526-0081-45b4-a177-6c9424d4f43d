<template>
  <div class="FO-Font-B14 flex items-center gap-4px c-FO-Content-Text3">
    <div class="flex items-center gap-2px">
      <span>{{ memberCount.users }}</span>
      <span>人</span>
    </div>
    <div class="h-12px w-1px bg-FO-Container-Stroke1" />
    <div class="flex items-center gap-2px">
      <span>{{ memberCount.departments }}</span>
      <span>部门</span>
    </div>
    <div class="h-12px w-1px bg-FO-Container-Stroke1" />
    <div class="flex items-center gap-2px">
      <span>{{ memberCount.groups }}</span>
      <span>组</span>
    </div>
  </div>
</template>

<script setup lang="ts">
interface MemberCount {
  users: number;
  departments: number;
  groups: number;
}

defineProps<{
  memberCount: MemberCount;
}>();
</script>
