import { Button, Collapse, Form, FormItem, Input, Select } from 'ant-design-vue';
import { type MergeBasicRuleForm, MergeRuleFormPanelKey, TriggerTypeOptions } from '../../../models/config.model';
import { type PropType, type Ref, computed, defineComponent, ref } from 'vue';
import type { Rule } from 'ant-design-vue/es/form';
import Icon from '@ant-design/icons-vue';
import { type RuleV1Depot, RuleV1MergeTrigger, RuleV1StreamType } from '@hg-tech/api-schema-merge';
import { useMergeHome } from '../use-merge-home';
import { renderBranchTypeIcon } from '../../../models/config.model';
import { toHighlightSegments } from '@hg-tech/utils';

import ForwardArrow from '../../../assets/svg/ForwardArrow.svg?component';
import SystemFillCodebase from '../../../assets/svg/SystemFillCodebase.svg?component';

const BasicForm = defineComponent({
  props: {
    form: {
      type: Object as PropType<MergeBasicRuleForm>,
      default: () => ({}),
    },
    rules: {
      type: Object as PropType<Record<string, Rule[]>>,
      default: () => ({}),
    },
    activeKey: {
      type: Array as PropType<string[]>,
      default: () => [],
    },
    formRef: {
      type: Object as PropType<Ref<any>>,
      default: () => ({}),
    },
    isEdit: {
      type: Boolean as PropType<boolean>,
      default: false,
    },
  },
  emits: ['update:activeKey', 'update:form'],
  setup(props, { emit }) {
    const { streamList } = useMergeHome();
    const basicForm = computed({
      get: () => props.form,
      set: (value) => {
        emit('update:form', value);
      },
    });
    const activeKey = computed({
      get: () => props.activeKey,
      set: (value) => {
        emit('update:activeKey', value);
      },
    });
    const searchOptions = ref<RuleV1Depot[]>(streamList.value);
    const searchValue = ref<string>('');

    const onStreamReset = () => {
      searchOptions.value = streamList.value;
    };

    const onStreamSearch = (value: string) => {
      searchValue.value = value;
      if (!value) {
        onStreamReset();
        return;
      }
      const filtered: RuleV1Depot[] = [];
      for (const depot of streamList.value) {
        const matchedStreams = depot.streams?.filter(
          (stream) => stream.name?.includes(value) || stream.path?.includes(value),
        );
        if (matchedStreams && matchedStreams.length > 0) {
          filtered.push({
            ...depot,
            streams: matchedStreams,
          });
        }
      }
      searchOptions.value = filtered;
    };

    return () => (
      <Form layout="vertical" model={props.form} ref={props.formRef} rules={props.rules}>
        <Collapse
          class="b-none"
          collapsible="icon"
          expandIcon={({ isActive }) => (
            <Button
              class="btn-fill-default flex items-center justify-center"
              icon={(
                <Icon class="font-size-18px" component={<ForwardArrow class={isActive ? 'rotate-90' : ''} />} />
              )}
              type="text"
            />
          )}
          expandIconPosition="end"
          ghost
          v-model:activeKey={activeKey.value}
        >
          <Collapse.Panel
            header={<div class="FO-Font-B16">基础配置</div>}
            key={MergeRuleFormPanelKey.Default}
          >
            <FormItem label={<span class="FO-Font-B14">合并源分支</span>} name="sourceStreamId" required>
              <Select
                allowClear
                disabled={props.isEdit}
                filterOption={false}
                onBlur={onStreamReset}
                onSearch={onStreamSearch}
                placeholder="请选择分支"
                showSearch
                v-model:value={basicForm.value.sourceStreamId}
              >
                {
                  searchOptions.value.map((dep) => (
                    <Select.OptGroup key={dep.name}>
                      {{
                        label: () => (
                          <div class="FO-Font-B14 flex items-center gap-4px c-FO-Content-Text1">
                            <Icon class="font-size-16px c-FO-Content-Icon2" component={<SystemFillCodebase />} />
                            {dep.name}
                          </div>
                        ),
                        default: () => (
                          <>
                            {dep.streams?.map((item) => (
                              <Select.Option key={item.id} label={item.name} value={item.id}>
                                <div class="h-full flex items-center gap-4px">
                                  {renderBranchTypeIcon[item?.streamType ?? RuleV1StreamType.DEVELOPMENT]()}
                                  <span class={['FO-Font-R14', props.isEdit ? 'c-FO-Content-Text3' : 'c-FO-Content-Text1']}>
                                    {item.name
                                      ? searchValue.value
                                        ? toHighlightSegments(item.name, searchValue.value).map((seg) => (
                                          <span class={seg.highlight ? 'c-FO-Brand-Primary-Default' : ''}>{seg.text}</span>
                                        ))
                                        : item.name
                                      : '--'}
                                  </span>
                                  <span class={['FO-Font-R14 ml-4px', props.isEdit ? 'c-FO-Content-Text4' : 'c-FO-Content-Text2']}>(
                                    {item.path
                                      ? searchValue.value
                                        ? toHighlightSegments(item.path, searchValue.value).map((seg) => (
                                          <span class={seg.highlight ? 'c-FO-Brand-Primary-Default' : ''}>{seg.text}</span>
                                        ))
                                        : item.path
                                      : '--'})
                                  </span>
                                </div>
                              </Select.Option>
                            ))}
                          </>
                        ),
                      }}
                    </Select.OptGroup>
                  ))
                }
              </Select>
            </FormItem>
            <FormItem label={<span class="FO-Font-B14">合并目标分支</span>} name="targetStreamId" required>
              <Select
                allowClear
                disabled={props.isEdit}
                filterOption={false}
                onBlur={onStreamReset}
                onSearch={onStreamSearch}
                placeholder="请选择分支"
                showSearch
                v-model:searchValue={searchValue.value}
                v-model:value={basicForm.value.targetStreamId}
              >
                {
                  searchOptions.value.map((dep) => (
                    <Select.OptGroup key={dep.name}>
                      {{
                        label: () => (
                          <div class="FO-Font-B14 flex items-center gap-4px c-FO-Content-Text1">
                            <Icon class="font-size-16px c-FO-Content-Icon2" component={<SystemFillCodebase />} />
                            {dep.name}
                          </div>
                        ),
                        default: () => (
                          <>{dep.streams?.map((item) => (
                            <Select.Option key={item.id} label={item.name} value={item.id}>
                              <div class="h-full flex items-center gap-4px">
                                {renderBranchTypeIcon[item?.streamType ?? RuleV1StreamType.DEVELOPMENT]()}
                                <span class={['FO-Font-R14', props.isEdit ? 'c-FO-Content-Text3' : 'c-FO-Content-Text1']}>
                                  {item.name
                                    ? searchValue.value
                                      ? toHighlightSegments(item.name, searchValue.value).map((seg) => (
                                        <span class={seg.highlight ? 'c-FO-Brand-Primary-Default' : ''}>{seg.text}</span>
                                      ))
                                      : item.name
                                    : '--'}
                                </span>
                                <span class={['FO-Font-R14 ml-4px', props.isEdit ? 'c-FO-Content-Text4' : 'c-FO-Content-Text2']}>(
                                  {item.path
                                    ? searchValue.value
                                      ? toHighlightSegments(item.path, searchValue.value).map((seg) => (
                                        <span class={seg.highlight ? 'c-FO-Brand-Primary-Default' : ''}>{seg.text}</span>
                                      ))
                                      : item.path
                                    : '--'})
                                </span>
                              </div>
                            </Select.Option>
                          ))}
                          </>
                        ),

                      }}

                    </Select.OptGroup>
                  ))
                }
              </Select>
            </FormItem>
            <FormItem label={<span class="FO-Font-B14">合并触发方式</span>} name="mergeTrigger" required>
              <Select
                allowClear
                placeholder="请选择触发方式"
                v-model:value={basicForm.value.mergeTrigger}
              >
                {TriggerTypeOptions.map((item) => (
                  <Select.Option key={item.value} value={item.value}>
                    <div class="h-full flex items-center gap-4px">
                      <span class="FO-Font-R14 c-FO-Content-Text1">{item.label}</span>
                      <span class="FO-Font-R14 ml-4px c-FO-Content-Text2">{item.desc}</span>
                    </div>
                  </Select.Option>
                ))}
              </Select>
            </FormItem>
            {
              basicForm.value.mergeTrigger === RuleV1MergeTrigger.TAG && (
                <FormItem label={<span class="FO-Font-B14">触发标签（只需要填写[]内的文字）</span>} name="triggerTag">
                  <Input
                    class="w-full"
                    placeholder="请输入触发标签"
                    v-model:value={basicForm.value.triggerTag}
                  />
                </FormItem>
              )
            }
          </Collapse.Panel>
        </Collapse>
      </Form>
    );
  },
});

export {
  BasicForm,
};
