import type { ImageRenderToolbarProps, UploadCustomRequestOptions, UploadFileInfo } from 'naive-ui';
import type {
  CSSProperties,
  PropType,
} from 'vue';
import {
  NButton,
  NEllipsis,
  NIcon,
  NImage,
  NText,
  NUpload,
  NUploadTrigger,
  useMessage,
  useThemeVars,
} from 'naive-ui';
import { happy, shock } from 'random-jpn-emoji';
import { v4 as uuidv4 } from 'uuid';
import {
  defineComponent,
  ref,
  Transition,
  watch,
} from 'vue';

import {
  ArchiveOutline,
  AudioFileOutlined,
  Edit20Regular,
  EyeOutline,
  FileCsv,
  FileZip,
  Loading3QuartersOutlined,
  TrashBinOutline,
  UploadFileOutlined,
} from '../svg-icons';

import { uploadImageByForm, UploadScene } from '@/apis/oss.api';
import { useApiRequest } from '@/common/hooks';
import {
  compressImage,
  file2BlobUrlPromise,
  renderToolbar,
  urlToFile,
} from '@/common/utils/utils';
import CustomError from '@/common/utils/custom-error';
import { getFileNameAndExtension } from '@/common/utils/file';

type ToolType = 'preview' | 'edit' | 'delete';

interface FileUploaderExpose {
  clearFile: () => void;
}

const FileUploader = defineComponent({
  props: {
    fileList: {
      type: Array as PropType<UploadFileInfo[]>,
    },
    maxSize: {
      type: Number as PropType<number>,
      default: 40,
    },
    containerStyle: {
      type: Object as PropType<CSSProperties>,
      default: () => ({}),
    },
    showTools: {
      type: Array as PropType<ToolType[]>,
      default: () => ['preview', 'delete'],
    },
    toolIconSize: {
      type: Number as PropType<number>,
      default: 20,
    },
    disabled: {
      type: Boolean as PropType<boolean>,
      default: false,
    },
    toolDisabled: {
      type: Boolean as PropType<boolean>,
      default: false,
    },
    mode: {
      type: String as PropType<'card' | 'slot' | 'file'>,
      default: 'card',
    },
    fileName: {
      type: String as PropType<string>,
      default: 'Upload',
    },
    useOriginName: {
      type: Boolean as PropType<boolean>,
      default: false,
    },
    /**
     * 上传类型
     * oss: 上传到oss
     * local: 上传到本地
     */
    uploadType: {
      type: String as PropType<'oss' | 'local'>,
      default: 'local',
    },
    /**
     * oss场景
     * oss需指定server对应的场景
     */
    ossScene: {
      type: String as PropType<UploadScene>,
      default: UploadScene.Chat,
    },
    accept: {
      type: String as PropType<string>,
      default: 'image/*',
    },
    placeholder: {
      type: String as PropType<string>,
      default: '点击或者拖动文件到该区域',
    },
    compressDisabled: {
      type: Boolean as PropType<boolean>,
      default: false,
    },
    beforeCheck: {
      type: Function as PropType<
        (data: {
          file: UploadFileInfo;
          fileList: UploadFileInfo[];
        }) => boolean | Promise<boolean>
      >,
    },
    customPreview: {
      type: Function as PropType<(files: UploadFileInfo[]) => void>,
    },
  },
  emits: [
    'update:fileList',
    'input',
    'change',
    'fileUploadDone',
    'beforeUpload',
    'edit',
    'fileClean',
  ],
  setup(props, { emit, expose, slots }) {
    const message = useMessage();
    const imageRef = ref<typeof NImage>();
    const uploadRef = ref<typeof NUpload>();
    const isMoveIn = ref<boolean>(false);
    const isDragMoveOver = ref<boolean>(false);
    const themeVars = useThemeVars();
    const fileList = ref<UploadFileInfo[]>(props.fileList ?? []);
    const loading = ref<boolean>(false);
    const tempFileName = ref<string>('');

    const {
      code,
      error,
      mutate: mutateUpload,
    } = useApiRequest({
      request: uploadImageByForm,
      errorText: '文件上传失败',
    });

    watch(
      () => props.fileList,
      (newVal) => {
        if (newVal && newVal.length <= 0) {
          uploadRef.value?.clear();
        }
      },
    );

    const getFileTypeIcon = (type: string) => {
      switch (type) {
        case 'image/png':
        case 'image/jpeg':
        case 'image/jpg':
        case 'image/webp':
          return <UploadFileOutlined />;
        case 'audio/wav':
          return <AudioFileOutlined />;
        case 'text/csv':
          return <FileCsv />;
        case 'application/zip':
        case 'application/x-zip-compressed':
        case 'application/x-zip':
          return <FileZip />;
        default:
          return <UploadFileOutlined />;
      }
    };

    const onUpdateFile = (files: UploadFileInfo[]) => {
      fileList.value = files;
      emit('update:fileList', files);
      emit('input', files);
      if (fileList.value.length > 0) {
        emit('fileUploadDone', files);
      }
      setTimeout(() => {
        emit('change', fileList.value);
      }, 100);
    };

    const onFileDragover = async (e: DragEvent) => {
      e.preventDefault();
      isDragMoveOver.value = true;
    };

    /** 文件上传前类型/大小check */
    const beforeUploadCheck = async (data: {
      file: UploadFileInfo;
      fileList: UploadFileInfo[];
    }) => {
      // 图片处理
      if (props.accept === 'image/*') {
        if (
          !['image/jpeg', 'image/jpg', 'image/png', 'image/webp'].includes(
            data.file.file?.type ?? 'unknown',
          )
          && props.accept === 'image/*'
        ) {
          message.warning(
            `当前只能上传jpg、jpeg、webp图片文件，请重新上传: ${shock()}`,
          );
          return false;
        }
      }

      // 音频处理
      if (
        !['audio/wav'].includes(data.file.file?.type ?? 'unknown')
        && props.accept === 'audio/*'
      ) {
        message.warning(`当前只能上传wav音频文件，请重新上传: ${shock()}`);
        return false;
      }

      if (props.beforeCheck && !(await props.beforeCheck(data))) {
        return false;
      }

      // 文件大小处理
      if (
        data.file.file?.size
        && data.file.file?.size > props.maxSize * 1024 * 1024
      ) {
        message.warning(
          `上传文件已经超过${props.maxSize}MB了，请压缩下吧: ${shock()}`,
        );
        return false;
      }

      emit('beforeUpload', data.fileList);
      loading.value = true;
      /*
       * const { data: resData, error } = await getFileUploadUrl(
       *   `${props.fileName}-${tempUid.value}`,
       *   data.file.file?.type ?? 'unknown',
       * );
       * if (error) {
       *   loading.value = false;
       *   return false;
       * }
       * action.value = resData?.url ?? '';
       */
      return true;
    };

    const handleCustomRequest = async ({
      file: files,
    }: UploadCustomRequestOptions) => {
      const { fileName, extension } = getFileNameAndExtension(files.file?.name ?? '');
      tempFileName.value = `${props.useOriginName ? fileName : props.fileName}-${uuidv4()}.${extension}`;
      loading.value = true;
      const targetFile = props.compressDisabled
        ? files.file!
        : await compressImage(files.file!);

      // 上传到oss
      if (props.uploadType === 'oss') {
        const form = new FormData();
        form.append('file', targetFile, tempFileName.value);
        await mutateUpload({
          form,
          scene: props.ossScene,
        });
        if (code.value !== 0 || error.value) {
          loading.value = false;
          throw new CustomError('上传失败', true);
        }
      }

      fileList.value = [
        {
          id: 'upload',
          name: tempFileName.value,
          type: targetFile.type ?? 'image/jpeg',
          status: 'finished',
          url: await file2BlobUrlPromise(targetFile),
          file: targetFile,
          fullPath: files.file?.name ?? '',
        },
      ];
      onUpdateFile(fileList.value);
      tempFileName.value = '';
      loading.value = false;
    };

    const onFileDrop = async (e: DragEvent) => {
      e.preventDefault();

      try {
        if (!e.dataTransfer) {
          return;
        }

        const url
          = e.dataTransfer?.getData('text')
          // 适配mac拖拽;
          || e.dataTransfer?.getData('text/uri-list');
        const files
          = (e.dataTransfer?.files && e.dataTransfer?.files.length > 0
            ? e.dataTransfer?.files
            : [await urlToFile(url)]) ?? [];

        const uploadFileInfo: UploadFileInfo = {
          id: 'drop',
          name: 'drop',
          status: 'pending',
          url,
          file: files[0],
        };
        const res = await beforeUploadCheck({
          file: uploadFileInfo,
          fileList: [uploadFileInfo],
        });
        if (res) {
          await handleCustomRequest({
            file: uploadFileInfo,
          } as UploadCustomRequestOptions);
        }
      } catch (error) {
        console.error('【图片读取失败】:', error);
        message.error(
          `【图片读取失败】您拖拽的可能不是本地或者网络图片存在服务器拦截哦。如果存在类似问题，可以先保存原图到本地后再上传[${happy()}]`,
        );
      } finally {
        isDragMoveOver.value = false;
      }
    };

    const clearFile = () => {
      uploadRef.value?.clear();
      onUpdateFile([]);
      emit('fileClean');
    };

    expose({
      clearFile,
    });

    const renderFileList = (files: UploadFileInfo[]) => {
      return (
        <>
          {props.mode === 'card'
            ? (
              <div class="file-list pos-relative h-full w-full flex justify-center">
                {files.map((file) => {
                  return (
                    <NImage
                      class="h-full justify-center"
                      key={file.id}
                      objectFit="contain"
                      ref={imageRef}
                      renderToolbar={(props: ImageRenderToolbarProps) =>
                        renderToolbar(props, [
                          {
                            url: files[0].url ?? '',
                            name: files[0].name,
                          },
                        ])}
                      src={file.url!}
                    />
                  );
                })}
                <div
                  class="operation pos-absolute bottom-0 left-0 h-full w-full transition-all transition-duration-200"
                  onMouseleave={() => (isMoveIn.value = false)}
                  onMousemove={() => (isMoveIn.value = true)}
                  style={{
                    background: isMoveIn.value
                      ? 'rgba(0,0,0,0.2)'
                      : 'transparent',
                  }}
                >
                  <Transition
                    enterActiveClass="a-fade-in"
                    leaveActiveClass="a-fade-out"
                  >
                    {isMoveIn.value
                      ? (
                        <div class="flex flex-nowrap absolute-center">
                          {props.showTools.includes('preview')
                            ? (
                              <div class="mx-3px rd-4px bg-[rgba(46,51,56,.75)] flex-c-center">
                                <NButton
                                  class="p-4px"
                                  onClick={() => imageRef.value?.click()}
                                  size="small"
                                  strong
                                  text
                                  themeOverrides={{
                                    textColorText: '#fff',
                                    textColorTextHover: themeVars.value.primaryColor,
                                  }}
                                >
                                  {{
                                    icon: () => (
                                      <NIcon size={props.toolIconSize}>
                                        <EyeOutline />
                                      </NIcon>
                                    ),
                                  }}
                                </NButton>
                              </div>
                            )
                            : null}
                          {props.showTools.includes('edit')
                            ? (
                              <div class="mx-3px rd-4px bg-[rgba(46,51,56,.75)] c-FO-Content-Text0 flex-c-center">
                                <NButton
                                  class="p-4px"
                                  disabled={props.toolDisabled}
                                  onClick={() => emit('edit')}
                                  size="small"
                                  strong
                                  text
                                  themeOverrides={{
                                    textColorText: '#fff',
                                    textColorTextHover: themeVars.value.primaryColor,
                                  }}
                                >
                                  {{
                                    icon: () => (
                                      <NIcon size={props.toolIconSize}>
                                        <Edit20Regular />
                                      </NIcon>
                                    ),
                                  }}
                                </NButton>
                              </div>
                            )
                            : null}
                          {props.showTools.includes('delete')
                            ? (
                              <div class="mx-3px rd-4px bg-[rgba(46,51,56,.75)] flex-c-center">
                                <NButton
                                  class="p-4px"
                                  disabled={props.toolDisabled}
                                  onClick={clearFile}
                                  size="small"
                                  strong
                                  text
                                  themeOverrides={{
                                    textColorText: '#fff',
                                    textColorTextHover: themeVars.value.primaryColor,
                                  }}
                                >
                                  {{
                                    icon: () => (
                                      <NIcon size={props.toolIconSize}>
                                        <TrashBinOutline />
                                      </NIcon>
                                    ),
                                  }}
                                </NButton>
                              </div>
                            )
                            : null}
                        </div>
                      )
                      : null}
                  </Transition>
                </div>
              </div>
            )
            : null}

          {props.mode === 'file'
            ? (
              <div class="file-list pos-relative h-full w-full flex justify-center">
                <div class="h-full w-full flex flex-col flex-c-center">
                  <NIcon class="mb-8px" depth={3} size="120">
                    {getFileTypeIcon(files[0].type!)}
                  </NIcon>
                  <NEllipsis
                    class="c-FO-Content-Text1 break-words text-center"
                    line-clamp="2"
                    style={{
                      '--text-color': themeVars.value.textColor3,
                    }}
                  >{files[0]?.fullPath}
                  </NEllipsis>
                </div>
                <div
                  class="operation pos-absolute bottom-0 left-0 h-full w-full transition-all transition-duration-200"
                  onMouseleave={() => (isMoveIn.value = false)}
                  onMousemove={() => (isMoveIn.value = true)}
                  style={{
                    background: isMoveIn.value
                      ? 'rgba(0,0,0,0.2)'
                      : 'transparent',
                  }}
                >
                  <Transition
                    enterActiveClass="a-fade-in"
                    leaveActiveClass="a-fade-out"
                  >
                    {isMoveIn.value
                      ? (
                        <div class="flex flex-nowrap absolute-center">
                          {props.showTools.includes('preview')
                            ? (
                              <div class="mx-3px rd-4px bg-[rgba(46,51,56,.75)] flex-c-center">
                                <NButton
                                  class="p-4px"
                                  onClick={() => (props.customPreview ? props.customPreview(files) : window.open(files[0].url!, '_blank'))}
                                  size="small"
                                  strong
                                  text
                                  themeOverrides={{
                                    textColorText: '#fff',
                                    textColorTextHover: '#1CBBFF',
                                  }}
                                >
                                  {{
                                    icon: () => (
                                      <NIcon size={props.toolIconSize}>
                                        <EyeOutline />
                                      </NIcon>
                                    ),
                                  }}
                                </NButton>
                              </div>
                            )
                            : null}
                          {props.showTools.includes('delete')
                            ? (
                              <div class="mx-3px rd-4px bg-[rgba(46,51,56,.75)] flex-c-center">
                                <NButton
                                  class="p-4px"
                                  disabled={props.toolDisabled}
                                  onClick={clearFile}
                                  size="small"
                                  strong
                                  text
                                  themeOverrides={{
                                    textColorText: '#fff',
                                    textColorTextHover: '#1CBBFF',
                                  }}
                                >
                                  {{
                                    icon: () => (
                                      <NIcon size={props.toolIconSize}>
                                        <TrashBinOutline />
                                      </NIcon>
                                    ),
                                  }}
                                </NButton>
                              </div>
                            )
                            : null}
                        </div>
                      )
                      : null}
                  </Transition>
                </div>
              </div>
            )
            : null}
          {props.mode === 'slot'
            ? (
              <NUploadTrigger abstract>
                {{
                  default: () => (
                    <>
                      {slots.display?.({
                        fileList,
                        click: () =>
                          !props.disabled
                          && uploadRef.value?.openOpenFileDialog(),
                      })}
                    </>
                  ),
                }}
              </NUploadTrigger>
            )
            : null}
        </>
      );
    };

    return () => (
      <div
        class="pos-relative b-2px rd-4px border-dashed"
        style={{
          borderColor: isDragMoveOver.value
            ? themeVars.value.primaryColor
            : themeVars.value.borderColor,
          ...props.containerStyle,
        }}
      >
        <NUpload
          abstract
          accept={props.accept}
          customRequest={handleCustomRequest}
          listType="text"
          max={1}
          onBeforeUpload={beforeUploadCheck}
          ref={uploadRef}
        >
          {!props.fileList?.length || (props.fileList && props.fileList.length <= 0)
            ? (
              <NUploadTrigger abstract>
                {{
                  default: () => {
                    return (
                      <>
                        {props.mode === 'card' || props.mode === 'file'
                          ? (
                            <div
                              class={`h-full w-full flex-col text-center flex-c-center ${props.disabled ? 'cursor-not-allowed' : 'cursor-pointer'}`}
                              onClick={() =>
                                !props.disabled
                                && !loading.value
                                && uploadRef.value?.openOpenFileDialog()}
                              onDragleave={() => (isDragMoveOver.value = false)}
                              onDragover={(e) =>
                                !props.disabled && onFileDragover(e)}
                              onDrop={(e) => !props.disabled && onFileDrop(e)}
                            >
                              <NIcon class={['mb-8px', loading.value ? 'animate-spin' : '']} depth="3" size="20">
                                {loading.value
                                  ? (
                                    <Loading3QuartersOutlined />
                                  )
                                  : (
                                    <ArchiveOutline />
                                  )}
                              </NIcon>
                              <NText class="font-size-[12px]" depth={3}>
                                {props.placeholder}
                              </NText>
                            </div>
                          )
                          : null}
                        {props.mode === 'slot'
                          ? (
                            <>
                              {slots.trigger?.({
                                click: () => uploadRef.value?.openOpenFileDialog(),
                              })}
                            </>
                          )
                          : null}
                      </>
                    );
                  },
                }}
              </NUploadTrigger>
            )
            : (
              renderFileList(props.fileList)
            )}
        </NUpload>
        <>{slots.extra?.()}</>
      </div>
    );
  },
});

export { FileUploader, FileUploaderExpose };
