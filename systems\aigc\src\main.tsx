import '@unocss/reset/tailwind-compat.css';
import '@hg-tech/forgeon-style/style';
import '@hg-tech/oasis-common/style';
import 'virtual:uno.css';
import '@lancercomet/style/dist/animation.css';
import '@/assets/styles/reset.css';

import 'reflect-metadata';
import * as Sentry from '@sentry/vue';

import dayjs from 'dayjs';
import 'dayjs/locale/zh-cn';
import { type App, createApp } from 'vue';

import { AigcApp } from '@/layout/app';
import { router } from '@/plugins/router';

import '@/assets/font/fonts.css';
import { GlobalEnv } from './configs/global-env';

import CustomError from './common/utils/custom-error';
import { useNaiveUIApi, useUserBase } from './common/hooks';
import { happy } from 'random-jpn-emoji';
import { Logger } from './common/utils/logger';
import { useMicroAppInject, useUserAuthInfoCtx } from '@hg-tech/oasis-common';

dayjs.locale('zh-cn');

function initApp() {
  const app = createApp(AigcApp);
  app.use(router);
  return app;
}

function initSentry(app: App<Element>) {
  if (GlobalEnv.isProd) {
    Sentry.init({
      app,
      environment: GlobalEnv.HGEnv,
      release: GlobalEnv.version,
      dsn: 'https://<EMAIL>/56',
      integrations: [
        Sentry.browserTracingIntegration({ router }),
        Sentry.replayIntegration({
          /**
           * FIXME 在 @sentry/vue@9.32.0及之前 + Chrome138版本之后会出现严重的卡顿，暂时限制 mutationLimit 以规避卡死问题
           */
          mutationLimit: 150,
          maskAllText: false,
          blockAllMedia: false,
        }),
        Sentry.browserApiErrorsIntegration({
          setTimeout: true,
          setInterval: true,
          requestAnimationFrame: true,
          XMLHttpRequest: true,
          eventTarget: true,
        }),
      ],
      replaysSessionSampleRate: 0.1,
      replaysOnErrorSampleRate: 1.0,
      tracesSampleRate: 0.2,
      beforeSend(event, hint) {
        const { message } = useNaiveUIApi();
        const error = hint.originalException;
        Logger.error('error', (error as Error).message, error instanceof CustomError);
        if (typeof (error as Error).message === 'string' && (error as Error).message.includes('Failed to fetch dynamically imported module')) {
          message.warning(`平台已发布新版本，请刷新页面以获取最新版本吧~${happy()}`);
          return null;
        }
        // todo test https://sentry.hypergryph.com/organizations/hypergryph/replays/f97c27a02799455f825ccfa925a536da/?referrer=%2Forganizations%2F%3AorgId%2Fissues%2F%3AgroupId%2F&t=6&t_main=errors
        if (error instanceof CustomError && !error.isReportable) {
          return null;
        }
        return event;
      },
    });
  }
}

if (window.__MICRO_APP_ENVIRONMENT__) {
  // 在微前端环境
  let app: App;

  window.mount = () => {
    app = initApp();
    app.mount('#sub-app');
    initSentry(app);
    Logger.debug('[Forgeon-AIGC]子系统已内嵌启动');
  };
  window.unmount = () => {
    app.unmount();
    Logger.debug('[Forgeon-AIGC]子系统已卸载');
  };
  const { setUserToken } = useUserBase();
  const { data } = useMicroAppInject(useUserAuthInfoCtx);
  setUserToken(data.value?.accessToken ?? '');
} else {
  const app = initApp();
  app.mount('#sub-app');
  initSentry(app);
  Logger.debug('[Forgeon-AIGC]子系统已独立启动');
}
