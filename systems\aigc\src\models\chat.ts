import { JsonProperty, Serializable } from '@lancercomet/suntori';

export enum ChatScene {
  ImageDesc = 'image_desc',
  Translate = 'translate',
}

export enum PromptDescType {
  Material = 'material',
  Detail = 'detail',
  Structure = 'structure',
  Quality = 'quality',
}

export const PromptDescTypeLabel = {
  [PromptDescType.Material]: '材质',
  [PromptDescType.Detail]: '细节',
  [PromptDescType.Structure]: '结构',
  [PromptDescType.Quality]: '画质提高',
};

@Serializable()
export class PromptDescForm {
  @JsonProperty()
  content: string = '';

  @JsonProperty()
  descType: PromptDescType[] = [
    PromptDescType.Material,
    PromptDescType.Detail,
    PromptDescType.Structure,
    PromptDescType.Quality,
  ];

  get descSystemContent() {
    return `你是提示词转化器，将任何用户输入转换成有画面感的一段中文提示词，在用户输入的基础上发挥想象力${
      this.descType.length > 0
        ? `，补充${this.descType
          .map((item) => PromptDescTypeLabel[item])
          .join(',')}的内容`
        : ''
    }。你需要从用户输入的信息里面提取出画面的主体，第一句话固定以量词+主体开头`;
  }
}

@Serializable()
export class PromptDescResponse {
  @JsonProperty()
  content: string = '';
}

@Serializable()
export class PromptDescRequest {
  @JsonProperty()
  system: string = '';

  @JsonProperty()
  scene: ChatScene = ChatScene.ImageDesc;

  @JsonProperty()
  content: string = '';
}

// chatgpt model

enum ChatMessageStatus {
  Finish = 'finish',
  Progress = 'progress',
  Error = 'error',
}

@Serializable()
class ChatSessionResponse {
  @JsonProperty('session_id')
  sessionId: string = '';
}

@Serializable()
class ChatModel {
  @JsonProperty()
  name: string = '';

  @JsonProperty()
  description: string = '';
}
@Serializable()
class ChatModelResponse {
  @JsonProperty({
    type: ChatModel,
    name: 'models',
  })
  models: ChatModel[] = [];
}

@Serializable()
class ChatMessageSearch {
  @JsonProperty()
  content: string = '';

  @JsonProperty()
  title: string = '';

  @JsonProperty()
  url: string = '';

  @JsonProperty()
  index: number = 0;
}

@Serializable()
class ChatMessage {
  @JsonProperty()
  role: string = '';

  @JsonProperty('content')
  _content?: string = '';

  @JsonProperty('reasoning_content')
  thinkingContent?: string = '';

  @JsonProperty({
    type: ChatMessageSearch,
    name: 'search',
  })
  search?: ChatMessageSearch[] = [];

  @JsonProperty()
  state: ChatMessageStatus = ChatMessageStatus.Finish;

  @JsonProperty()
  parentId: string = '';

  @JsonProperty('message_id')
  id: number = 0;

  set content(value: string) {
    this._content = value;
  }

  get content() {
    if (this.thinkingContent) {
      return `> ${this.thinkingContent.split('\n').filter((i) => i.trim()).map((line) => `\n > \n > ${line.trim()}`).join('')}\n\n${this._content}`;
    }
    return this._content || '';
  }
}

@Serializable()
class ChatSessionItem {
  @JsonProperty('session_id')
  sessionId: string = '';

  @JsonProperty()
  title: string = '';

  @JsonProperty('updated_at')
  updateTime?: number = 0;

  @JsonProperty({
    type: ChatMessage,
    name: 'messages',
  })
  messages: ChatMessage[] = [];
}

@Serializable()
class ChatSessionListResponse {
  @JsonProperty({
    type: ChatSessionItem,
    name: 'list',
  })
  sessionList: ChatSessionItem[] = [];

  @JsonProperty()
  total: number = 0;
}

@Serializable()
class ChatMessageResponse {
  @JsonProperty({
    type: ChatMessage,
    name: 'message',
  })
  messages: ChatMessage[] = [];

  @JsonProperty()
  title: string = '';
}

export {
  ChatMessage,
  ChatMessageResponse,
  ChatMessageSearch,
  ChatMessageStatus,
  ChatModel,
  ChatModelResponse,
  ChatSessionItem,
  ChatSessionListResponse,
  ChatSessionResponse,
};
