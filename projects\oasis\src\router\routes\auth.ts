import type { RouteRecordRaw } from 'vue-router';
import { PlatformEnterPoint, PlatformRoutePath } from '@hg-tech/oasis-common';

const oauthCallbacks: Readonly<RouteRecordRaw[]> = [
  {
    name: PlatformEnterPoint.FeishuReturn,
    path: PlatformRoutePath.FeishuReturn,
    alias: '/feishuReturn',
    component: () => import('../../views/sys/login/FeishuLoginReturn.vue'),
    meta: {
      title: '飞书登录返回页',
      icon: 'ant-design:twitter-outlined',
      hideTab: true,
      hideBreadcrumb: true,
      hideChildrenInMenu: true,
    },
  },
  {
    name: PlatformEnterPoint.LinkToApp,
    path: PlatformRoutePath.LinkToApp,
    alias: '/link/app',
    component: () => import('../../views/sys/link/LinkToApp.vue'),
    meta: {
      title: 'APP跳转页',
      icon: 'ant-design:link-outlined',
      hideTab: true,
      hideBreadcrumb: true,
      hideChildrenInMenu: true,
    },
  },
  {
    name: PlatformEnterPoint.SSOReturn,
    path: PlatformRoutePath.SSOReturn,
    alias: '/ssoReturn',
    component: () => import('../../views/sys/login/SSOLoginReturn.vue'),
    meta: {
      title: 'SSO登录返回页',
      icon: 'simple-icons:sega',
      hideTab: true,
      hideBreadcrumb: true,
      hideChildrenInMenu: true,
    },
  },
  {
    name: PlatformEnterPoint.LinkToOther,
    path: PlatformRoutePath.LinkToOther,
    alias: '/link/other',
    component: () => import('../../views/sys/link/LinkToOther.vue'),
    meta: {
      title: '第三方SSO登录跳转页',
      icon: 'simple-icons:vitest',
      hideTab: true,
      hideBreadcrumb: true,
      hideChildrenInMenu: true,
    },
  },
];

export const authRoutes: Readonly<RouteRecordRaw[]> = [
  ...oauthCallbacks,
  {
    name: PlatformEnterPoint.Login,
    path: PlatformRoutePath.Login,
    component: () => import('/@/views/sys/login/Login.vue'),
    meta: {
      title: '登录',
    },
  },
];
