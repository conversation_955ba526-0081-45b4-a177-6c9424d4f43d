<template>
  <span :class="`inline-flex items-center px-4px py-1px rounded-4px FO-Font-B14 ${methodStyleClasses[method.toLowerCase()]}`">
    {{ method.toUpperCase() }}
  </span>
</template>

<script setup lang="ts">
withDefaults(defineProps<{
  method: string;
}>(), {
  method: 'get',
});

const methodStyleClasses: Record<string, string> = {
  get: 'bg-FO-Datavis-Blue3 text-FO-Datavis-Blue1',
  post: 'bg-FO-Datavis-Teal3 text-FO-Datavis-Teal1',
  put: 'bg-FO-Datavis-Yellow3 text-FO-Datavis-Yellow1',
  delete: 'bg-FO-Datavis-Orange3 text-FO-Datavis-Orange1',
  patch: 'bg-FO-Datavis-Pink3 text-FO-Datavis-Pink1',
};
</script>
