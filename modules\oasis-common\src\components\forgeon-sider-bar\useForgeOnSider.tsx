import { computed, reactive, ref, watch } from 'vue';
import { findItemInMenuByPath } from './helper';
import { findInTrees } from '@hg-tech/utils';
import { message } from 'ant-design-vue';
import { type ICommonMenuItem, PlatformEnterPoint } from '../../configs/index';

const collapsed = ref<boolean>(false);
const activeMainModule = ref<PlatformEnterPoint>();
const activeMenu = ref<PlatformEnterPoint>(PlatformEnterPoint.DevGuard);
const activeMenuItem = ref<ICommonMenuItem>();
const openKeys = ref<PlatformEnterPoint[]>([]);
// 侧边涉及的全量模块
const modules = ref<ICommonMenuItem[]>([]);
const showSubSider = ref<boolean>(false);
const routerInstance = reactive<{
  push?: (path: PlatformEnterPoint | string, openInNewTab?: boolean) => void;
}>({});
const currentPath = ref<string>('');
const isUserLogin = ref<boolean>(false);

export function useForgeOnSider() {
  const subMenus = computed(() => getMenuByModule(activeMainModule.value));

  function getMenuByModule(module?: PlatformEnterPoint) {
    return modules.value.find((item) => item.key === module)?.children ?? [];
  }

  function setModule(module: PlatformEnterPoint) {
    const moduleItem = modules.value.find((item) => item.key === module);
    if (!moduleItem) {
      return;
    }
    activeMainModule.value = module;

    if (moduleItem.name) {
      // 优先前往当前模块的落地页
      routerInstance.push?.(moduleItem.name, moduleItem.openInNewTab);
      showSubSider.value = Boolean(moduleItem.children?.length);
    } else if (moduleItem.children?.length) {
      // 否则检查是否有子模块有落地页
      const canActiveItem = findInTrees(moduleItem.children, (item) => item?.name != null);
      if (canActiveItem.target?.name) {
        routerInstance.push?.(canActiveItem.target.name, canActiveItem.target.openInNewTab);
        showSubSider.value = true;
      }
    } else {
      message.error('当前模块没有可访问的页面，请联系管理员');
    }
  }

  const initRouterPush = (options: typeof routerInstance) => {
    routerInstance.push = options.push;
  };

  const updateModules = (inputModules: ICommonMenuItem[]) => {
    modules.value = inputModules;
  };

  const updateActiveInfo = (initPath?: string) => {
    if (initPath) {
      currentPath.value = initPath;
    }

    const { path, target } = findItemInMenuByPath(modules.value, initPath || currentPath.value);

    const targetPath = path;
    const module = targetPath[0];

    if (module && Object.values(PlatformEnterPoint).includes(module as PlatformEnterPoint)) {
      activeMainModule.value = module;
      if (subMenus.value.length > 0) {
        showSubSider.value = true;
      } else {
        showSubSider.value = false;
      }
      openKeys.value = targetPath.slice(-2, -1) as PlatformEnterPoint[];
    }

    if (target) {
      activeMenu.value = target?.name || PlatformEnterPoint.Home;
      activeMenuItem.value = target;
    }
  };

  const setLoginStatus = (isLogin: boolean) => {
    isUserLogin.value = isLogin;
  };

  watch(() => modules.value, () => {
    updateActiveInfo();
  }, {
    deep: true,
  });

  return {
    activeMenuItem,
    collapsed,
    modules,
    subMenus,
    openKeys,
    activeMenu,
    activeMainModule,
    showSubSider,
    routerInstance,
    isUserLogin,
    setModule,
    getMenuByModule,
    initRouterPush,
    updateModules,
    updateActiveInfo,
    setLoginStatus,
  };
}
