import Icon from '@ant-design/icons-vue';
import { <PERSON><PERSON>, Drawer, Pagination } from 'ant-design-vue';
import { type PropType, computed, defineComponent, ref, useTemplateRef, watch } from 'vue';
import Close from '../../assets/svg/Close.svg?component';
import BasicStrokeHistory from '../../assets/svg/BasicStrokeHistory.svg?component';
import BasicStrokeChevronRight from '../../assets/svg/BasicStrokeChevronRight.svg?component';
import { useMergeHistory } from './use-merge-histroy';
import { type MergeV1Operation, MergeV1OperationType } from '@hg-tech/api-schema-merge';
import type { VxeGridInstance, VxeGridProps } from 'vxe-table';
import dayjs from 'dayjs';
import { type UserTagInfo, UserTag } from '../../components/UserTag';
import { BasicVxeTable, ForgeonTemplateRenderer } from '@hg-tech/oasis-common';
import { MergeV1OperationTypeLabelMap, renderOperationTypeIcon } from '../../models/config.model';
import { safeParseJSON } from '@hg-tech/utils';
import { FileDrawer } from './file-drawer';

const HistoryDrawer = defineComponent({
  props: {
    id: {
      type: String as PropType<string>,
    },
    cl: {
      type: String as PropType<string>,
      default: '',
    },
    visible: {
      type: Boolean as PropType<boolean>,
      default: false,
    },
  },
  emits: ['update:visible'],
  setup(props, { emit }) {
    const { fetchOperationList, operationList, operationTotal, operationListLoading } = useMergeHistory();
    const tableRef = useTemplateRef<VxeGridInstance<MergeV1Operation>>('baseTableRef');
    const open = computed({
      get: () => props.visible,
      set: (value) => {
        emit('update:visible', value);
      },
    });
    const queryParams = ref({
      recordId: props.id,
      page: 1,
      pageSize: 20,
    });
    const isShowFileDrawer = ref(false);
    const currentRow = ref<MergeV1Operation>();
    const tableRows = computed<MergeV1Operation[]>(() => operationList.value);
    const tableColumns = computed<VxeGridProps<MergeV1Operation>['columns']>(() => [
      { type: null, width: 32, slots: {
        default: () => <div class="bg-icon-gray3 h-6px w-6px rd-half" />,
      } },
      { field: 'createdAt', title: '操作时间', width: 200, slots: {
        default({ row }) {
          return row.createdAt ? dayjs(Number.parseInt(row.createdAt) * 1000).format('YYYY-MM-DD HH:mm:ss') : '';
        },
      } },
      { field: 'submitter', title: '操作人', width: 180, slots: {
        default({ row }) {
          const user: UserTagInfo = {
            openId: row.operator?.feishuOpenId || '',
            name: row.operator?.name || row.operator?.hgAccount || '--',
            avatar: row.operator?.avatar || '',
            nickname: row.operator?.nickname || '',
          };
          return <UserTag avatarSize={20} user={user} />;
        },
      } },
      { field: 'operationType', title: '操作类型', width: 130, slots: {
        default({ row }) {
          return (
            <div class="flex items-center gap-4px">
              {renderOperationTypeIcon[row.operationType as MergeV1OperationType]()}
              <span>{MergeV1OperationTypeLabelMap[row.operationType as MergeV1OperationType]}</span>
            </div>
          );
        },
      } },
      { field: 'operationDetail', title: '操作详情', slots: {
        default({ row }) {
          const data = safeParseJSON(row.operationDetail) ?? {};
          return (
            <div class="flex items-center gap-8px">
              <ForgeonTemplateRenderer
                class="flex-1 whitespace-pre-wrap"
                customRender={{
                  workspace: (val) => <span class="FO-Font-B14 c-FO-Content-Text1">{val}</span>,
                  total: (val) => <span class="FO-Font-B14 c-FO-Content-Text1">{val}</span>,
                  reason: (val) => <span class="FO-Font-B14 c-FO-Content-Text1">{val}</span>,
                  users: (users: string) => <span class="FO-Font-B14 c-FO-Content-Text1">{users}</span>,
                }}
                data={data}
                template={row.operationDetailTemplate ?? ''}
              />
              {row.operationType && [MergeV1OperationType.OPERATION_TYPE_ONLINE_PROCESS, MergeV1OperationType.OPERATION_TYPE_SAVE_RESOLVE_PROGRESS]
                .includes(row.operationType) && (
                <Button
                  class="btn-fill-default c-FO-Content-Icon2"
                  icon={<Icon component={BasicStrokeChevronRight} />}
                  onClick={() => {
                    currentRow.value = row;
                    isShowFileDrawer.value = true;
                  }}
                />
              )}
            </div>

          );
        },
      } },
    ]);
    const gridOptions = computed(() => ({
      rowConfig: {
        keyField: 'id',
      },
      virtualYConfig: {
        enabled: true,
        gt: 20,
      },
      height: 'auto',
      showOverflow: true,
      loading: operationListLoading.value,
      columns: tableColumns.value,
      data: tableRows.value,
    }) as VxeGridProps);

    const onClose = () => {
      open.value = false;
    };

    watch(() => props.id, (newVal) => {
      if (newVal) {
        queryParams.value.recordId = newVal;
        fetchOperationList(queryParams.value);
      }
    }, { immediate: true });

    const renderHistory = () => {
      return (
        <div class="merge-history h-full flex flex-col rd-12px bg-FO-Container-Fill1 p-20px">
          <div class="mb-24px flex items-center">
            <Icon component={<BasicStrokeHistory class="font-size-20px" />} />
            <div class="FO-Font-B16 ml-8px c-FO-Content-Text1">CL:{props.cl} 操作历史</div>
          </div>
          <div class="mb-12px flex-1 overflow-hidden">
            <BasicVxeTable
              options={gridOptions.value}
              ref={tableRef}
            />
          </div>
          <div class="mb-12px flex items-center justify-end">
            <Pagination
              onChange={() => fetchOperationList(queryParams.value)}
              showSizeChanger
              showTotal={(total, range) => `显示 ${range[0]} - ${range[1]} 条，共 ${total} 条`}
              total={operationTotal.value}
              v-model:current={queryParams.value.page}
              v-model:pageSize={queryParams.value.pageSize}
            />
          </div>
        </div>
      );
    };

    return () => (
      <div>
        <Drawer
          bodyStyle={{ padding: '8px', paddingTop: '12px' }}
          closable={false}
          destroyOnClose={true}
          mask={true}
          maskClosable={false}
          onClose={onClose}
          placement="right"
          title="操作历史"
          v-model:open={open.value}
          width={924}
        >
          {{
            extra: () => (
              <Button
                class="flex items-center justify-center"
                icon={(
                  <Icon class="font-size-18px" component={<Close />} />
                )}
                onClick={onClose}
                type="text"
              />
            ),
            default: () => renderHistory(),
            footer: () => (
              <div class="flex justify-end gap-12px">
                <Button class="btn-fill-default" onClick={onClose} type="text">关闭</Button>
              </div>
            ),
          }}
        </Drawer>
        <FileDrawer id={currentRow.value?.id} v-model:visible={isShowFileDrawer.value} />
      </div>

    );
  },
});

export {
  HistoryDrawer,
};
