import type MarkdownIt from 'markdown-it';
import katex from 'katex';

// 插件选项接口
interface MarkdownItKatexOptions {
  blockClass?: string; // 自定义用于包裹 KaTeX block 公式的 class
  inlineClass?: string; // 自定义用于包裹 KaTeX block 公式的 class
}

// 自定义插件函数
function markdownItKatexEnhance(md: MarkdownIt, options: MarkdownItKatexOptions = {}) {
  const defaultOptions: MarkdownItKatexOptions = {};
  const opts = { ...defaultOptions, ...options };
  // 处理行内公式 \[ 和 \]
  md.inline.ruler.before('escape', 'math_inline', (state, silent) => {
    const start = state.pos;
    if (silent) {
      return false;
    } // 如果是silent模式，就不做任何事

    // 检查是否匹配行内公式
    if (state.src.charAt(start) !== '\\' || state.src.charAt(start + 1) !== '[') {
      return false;
    }

    const end = state.src.indexOf('\\]', start + 2);
    if (end === -1) {
      return false;
    } // 如果找不到结束标记

    const content = state.src.slice(start + 2, end); // 提取公式内容

    // 将公式转换为 $$ 包裹的块级公式
    const token = state.push('math_inline', 'span', 0);
    token.content = content;
    state.pos = end + 2; // 更新当前位置
    return true;
  });

  // 处理块级公式 \\[ 和 \\]
  md.block.ruler.before('fence', 'math_block', (state, startLine, endLine, silent) => {
    const start = state.bMarks[startLine] + state.tShift[startLine];
    const src = state.src;
    if (silent) {
      return false;
    }

    // 只匹配以 \[ 开头的块级公式（必须是行首且前面不能有其他字符）
    if (
      src.slice(start, start + 2) !== '\\['
      || (start > 0 && src[start - 1] !== '\n')
    ) {
      return false;
    }

    // 查找对应的 \] 结束符，且必须在本行内
    const lineEnd = state.eMarks[startLine];
    const closePos = src.indexOf('\\]', start + 2);
    if (closePos === -1 || closePos > lineEnd) {
      return false;
    }

    // 提取公式内容
    const content = src.slice(start + 2, closePos);

    // 仅当 \[ 和 \] 之间有内容时才匹配
    if (!content.trim()) {
      return false;
    }

    // 创建 token
    const token = state.push('math_block', 'div', 0);
    token.content = content;
    state.line = startLine + 1;
    return true;
  });
  // 处理行内公式 \( 和 \)
  md.inline.ruler.before('escape', 'math_inline', (state, silent) => {
    const start = state.pos;
    if (silent) {
      return false;
    } // 如果是silent模式，就不做任何事

    // 检查是否匹配行内公式
    if (state.src.charAt(start) !== '\\' || state.src.charAt(start + 1) !== '(') {
      return false;
    }

    const end = state.src.indexOf('\\)', start + 2);
    if (end === -1) {
      return false;
    } // 如果找不到结束标记

    const content = state.src.slice(start + 2, end); // 提取公式内容

    // 将公式转换为 $$ 包裹的块级公式
    const token = state.push('math_inline', 'span', 0);
    token.content = content;
    state.pos = end + 2; // 更新当前位置
    return true;
  });

  // 处理块级公式 \\( 和 \\)
  md.block.ruler.before('fence', 'math_block_paren', (state, startLine, endLine, silent) => {
    const start = state.bMarks[startLine] + state.tShift[startLine];
    const src = state.src;
    if (silent) {
      return false;
    }

    // 只匹配以 \\( 开头的块级公式（必须是行首且前面不能有其他字符）
    if (
      src.slice(start, start + 2) !== '\\('
      || (start > 0 && src[start - 1] !== '\n')
    ) {
      return false;
    }

    // 查找对应的 \\) 结束符，且必须在本行内
    const lineEnd = state.eMarks[startLine];
    const closePos = src.indexOf('\\)', start + 2);
    if (closePos === -1 || closePos > lineEnd) {
      return false;
    }

    // 提取公式内容
    const content = src.slice(start + 2, closePos);

    // 仅当 \\( 和 \\) 之间有内容时才匹配
    if (!content.trim()) {
      return false;
    }

    // 创建 token
    const token = state.push('math_block', 'div', 0);
    token.content = content;
    state.line = startLine + 1;
    return true;
  });

  // 自定义渲染 math_inline（行内公式）
  md.renderer.rules.math_inline = function (tokens, idx) {
    const token = tokens[idx];

    const rendered = katex.renderToString(token.content, {
      // 行内公式
      displayMode: false,
      // 遇到错误时不抛出异常, katex针对部分特殊字符会报错，这里考虑是轻量渲染，而非要高要求的学术展示场景，因此仅忽略报错处理
      throwOnError: false,
    });
    return `<span class="${opts.inlineClass}">${rendered}</span>`;
  };

  // 自定义渲染 math_block（块级公式）
  md.renderer.rules.math_block = function (tokens, idx) {
    const token = tokens[idx];
    // 渲染 KaTeX block 公式，并为其添加自定义 class
    const rendered = katex.renderToString(token.content, {
      // 块级公式
      displayMode: true,
      // 遇到错误时不抛出异常, katex针对部分特殊字符会报错，这里考虑是轻量渲染，而非要高要求的学术展示场景，因此仅忽略报错处理
      throwOnError: false,
    });
    return `<div class="${opts.blockClass}">${rendered}</div>`;
  };
}

export {
  markdownItKatexEnhance,
};
