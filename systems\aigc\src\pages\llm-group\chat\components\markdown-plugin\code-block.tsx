import { NButton, NButtonGroup, useMessage } from 'naive-ui';
import { happy } from 'random-jpn-emoji';
import { type PropType, defineComponent, onMounted, ref, watch, watchEffect } from 'vue';
import styles from '../chat-bubble.module.less';
import hljs from 'highlight.js';
import 'highlight.js/styles/atom-one-dark.css';
import CustomError from '@/common/utils/custom-error';
import mermaid from 'mermaid';
import { FullScreenMaximize16Regular, FullScreenMinimize24Regular } from '@/common/components/svg-icons';
import { useAppTheme } from '@/common/hooks';
import { useClipboard } from '@vueuse/core';

const CodeBlock = defineComponent({
  props: {
    code: {
      type: String as PropType<string>,
      required: true,
    },
    lang: {
      type: String as PropType<string>,
      default: 'plaintext',
    },
  },
  setup(props) {
    const mermaidContainerRef = ref<HTMLDivElement | null>(null);
    const codeBlockRef = ref<HTMLDivElement | null>(null);
    const activeTabs = ref<'graph' | 'code'>('graph');
    const targetId = ref<string | null>(null);
    const isFullScreen = ref(false);

    const message = useMessage();
    const { currentTheme } = useAppTheme();

    const tryRenderMermaid = async (mermaidCode: string, container: HTMLDivElement | null) => {
      if (!container || props.lang !== 'mermaid') {
        return;
      }

      if (!targetId.value) {
        targetId.value = `generated-mermaid-${Math.random().toString(16).slice(2)}`;
      }

      try {
        await mermaid.parse(mermaidCode);
        const { svg } = await mermaid.render(targetId.value, mermaidCode);
        container.innerHTML = svg;
      } catch {
        container.innerHTML = '';
        mermaidContainerRef.value!.innerHTML = '';
        document.getElementById(`d${targetId.value}`)?.remove();
      }
    };

    const fullscreenChange = () => {
      if (document.fullscreenElement) {
        isFullScreen.value = true;
      } else {
        isFullScreen.value = false;
      }
    };

    watchEffect((clearup) => {
      document.addEventListener('fullscreenchange', fullscreenChange);
      clearup(() => {
        document.removeEventListener('fullscreenchange', fullscreenChange);
      });
    });

    watch([mermaidContainerRef, () => props.code], async ([el, c]) => {
      await tryRenderMermaid(c, el);
    }, {
      immediate: true,
    });

    watch(() => currentTheme.value, async () => {
      if (!mermaidContainerRef.value) {
        return;
      }
      mermaid.initialize({
        startOnLoad: false,
        theme: currentTheme.value === 'dark' ? 'dark' : 'default',
      });
      await tryRenderMermaid(props.code, mermaidContainerRef.value);
    });

    function enterFullScreen() {
      const el = codeBlockRef.value as HTMLDivElement;
      if (el.requestFullscreen) {
        el.requestFullscreen();
        isFullScreen.value = true;
      }
    }

    const renderCode = () => {
      const highlighted = props.lang && hljs.getLanguage(props.lang)
        ? hljs.highlight(props.code, { language: props.lang }).value
        : hljs.highlightAuto(props.code).value;
      return highlighted;
    };

    const copyCode = () => {
      if (codeBlockRef.value) {
        const { copy } = useClipboard();
        copy(props.code);
        message.success(`已复制代码${happy()}`);
      } else {
        message.error('code元素获取失败，请稍后再试');
        throw new CustomError('code元素获取失败，请稍后再试');
      }
    };

    const switchTab = (value: 'graph' | 'code') => {
      activeTabs.value = value;
    };

    onMounted(() => {
      mermaid.initialize({
        startOnLoad: false,
        theme: currentTheme.value === 'dark' ? 'dark' : 'default',
      });
    });

    return () => (
      <div>
        <div class={styles.codeBlock} ref={codeBlockRef}>
          <div class={[styles.codeBlockHeader, 'flex items-center justify-between']}>
            <div class="flex-c-center gap-8px">
              <div class="FO-Font-R12">{props.lang}</div>
              <NButtonGroup v-show={props.lang === 'mermaid'}>
                <NButton
                  onClick={() => switchTab('graph')}
                  secondary
                  size="small"
                  type={activeTabs.value === 'graph' ? 'primary' : 'tertiary'}
                >图表
                </NButton>
                <NButton
                  onClick={() => switchTab('code')}
                  secondary
                  size="small"
                  type={activeTabs.value === 'code' ? 'primary' : 'tertiary'}
                >代码
                </NButton>
              </NButtonGroup>
            </div>
            <div class="flex gap-8px">
              <NButton
                class="px-8px"
                onClick={copyCode}
                quaternary
                size="small"
              >复制
              </NButton>
              {
                !isFullScreen.value
                  ? (
                    <NButton
                      class="px-8px"
                      onClick={enterFullScreen}
                      quaternary
                      renderIcon={() => <FullScreenMaximize16Regular />}
                      size="small"
                    />
                  )
                  : (
                    <NButton
                      class="px-8px"
                      onClick={() =>
                        document.exitFullscreen()}
                      quaternary
                      renderIcon={() => <FullScreenMinimize24Regular />}
                      size="small"
                    />
                  )
              }

            </div>

          </div>

          <pre class="code-content h-full">
            <div
              class="flex-c-center h-full w-full overflow-auto svg:max-h-full"
              ref={mermaidContainerRef}
              v-show={props.lang === 'mermaid' && activeTabs.value === 'graph'}
            />
            <div
              class={[isFullScreen.value ? 'overflow-auto h-full FO-Font-R16' : '']}
              v-show={
                props.lang !== 'mermaid'
                || (props.lang === 'mermaid' && activeTabs.value === 'code')
              }
            >
              <div class={[isFullScreen.value ? 'm-auto max-w-60%' : '']} v-html={renderCode()} />
            </div>
          </pre>

        </div>
      </div>
    );
  },
});

export {
  CodeBlock,
};
