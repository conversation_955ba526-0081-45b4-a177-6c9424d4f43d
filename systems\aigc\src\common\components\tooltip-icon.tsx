import type { PropType } from 'vue';
import type { Depth } from 'naive-ui/es/icon/src/Icon';
import { NButton, NIcon, NPopover } from 'naive-ui';
import { defineComponent, onMounted, ref } from 'vue';

const TooltipIcon = defineComponent({
  props: {
    trigger: {
      type: String as PropType<'hover' | 'click'>,
      default: 'hover',
    },
    triggerClass: {
      type: String as PropType<string>,
      default: '',
    },
    duration: {
      type: Number as PropType<number>,
      default: 100,
    },
    triggerContent: {
      type: String as PropType<string>,
    },
    size: {
      type: Number as PropType<number>,
      default: 20,
    },
    depth: {
      type: Number as PropType<number>,
      default: 1,
    },
    placement: {
      type: String as PropType<
        | 'top'
        | 'bottom'
        | 'left'
        | 'right'
        | 'top-start'
        | 'top-end'
        | 'left-start'
        | 'left-end'
        | 'right-start'
        | 'right-end'
        | 'bottom-start'
        | 'bottom-end'
      >,
      default: 'top',
    },
    color: {
      type: String as PropType<string>,
    },
    disabled: {
      type: Boolean as PropType<boolean>,
      default: false,
    },
    maxWidth: {
      type: Number as PropType<number>,
      default: 300,
    },
    secondary: {
      type: Boolean as PropType<boolean>,
      default: false,
    },
    text: {
      type: Boolean as PropType<boolean>,
      default: true,
    },
    btnSize: {
      type: String as PropType<'large' | 'medium' | 'small' | 'tiny'>,
      default: 'medium',
    },
  },
  emits: ['click'],
  setup(props, { slots, emit }) {
    const containerRef = ref<string | HTMLElement>();
    onMounted(() => {
      containerRef.value = window.__MICRO_APP_ENVIRONMENT__ ? document.getElementById('sub-app')! : 'body';
    });

    return () => (
      <NPopover
        duration={props.duration}
        placement={props.placement}
        style={{
          maxWidth: `${props.maxWidth}px`,
        }}
        to={containerRef.value}
        trigger={props.trigger}
      >
        {{
          trigger: () => (
            <>
              {slots.trigger?.() && (
                <NButton
                  class={props.triggerClass}
                  disabled={props.disabled}
                  onClick={() => emit('click')}
                  secondary={props.secondary}
                  size={props.btnSize}
                  text={props.text}
                >
                  <NIcon
                    class="v-middle"
                    color={props.color}
                    depth={props.depth as unknown as Depth}
                    size={props.size}
                  >
                    {slots.trigger?.()}
                  </NIcon>
                </NButton>
              )}
            </>
          ),
          default: () => <>{slots.default?.() ?? props.triggerContent}</>,
        }}
      </NPopover>
    );
  },
});

export { TooltipIcon };
