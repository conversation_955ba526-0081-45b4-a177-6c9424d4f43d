import { ForgeonTheme } from '@hg-tech/forgeon-style';
import type { PropType } from 'vue';
import type { ICommonMenuItem, PlatformRoutePath } from '../../configs';
import type { RoleListItem, UserInfoModel } from '../../models';
import { OutSideRoutePath, PlatformEnterPoint } from '../../configs';
import { defineComponent, onMounted, Transition, watch } from 'vue';
import { MainSider } from './components/main-sider';
import { SubSider } from './components/sub-sider';
import styles from './style.module.less';
import { checkInsideRoutePath } from './helper';
import { useForgeOnSider } from './useForgeOnSider';

export const ForgeonSideBar = defineComponent({
  props: {
    modules: {
      type: Array as PropType<ICommonMenuItem[]>,
      default: () => [],
    },
    path: {
      type: String as PropType<string>,
      default: '',
    },
    defaultCollapse: {
      type: Boolean as PropType<boolean>,
      default: false,
    },
    userInfo: {
      type: Object as PropType<UserInfoModel>,
      default: () => ({}),
    },
    token: {
      type: String as PropType<string>,
      default: '',
    },
    theme: {
      type: String as PropType<ForgeonTheme>,
      default: ForgeonTheme.Dark,
    },
    displayModules: {
      type: Array as PropType<PlatformEnterPoint[]>,
      default: () => [],
    },
    onPathChange: {
      type: Function as PropType<(params: { name: PlatformEnterPoint }, openInNewTab?: boolean) => void>,
      default: () => {},
    },
    onSwitchRole: {
      type: Function as PropType<(role?: RoleListItem) => void>,
      default: () => {},
    },
    onLogout: {
      type: Function as PropType<() => void>,
      default: () => {},
    },
    onLogin: {
      type: Function as PropType<() => void>,
      default: () => {},
    },
    onToggleDarkMode: {
      type: Function as PropType<(e: MouseEvent, mode: ForgeonTheme) => void>,
      default: () => {},
    },
    onCollapseClick: {
      type: Function as PropType<() => void>,
      default: () => {},
    },
    onOutsidePathChange: {
      type: Function as PropType<(params: { name: string; path: string }) => void>,
      default: () => {},
    },
  },
  setup(props) {
    const { subMenus, collapsed, showSubSider, routerInstance, initRouterPush, updateModules, updateActiveInfo, setLoginStatus } = useForgeOnSider();

    watch(() => props.modules, (modules) => {
      if (modules.length > 0) {
        updateModules(modules);
      }
    }, {
      immediate: true,
      deep: true,
    });

    watch(() => props.path, (path) => {
      if (path) {
        updateActiveInfo(path);
      }
    }, {
      immediate: true,
    });

    watch(() => props.token, (token) => {
      setLoginStatus(Boolean(token));
    }, {
      immediate: true,
    });

    watch(() => props.defaultCollapse, (defaultCollapse) => {
      collapsed.value = defaultCollapse;
    }, {
      immediate: true,
    });

    const onPathChange = ({ path, key }: {
      path: PlatformRoutePath | string;
      key: PlatformEnterPoint | string;
    }) => {
      if (checkInsideRoutePath(path)) {
        routerInstance.push?.(key);
      } else {
        routerInstance.push?.(path);
      }
    };

    onMounted(() => {
      initRouterPush({
        push: (name, openInNewTab) => {
          if (Object.values(PlatformEnterPoint).includes(name as PlatformEnterPoint)) {
            props.onPathChange({ name: name as PlatformEnterPoint }, openInNewTab);
          } else if (name) {
            const pathName = Object.entries(OutSideRoutePath).find(([_, value]) => value === name)?.[0] ?? '';
            props.onOutsidePathChange({ name: pathName, path: name });
          }
        },
      });
    });

    return () => (
      <div class={[styles.forgeonSider]}>
        <MainSider
          displayModules={props.displayModules}
          onLogin={props.onLogin}
          onLogout={props.onLogout}
          onPathChange={onPathChange}
          onSwitchRole={props.onSwitchRole}
          onToggleDarkMode={props.onToggleDarkMode}
          theme={props.theme}
          userInfo={props.userInfo}
        />
        <Transition mode="out-in">
          <SubSider
            onCollapseClick={props.onCollapseClick}
            onPathChange={onPathChange}
            subMenus={subMenus.value}
            v-show={showSubSider.value && !collapsed.value}
          />
        </Transition>
      </div>
    );
  },
});
