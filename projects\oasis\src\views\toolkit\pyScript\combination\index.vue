<template>
  <InstructionsTab>
    <template #afterTab>
      <div class="ml-4 self-end text-xs">
        将元件配置成指令，指令会显示在Oasis-指令集的指令商店中，供用户下载和运行。
      </div>
    </template>
    <template #filter>
      <div class="ml-2 mt-4 flex items-center gap-2 font-bold">
        <div> 指令类型: </div>
        <TagSelect v-model="filterState.type" :options="tagInfo?.typeList" @change="getList" />
      </div>
      <div class="ml-2 mt-4 flex items-center gap-2 font-bold">
        <div> 指令作者: </div>
        <TagSelect
          v-model="filterState.authorID"
          :options="tagInfo?.authorList"
          :fieldNames="{ label: 'nickName', value: 'ID' }"
          @change="getList"
        >
          <template #label="{ item }">
            {{ formatNickName(item) }}
          </template>
        </TagSelect>
      </div>
      <div class="ml-2 mt-4 flex items-center gap-2 font-bold">
        <div> 适用项目: </div>
        <TagSelect
          v-model="filterState.projectIDs"
          :options="showProjectList"
          isMultiple
          :fieldNames="{ label: 'name', value: 'ID' }"
          @change="getList"
        />
      </div>
    </template>
    <div :class="prefixCls">
      <ACard v-for="item in combinationList" :key="item.ID" :class="`${prefixCls}__card`">
        <div class="flex">
          <div>
            <div class="flex items-center">
              <div
                class="h-4 w-4 rounded"
                :style="{
                  backgroundColor:
                    defaultColorList[item.marker && item.marker > 0 ? item.marker - 1 : 0],
                }"
              />
              <ATypographyText
                class="mx-1 c-FO-Content-Text1 font-bold !max-w-[200px]"
                :content="item.name"
                :ellipsis="{ tooltip: true }"
              />
              <Icon
                v-for="platform in item.platform"
                :key="platform"
                :icon="PyScriptPlatformOptions.find((e) => e.value === platform)?.icon"
                class="mr-1"
                color="#777"
                size="12"
              />
              <div class="text-[#777]">
                v{{ item.version }}.0
              </div>
            </div>
            <div class="my-1 flex flex-wrap items-center gap-2 c-FO-Content-Text2">
              <div class="border border-gray-400 rounded px-1">
                {{ item.type }}
              </div>
              <div v-if="item.toAllProjects" class="border border-gray-400 rounded px-1">
                通用
              </div>
              <div
                v-for="project in item.projects"
                v-else
                :key="project.ID"
                class="border border-gray-400 rounded px-1"
              >
                {{ project.name }}
              </div>
              <div class="border border-gray-400 rounded px-1">
                {{ formatNickName(item.author) }}
              </div>
            </div>
            <div class="flex flex-wrap items-center overflow-x-auto">
              <div
                v-for="(com, i) in item.allComponents"
                :key="i"
                :class="`${prefixCls}__card-component`"
              >
                {{ i + 1 }}. {{ com.name }}
                <div
                  v-if="com.isHistory && canManage(item)"
                  :class="`${prefixCls}__card-component-dot`"
                />
              </div>
            </div>
          </div>
          <div class="ml-4 flex items-center self-start">
            <a-button
              v-if="canManage(item)"
              v-tippy="'复制指令'"
              type="text"
              class="!px-1 !py-0"
              size="small"
              @click.stop="handleCopy(item)"
            >
              <Icon icon="icon-park-outline:copy" />
            </a-button>
            <a-button
              v-if="canManage(item)"
              v-tippy="'编辑指令'"
              type="text"
              class="!px-1 !py-0"
              size="small"
              @click.stop="handleEdit(item)"
            >
              <Icon icon="icon-park-outline:edit" />
            </a-button>
          </div>
        </div>
      </ACard>
      <ACard :hoverable="true" :class="`${prefixCls}__add`" @click="handleAdd">
        <Icon icon="icon-park-outline:plus" :class="`${prefixCls}__add-icon`" :size="25" />
      </ACard>
    </div>

    <CombinationDrawer @register="registerDrawer" @success="handleSuccess" />
  </InstructionsTab>
</template>

<script lang="ts" setup name="PythonScriptCombination">
import { Card as ACard, TypographyText as ATypographyText } from 'ant-design-vue';
import { cloneDeep, omit } from 'lodash-es';
import { computed, onBeforeMount, reactive, ref } from 'vue';
import { PyScriptPlatformOptions } from '../component/component.data';
import InstructionsTab from '../tab.vue';
import CombinationDrawer from './CombinationDrawer.vue';
import type {
  PyScriptCombinationListItem,
  PyScriptCombinationTagsItem,
} from '/@/api/page/model/pyScriptModel';
import type { ProjectListItem } from '/@/api/page/model/systemModel';
import {
  getPyScriptCombinationListByPage,
  getPyScriptCombinationTags,
} from '/@/api/page/pyScript';
import { defaultColorList } from '/@/components/ColorPopover';
import { useDrawer } from '/@/components/Drawer';
import Icon from '/@/components/Icon';
import TagSelect from '/@/components/TagSelect';
import { formatNickName } from '/@/hooks/system/useUserList';
import { useDesign } from '/@/hooks/web/useDesign';
import { useUserStore } from '/@/store/modules/user';

const { prefixCls } = useDesign('python-script-combination');

const [registerDrawer, { openDrawer }] = useDrawer();
const userStore = useUserStore();
const combinationList = ref<PyScriptCombinationListItem[]>([]);
const filterState = reactive<{
  type?: string;
  authorID?: number;
  projectIDs?: number[];
  toAllProjects?: boolean;
}>({
  type: undefined,
  authorID: undefined,
  projectIDs: undefined,
  toAllProjects: undefined,
});
const tagInfo = ref<PyScriptCombinationTagsItem>();

async function getTagInfo() {
  const { tags } = await getPyScriptCombinationTags();
  tagInfo.value = tags;
}

const showProjectList = computed(() => {
  const general: ProjectListItem = {
    ID: 0,
    name: '通用',
  };
  return [general].concat(tagInfo.value?.projectList || []);
});

function getComponentList(item: PyScriptCombinationListItem[]) {
  return (
    item?.map((e) => {
      const allComponents = e.configJson?.scripts?.map((sc) =>
        cloneDeep(e.components?.find((c) => c.name === sc[0])),
      );
      return {
        ...e,
        allComponents,
      } as PyScriptCombinationListItem;
    }) || []
  );
}

async function getList() {
  filterState.toAllProjects = !!filterState.projectIDs?.includes(0);
  const { list } = await getPyScriptCombinationListByPage(
    {
      page: 1,
      pageSize: 999,
      ...omit(filterState, ['projectIDs']),
    },
    filterState.projectIDs?.filter((e) => e !== 0),
  );
  combinationList.value = getComponentList(list);
}

function canManage(item: PyScriptCombinationListItem) {
  return item.authorID === userStore.getUserInfo.ID || userStore.isSuperAdmin;
}

function handleAdd() {
  openDrawer(true, { isUpdate: false });
}

function handleEdit(record: PyScriptCombinationListItem) {
  openDrawer(true, { isUpdate: true, record });
}

function handleCopy(record: PyScriptCombinationListItem) {
  openDrawer(true, { isCopy: true, record });
}

function handleSuccess() {
  getList();
  getTagInfo();
}

onBeforeMount(() => {
  handleSuccess();
});
</script>

<style lang="less">
@prefix-cls: ~'hypergryph-python-script-combination';
.@{prefix-cls} {
  display: flex;
  flex-wrap: wrap;
  margin: 20px;
  padding: 16px 16px 0 16px;
  border-radius: 8px;
  background-color: @FO-Container-Fill1;

  &__card {
    width: fit-content;
    max-width: 100%;
    margin: 0 16px 16px 0;
    margin-bottom: 16px;
    border-radius: 5px;
    background-color: @report-card-background;
    border-color: @FO-Container-Stroke1;

    & > .ant-card-body {
      padding: 8px 12px !important;
    }

    &-component {
      position: relative;
      margin-top: 4px;
      margin-right: 12px;
      padding: 4px 12px;
      border-radius: 5px;
      background-color: @FO-Brand-Primary-Default;
      color: @FO-Content-Components1;
      white-space: nowrap;

      &:not(:last-of-type) {
        &::before {
          content: '';
          position: absolute;
          z-index: 1;
          top: 50%;
          right: -12px;
          width: 12px;
          height: 1px;
          background-color: @FO-Brand-Primary-Default;
        }
      }

      &-dot {
        position: absolute;
        z-index: 2;
        top: 4px;
        right: 4px;
        width: 6px;
        height: 6px;
        border-radius: 100%;
        background-color: #ffda55;
      }
    }
  }

  &__add {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 106px;
    height: 106px;
    overflow: hidden;
    border-radius: 5px;
    background-color: @report-card-background;
  }
}
</style>
