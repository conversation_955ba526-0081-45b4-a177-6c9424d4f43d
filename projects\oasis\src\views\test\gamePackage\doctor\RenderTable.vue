<template>
  <VxeGrid v-bind="gridOptions" />
</template>

<script setup lang="ts" generic="DataType extends Record<string, any>">
import { computed } from 'vue';
import { type VxeGridProps, type VxeGridPropTypes, VxeGrid } from 'vxe-table';

const props = defineProps<{
  tableData: DataType[] | undefined;
  columns: VxeGridPropTypes.Column<DataType>[];
  loading?: boolean;
}>();

const gridOptions = computed<VxeGridProps<DataType>>(() => ({
  height: 600,
  border: true,
  stripe: true,
  sortConfig: { multiple: true },
  showHeaderOverflow: true,
  virtualXConfig: { enabled: false },
  rowConfig: { isHover: true },
  columnConfig: { resizable: true },
  virtualYConfig: { enabled: true, gt: 0 },
  loading: props.loading,
  columns: props.columns,
  data: props.tableData,
}));
</script>
