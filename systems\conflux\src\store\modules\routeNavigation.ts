import { defineStore } from 'pinia';
import { useMicroAppInject, useRouteNavigationCtx } from '@hg-tech/oasis-common';

/**
 * 处理并调用父路由的授权异常处理
 */
export const useRouteNavigationStore = defineStore('RouteNavigation', () => {
  const { data, loading } = useMicroAppInject(useRouteNavigationCtx);

  return {
    onUnauthorized(msg?: string) {
      data.value?.onUnauthorized?.(msg);
    },
    onForbidden(msg?: string) {
      data.value?.onForbidden?.(msg);
    },
    loading,
  };
});
