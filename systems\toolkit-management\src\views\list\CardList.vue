<template>
  <div class="toolkit-card-list">
    <Spin v-if="dataList?.length" :spinning="loading">
      <div class="toolkit-card-list__card-list">
        <div v-for="item in dataList" :key="item.ID" class="toolkit-card-list__card-list-item" @click="() => goToDetail(item.ID)">
          <div class="toolkit-card-list__card-list-item-content">
            <img :src="preprocessFilePath(item.icon)" alt="icon" class="h-[80px] w-[80px] rounded-2xl">
            <div class="toolkit-card-list__card-list-item-content-text">
              <div class="w-[300px] flex">
                <EllipsisText class="font-bold">
                  {{ item.name }}
                </EllipsisText>
              </div>
              <div class="flex text-xs">
                <div>
                  版本:<b>{{ item?.latestVersion?.version }}</b>
                </div>
                <div class="ml-2">
                  大小:<b>{{
                    formatKBSize(item.latestVersion ? item.latestVersion.sizeKB : 0)
                  }}</b>
                </div>
                <div class="ml-2">
                  发布:<b>{{ dayjs(item.UpdatedAt).format('MM/DD HH:mm') }}</b>
                </div>
              </div>
              <div class="w-[300px] flex gap-[4px] flex-items-center">
                <RenderMaybeVNode
                  v-for="(p) in item.platforms"
                  :key="p"
                  :nodes="platformInfoMap[p]?.icon"
                />
                <div class="h-[100%] border rounded-1 p-[2px] text-xs">
                  {{ item?.project?.name ? item?.project?.name : '通用' }}
                </div>
              </div>

              <div class="text-secondary flex items-center text-xs">
                <EllipsisText class="!text-xs" :lines="1">
                  {{ item.description || '-' }}
                </EllipsisText>
              </div>
            </div>
          </div>
          <div class="relative w-[80px] flex flex-col justify-center">
            <div class="h-[32px] flex justify-center flex-items-center">
              <BasicButton
                shape="round"
                type="primary"
                class="flex flex-items-center"
                size="small"
                @click="() => goToDetail(item.ID)"
              >
                <Icon :icon="CharmDownload" />
                <span class="font-bold !m-0">详情</span>
              </BasicButton>
            </div>
          </div>
        </div>
      </div>
    </Spin>
    <Empty v-else class="my-[40px]" />
  </div>
</template>

<script lang="ts" setup>
import { Empty, Spin } from 'ant-design-vue';
import dayjs from 'dayjs';
import { useRouter } from 'vue-router';
import { Icon } from '@iconify/vue';
import CharmDownload from '@iconify-icons/charm/download';
import { BasicButton, formatKBSize } from '@hg-tech/oasis-common/deprecated';
import { EllipsisText, PlatformEnterPoint, preprocessFilePath, RenderMaybeVNode } from '@hg-tech/oasis-common';
import { platformInfoMap } from '../configs.tsx';
import type { ToolkitListItem } from '../../api';

const props = defineProps<{
  dataList?: ToolkitListItem[];
  loading?: boolean;
}>();

const router = useRouter();

function goToDetail(id: ToolkitListItem['ID']) {
  router.push({
    name: PlatformEnterPoint.ToolkitDetail,
    params: { id },
  });
}
</script>

<style lang="less" scoped>
@import (reference) '@hg-tech/forgeon-style/vars.less';

.toolkit-card-list {
  overflow: auto;

  &__card-list {
    padding: 16px;
    overflow: auto;
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(500px, 1fr));
    gap: 12px;

    &-item {
      display: flex;
      position: relative;
      padding: 12px;
      transition: all 0.3s ease-in-out;
      border: 2px solid var(--FO-Container-Stroke1);
      border-radius: 20px;
      cursor: pointer;
      user-select: none;

      &-content {
        display: flex;
        flex: 1;
        align-items: center;
        width: 0;

        &-text {
          margin: 0 10px;
          line-height: 24px;
        }
      }
    }
  }
}
</style>
