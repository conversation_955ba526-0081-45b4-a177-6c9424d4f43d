<template>
  <div :class="prefixCls">
    <ACollapse>
      <ACollapsePanel key="1" header="权限组">
        <div v-for="group in groupList" :key="group.ID" class="my-3 flex items-center pr-80px">
          <div class="w-80px">
            {{ group.name }}
          </div>
          <div class="flex-1">
            <Member :group="group" :is-edit-mode="isEditMode" />
          </div>
        </div>
      </ACollapsePanel>
    </ACollapse>
  </div>
</template>

<script lang="ts" setup name="TrackingToolGroups">
import { Collapse as ACollapse } from 'ant-design-vue';
import { ref, watchEffect } from 'vue';
import Member from './member.vue';
import type { TrackingToolGroupsListItem } from '/@/api/page/model/trackingModel';
import { getTrackingToolGroupsList } from '/@/api/page/tracking';
import { useUserList } from '/@/hooks/system/useUserList';
import { useDesign } from '/@/hooks/web/useDesign';

const props = defineProps({
  toolID: {
    type: Number,
    required: true,
  },
  isEditMode: {
    type: Boolean,
    default: false,
  },
});
const { prefixCls } = useDesign('tracking-tool-groups');
const ACollapsePanel = ACollapse.Panel;
const groupList = ref<TrackingToolGroupsListItem[]>([]);
const { getUserList } = useUserList();

getUserList();

async function getGroupList() {
  const { groups } = await getTrackingToolGroupsList({ toolID: props.toolID });

  groupList.value = groups || [];
}

watchEffect(() => {
  // toolID或isEditMode变化时，重新获取数据
  if (props.toolID && typeof props.isEditMode === 'boolean') {
    getGroupList();
  }
});
</script>

<style lang="less">
@prefix-cls: ~'hypergryph-tracking-tool-groups';
.@{prefix-cls} {
  margin: 0 200px 0 16px;
}
</style>
