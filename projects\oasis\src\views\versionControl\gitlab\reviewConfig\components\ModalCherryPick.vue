<template>
  <Modal
    width="700px"
    :open="show"
    :maskClosable="false"
    :destroyOnClose="true"
    :centered="true"
    :showFooter="true"
    :afterClose="modalDestroy"
    @ok="handleSubmit"
    @cancel="() => modalCancel()"
  >
    <template #title>
      <div class="w-full flex justify-center">
        cherry-pick配置
      </div>
    </template>
    <Spin :spinning="loadingCherrypickInfo">
      <div class="my-[24px] flex flex-col items-center c-FO-Content-Text2">
        <div>当merge request在飞书卡片或DevGuard中通过时，</div>
        <div>会自动将本次提交内容cherry-pick到指定分支中。</div>
      </div>
      <BasicForm class="px-20px" @register="registerForm">
        <template #targets="{ model, field }">
          <div class="font-bold">
            将提交至该分支的内容cherry-pick到这些分支
          </div>
          <div class="flex items-center">
            <div class="w-0 flex-1">
              <Select v-model:value="model[field]" :loading="loadingBranchList" placeholder="请选择分支" mode="multiple">
                <SelectOption
                  v-for="(item, index) in branchList"
                  :key="index"
                  :value="item.name"
                >
                  {{ item.name }}
                </SelectOption>
              </Select>
            </div>
            <div class="ml-3">
              <BasicButton
                size="small"
                shape="round"
                type="primary"
                danger
                @click="model[field] = []"
              >
                清空分支
              </BasicButton>
            </div>
          </div>
        </template>
      </BasicForm>
    </Spin>
  </Modal>
</template>

<script lang="tsx" setup>
import { computed, watch } from 'vue';
import { Modal, Select, SelectOption, Spin } from 'ant-design-vue';
import { type ModalBaseProps, useLatestPromise } from '@hg-tech/utils-vue';
import { BasicForm, useForm } from '../../../../../components/Form/index';
import {
  type CherrypickInfo,
  addCherryPicks,
  getAllBranchByRepositories,
  getCherryPicksList,
  updateCherryPicks,
} from '../../../../../api/page/gitlab';
import { useUserStore } from '../../../../../store/modules/user';
import type { BranchesBaseItemListItem } from '../../../../../api/page/model/gitlabModel';
import { BasicButton } from '../../../../../components/Button';

const props = defineProps<ModalBaseProps & {
  repoID?: number;
  branchInfo?: BranchesBaseItemListItem;
}>();

const userStore = useUserStore();
const [registerForm, { validate, setFieldsValue }] = useForm({
  labelWidth: 560,
  schemas: [
    {
      label: '',
      field: 'targets',
      component: 'Select',
      slot: 'targets',
    },

    {
      label: (
        <div>
          <b>
            cherry-pick创建方式
          </b>
        </div>
      ),
      field: 'mergeType',
      component: 'RadioGroup',
      required: true,
      defaultValue: 1,
      componentProps: {
        options: [
          { label: '直接合并到分支', value: 1 },
          { label: '新建merge request', value: 2 },
        ],
      },
    },
  ],
  showActionButtonGroup: false,
  layout: 'vertical',
  baseColProps: { span: 23 },
});

const { data: cherrypickInfoRes, execute: fetchCherrypickInfo, loading: loadingCherrypickInfo } = useLatestPromise((pId: number, rId: number, bId: number) => getCherryPicksList(pId, {
  page: 1,
  pageSize: 1,
  repoID: rId,
  branchID: bId,
}));
const cherryPickInfo = computed(() => cherrypickInfoRes.value?.list?.[0]);
watch([() => userStore.getProjectId, () => props.repoID, () => props.branchInfo?.ID], async ([pId, rId, bId]) => {
  if (pId && rId && bId) {
    await fetchCherrypickInfo(pId, rId, bId);
  }
}, { immediate: true });

const { data: branchListRes, execute: fetchBranchList, loading: loadingBranchList } = useLatestPromise(getAllBranchByRepositories);
const branchList = computed(() => branchListRes?.value?.list?.filter((item) => item.name !== props.branchInfo?.name));
watch([() => userStore.getProjectId, () => props.repoID], async ([pId, rId]) => {
  if (pId && rId) {
    await fetchBranchList(pId, { repoID: rId });
    const targets = cherryPickInfo.value?.targets.filter((value) => branchList.value?.map((item) => item?.name).includes(value));
    setFieldsValue({
      targets: targets || [],
      mergeType: cherryPickInfo.value?.mergeType,
    });
  }
}, { immediate: true });

async function handleSubmit() {
  const values = await validate();

  const submitData: CherrypickInfo = {
    repoID: props.repoID,
    branchID: props.branchInfo?.ID,
    ...values,
  };

  if (cherryPickInfo.value?.ID) {
    // 更新
    await updateCherryPicks(userStore.getProjectId, cherryPickInfo.value.ID, submitData);
  } else {
    // 新增
    await addCherryPicks(userStore.getProjectId, submitData);
  }

  props.modalConfirm();
}
</script>
