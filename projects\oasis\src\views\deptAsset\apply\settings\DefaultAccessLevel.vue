<template>
  <div class="default-access-level">
    <div class="flex items-center justify-between">
      <div>部门设备默认配置</div>
      <AButton v-if="item.ID !== editingId" :disabled="deptAssetStore.isEdit && (deptAssetStore.getEditingId !== item.ID || deptAssetStore.getEditType !== 'accessLevel')" @click="() => handleEdit(item)">
        <Icon :icon="EditIcon" />
        编辑
      </AButton>
      <template v-else>
        <div class="flex items-center gap-2">
          <AButton @click="() => handleCancel()">
            取消
          </AButton>
          <AButton type="primary" @click="() => handleSave(item)">
            保存
          </AButton>
        </div>
      </template>
    </div>
    <div class="mt flex flex-col rounded-md bg-FO-Container-Fill2 p-4">
      <BasicForm v-if="item.ID === editingId" :schemas="accessLevelFormSchema" @register="registerForm" />
      <div v-else>
        <div class="flex gap-2 rounded-md">
          <div class="w-120px">
            默认流通级别
          </div>
          <div class="min-w-0 flex flex-1 flex-col gap-2">
            {{ getAccessLevel() }}
          </div>
        </div>
        <div v-if="accessLevel.accessLevel === DeviceAccessLevelEnum.DEPT" class="mt-2 flex items-center gap-2">
          <div class="w-120px">
            默认部门范围
          </div>
          <div class="min-w-0 flex flex-1 flex-col gap-2">
            <div class="flex flex-wrap gap-1">
              <template v-for="dept in accessLevel?.deptIds" :key="dept">
                <div
                  v-if="dept"
                  v-tippy="formatDept(dept, deptList, true) !== formatDept(dept, deptList) ? formatDept(dept, deptList, true) : undefined"
                  class="b-1 b-input-border rd-2 px-1"
                >
                  {{ formatDept(dept, deptList) }}
                </div>
              </template>
            </div>
          </div>
        </div>
        <div v-if="accessLevel.accessLevel === DeviceAccessLevelEnum.PROJECT" class="mt-2 flex gap-2">
          <div class="w-120px">
            默认项目范围
          </div>
          <div class="min-w-0 flex flex-1 flex-col gap-2">
            <div class="flex flex-wrap gap-1">
              <template v-for="projectID in accessLevel?.projectIds" :key="projectID">
                <div class="b-1 b-input-border rd-2 px-1">
                  {{ allProjectList?.find(item => item.ID === projectID)?.name || projectID }}
                </div>
              </template>
            </div>
          </div>
        </div>
      </div>
      <div class="flex gap-2">
        <div class="w-120px" />
        <div class="c-FO-Content-Text2">
          当部门下有新增设备时，流通级别和范围将自动更新为默认值
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { type FormSchema, BasicForm, useForm } from '/@/components/Form';
import { Button as AButton, message } from 'ant-design-vue';
import { computed, onMounted, reactive, ref } from 'vue';
import EditIcon from '@iconify-icons/icon-park-outline/edit';
import { type DeviceAdminListItem, DeviceAccessLevelEnum } from '/@/api/page/model/deptAssetModel';
import { Icon } from '/@/components/Icon';
import { accessLevelList } from '../device.data';
import type { DeptListItem, ProjectListItem } from '/@/api/page/model/systemModel';
import { getSimpleProjectListByPage } from '/@/api/page/system';
import { getAllPaginationList } from '/@/hooks/web/usePagination';
import { getDeptDeviceAccessLevelList, setDeptDeviceAccessLevelList } from '/@/api/page/deptAsset';
import { useDeptAssetApply } from '../hook';
import { usedeptAssetStoreWithOut } from '/@/store/modules/deptAsset';

const props = withDefaults(defineProps<{
  item: Partial<DeviceAdminListItem> ;
  deptList: DeptListItem[];
  editingId?: number;
}>(), {
  item: () => ({}),
  deptList: () => [],
  editingId: undefined,
});

const deptAssetStore = usedeptAssetStoreWithOut();
const { formatDept } = useDeptAssetApply();
const [registerForm, { validate, setFieldsValue }] = useForm({
  labelAlign: 'left',
  labelWidth: 120,
  baseColProps: { span: 22 },
  showActionButtonGroup: false,
});

const allProjectList = ref<ProjectListItem[]>([]);

const accessLevel = reactive({
  accessLevel: DeviceAccessLevelEnum.PUBLIC,
  deptIds: [] as number[],
  projectIds: [] as number[],
});

function getAccessLevel() {
  if (accessLevel.accessLevel === DeviceAccessLevelEnum.PUBLIC) {
    return '公共';
  } else if (accessLevel.accessLevel === DeviceAccessLevelEnum.DEPT) {
    return '部门内流通';
  } else if (accessLevel.accessLevel === DeviceAccessLevelEnum.PROJECT) {
    return '项目内流通';
  } else if (accessLevel.accessLevel === DeviceAccessLevelEnum.UNAVAILABLE) {
    return '不可借用';
  }
}
const accessLevelFormSchema = computed(() => [
  {
    label: '默认流通级别',
    field: 'accessLevel',
    component: 'Select',
    defaultValue: accessLevel.accessLevel || DeviceAccessLevelEnum.PUBLIC,
    componentProps: {
      options: accessLevelList,
      onChange: (value: number) => {
        if (value === DeviceAccessLevelEnum.DEPT) {
          setFieldsValue({
            deptIds: [props.item.deptID],
          });
        }
      },
    },
    required: true,
  },
  {
    label: '默认部门范围',
    field: 'deptIds',
    component: 'TreeSelect',
    defaultValue: accessLevel.deptIds,
    componentProps: {
      showSearch: false,
      showArrow: true,
      treeData: props.deptList,
      placeholder: '请选择部门范围',
      fieldNames: {
        label: 'name',
        key: 'ID',
        value: 'ID',
      },
      treeNodeLabelProp: 'orgPath',
      treeCheckable: true,
      showCheckedStrategy: 'SHOW_PARENT',
      maxTagCount: 3,
    },
    ifShow: ({ values }) => {
      return values?.accessLevel === DeviceAccessLevelEnum.DEPT;
    },
    required: true,
  },
  {
    label: '默认项目范围',
    field: 'projectIds',
    component: 'Select',
    defaultValue: accessLevel.projectIds,
    componentProps: {
      mode: 'multiple',
      optionFilterProp: 'name',
      options: allProjectList.value,
      placeholder: '请选择项目范围',
      fieldNames: {
        label: 'name',
        key: 'ID',
        value: 'ID',
      },
      maxTagCount: 3,
    },
    ifShow: ({ values }) => {
      return values?.accessLevel === DeviceAccessLevelEnum.PROJECT;
    },
    required: true,
  },
] as FormSchema[]);

async function getAllProjectList() {
  const { list } = await getAllPaginationList(getSimpleProjectListByPage);
  allProjectList.value = list || [];
}

const editingId = ref<number>();

function handleEdit(item: Partial<DeviceAdminListItem>) {
  editingId.value = item.ID;
  deptAssetStore.setIsEdit(true);
  deptAssetStore.setEditingId(item.ID);
  deptAssetStore.setEditType('accessLevel');
}

async function handleSave(item: Partial<DeviceAdminListItem>) {
  // 保存
  if (!item.deptID) {
    return;
  }
  const values = await validate();

  const res = await setDeptDeviceAccessLevelList({
    deptID: item.deptID,
    accessLevel: values?.accessLevel,
    deptIds: values?.deptIds,
    projectIds: values?.projectIds,
  });

  if (res?.code !== 7) {
    editingId.value = undefined;
    deptAssetStore.setIsEdit(false);
    deptAssetStore.setEditingId(undefined);
    accessLevel.accessLevel = values?.accessLevel;
    accessLevel.deptIds = values?.deptIds;
    accessLevel.projectIds = values?.projectIds;
    message.success('配置成功');
  }
}

function handleCancel() {
  editingId.value = undefined;
  deptAssetStore.setIsEdit(false);
  deptAssetStore.setEditingId(undefined);
}
onMounted(async () => {
  if (!props.item.deptID) {
    return;
  }
  await getAllProjectList();
  const { list } = await getAllPaginationList((p) => getDeptDeviceAccessLevelList({
    ...p,
    deptID: props.item.deptID || 0,
  }));
  if (list?.length) {
    accessLevel.accessLevel = list[0].accessLevel;
    accessLevel.deptIds = list[0].deptIds;
    accessLevel.projectIds = list[0].projectIds;
  }
});
</script>

<style lang="less">
.default-access-level .ant-form-item-row {
  gap: 0.5rem;
}
.default-access-level .ant-form-item {
  margin-bottom: 0.5rem !important;
}
</style>
