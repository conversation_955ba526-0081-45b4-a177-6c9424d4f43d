<template>
  <InstructionsTab curTab="InstructionComponents">
    <template #afterTab>
      <div class="text-xs ml-4 self-end">
        使用 Oasis-指令集-工作台 配置并上传元件，用以组合成各种方便操作的指令。
      </div>
    </template>
    <template #filter>
      <div class="flex font-bold ml-2 mt-4 gap-2 items-center">
        <div> 元件作者: </div>
        <TagSelect
          v-model="filterState.authorID"
          :options="tagInfo?.authorList"
          :fieldNames="{ label: 'nickName', value: 'ID' }"
          @change="getComponentList(filterState)"
        >
          <template #label="{ item }">{{ formatNickName(item) }}</template>
        </TagSelect>
      </div>
    </template>
    <div :class="prefixCls">
      <div v-for="item in componentList" :key="item.ID" :class="`${prefixCls}__card`">
        <div
          :class="`${prefixCls}__card-input`"
          :disabled="(getSortList(item.configJson?.input)?.length || 0) < 3"
        >
          <div class="flex">
            <span class="mr-3 min-w-28px">输入</span>
            <template v-for="{ name, type } in getSortList(item.configJson?.input)" :key="name">
              <span :class="`${prefixCls}__card-input-tag`" v-tippy="type">
                {{ name }}
              </span>
            </template>
          </div>
        </div>
        <div :class="`${prefixCls}__card-main`">
          <div>
            <div class="flex items-center">
              <a-typography-text
                :content="item.name"
                :ellipsis="{ tooltip: true }"
                class="font-bold text-lg !max-w-160px !c-FO-Content-Components1"
              />
              <div class="c-FO-Content-Components1">
                <Icon
                  v-for="platform in item.platform"
                  :key="platform"
                  :icon="PyScriptPlatformOptions.find((e) => e.value === platform)?.icon"
                  class="mr-1"
                  size="12"
                />
              </div>
              <div class="c-FO-Content-Components1">v{{ item.version }}.0</div>
            </div>
            <div class="border-1 b-FO-Content-Components1 c-FO-Content-Components1 rounded w-fit px-1 mt-2px mb-4px">
              {{ formatNickName(item.author) }}
            </div>
          </div>

          <div class="flex justify-center">
            <a-button
              type="text"
              @click.stop="handleEdit(item)"
              class="!c-FO-Content-Components1 !px-1"
              title="编辑元件"
              size="small"
              v-if="item.authorID === userStore.getUserInfo.ID || userStore.isSuperAdmin"
            >
              <Icon icon="icon-park-outline:edit" />
            </a-button>
            <a-button
              @click="downloadByUrl({ url: item.scriptURL! })"
              type="text"
              title="下载文件"
              class="!px-1"
              size="small"
            >
              <Icon icon="ic:round-download" size="20" color="white" />
            </a-button>
          </div>
        </div>
        <div
          :class="`${prefixCls}__card-output`"
          :disabled="(getSortList(item.configJson?.output)?.length || 0) < 3"
        >
          <div class="flex">
            <span class="mr-3 min-w-28px">输出</span>
            <template v-for="{ name, type } in getSortList(item.configJson?.output)" :key="name">
              <span :class="`${prefixCls}__card-output-tag`" v-tippy="type">
                {{ name }}
              </span>
            </template>
          </div>
        </div>
      </div>
      <div :hoverable="true" :class="`${prefixCls}__add`" @click="handleAdd">
        <Icon icon="icon-park-outline:plus" :class="`${prefixCls}__add-icon`" :size="50" />
      </div>
    </div>
    <ComponentModal @register="registerModal" @success="handleSuccess" />
  </InstructionsTab>
</template>

<script lang="ts" setup name="PythonScriptComponent">
  import { TypographyText as ATypographyText } from 'ant-design-vue';
  import { onBeforeMount, reactive, ref } from 'vue';
  import InstructionsTab from '../tab.vue';
  import ComponentModal from './ComponentModal.vue';
  import { PyScriptPlatformOptions } from './component.data';
  import { getSortList, usePyScriptComponent } from './hook';
  import {
    PyScriptComponentListItem,
    PyScriptComponentTagsItem,
  } from '/@/api/page/model/pyScriptModel';
  import { getPyScriptComponentTags } from '/@/api/page/pyScript';
  import Icon from '/@/components/Icon';
  import { useModal } from '/@/components/Modal';
  import TagSelect from '/@/components/TagSelect';
  import { formatNickName } from '/@/hooks/system/useUserList';
  import { useDesign } from '/@/hooks/web/useDesign';
  import { useUserStore } from '/@/store/modules/user';
  import { downloadByUrl } from '/@/utils/file/download';

  const { prefixCls } = useDesign('python-script-component');

  const [registerModal, { openModal }] = useModal();
  const userStore = useUserStore();
  const { componentList, getComponentList } = usePyScriptComponent();

  const filterState = reactive<{ authorID?: number }>({
    authorID: undefined,
  });
  const tagInfo = ref<PyScriptComponentTagsItem>();

  const getTagInfo = async () => {
    const { tags } = await getPyScriptComponentTags();
    tagInfo.value = tags;
  };

  const handleAdd = () => {
    openModal(true, { isUpdate: false });
  };

  const handleEdit = (record: PyScriptComponentListItem) => {
    openModal(true, { isUpdate: true, record });
  };

  const handleSuccess = () => {
    getComponentList(filterState);
    getTagInfo();
  };

  onBeforeMount(() => {
    handleSuccess();
  });
</script>

<style lang="less">
@prefix-cls: ~'hypergryph-python-script-component';
  .@{prefix-cls} {
    display: grid;
    grid-gap: 16px;
    grid-template-columns: repeat(auto-fit, 300px);
    margin: 16px;
    padding: 16px;
    border-radius: 5px;
    background-color: @FO-Container-Fill1;

    &__card {
      user-select: none;

      &-input {
        border-radius: 8px 8px 0 0;
      }

      &-output {
        border-radius: 0 0 8px 8px;
      }

      &-input,
      &-output {
        display: flex;
        position: relative;
        align-items: center;
        max-width: 300px;
        min-height: 40px;
        padding: 12px 16px;
        overflow: hidden;
        background-color: @report-card-background;

        &:not(:hover) {
          &::after {
            content: '';
            position: absolute;
            z-index: 1;
            top: 0;
            right: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(to right, transparent 90%, @report-card-background);
          }
        }

        &:not([disabled='true']):hover {
          z-index: 10;
          width: auto;
          max-width: 300px;
          overflow: visible;

          & > div {
            position: absolute;
            top: 0;
            left: 0;
            flex-wrap: wrap;
            padding: 12px 16px;
            border-radius: 8px;
            background-color: @report-card-background;
            box-shadow: 0 0 10px 0 #00000040;
            row-gap: 4px;
          }
        }

        &-tag {
          margin: 0 4px;
          padding: 0 8px;
          border-radius: 8px;
          background-color: @FO-Container-Fill5;
          white-space: nowrap;
          cursor: pointer;
        }
      }

      &-main {
        display: flex;
        flex: 1;
        align-items: center;
        justify-content: space-between;
        padding: 0 8px 0 16px;
        background-color: @FO-Brand-Primary-Default;
        color: @FO-Content-Components1;

        &-file {
          display: flex;
          align-items: center;
          justify-content: center;
          padding: 0 8px;

          & > .ant-typography {
            width: 50px !important;
            margin: 0 !important;
          }
        }
      }
    }

    &__add {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 136px;
      height: 136px;
      overflow: hidden;
      border-radius: 8px;
      background-color: @report-card-background;
    }
  }
</style>
