import type { PermissionAppInfo } from './app.ts';
import type { PermissionBaseRes } from './_common.ts';
import { apiService } from '../services/req.ts';

export interface PermissionCheckPointCategory {
  id?: number;
  appId?: number;
  createAt?: string;
  updateAt?: string;
  name?: string;
  creator?: string;
  position?: number;
  resources?: Omit<PermissionCheckPoint, 'apis'>[];
}

export interface PermissionCheckPoint {
  id?: number;
  created_at?: string;
  updated_at?: string;
  appId?: number;
  name?: string;
  code?: string;
  description?: string;
  categoryId?: PermissionCheckPointCategory['id'];
  creator?: string;
  position?: number;
  apis?: PermissionCheckPointApi[];
}

export interface PermissionCheckPointApi {
  id?: number;
  created_at?: string;
  updated_at?: string;
  appId?: number;
  name?: string;
  method?: string;
  path?: string;
  description?: string;
  creator?: string;
  position?: number;
}

/**
 * 获取权限分类
 */
export const fetchPermissionCheckPointCategory = apiService.GET<
  { appId: PermissionAppInfo['id'];search?: string },
  Record<string, never>,
  PermissionBaseRes<PermissionCheckPointCategory[]>
>(`/api/auth/v1/app/{appId}/categories`);

/**
 * 创建权限分类
 */
export const createPermissionCheckPointCategory = apiService.POST<
  { appId: PermissionAppInfo['id'] },
  Pick<PermissionCheckPointCategory, 'name'>,
  PermissionBaseRes<PermissionCheckPointCategory>
>(`/api/auth/v1/app/{appId}/category`);

/**
 * 编辑权限分类
 */
export const updatePermissionCheckPointCategory = apiService.PUT<
  { appId: PermissionAppInfo['id'];categoryId: PermissionCheckPointCategory['id'] },
  Pick<PermissionCheckPointCategory, 'name'>,
  PermissionBaseRes<PermissionCheckPointCategory>
>(`/api/auth/v1/app/{appId}/category/{categoryId}`);

/**
 * 更新权限分类排序
 */
export const sortPermissionCheckPointCategory = apiService.PUT<
  { appId: PermissionAppInfo['id'] },
  { order?: PermissionCheckPointCategory['id'][] },
  PermissionBaseRes<PermissionCheckPointCategory>
>(`/api/auth/v1/app/{appId}/categories/order`);

/**
 * 删除权限分类
 */
export const deletePermissionCheckPointCategory = apiService.DELETE<
  { appId: PermissionAppInfo['id'];categoryId: PermissionCheckPointCategory['id'] },
  Record<string, never>,
  PermissionBaseRes<PermissionCheckPointCategory>
>(`/api/auth/v1/app/{appId}/category/{categoryId}`);

/**
 * 更新权限分类下资源组
 */
export const updatePermissionCheckPointCategoryRelatePoints = apiService.PUT<
  { appId: PermissionAppInfo['id'];categoryId: PermissionCheckPointCategory['id'] },
  PermissionCheckPoint['id'][],
  PermissionBaseRes
>(`/api/auth/v1/app/{appId}/category/{categoryId}/resource`);

/**
 * 获取资源组列表
 */
export const fetchPermissionCheckPointList = apiService.GET<
  { appId: PermissionAppInfo['id']; search?: string },
  Record<string, never>,
  PermissionBaseRes<PermissionCheckPoint[]>
>(`/api/auth/v1/app/{appId}/resources`);

export interface PermissionCheckPointCreate {
  name?: string;
  code?: string;
  categoryId?: PermissionCheckPointCategory['id'];
  description?: string;
}

/**
 * 创建资源组
 */
export const createPermissionCheckPoint = apiService.POST<
  { appId: PermissionAppInfo['id'] },
  PermissionCheckPointCreate,
  PermissionBaseRes<PermissionCheckPoint>
>(`/api/auth/v2/app/{appId}/resource`);

/**
 * 更新资源组
 */
export const updatePermissionCheckPoint = apiService.PUT<
  { appId: PermissionAppInfo['id'];resourceId: PermissionCheckPoint['id'] },
  PermissionCheckPointCreate,
  PermissionBaseRes<PermissionCheckPoint>
>(`/api/auth/v2/app/{appId}/resource/{resourceId}`);

/**
 * 移动资源组到指定分类下
 */
export const movePermissionCheckPoint = apiService.PUT<
  { appId: PermissionAppInfo['id'] },
  { resourceId: PermissionCheckPoint['id']; toCategoryId: PermissionCheckPointCategory['id'] },
  PermissionBaseRes<PermissionCheckPoint>
>(`/api/auth/v2/app/{appId}/category/move_resource`);

/**
 * 更新资源组下Api
 */
export const updatePermissionCheckPointRelateApis = apiService.PUT<
  { appId: PermissionAppInfo['id'];resourceId: PermissionCheckPoint['id'] },
  PermissionCheckPointApi['id'][],
  PermissionBaseRes
>(`/api/auth/v1/app/{appId}/resource/{resourceId}/api`);

/**
 * 获取资源组详情
 */
export const getPermissionCheckPointDetail = apiService.GET<
  { appId: PermissionAppInfo['id'];resourceId: PermissionCheckPoint['id'] },
  Record<string, never>,
  PermissionBaseRes<PermissionCheckPoint>
>(`/api/auth/v1/app/{appId}/resource/{resourceId}`);

/**
 * 删除资源组
 */
export const deletePermissionCheckPoint = apiService.DELETE<
  { appId: PermissionAppInfo['id']; resourceId: PermissionCheckPoint['id'] },
  Record<string, never>,
  PermissionBaseRes<never>
>(`/api/auth/v1/app/{appId}/resource/{resourceId}`);

/**
 * 获取 API 列表
 */
export const fetchPermissionCheckPointApis = apiService.GET<
  { appId: PermissionAppInfo['id']; search?: string },
  Record<string, never>,
  PermissionBaseRes<PermissionCheckPointApi[]>
>(`/api/auth/v1/app/{appId}/apis`);

/**
 * 获取 API 详情
 */
export const getPermissionCheckPointApiDetail = apiService.GET<
  { appId: PermissionAppInfo['id'];apiId: PermissionCheckPointApi['id'] },
  Record<string, never>,
  PermissionBaseRes<PermissionCheckPointApi>
>(`/api/auth/v1/app/{appId}/api/{apiId}`);

export interface PermissionCheckPointApiCreate {
  name?: string;
  method?: PermissionCheckPointApi['method'];
  path?: string;
  description?: string;
}

/**
 * 创建 API
 */
export const createPermissionCheckPointApi = apiService.POST<
  { appId: PermissionAppInfo['id'] },
  PermissionCheckPointApiCreate,
  PermissionBaseRes<PermissionCheckPointApi>
>(`/api/auth/v1/app/{appId}/api`);

/**
 * 更新 API
 */
export const updatePermissionCheckPointApi = apiService.PUT<
  { appId: PermissionAppInfo['id'];apiId: PermissionCheckPointApi['id'] },
  PermissionCheckPointApiCreate,
  PermissionBaseRes<PermissionCheckPointApi>
>(`/api/auth/v1/app/{appId}/api/{apiId}`);

/**
 * 删除 API
 */
export const deletePermissionApi = apiService.DELETE<
  { appId: PermissionAppInfo['id']; apiId: PermissionCheckPointApi['id'] },
  Record<string, never>,
  PermissionBaseRes
>(`/api/auth/v1/app/{appId}/api/{apiId}`);
