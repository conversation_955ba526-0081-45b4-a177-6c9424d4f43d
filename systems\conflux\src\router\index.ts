import { createRouter, createWebHistory } from 'vue-router';
import type { App } from 'vue';
import { withDocumentTitle } from './modules/withDocumentTitle.ts';
import { withTrack } from './modules/withTrack.ts';
import { ForgeonTitleMap, PlatformEnterPoint, PlatformRoutePath } from '@hg-tech/oasis-common';
import { MergePermission } from '../constants/premission.ts';
import { withPermission } from './modules/withPermission.ts';

export const router = createRouter({
  history: createWebHistory(),
  routes: [
    {
      name: PlatformEnterPoint.Conflux,
      path: PlatformRoutePath.Conflux,
      component: () => import('../views/home/<USER>').then((item) => item.MergeHome),
      meta: {
        title: 'Conflux',
        breadcrumb: [
          { label: ForgeonTitleMap[PlatformEnterPoint.Conflux], name: PlatformEnterPoint.Conflux },
        ],
      },
    },
    {
      name: PlatformEnterPoint.ConfluxHistory,
      path: PlatformRoutePath.ConfluxHistory,
      component: () => import('../views/history/index.tsx').then((item) => item.MergeHistory),
      meta: {
        title: '合并历史',
        breadcrumb: [
          { label: ForgeonTitleMap[PlatformEnterPoint.Conflux], name: PlatformEnterPoint.Conflux },
          { label: '合并历史', name: PlatformEnterPoint.ConfluxHistory },
        ],
        permissionDeclare: {
          any: [MergePermission.ViewMergeHistory],
        },
      },
    },
    {
      name: PlatformEnterPoint.ConfluxTask,
      path: PlatformRoutePath.ConfluxTask,
      component: () => import('../views/task/index.tsx').then((item) => item.MergeTask),
      meta: {
        title: '查看失败的合并',
        breadcrumb: [
          { label: ForgeonTitleMap[PlatformEnterPoint.Conflux], name: PlatformEnterPoint.Conflux },
          { label: '查看失败的合并', name: PlatformEnterPoint.ConfluxTask },
        ],
        permissionDeclare: {
          any: [MergePermission.ViewTask],
        },
      },
    },
  ],
  strict: true,
  scrollBehavior: () => ({ left: 0, top: 0 }),
});

export function setupRouter(app: App<Element>) {
  app.use(router);

  // with plugins
  withPermission(router);
  withDocumentTitle(router);
  withTrack(router);
}
