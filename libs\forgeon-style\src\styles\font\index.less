/* This file is automatically generated. DO NOT EDIT it manually. */

/** font defines  */
:root {
  --FO-Font-B20-Font-Size: 20px;
  --FO-Font-B20-Font-Weight: 600;
  --FO-Font-B20-Line-Height: 28px;
  --FO-Font-B18-Font-Size: 18px;
  --FO-Font-B18-Font-Weight: 600;
  --FO-Font-B18-Line-Height: 26px;
  --FO-Font-B16-Font-Size: 16px;
  --FO-Font-B16-Font-Weight: 600;
  --FO-Font-B16-Line-Height: 24px;
  --FO-Font-R16-Font-Size: 16px;
  --FO-Font-R16-Font-Weight: 400;
  --FO-Font-R16-Line-Height: 24px;
  --FO-Font-B14-Font-Size: 14px;
  --FO-Font-B14-Font-Weight: 600;
  --FO-Font-B14-Line-Height: 22px;
  --FO-Font-R14-Font-Size: 14px;
  --FO-Font-R14-Font-Weight: 400;
  --FO-Font-R14-Line-Height: 22px;
  --FO-Font-R12-Font-Size: 12px;
  --FO-Font-R12-Font-Weight: 400;
  --FO-Font-R12-Line-Height: 16px;
}

.FO-Font-B20 {
  font-size: var(--FO-Font-B20-Font-Size);
  font-weight: var(--FO-Font-B20-Font-Weight);
  line-height: var(--FO-Font-B20-Line-Height);
}

.FO-Font-B18 {
  font-size: var(--FO-Font-B18-Font-Size);
  font-weight: var(--FO-Font-B18-Font-Weight);
  line-height: var(--FO-Font-B18-Line-Height);
}

.FO-Font-B16 {
  font-size: var(--FO-Font-B16-Font-Size);
  font-weight: var(--FO-Font-B16-Font-Weight);
  line-height: var(--FO-Font-B16-Line-Height);
}

.FO-Font-R16 {
  font-size: var(--FO-Font-R16-Font-Size);
  font-weight: var(--FO-Font-R16-Font-Weight);
  line-height: var(--FO-Font-R16-Line-Height);
}

.FO-Font-B14 {
  font-size: var(--FO-Font-B14-Font-Size);
  font-weight: var(--FO-Font-B14-Font-Weight);
  line-height: var(--FO-Font-B14-Line-Height);
}

.FO-Font-R14 {
  font-size: var(--FO-Font-R14-Font-Size);
  font-weight: var(--FO-Font-R14-Font-Weight);
  line-height: var(--FO-Font-R14-Line-Height);
}

.FO-Font-R12 {
  font-size: var(--FO-Font-R12-Font-Size);
  font-weight: var(--FO-Font-R12-Font-Weight);
  line-height: var(--FO-Font-R12-Line-Height);
}
