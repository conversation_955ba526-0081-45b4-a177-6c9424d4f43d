import type { IFetcherApiResult } from '@lancercomet/fetcher';
import type { Ref, UnwrapRef } from 'vue';
import { ref, unref } from 'vue';

import { useNaiveUIApi } from './naive-api.hook';
import CustomError from '../utils/custom-error';
import { PageName } from '@/configs/page-config';
import { router } from '@/plugins/router';
import { useUserBase } from './user-base.hook';
import { LOGIN_STATE } from '../constants/login.constant';
import { safeEncodeURL } from '../utils/url/compressor';

interface UseApiRequestOptions<T, P> {
  request:
  | ((params: P) => Promise<IFetcherApiResult<T>>)
  | ((
    params: P extends any[] ? P[number] : P,
  ) => Promise<IFetcherApiResult<T extends any[] ? T[number] : T>>)[];
  errorText?: string;
  concurrent?: boolean; // 是否并发执行
  limit?: number; // 并发限制
  showErrorMsg?: boolean; // 是否显示错误信息
  showAuthWarn?: boolean; // 是否显示权限警告
  minLoadingTime?: number; // 最小加载时间
}

interface UseApiRequestResult<R, P = undefined> {
  data: Ref<R | null> | Ref<UnwrapRef<R> | null>;
  loading: Ref<boolean>;
  error: Ref<Error | null>;
  msg: Ref<string | null>;
  code: Ref<number | null>;
  mutate: (params?: P) => Promise<R>;
}

function useApiRequest<R, P>(
  options: UseApiRequestOptions<R, P>,
): UseApiRequestResult<R, P> {
  const {
    request,
    concurrent = true,
    limit,
    showAuthWarn = true,
    showErrorMsg = true,
    errorText = '请求发生错误',
    minLoadingTime = 0,
  } = options;

  const { message } = useNaiveUIApi();

  const isSingleRequest = !(
    Array.isArray(request) && typeof request[0] === 'function'
  );
  const data = ref<R | null>(null);
  const loading = ref<boolean>(false);
  const error = ref<Error | null>(null);
  const msg = ref<string>('success');
  const code = ref<number>(0);
  const startDate = ref(new Date().getTime());

  const mutate = async (params?: P | P[]) => {
    startDate.value = new Date().getTime();
    loading.value = true;
    error.value = null;

    try {
      const requests = Array.isArray(request) ? request : [request];
      const paramsArray = (
        Array.isArray(params) ? params : [params]
      ) as P extends any[] ? P : [P];

      const results: {
        data: R | null;
        code: number;
        message: string;
      }[] = [];

      const tasks = requests.map((req, index) => {
        return async () => {
          try {
            const response = await (typeof req === 'function'
              ? req(paramsArray[index])
              : req);
            const {
              data: resultData,
              code: responseCode,
              message: responseMsg,
              status: responseStatus,
            } = response;

            if (responseStatus !== 200 && responseStatus !== 401) {
              throw new CustomError(errorText, true);
            }

            if (response.error) {
              throw new CustomError(response.error.message, true);
            }

            if (!response.error && responseCode !== 0) {
              // 处理错误
              if (responseCode === 7) {
                showAuthWarn && message.warning(
                  `当前接口没有权限，请联系管理员进行申请${responseMsg ? `: ${responseMsg}` : ''}`,
                );
              } else if (responseCode === 1) {
                showAuthWarn && message.warning(
                  `当前登录信息已经过期，即将跳转登录授权页${responseMsg ? `: ${responseMsg}` : ''}`,
                );
                const { loginState } = useUserBase();
                loginState.value = LOGIN_STATE.LOGIN_FAILED;
                router.push({
                  name: PageName.Login,
                  query: {
                    redirect: safeEncodeURL(router.currentRoute.value.fullPath),
                    fullscreen: router.currentRoute.value.query.fullscreen,
                  },
                });
              } else {
                // 是否显示错误信息
                showErrorMsg && message.warning(
                  `【${responseCode}】${errorText} ${responseMsg ? `: ${responseMsg}` : ''}`,
                );
              }
              error.value = new CustomError(
                `【${responseCode}】${errorText} ${responseMsg ? `: ${responseMsg}` : ''}`,
                true,
              );
              msg.value = responseMsg;
              code.value = responseCode;
              results[index] = {
                data: resultData as R,
                code: responseCode,
                message: responseMsg,
              };
              return;
            }

            results[index] = {
              data: resultData as R,
              code: responseCode,
              message: responseMsg,
            };
          } catch (err) {
            error.value = err as CustomError;
            showErrorMsg && message.error(error.value.message || errorText);
            results[index] = {
              data: null,
              code: 500,
              message: (err as Error).message || errorText,
            };
            throw error.value;
          }
        };
      });

      // 并发执行请求并应用并发限制
      const executeConcurrent = async (
        tasks: (() => Promise<void>)[],
        limit?: number,
      ) => {
        if (!limit || limit <= 0) {
          await Promise.all(tasks.map((task) => task()));
        } else {
          const executing: Promise<void>[] = [];

          for (const task of tasks) {
            const promise = task();
            executing.push(promise);

            if (executing.length >= limit) {
              await Promise.race(executing);
              executing.splice(
                executing.findIndex((p) => p === promise),
                1,
              );
            }
          }

          await Promise.all(executing);
        }
      };

      if (concurrent) {
        await executeConcurrent(tasks, limit);
      } else {
        for (const task of tasks) {
          await task();
        }
      }

      if (isSingleRequest) {
        data.value = unref(results[0].data) as UnwrapRef<R> | null;
      } else {
        data.value = unref(
          results.map((result) => result.data),
        ) as UnwrapRef<R> | null;
      }
    } finally {
      if (
        minLoadingTime
        && new Date().getTime() - startDate.value <= minLoadingTime
      ) {
        await new Promise((resolve) =>
          setTimeout(
            resolve,
            minLoadingTime - (new Date().getTime() - startDate.value),
          ),
        );
      }
      loading.value = false;
    }
    return data.value as R;
  };

  return { data, loading, error, msg, code, mutate };
}
export { useApiRequest };
