import type { Router } from 'vue-router';
import { usePermissionStore } from '../../store/modules/permission';
import { store } from '../../store/pinia';
import { useRouteNavigationStore } from '../../store/modules/routeNavigation';

export function withPermission(router: Router) {
  const { checkPermission } = usePermissionStore(store);
  const { onForbidden } = useRouteNavigationStore(store);
  router.afterEach(async (to) => {
    if (to.meta.permissionDeclare) {
      const { permissionDeclare } = to.meta;
      const hasPermission = await checkPermission(permissionDeclare);
      if (!hasPermission) {
        onForbidden();
      }
    }
    return true;
  });
}
