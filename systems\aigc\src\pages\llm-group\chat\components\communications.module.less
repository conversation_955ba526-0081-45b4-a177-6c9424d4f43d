@import (reference) '@hg-tech/forgeon-style/vars.less';

.communicationContainer {
  .communicationItem {
    &.active {
      color: @FO-Brand-Primary-Default;
      background-color: @FO-Brand-Tertiary-Active;
      &:hover {
        background-color: @FO-Brand-Tertiary-Active;
      }
    }
    &:hover {
      background-color: @FO-Container-Fill2;
      .communicationItemAction {
        opacity: 1;
      }
    }
    .communicationItemAction {
      opacity: 0;
      padding: 0 4px;
    }
  }
}
