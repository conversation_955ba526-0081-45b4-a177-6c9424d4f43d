<template>
  <Spin :spinning="loading">
    <div v-if="type === 'story'" class="flex flex-col gap-5px">
      <div v-for="(item, index) in storyValue" :id="`story-item-${index}`" :key="index" class="flex items-center">
        <span>若提交前单号状态为 </span>
        <a-select
          v-model:value="item.states"
          placeholder="请选择"
          class="mx w-200px"
          mode="multiple"
          :showArrow="true"
          :fieldNames="fieldNames"
          :options="getStoryOptionsDisabled(item)"
          allowClear
          optionFilterProp="label"
          :dropdownMatchSelectWidth="false"
          :getPopupContainer="getPopupContainer"
          @change="storyChange"
        />
        <span>则完成该状态。</span>
        <a-button
          preIcon="icon-park-outline:minus"
          class="mx b-color-FO-Brand-Primary-Default c-FO-Brand-Primary-Default"
          @click="statesDelete(index)"
        />
        <span class="c-FO-Functional-Error1-Default!">
          {{ error[index] }}
        </span>
      </div>
    </div>
    <div v-else-if="type === 'issue'" class="flex flex-col gap-5px">
      <div v-for="(item, index) in issueValue" :id="`issue-item-${index}`" :key="index" class="flex items-center">
        <span>若提交前单号状态为</span>

        <a-select
          v-model:value="item.fromStates"
          :showArrow="true"
          placeholder="请选择"
          class="mx w-200px"
          mode="multiple"
          :fieldNames="fieldNames"
          allowClear
          :options="getIssueOptionsDisabled(item)"
          optionFilterProp="label"
          :dropdownMatchSelectWidth="false"
          :getPopupContainer="getPopupContainer"
          @change="issueChange"
        />
        <span>则流转为</span>
        <a-select
          v-model:value="item.toState"
          :options="options.filter(i => !item.fromStates?.includes(i.state_key))"
          placeholder="请选择"
          :fieldNames="fieldNames"
          :showArrow="true"
          class="mx w-200px"
          allowClear
          :disabled="!item.fromStates?.length"
          optionFilterProp="label"
          :dropdownMatchSelectWidth="false"
          :getPopupContainer="getPopupContainer"
          @change="issueChange"
        />

        <a-button
          preIcon="icon-park-outline:minus"
          class="mx b-color-FO-Brand-Primary-Default c-FO-Brand-Primary-Default"
          @click="statesDelete(index)"
        />
        <span class="c-FO-Functional-Error1-Default!">
          {{ error[index] }}
        </span>
      </div>
    </div>
    <a-button
      preIcon="icon-park-outline:plus"
      class="b-color-FO-Brand-Primary-Default c-FO-Brand-Primary-Default"
      @click="statesAdd"
    >
      添加条件
    </a-button>
  </Spin>
</template>

<script setup lang='ts'>
import { Spin } from 'ant-design-vue';
import { useVModel } from '@vueuse/core';
import type { IssueItem, StateNode, StoryItem } from '/@/api/page/model/p4Model';
import { computed, onMounted, ref } from 'vue';
import { useLatestPromise } from '@hg-tech/utils-vue';
import { getStateNodes } from '/@/api/page/p4';
import { useUserStore } from '/@/store/modules/user';
import { getPopupContainer } from '../../../../../utils/index.ts';

const props = defineProps<{
  value: IssueItem[] | StoryItem[] ;
  type: 'story' | 'issue';
  fieldNames: { label: string; value: string | number };
  error: string[];
}>();
const emit = defineEmits<{
  (e: 'update:value', value: IssueItem[] | StoryItem[]): void;
  (e: 'storyChange'): void;
  (e: 'issueChange'): void;
}>();

const userStore = useUserStore();
const options = ref<StateNode[]>([]);

const { data: stateNodes, execute: fetchStateNodes, loading } = useLatestPromise(getStateNodes);

const valueProxy = useVModel(props, 'value', emit);

const storyValue = computed(() => {
  if (props.type === 'story') {
    return valueProxy.value as StoryItem[];
  }
  return [];
});

const issueValue = computed(() => {
  if (props.type === 'issue') {
    return valueProxy.value as IssueItem[];
  }
  return [];
});

function storyChange() {
  emit('storyChange');
}
function issueChange() {
  emit('issueChange');
}

// 添加disabled并加上叉号
function getIssueOptionsDisabled(item: IssueItem) {
  return options.value.map((opt) => {
    const currentSelected = item.fromStates || [];
    const otherUsed = issueValue.value
      .filter((i) => i !== item)
      .map((i) => i.fromStates)
      .flat()
      .filter(Boolean);

    return {
      ...opt,
      disabled: (!currentSelected.includes(opt.state_key) && otherUsed.includes(opt.state_key)) || [item.toState].includes(opt.state_key),
    };
  });
}
function getStoryOptionsDisabled(item: StoryItem) {
  return options.value.map((opt) => {
    const currentSelected = item.states || [];
    const otherUsed = storyValue.value
      .filter((i) => i !== item)
      .map((i) => i.states)
      .flat()
      .filter(Boolean);

    return {
      ...opt,
      disabled: !currentSelected.includes(opt.state_key) && otherUsed.includes(opt.state_key),
    };
  });
}
function statesAdd() {
  if (props.type === 'story') {
    const newItem: StoryItem = {
      states: [],
    };
    const newValue = [...(valueProxy.value as StoryItem[]), newItem];
    valueProxy.value = newValue;
    emit('storyChange');
  } else {
    const newItem: IssueItem = {
      fromStates: [],
      toState: null,
    };
    const newValue = [...(valueProxy.value as IssueItem[]), newItem];
    valueProxy.value = newValue;
    emit('issueChange');
  }
}

function statesDelete(index: number) {
  valueProxy.value.splice(index, 1);
}

onMounted(async () => {
  await fetchStateNodes(userStore.getProjectId, props.type);
  options.value = stateNodes.value ?? [];
});
</script>
