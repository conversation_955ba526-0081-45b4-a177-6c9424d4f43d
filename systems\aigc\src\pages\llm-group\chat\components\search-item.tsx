import { ArrowForwardIosRound } from '@/common/components/svg-icons';
import type { ChatMessageSearch } from '@/models/chat';
import { NEllipsis, NIcon, NText } from 'naive-ui';
import { type PropType, defineComponent } from 'vue';

const SearchItem = defineComponent({
  props: {
    info: {
      type: Object as PropType<ChatMessageSearch>,
      required: true,
    },
    showArrow: {
      type: Boolean as PropType<boolean>,
      default: true,
    },
  },
  setup(props) {
    return () => (
      <div class="flex flex-col gap-8px">
        <a class="block flex-c-between" href={props.info.url} target="_blank">
          <div class="flex flex-grow-1 flex-col gap-4px overflow-hidden">
            <NText class="FO-Font-B14 flex">
              <NEllipsis class="flex-1" line-clamp={1} tooltip={{ contentClass: 'max-w-400px max-h-40vh whitespace-pre-wrap', scrollable: true }}>
                {props.info.title}
              </NEllipsis>
              <div class="FO-Font-R12 h-22px w-22px flex items-center justify-center rd-full bg-FO-Brand-Secondary-Hover c-FO-Brand-Primary-Default">
                {props.info.index}
              </div>
            </NText>
            <NText class="FO-Font-R12 c-FO-Content-Text2">
              <NEllipsis line-clamp={2} tooltip={{ contentClass: 'max-w-400px max-h-40vh whitespace-pre-wrap', scrollable: true }}>
                {props.info.content}
              </NEllipsis>
            </NText>
          </div>
          {props.showArrow && (
            <div class="flex-shrink-0">
              <NIcon size={16}>
                <ArrowForwardIosRound />
              </NIcon>
            </div>
          )}
        </a>
      </div>
    );
  },
});

export {
  SearchItem,
};
