<template>
  <Tab :curTab="PlatformEnterPoint.DeviceManagementAdminConfig">
    <div class="m-4 rounded-md bg-FO-Container-Fill1 p-4">
      <BasicTable @register="registerTable">
        <template #toolbar>
          <div class="w-full">
            <AButton type="primary" @click="handleAdd">
              新增管理员
            </AButton>
          </div>
        </template>
        <template #bodyCell="{ column, record }">
          <template v-if="column.dataIndex === 'dept'">
            <ATag v-if="record.dept?.orgPath">
              {{ record.dept?.orgPath?.replace('鹰角>', '') }}
            </ATag>
          </template>
          <template v-else-if="column.dataIndex === 'subAdmins'">
            <EllipsisText v-if="record.subAdmins" class="block">
              {{ record.subAdmins?.map((item: UserInfoModel) => formatNickName(item)).join('; ') || '无' }}
            </EllipsisText>
          </template>
          <template v-else-if="column.dataIndex === 'action'">
            <div class="flex items-center justify-center gap-1">
              <ATooltip title="部门设备管理">
                <AButton type="text" class="px-2" @click="handleDeptDevice(record)">
                  <Icon :icon="CategoryManagementIcon" />
                </AButton>
              </ATooltip>
              <ATooltip title="编辑">
                <AButton type="text" class="px-2" @click="handleEdit(record)">
                  <Icon :icon="EditIcon" />
                </AButton>
              </ATooltip>
              <APopconfirm title="确定删除该管理员吗？" okText="确定" cancelText="取消" @confirm="handleDelete(record)">
                <ATooltip title="删除">
                  <AButton type="text" class="px-2">
                    <Icon :icon="DeleteIcon" />
                  </AButton>
                </ATooltip>
              </APopconfirm>
            </div>
          </template>
        </template>
        <template #emptyText>
          <div class="flex items-center justify-center">
            <AEmpty description="暂无记录" />
          </div>
        </template>
      </BasicTable>

      <AdminModal @register="registerModal" @success="reloadByType" />
      <DeptDeviceModal @register="registerDeptDeviceModal" @success="reloadByType" />
    </div>
  </Tab>
</template>

<script lang="ts" setup>
import type { BasicColumn } from '/@/components/Table';
import { BasicTable, useTable } from '/@/components/Table';
import { Button as AButton, Empty as AEmpty, Popconfirm as APopconfirm, Tag as ATag, Tooltip as ATooltip } from 'ant-design-vue';
import { deleteDeviceAdmin, getDeviceAdminList } from '/@/api/page/deptAsset';
import { useModal } from '/@/components/Modal';
import AdminModal from './AdminModal.vue';
import type { DeviceAdminListItem } from '/@/api/page/model/deptAssetModel';
import { formatNickName } from '/@/hooks/system/useUserList';
import CategoryManagementIcon from '@iconify-icons/icon-park-outline/category-management';
import DeleteIcon from '@iconify-icons/icon-park-outline/delete';
import EditIcon from '@iconify-icons/icon-park-outline/edit';
import { Icon } from '/@/components/Icon';
import DeptDeviceModal from './DeptDeviceModal.vue';
import { onMounted, ref } from 'vue';
import type { DeptListItem } from '/@/api/page/model/systemModel';
import { getDeptList } from '/@/api/page/system';
import { ResultEnum } from '/@/enums/httpEnum';
import Tab from '../components/Tab.vue';
import { PlatformEnterPoint } from '@hg-tech/oasis-common';
import type { UserInfoModel } from '/@/api/sys/model/userModel';

const deptList = ref<DeptListItem[]>([]);

const columns: BasicColumn[] = [
  {
    title: '管理员',
    dataIndex: 'user',
    format: (_, record) => formatNickName(record.user),
    width: 150,
  },
  {
    title: '部门范围',
    dataIndex: 'dept',
    align: 'left',
    width: 300,
  },
  {
    title: '候补审批人',
    dataIndex: 'subAdmins',
    align: 'left',
    width: 300,
  },
  {
    title: '最长借用天数',
    dataIndex: 'maxDay',
    width: 150,
    format: (_, record) => (record.maxDay && record.maxDay !== -1 ? `${record.maxDay}天` : '不限制'),
  },
];

const [registerTable, { reloadByType }] = useTable({
  api: getDeviceAdminList,
  columns,
  inset: true,
  resizeHeightOffset: 36,
  useSearchForm: false,
  showIndexColumn: false,
  bordered: true,
  pagination: {
    pageSize: 20,
    showTotal: () => '',
  },
  actionColumn: {
    width: 130,
    title: '操作',
    dataIndex: 'action',
    fixed: 'right',
  },
});
const [registerModal, { openModal }] = useModal();
const [registerDeptDeviceModal, { openModal: openDeptDeviceModal }] = useModal();

function handleAdd() {
  openModal(true, {
    isUpdate: false,
    deptList: deptList.value,
  });
}

// 获取所有部门列表
async function getDeptTreeList() {
  try {
    const { list } = await getDeptList();
    deptList.value = list || [];
  } catch (error) {
    console.error('获取部门列表失败:', error);
  }
}

function handleEdit(record: DeviceAdminListItem) {
  openModal(true, {
    isUpdate: true,
    record,
    deptList: deptList.value,
  });
}

async function handleDelete(record: DeviceAdminListItem) {
  try {
    const res = await deleteDeviceAdmin(record.ID as number);
    if (res?.code !== ResultEnum.API_ERROR) {
      reloadByType('delete');
    }
  } catch (error) {
    console.error(error);
  }
}

function handleDeptDevice(record: DeviceAdminListItem) {
  openDeptDeviceModal(true, {
    record,
    deptList: deptList.value,
  });
}

onMounted(() => {
  getDeptTreeList();
});
</script>
