<template>
  <Popover
    v-model:open="open"
    trigger="click"
    placement="right"
    :arrow="false"
    :overlayInnerStyle="{ padding: 0 }"
  >
    <template #content>
      <div class="w-320px flex flex-col">
        <div class="flex items-center justify-between px-16px pt-16px">
          <span class="FO-Font-B14 c-FO-Content-Text1">{{ title }}</span>
          <Button
            class="btn-fill-text"
            size="small"
            @click="handleClose"
          >
            <template #icon>
              <CloseIcon class="FO-Font-R12 c-FO-Content-Icon2" />
            </template>
          </Button>
        </div>
        <div class="max-h-160px flex-1 overflow-y-auto">
          <div v-if="loading" class="flex items-center justify-center px-16px py-24px">
            <Spin size="small" />
            <span class="FO-Font-R14 ml-8px c-FO-Content-Text3">加载中...</span>
          </div>
          <div v-else-if="members.length === 0" class="flex items-center justify-center px-16px py-24px">
            <span class="FO-Font-R14 c-FO-Content-Text3">该组无成员</span>
          </div>
          <div v-else class="px-16px pb-16px pt-12px">
            <span class="FO-Font-R14 c-FO-Content-Text2">
              {{ formattedMembersText }}
            </span>
          </div>
        </div>
      </div>
    </template>
    <div @click="handleClick">
      <slot />
    </div>
  </Popover>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import { Button, Popover, Spin } from 'ant-design-vue';
import type { SysUserInfo } from '../../../../api/users.ts';
import CloseIcon from '../../../../assets/icons/fill-close.svg?component';

interface Props {
  title: string;
  members: SysUserInfo[];
  loading?: boolean;
}

interface Emits {
  (e: 'click'): void;
}

const props = withDefaults(defineProps<Props>(), {
  loading: false,
});

const emit = defineEmits<Emits>();
const open = defineModel<boolean>('open');

const formattedMembersText = computed(() => {
  if (props.members.length === 0) {
    return '';
  }
  return props.members.map((member) => formatMemberName(member)).join('、');
});

function formatMemberName(member: SysUserInfo): string {
  return `${member.name}${member.nickname ? ` (${member.nickname})` : ''}`;
}

function handleClick() {
  emit('click');
}

function handleClose() {
  open.value = false;
}
</script>
