@splitpanes-splitter-bg: var(--splitpanes-splitter-bg-color);
@splitpanes-splitter-icon: var(--splitpanes-splitter-icon-color);

.splitpanes {
  .splitpanes-drag-btn(@rotate:0) {
    --svg: url('data:image/svg+xml;charset=utf-8;base64,PHN2ZyB2aWV3Qm94PScwIDAgMTAyNCAxMDI0JyB2ZXJzaW9uPScxLjEnIHhtbG5zPSdodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2Zycgd2lkdGg9JzIwMCcgaGVpZ2h0PScyMDAnPjxwYXRoIGQ9J00zNzIuMTYgMTM5Ljg0YTQ3LjM2IDQ3LjM2IDAgMCAwIDk0LjcyIDAgNDcuMzYgNDcuMzYgMCAwIDAtOTQuNzIgMHogbTE4NC45NiAwYTQ3LjM2IDQ3LjM2IDAgMCAwIDk0LjcyIDAgNDcuMzYgNDcuMzYgMCAwIDAtOTQuNzIgMHogbS0xODQuOTYgMTg0Ljk2YTQ3LjM2IDQ3LjM2IDAgMCAwIDk0LjcyIDAgNDcuMzYgNDcuMzYgMCAwIDAtOTQuNzIgMHogbTE4NC45NiAwYTQ3LjM2IDQ3LjM2IDAgMCAwIDk0LjcyIDAgNDcuMzYgNDcuMzYgMCAwIDAtOTQuNzIgMHpNMzcyLjE2IDUxMmE0Ny4zNiA0Ny4zNiAwIDAgMCA5NC43MiAwIDQ3LjM2IDQ3LjM2IDAgMCAwLTk0LjcyIDB6IG0xODQuOTYgMGE0Ny4zNiA0Ny4zNiAwIDAgMCA5NC43MiAwIDQ3LjM2IDQ3LjM2IDAgMCAwLTk0LjcyIDB6IG0tMTg0Ljk2IDE4NC45NmE0Ny4zNiA0Ny4zNiAwIDAgMCA5NC43MiAwIDQ3LjM2IDQ3LjM2IDAgMCAwLTk0LjcyIDB6IG0xODQuOTYgMGE0Ny4zNiA0Ny4zNiAwIDAgMCA5NC43MiAwIDQ3LjM2IDQ3LjM2IDAgMCAwLTk0LjcyIDB6IG0tMTg0Ljk2IDE4NC45MjhhNDcuMzYgNDcuMzYgMCAwIDAgOTQuNzIgMCA0Ny4zNiA0Ny4zNiAwIDAgMC05NC43MiAweiBtMTg0Ljk2IDBhNDcuMzYgNDcuMzYgMCAwIDAgOTQuNzIgMCA0Ny4zNiA0Ny4zNiAwIDAgMC05NC43MiAweic+PC9wYXRoPjwvc3ZnPg==');

    mask-image: var(--svg);
    mask-repeat: no-repeat;
    mask-size: 100% 100%;
    content: ' ';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 16px;
    height: 20px;
    background-color: @splitpanes-splitter-icon;
    background-size: 16px 20px;
    transform: translate(-50%, -50%) rotate(@rotate);
  }

  &--vertical > &__splitter {
    min-width: 8px !important;
    background-color: @splitpanes-splitter-bg;
    position: relative;

    &::before {
      .splitpanes-drag-btn;
    }
  }

  &--horizontal > &__splitter {
    min-height: 8px !important;
    background-color: @splitpanes-splitter-bg;
    position: relative;

    &::before {
      .splitpanes-drag-btn(90deg);
    }
  }
}
