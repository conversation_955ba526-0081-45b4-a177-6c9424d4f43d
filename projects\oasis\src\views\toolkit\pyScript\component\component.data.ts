import type { BasicColumn, FormSchema } from '/@/components/Table';

export const PyScriptPlatformOptions = [
  {
    label: 'Windows',
    value: 'windows',
    icon: 'ph:windows-logo-fill',
  },
  {
    label: 'Mac',
    value: 'mac',
    icon: 'ph:apple-logo-fill',
  },
];

export const pyParamTypeOptions = [
  {
    label: 'str - 字符串',
    value: 'str',
  },
  {
    label: 'int - 整数',
    value: 'int',
  },
  {
    label: 'listint - 整数列表(使用换行分隔)',
    value: 'listint',
  },
  {
    label: 'liststr - 字符串列表(使用换行分隔)',
    value: 'liststr',
  },
  {
    label: 'bool - 布尔值',
    value: 'bool',
  },
  {
    label: 'float - 浮点数',
    value: 'float',
  },
  {
    label: 'pathfile - 需要用户选择的文件路径',
    value: 'pathfile',
  },
  {
    label: 'pathfolder - 需要用户选择的目录路径',
    value: 'pathfolder',
  },
  {
    label: 'choice - 用户多选一',
    value: 'choice',
  },
];

export const formSchema: FormSchema[] = [
  {
    field: 'scriptURL',
    label: '上传脚本',
    component: 'Upload',
    valueField: 'singleValue',
    rules: [{ required: true, message: '请上传py脚本文件' }],
    componentProps: ({ formModel }) => ({
      valueFormat: 'string',
      maxNumber: 1,
      multiple: false,
      accept: ['.py'],
      onChange: (val) => {
        const splitVal = val.split('/');
        formModel.name = splitVal[splitVal.length - 1]?.replace('.py', '');
      },
    }),
    required: true,
  },
  {
    field: 'name',
    label: '脚本名称',
    component: 'Input',
    componentProps: {
      placeholder: '脚本名称根据文件名自动生成',
      disabled: true,
    },
    required: true,
  },
  {
    field: 'nickName',
    label: '作者',
    component: 'Input',
    componentProps: {
      placeholder: '获取失败',
      disabled: true,
    },
  },
  {
    field: 'platform',
    label: '支持平台',
    component: 'CheckboxGroup',
    componentProps: {
      options: PyScriptPlatformOptions,
    },
    required: true,
  },
  {
    field: 'version',
    label: '版本',
    component: 'InputNumber',
    slot: 'version',
    defaultValue: 1,
  },
];

export const inputColumns: BasicColumn[] = [
  {
    title: '',
    dataIndex: 'sort',
    width: 50,
  },
  {
    title: 'name',
    dataIndex: 'name',
    editRow: true,
    editComponent: 'Input',
    editComponentProps: {
      placeholder: '请输入名称',
    },
    editRule: async (text) => {
      if (!text) {
        return '请输入名称';
      }
      return '';
    },
  },
  {
    title: 'type',
    dataIndex: 'type',
    editRow: true,
    editComponent: 'Select',
    editComponentProps: {
      placeholder: '请选择类型',
      options: pyParamTypeOptions,
    },
    editRule: async (text) => {
      if (!text) {
        return '请选择类型';
      }
      return '';
    },
  },
];

export const inputAddData = {
  name: '',
  type: '',
};
