<template>
  <BasicModal
    v-bind="$attrs"
    :wrap-class-name="prefixCls"
    :after-close="handleClose"
    :width="600"
    :footer="null"
    @register="registerModal"
  >
    <div class="h-300px w-full flex flex-col items-center justify-center">
      <div class="mb-50px text-4xl font-medium">
        您无权限进行该下载
      </div>
      <div class="absolute bottom-40px left-1/3 flex items-center justify-around">
        <div
          class="cursor-pointer rounded-full bg-gray-300 px-4 py-2 c-FO-Content-Text1 hover:bg-gray-400"
          @click="handleClose()"
        >
          确认
        </div>
        <div
          type="primary"
          class="ml-8 cursor-pointer rounded-full bg-gray-300 px-4 py-2 c-FO-Content-Text1 hover:bg-gray-400"
          @click="openContactUrl()"
        >
          联系技术中心
        </div>
      </div>
    </div>
  </BasicModal>
</template>

<script lang="ts" setup>
import { BasicModal, useModalInner } from '/@/components/Modal';
import { useDesign } from '/@/hooks/web/useDesign';
import { CONTACT_URL } from '/@/settings/siteSetting';
import { openWindow } from '/@/utils';

defineOptions({
  name: 'NoPermissionDownloadModal',
});

const { prefixCls } = useDesign('no-permission-download-modal');

const [registerModal, { closeModal }] = useModalInner();

function openContactUrl() {
  openWindow(CONTACT_URL);
}

async function handleClose() {
  closeModal();
}
</script>

<style lang="less">
@prefix-cls: ~'hypergryph-no-permission-download-modal';
// .@{prefix-cls} {
// }
</style>
