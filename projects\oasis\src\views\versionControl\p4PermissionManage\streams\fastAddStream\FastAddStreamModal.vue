<template>
  <BasicModal
    :footer="null"
    :width="1100"
    :class="prefixCls"
    destroyOnClose
    :maskClosable="false"
    @register="registerModal"
    @cancel="handleSuccess"
  >
    <template #title>
      <div :class="`${prefixCls}__steps`">
        <template v-for="(item, index) in stepList" :key="index">
          <div
            :status="item.status"
            :class="`${prefixCls}__steps-item`"
            :isExistStream="!!isExistStream"
            :current="current === index"
          >
            {{ item.title }}
          </div>
          <Icon
            v-if="index !== stepList.length - 1"
            icon="ep:arrow-right-bold"
            class="mx-5 c-FO-Content-Text2"
            :size="14"
          />
        </template>
      </div>
    </template>
    <component
      :is="showStepComponentList[current]"
      v-if="depotID"
      :depotID="depotID"
      :isExistStream="isExistStream"
      :streamList="streamList"
      :streamID="streamID"
      :auditList="auditList"
      :dm01GroupList="dm01GroupList"
      :hasAudit="hasAudit"
      @nextStep="handleNextStep"
      @success="handleSuccess"
      @streamTypeChange="(type:number) => (streamType = type)"
    />
  </BasicModal>
</template>

<script lang="ts" setup>
import { computed, ref } from 'vue';
import {
  DM01StepComponentList,
  DM01StepTitleList,
  stepComponentList,
  stepTitleList,
} from './fastAddStream.data';
import type { StreamOperationListItem, StreamsListItem } from '/@/api/page/model/p4Model';
import type { SwarmReviewProjectsListItem } from '/@/api/page/model/swarmModel';
import { getStreamOperationRecord, recordStreamOperation } from '/@/api/page/p4';
import Icon from '/@/components/Icon';
import { BasicModal, useModalInner } from '/@/components/Modal';
import { useDesign } from '/@/hooks/web/useDesign';
import { useUserStoreWithOut } from '/@/store/modules/user';
import { isNullOrUnDef } from '/@/utils/is';

defineOptions({
  name: 'FastAddStreamModal',
});

const emit = defineEmits(['success', 'register']);

const { prefixCls } = useDesign('fast-add-stream-modal');
const current = ref(0);
const depotID = ref<number>();

const isExistStream = ref(false);
const streamList = ref<StreamsListItem[]>([]);
const streamID = ref<number>();
const auditList = ref<SwarmReviewProjectsListItem[]>([]);
const userStore = useUserStoreWithOut();
const operationList = ref<StreamOperationListItem[]>();
const dm01GroupList = ref<number[]>([]);
const hasAudit = ref<boolean>(false);
const streamType = ref<number>();

const showStepComponentList = computed(() => {
  if (userStore.isDM01) {
    return DM01StepComponentList;
  } else if (streamType.value === 4) {
    // fixme: streamType添加枚举
    return [stepComponentList[0], stepComponentList[stepComponentList.length - 1]];
  } else {
    return stepComponentList;
  }
});

const stepList = computed(() => {
  const list = (userStore.isDM01 ? DM01StepTitleList : stepTitleList).map((item, i) => ({
    title: isExistStream.value && i === 0 ? '添加分支' : item,
    status: i <= current.value ? getOperationStatus(i) : undefined,
  }));

  if (streamType.value === 4) {
    return [list[0], list[list.length - 1]];
  } else {
    return list;
  }
});

async function getOperationList() {
  if (streamID.value) {
    const { list } = await getStreamOperationRecord(userStore.getProjectId, streamID.value);

    operationList.value = list || [];
  }
}

function getOperationStatus(step: number) {
  const status = operationList.value?.find((e) => e.step === step)?.status;

  switch (status) {
    case 0:
      return 'wait';
    case 1:
      return 'finish';
    case 2:
      return 'error';
    default:
      return 'default';
  }
}

const [registerModal, { setModalProps, closeModal }] = useModalInner(async (data) => {
  setModalProps({ confirmLoading: true });
  current.value = !isNullOrUnDef(data.record?.step) ? data.record?.step + 1 : 0;
  streamID.value = data.record?.ID;
  depotID.value = data.depotID;
  isExistStream.value = data.isExistStream;
  streamList.value = (data.streamList || [])?.filter((e: StreamsListItem) => e.streamType !== 4);
  auditList.value = data.auditList;
  dm01GroupList.value = data.dm01GroupList;
  hasAudit.value = data.hasAudit;
  await getOperationList();

  setModalProps({ confirmLoading: false });
});

// 下一步
async function handleNextStep({ id, status }) {
  if (id) {
    streamID.value = id;
  }

  await recordStreamOperation(userStore.getProjectId, streamID.value!, {
    step: current.value,
    operation:
            isExistStream.value && current.value === 0
              ? 'AddBranch'
              : showStepComponentList.value[current.value].name,
    status,
  });

  // 没有失败则继续下一步
  if (status !== 2) {
    current.value += 1;
  }
}

function handleSuccess() {
  emit('success');
  closeModal();
}
</script>

<style lang="less">
@prefix-cls: ~'hypergryph-fast-add-stream-modal';

.@{prefix-cls} {
  &__steps {
    display: flex;
    align-items: center;
    margin-left: 16px;
    font-weight: normal;

    &-item {
      color: @FO-Content-Text2;

      &[current='true'] {
        font-size: 18px;
        font-weight: bolder;
      }

      &[status='default'],
      &[status='finish'] {
        color: @FO-Functional-Warning1-Default;

        &[isExistStream='false'] {
          color: @FO-Functional-Success1-Default;
        }
      }

      &[status='wait'] {
        color: @FO-Content-Text2;
      }

      &[status='error'] {
        color: @FO-Functional-Error1-Default;
      }
    }
  }
}
</style>
