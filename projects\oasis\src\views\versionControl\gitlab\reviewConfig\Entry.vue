<template>
  <ConfigList
    v-if="branchInfo?.reviewEnabled"
    :repoID="depotID"
    :branchInfo="branchInfo"
    @update="refreshBranchInfo"
    @back="handleBack"
  />
  <PresetPage
    v-else-if="branchInfo"
    :repoID="depotID"
    :branchInfo="branchInfo"
    :loading="loadingBranchInfo"
    @update="refreshBranchInfo"
    @back="handleBack"
  />
  <Spin v-else :spinning="loadingBranchInfo">
    <Empty />
  </Spin>
</template>

<script setup lang="ts">
import { computed, watch } from 'vue';
import { Empty, Spin } from 'ant-design-vue';
import { useRouter } from 'vue-router';
import ConfigList from './ConfigList.vue';
import PresetPage from './PresetPage.vue';
import { useLatestPromise } from '@hg-tech/utils-vue';
import { getBranchesByBranchID } from '../../../../api/page/gitlab.ts';
import { useUserStore } from '../../../../store/modules/user.ts';
import { PlatformEnterPoint } from '@hg-tech/oasis-common';

const router = useRouter();
const userStore = useUserStore();
const depotID = Number(router.currentRoute.value.params.id);
const branchID = computed(() => Number(router.currentRoute.value.params.branch_id));

const { data: branchInfo, execute: updateBranchInfo, loading: loadingBranchInfo } = useLatestPromise(async (branchID: number) => {
  const res = await getBranchesByBranchID(userStore.getProjectId, branchID);
  return res?.branch;
});

watch(branchID, () => refreshBranchInfo(), { immediate: true });
function refreshBranchInfo() {
  if (branchID.value) {
    updateBranchInfo(branchID.value);
  }
}

// 页面左侧点击返回链接时的操作
function handleBack() {
  router.push({ name: PlatformEnterPoint.Gitlab });
}

watch(
  () => userStore.getProjectId,
  (v, oldValue) => {
    if (v && v !== oldValue) {
      // 切换项目后返回仓库列表
      handleBack();
    }
  },
);
</script>
