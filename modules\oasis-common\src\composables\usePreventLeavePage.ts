import type { Ref } from 'vue';
import { watch } from 'vue';
import { useRouter } from 'vue-router';
import { Modal } from 'ant-design-vue';
import type { ModalFuncProps } from 'ant-design-vue/es/modal/Modal';

export interface ConfirmOptions extends ModalFuncProps {
  /**
   * 确定按钮回调
   * @returns 是否继续导航
   */
  okFunc?: () => Promise<boolean>;
  /**
   * 取消按钮回调
   * @returns 是否继续导航
   */
  cancelFunc?: () => Promise<boolean>;
}

export function usePreventLeavePage(enable: Ref<boolean>, confirmOptions?: ConfirmOptions) {
  const router = useRouter();
  const tips = confirmOptions?.content ?? confirmOptions?.title ?? '系统不会保存您的修改，是否确定离开？';

  watch(enable, (v, _, onCleanup) => {
    if (v) {
      const handler = (e: BeforeUnloadEvent) => {
        e.preventDefault();

        (e || window.event).returnValue = tips;

        return tips;
      };
      window.addEventListener('beforeunload', handler);

      const offRouterHook = router.beforeEach((to, from, next) => {
        if (confirmOptions) {
          Modal.confirm({
            centered: true,
            ...confirmOptions,
            cancelButtonProps: {
              // @ts-expect-error cancelButtonProps支持class但没有类型定义
              class: 'btn-fill-default',
            },
            onOk() {
              if (confirmOptions.okFunc) {
                return confirmOptions.okFunc().then(
                  (bool) => next(bool ?? true), // 保存成功则继续导航
                  () => next(false), // 保存失败则阻止导航
                );
              } else {
                return next(true);
              }
            },
            onCancel() {
              if (confirmOptions.cancelFunc) {
                return confirmOptions.cancelFunc().then(
                  (bool) => next(bool ?? false), // 取消则阻止导航
                  () => next(false), // 取消失败则阻止导航
                );
              } else {
                return next(false);
              }
            },
          });
        } else {
          Modal.confirm({
            title: '提示',
            content: tips,
            cancelButtonProps: {
              // @ts-expect-error cancelButtonProps支持class但没有类型定义
              class: 'btn-fill-default',
            },
            onOk() {
              return next();
            },
            onCancel() {
              return next(false);
            },
          });
        }
      });

      onCleanup(() => {
        window.removeEventListener('beforeunload', handler);
        offRouterHook();
      });
    }
  }, { immediate: true });
}
