<template>
  <Drawer
    :open="show"
    width="900px"
    :closable="false"
    placement="right"
    @afterOpenChange="v => !v && modalDestroy()"
    @close="() => modalConfirm()"
  >
    <Form ref="formRef" :model="formValue" :labelCol="{ span: 7 }" :wrapperCol="{ span: 17 }" :rules="rules">
      <BorderBox label="基本配置">
        <FormItem label="名称" name="name">
          <Input v-model:value="formValue.name" placeholder="请输入" />
        </FormItem>
        <FormItem label="工具类型" name="workTypeID">
          <RadioGroup
            v-model:value="formValue.workTypeID"
            showSearch
            :options="toolkitTypeListRef"
            placeholder="请选择工具类型"
            buttonStyle="solid"
            optionType="button"
          />
        </FormItem>
        <FormItem label="可管理者" name="adminIds">
          <RadioGroup
            v-model:value="formValue.adminIds"
            showSearch
            :options="toolkitTypeList"
            placeholder="请选择工具类型"
            buttonStyle="solid"
            optionType="button"
          />
        </FormItem>
        <FormItem label="适用项目" name="projectID">
          <Select
            v-model:value="formValue.projectID"
            showSearch
            optionFilterProp="name"
            :options="projectList"
            placeholder="请选择适用项目"
            :fieldNames="commonFieldNames"
            allowClear
          />
        </FormItem>
        <FormItem label="支持平台" name="platforms">
          <CheckboxGroup
            v-model:value="formValue.platforms"
            :options="platformOptions"
          />
        </FormItem>
        <FormItem name="bundleID">
          <template #label>
            <span class="mr-4px">Bundle ID</span>
            <Tooltip
              title="iOS平台需提供"
              :autoAdjustOverflow="true"
              placement="top"
            >
              <Icon :icon="infoCircleIcon" />
            </Tooltip>
          </template>
          <Input
            v-model:value="formValue.bundleID"
          />
        </FormItem>
        <FormItem name="icon" label="图标">
          <Upload
            v-model:fileList="iconFileList" :customRequest="handleIconUpload" :showUploadList="false"
            accept="image/*"
          >
            <div
              class="h-24 w-24 flex flex-col cursor-pointer items-center justify-center border-2 border-FO-Container-Stroke1 rounded-2xl border-dashed bg-FO-Container-Fill2 transition-colors hover:border-FO-Brand-Primary-Default"
              :class="{ 'pointer-events-none opacity-60': uploading }"
            >
              <template v-if="uploading">
                <LoadingOutlined class="FO-Font-R24 mb-2 c-FO-Brand-Primary-Default" />
                <span class="FO-Font-R14 c-FO-Brand-Primary-Default">上传中...</span>
              </template>
              <template v-else-if="formValue.icon">
                <img :src="formValue.icon" class="h-full w-full rounded-6 object-cover">
              </template>
              <template v-else>
                <PlusOutlined class="FO-Font-R24 mb-2 c-FO-Content-Text2" />
                <span class="FO-Font-R14 c-FO-Content-Text2">点击上传</span>
              </template>
            </div>
          </Upload>
        </FormItem>
        <FormItem label="描述" name="description">
          <Textarea v-model:value="formValue.description" placeholder="请输入描述" :maxLength="100" />
        </FormItem>
      </BorderBox>
      <BorderBox label="README">
        <FormItem name="readme">
          <template #label>
            <span class="mr-4px">README</span>
            <Tooltip
              :autoAdjustOverflow="true"
              placement="top"
            >
              <template #title>
                <div>Windows emoji快捷键:【 win + . 】</div>
                <div>macOS emoji快捷键:【 ctrl + cmd + 空格 】</div>
              </template>
              <Icon :icon="infoCircleIcon" />
            </Tooltip>
          </template>
          <!-- <Markdown v-model:value="formValue.readme" :height="300" mode="sv" theme="light" /> -->
        </FormItem>
      </BorderBox>
    </Form>
    <template #footer>
      <div class="flex justify-end">
        <BasicButton @click="close">
          关闭
        </BasicButton>
      </div>
    </template>
  </Drawer>
</template>

<script lang="tsx" setup>
import { CheckboxGroup, Drawer, Form, FormItem, Input, message, RadioGroup, Select, Textarea, Tooltip, Upload } from 'ant-design-vue';
import type { ModalBaseProps } from '@hg-tech/utils-vue';
import { computed, ref } from 'vue';
import { BasicButton } from '@hg-tech/oasis-common/deprecated';
import BorderBox from '../../components/BorderBox.vue';
import { commonFieldNames, platformOptions } from '../../configs.tsx';
import { cloneDeep } from 'lodash';
import infoCircleIcon from '@iconify-icons/ant-design/info-circle-outlined';
import { Icon } from '@iconify/vue';
import { uploadImageFile, validateImageFormat } from '../../../utils/upload.ts';

const props = defineProps<ModalBaseProps & {
  isUpdate: boolean;
  toolkitTypeList: any;
  projectList: any;
  record?: any;
}>();
const formRef = ref();
const iconFileList = ref([]);
const formValue = ref(props.isUpdate ? props.record : {});
const uploading = ref(false);
const toolkitTypeListRef = computed(() => {
  if (props?.toolkitTypeList) {
    const list = cloneDeep(props.toolkitTypeList);
    list.forEach((e) => {
      e.label = e.name;
      e.value = e.ID;
    });
    return list;
  }
  return [];
});
const rules = ref({
  name: [
    { required: true, message: '请输入tag名称' },
  ],
});
function close() {
  return props.modalConfirm();
}
// 图片上传处理
async function handleIconUpload(options: any) {
  const { file, onSuccess, onError } = options;

  // 文件大小验证（10MB限制）
  const maxSize = 10 * 1024 * 1024; // 10MB
  if (file.size > maxSize) {
    message.error('文件大小需小于10M');
    onError?.(new Error('文件大小超限'));
    return;
  }

  // 文件格式验证
  const formatValidation = validateImageFormat(file);
  if (!formatValidation.isValid) {
    message.error(formatValidation.errorMessage || '请上传图片格式的文件');
    onError?.(new Error('文件格式不支持'));
    return;
  }
  try {
    uploading.value = true;

    // 调用上传接口
    const result = await uploadImageFile(file);

    if (result.success && result.url) {
      formValue.value.icon = result.url;
      message.success('图片上传成功');
      onSuccess?.(result);
    } else {
      message.error(result.errorMessage || '上传失败，请重试');
      onError?.(new Error(result.errorMessage || '上传失败'));
    }
  } catch (error: any) {
    console.error('图片上传失败:', error);
    message.error('上传失败，请重试');
    onError?.(error);
  } finally {
    uploading.value = false;
  }
}
</script>
