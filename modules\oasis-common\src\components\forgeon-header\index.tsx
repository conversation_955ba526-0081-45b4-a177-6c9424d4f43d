import { type PropType, type SlotsType, defineComponent } from 'vue';
import styles from './style.module.less';
import { Tooltip } from 'ant-design-vue';
import MenuUnfold from '../../assets/svg/icons/menu-unfold.svg?component';

const ForgeonHeader = defineComponent({
  props: {
    title: {
      type: String as PropType<string>,
      default: '',
    },
    showUnfoldIcon: {
      type: Boolean as PropType<boolean>,
      default: true,
    },
    onHandleMenuExpand: {
      type: Function as PropType<() => void>,
      default: () => {},
    },
  },
  slots: Object as SlotsType<{
    actions?: Record<string, never>;
    title?: Record<string, never>;
  }>,
  setup(props, { slots }) {
    return () => (
      <div class={[styles.forgeonHeader]}>
        <div class="flex items-center">
          {props.showUnfoldIcon
            ? (
              <Tooltip destroyTooltipOnHide placement="top" title="展开">
                <MenuUnfold class={[styles.unfoldIcon, 'cursor-pointer']} onClick={props.onHandleMenuExpand} />
              </Tooltip>
            )
            : null}
          {slots.title
            ? slots.title()
            : (
              <div class="FO-Font-B18">
                { props.title }
              </div>
            )}
        </div>
        {/* action */}
        <div class={[styles.rightAction, 'flex items-center gap-16px']}>
          {slots.actions?.()}
        </div>
      </div>
    );
  },
});

export {
  ForgeonHeader,
};
