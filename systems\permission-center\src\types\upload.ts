/**
 * 文件验证结果
 */
export interface FileValidationResult {
  /** 验证是否通过 */
  isValid: boolean;
  /** 错误信息 */
  errorMessage?: string;
}

/**
 * 上传响应结果
 */
export interface UploadResult {
  /** 上传是否成功 */
  success: boolean;
  /** 文件URL */
  url?: string;
  /** 错误信息 */
  errorMessage?: string;
}

/**
 * 支持的图片格式
 */
export const SUPPORTED_IMAGE_FORMATS = ['jpg', 'jpeg', 'png', 'gif', 'webp', 'svg'] as const;

/**
 * 支持的图片MIME类型
 */
export const SUPPORTED_IMAGE_MIME_TYPES = [
  'image/jpeg',
  'image/jpg',
  'image/png',
  'image/gif',
  'image/webp',
  'image/svg+xml',
] as const;

export type SupportedImageFormat = typeof SUPPORTED_IMAGE_FORMATS[number];
export type SupportedImageMimeType = typeof SUPPORTED_IMAGE_MIME_TYPES[number];
