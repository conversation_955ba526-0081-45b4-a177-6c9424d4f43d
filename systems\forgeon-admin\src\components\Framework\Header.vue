<template>
  <header class="flex items-center justify-between border-b border-[#efefef] border-b-style-solid bg-FO-Container-Fill1 px-[24px] py-[12px]">
    <div class="font-size-[20px]">
      ForgeOn 管理后台
    </div>
    <Dropdown v-show="!loadingUserProfile" :trigger="['click']">
      <Avatar class="cursor-pointer" :src="userProfile?.headerImg" />
      <template #overlay>
        <Menu>
          <MenuItem @click="logout">
            退出登录
          </MenuItem>
        </Menu>
      </template>
    </Dropdown>
  </header>
</template>

<script setup lang="ts">
import { Avatar, Dropdown, Menu, MenuItem } from 'ant-design-vue';
import { useMicroAppInject, useUserAuthInfoCtx, useUserProfileInfoCtx } from '@hg-tech/oasis-common';

const { data: userProfile, loading: loadingUserProfile } = useMicroAppInject(useUserProfileInfoCtx);
const { data } = useMicroAppInject(useUserAuthInfoCtx);

function logout() {
  data.value?.doLogout();
}
</script>
