<template>
  <Modal
    width="700px"
    :open="show"
    :maskClosable="false"
    :destroyOnClose="true"
    :centered="true"
    :afterClose="modalDestroy"
    @cancel="() => modalCancel()"
  >
    <template #title>
      <div class="w-full flex justify-center">
        Review配置
      </div>
    </template>
    <div class="mx-[24px]">
      <Checkbox v-model:checked="formValue.excludeSubmitter">
        通知Reviewer时排除提交人
      </Checkbox>
      <div class="my-[8px]">
        <div class="mb-[4px]">
          添加干员
        </div>
        <Select
          v-model:value="formValue.approverIDs"
          placeholder="请选择"
          mode="multiple"
          class="w-full"
          :loading="loadingUserList"
          :options="userList"
          :fieldNames="{ label: 'displayName', value: 'ID' }"
          :showSearch="true"
          :filterOption="userFilterOption"
        />
      </div>
      <div class="my-[8px]">
        <div class="mb-[4px]">
          添加干员组
        </div>
        <UserGroupSelector
          v-model:value="formValue.approverGroups"
          class="w-full"
        />
      </div>
    </div>
    <template #footer>
      <Button type="primary" class="mr-6" @click="handleSubmit">
        保存
      </Button>
      <Button @click="modalCancel">
        取消
      </Button>
    </template>
  </Modal>
</template>

<script setup lang="ts">
import { Button, Checkbox, Modal, Select } from 'ant-design-vue';
import { type ModalBaseProps, useLatestPromise } from '@hg-tech/utils-vue';
import type { SelectReviewGroupsItem } from '../../../../../api/page/model/gitlabModel.ts';
import { updateReviewConfig } from '../../../../../api/page/gitlab.ts';
import { useUserStore } from '../../../../../store/modules/user.ts';
import { userFilterOption } from '../../../../../hooks/system/useUserList.ts';
import UserGroupSelector from '../../../../../components/UserGroupSelector.vue';
import { useSysUserList } from '../../../../../hooks/useUserList.ts';
import { shallowReactive } from 'vue';

const props = defineProps<ModalBaseProps & {
  groupInfo?: SelectReviewGroupsItem;
}>();

const { userList, loadingUserList } = useSysUserList();
const userStore = useUserStore();
const formValue = shallowReactive({
  approverIDs: props.groupInfo?.approverIDs ?? [],
  approverGroups: props.groupInfo?.approverGroups ?? [],
  excludeSubmitter: props.groupInfo?.excludeSubmitter ?? false,
});
const { execute: updateConfig } = useLatestPromise(updateReviewConfig);

async function handleSubmit() {
  await updateConfig(userStore.getProjectId, {
    id: props.groupInfo?.ID,
    ...formValue,
  });
  props.modalConfirm();
}
</script>
