import { describe, expect, it } from 'vitest';
import { getBasicColor, getForgeonColor } from './pick';
import { ForgeonTheme } from '../tokens';

describe('getBasicColor', () => {
  it('should return color', () => {
    expect(getBasicColor('LightGray0')).toBe('rgba(255,255,255,1.00)');
  });
});

describe('getForgeonColor', () => {
  it('should return color in dark theme', () => {
    expect(getForgeonColor('ContainerFill1', ForgeonTheme.Dark)).toBe('rgba(34,39,48,1.00)');
    expect(getForgeonColor('ContentIcon1', ForgeonTheme.Dark)).toBe('rgba(224,225,229,1.00)');
  });
  it('should return color in light theme', () => {
    expect(getForgeonColor('ContainerFill1', ForgeonTheme.Light)).toBe('rgba(255,255,255,1.00)');
    expect(getForgeonColor('ContentIcon1', ForgeonTheme.Light)).toBe('rgba(41,46,56,1.00)');
  });
});
