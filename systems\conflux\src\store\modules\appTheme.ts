import { defineStore } from 'pinia';
import { useMicroAppInject, usePlatformConfigCtx } from '@hg-tech/oasis-common';
import { computed } from 'vue';
import { AntdOverrideDark, AntdOverrideLight } from '@hg-tech/forgeon-style';

/**
 * 获取系统主题
 */
export const useAppThemeStore = defineStore('AppTheme', () => {
  const { data, loading } = useMicroAppInject(usePlatformConfigCtx);
  return {
    theme: computed(() => data.value?.theme),
    antdToken: computed(() => {
      if (data.value?.theme === 'dark') {
        return AntdOverrideDark;
      }
      return AntdOverrideLight;
    }),
    loading,
  };
});
