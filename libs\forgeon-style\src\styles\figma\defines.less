/* This file is automatically generated. DO NOT EDIT it manually. */

/** basic color defines  */
:root {
  --Light-Violet-1: rgba(235, 235, 255, 1);
  --Light-Violet-2: rgba(212, 209, 255, 1);
  --Light-Violet-3: rgba(182, 176, 255, 1);
  --Light-Violet-4: rgba(155, 143, 255, 1);
  --Light-Violet-5: rgba(124, 106, 255, 1);
  --Light-Violet-6: rgba(94, 69, 255, 1);
  --Light-Violet-7: rgba(78, 53, 219, 1);
  --Light-Violet-8: rgba(63, 39, 184, 1);
  --Light-Violet-9: rgba(49, 27, 148, 1);
  --Light-Violet-10: rgba(36, 17, 112, 1);
  --Light-Blue-1: rgba(224, 240, 255, 1);
  --Light-Blue-2: rgba(196, 224, 255, 1);
  --Light-Blue-3: rgba(153, 200, 255, 1);
  --Light-Blue-4: rgba(107, 173, 255, 1);
  --Light-Blue-5: rgba(61, 145, 255, 1);
  --Light-Blue-6: rgba(13, 114, 255, 1);
  --Light-Blue-7: rgba(5, 91, 219, 1);
  --Light-Blue-8: rgba(0, 71, 184, 1);
  --Light-Blue-9: rgba(0, 54, 148, 1);
  --Light-Blue-10: rgba(0, 39, 112, 1);
  --Light-Lightblue-1: rgba(219, 246, 255, 1);
  --Light-Lightblue-2: rgba(188, 237, 254, 1);
  --Light-Lightblue-3: rgba(142, 223, 253, 1);
  --Light-Lightblue-4: rgba(96, 208, 252, 1);
  --Light-Lightblue-5: rgba(50, 191, 251, 1);
  --Light-Lightblue-6: rgba(0, 171, 250, 1);
  --Light-Lightblue-7: rgba(0, 139, 208, 1);
  --Light-Lightblue-8: rgba(0, 108, 167, 1);
  --Light-Lightblue-9: rgba(0, 79, 125, 1);
  --Light-Lightblue-10: rgba(0, 51, 83, 1);
  --Light-Teal-1: rgba(216, 248, 239, 1);
  --Light-Teal-2: rgba(178, 240, 225, 1);
  --Light-Teal-3: rgba(135, 225, 204, 1);
  --Light-Teal-4: rgba(84, 211, 183, 1);
  --Light-Teal-5: rgba(39, 196, 165, 1);
  --Light-Teal-6: rgba(0, 181, 148, 1);
  --Light-Teal-7: rgba(0, 151, 126, 1);
  --Light-Teal-8: rgba(0, 121, 103, 1);
  --Light-Teal-9: rgba(0, 91, 79, 1);
  --Light-Teal-10: rgba(0, 60, 53, 1);
  --Light-Green-1: rgba(218, 248, 224, 1);
  --Light-Green-2: rgba(181, 242, 194, 1);
  --Light-Green-3: rgba(142, 229, 161, 1);
  --Light-Green-4: rgba(101, 215, 128, 1);
  --Light-Green-5: rgba(65, 202, 98, 1);
  --Light-Green-6: rgba(34, 189, 75, 1);
  --Light-Green-7: rgba(27, 158, 64, 1);
  --Light-Green-8: rgba(20, 126, 52, 1);
  --Light-Green-9: rgba(14, 95, 39, 1);
  --Light-Green-10: rgba(9, 63, 27, 1);
  --Light-Lightgreen-1: rgba(233, 250, 210, 1);
  --Light-Lightgreen-2: rgba(207, 245, 171, 1);
  --Light-Lightgreen-3: rgba(182, 236, 132, 1);
  --Light-Lightgreen-4: rgba(156, 226, 95, 1);
  --Light-Lightgreen-5: rgba(131, 217, 61, 1);
  --Light-Lightgreen-6: rgba(106, 207, 29, 1);
  --Light-Lightgreen-7: rgba(86, 173, 24, 1);
  --Light-Lightgreen-8: rgba(66, 138, 18, 1);
  --Light-Lightgreen-9: rgba(47, 103, 12, 1);
  --Light-Lightgreen-10: rgba(30, 69, 8, 1);
  --Light-Yellow-1: rgba(255, 253, 204, 1);
  --Light-Yellow-2: rgba(255, 249, 176, 1);
  --Light-Yellow-3: rgba(255, 241, 135, 1);
  --Light-Yellow-4: rgba(252, 229, 93, 1);
  --Light-Yellow-5: rgba(252, 215, 50, 1);
  --Light-Yellow-6: rgba(252, 197, 0, 1);
  --Light-Yellow-7: rgba(213, 155, 0, 1);
  --Light-Yellow-8: rgba(170, 116, 0, 1);
  --Light-Yellow-9: rgba(128, 80, 0, 1);
  --Light-Yellow-10: rgba(85, 49, 0, 1);
  --Light-Orange-1: rgba(255, 240, 212, 1);
  --Light-Orange-2: rgba(254, 227, 180, 1);
  --Light-Orange-3: rgba(253, 207, 139, 1);
  --Light-Orange-4: rgba(252, 185, 98, 1);
  --Light-Orange-5: rgba(251, 162, 60, 1);
  --Light-Orange-6: rgba(250, 134, 17, 1);
  --Light-Orange-7: rgba(208, 101, 12, 1);
  --Light-Orange-8: rgba(167, 72, 8, 1);
  --Light-Orange-9: rgba(125, 47, 5, 1);
  --Light-Orange-10: rgba(83, 27, 2, 1);
  --Light-Red-1: rgba(254, 232, 226, 1);
  --Light-Red-2: rgba(253, 208, 197, 1);
  --Light-Red-3: rgba(251, 175, 161, 1);
  --Light-Red-4: rgba(249, 145, 129, 1);
  --Light-Red-5: rgba(247, 112, 99, 1);
  --Light-Red-6: rgba(242, 79, 68, 1);
  --Light-Red-7: rgba(209, 42, 37, 1);
  --Light-Red-8: rgba(174, 26, 26, 1);
  --Light-Red-9: rgba(138, 16, 21, 1);
  --Light-Red-10: rgba(102, 9, 16, 1);
  --Light-Pink-1: rgba(253, 228, 239, 1);
  --Light-Pink-2: rgba(250, 211, 229, 1);
  --Light-Pink-3: rgba(246, 168, 207, 1);
  --Light-Pink-4: rgba(241, 127, 188, 1);
  --Light-Pink-5: rgba(237, 87, 172, 1);
  --Light-Pink-6: rgba(229, 64, 163, 1);
  --Light-Pink-7: rgba(196, 36, 137, 1);
  --Light-Pink-8: rgba(161, 24, 120, 1);
  --Light-Pink-9: rgba(125, 15, 92, 1);
  --Light-Pink-10: rgba(89, 8, 66, 1);
  --Light-Purple-1: rgba(250, 231, 251, 1);
  --Light-Purple-2: rgba(245, 210, 250, 1);
  --Light-Purple-3: rgba(235, 167, 247, 1);
  --Light-Purple-4: rgba(219, 123, 240, 1);
  --Light-Purple-5: rgba(202, 83, 235, 1);
  --Light-Purple-6: rgba(188, 64, 229, 1);
  --Light-Purple-7: rgba(140, 30, 183, 1);
  --Light-Purple-8: rgba(107, 19, 148, 1);
  --Light-Purple-9: rgba(77, 11, 112, 1);
  --Light-Purple-10: rgba(58, 6, 89, 1);
  --Light-Gray-0: rgba(255, 255, 255, 1);
  --Light-Gray-1: rgba(245, 246, 247, 1);
  --Light-Gray-2: rgba(237, 239, 242, 1);
  --Light-Gray-3: rgba(227, 230, 237, 1);
  --Light-Gray-4: rgba(211, 216, 224, 1);
  --Light-Gray-5: rgba(194, 200, 212, 1);
  --Light-Gray-6: rgba(177, 183, 196, 1);
  --Light-Gray-7: rgba(157, 164, 178, 1);
  --Light-Gray-8: rgba(138, 146, 161, 1);
  --Light-Gray-9: rgba(122, 130, 145, 1);
  --Light-Gray-10: rgba(105, 112, 128, 1);
  --Light-Gray-11: rgba(88, 95, 110, 1);
  --Light-Gray-12: rgba(72, 78, 92, 1);
  --Light-Gray-13: rgba(56, 61, 74, 1);
  --Light-Gray-14: rgba(41, 46, 56, 1);
  --Light-Gray-15: rgba(28, 31, 38, 1);
  --Light-Gray-16: rgba(0, 0, 0, 1);
  --Dark-Violet-1: rgba(51, 56, 99, 1);
  --Dark-Violet-2: rgba(60, 66, 126, 1);
  --Dark-Violet-3: rgba(70, 77, 155, 1);
  --Dark-Violet-4: rgba(81, 87, 185, 1);
  --Dark-Violet-5: rgba(95, 99, 216, 1);
  --Dark-Violet-6: rgba(111, 111, 247, 1);
  --Dark-Violet-7: rgba(136, 134, 247, 1);
  --Dark-Violet-8: rgba(166, 164, 251, 1);
  --Dark-Violet-9: rgba(195, 194, 253, 1);
  --Dark-Violet-10: rgba(222, 222, 255, 1);
  --Dark-Blue-1: rgba(37, 60, 94, 1);
  --Dark-Blue-2: rgba(44, 75, 122, 1);
  --Dark-Blue-3: rgba(50, 90, 150, 1);
  --Dark-Blue-4: rgba(54, 104, 180, 1);
  --Dark-Blue-5: rgba(57, 118, 210, 1);
  --Dark-Blue-6: rgba(59, 132, 241, 1);
  --Dark-Blue-7: rgba(97, 158, 246, 1);
  --Dark-Blue-8: rgba(133, 183, 249, 1);
  --Dark-Blue-9: rgba(170, 208, 252, 1);
  --Dark-Blue-10: rgba(209, 232, 255, 1);
  --Dark-Lightblue-1: rgba(31, 65, 89, 1);
  --Dark-Lightblue-2: rgba(37, 83, 115, 1);
  --Dark-Lightblue-3: rgba(42, 106, 145, 1);
  --Dark-Lightblue-4: rgba(47, 125, 173, 1);
  --Dark-Lightblue-5: rgba(50, 144, 201, 1);
  --Dark-Lightblue-6: rgba(53, 162, 229, 1);
  --Dark-Lightblue-7: rgba(90, 181, 237, 1);
  --Dark-Lightblue-8: rgba(128, 198, 242, 1);
  --Dark-Lightblue-9: rgba(166, 216, 250, 1);
  --Dark-Lightblue-10: rgba(204, 238, 255, 1);
  --Dark-Teal-1: rgba(29, 71, 70, 1);
  --Dark-Teal-2: rgba(32, 89, 87, 1);
  --Dark-Teal-3: rgba(33, 107, 103, 1);
  --Dark-Teal-4: rgba(33, 128, 121, 1);
  --Dark-Teal-5: rgba(34, 148, 138, 1);
  --Dark-Teal-6: rgba(33, 166, 152, 1);
  --Dark-Teal-7: rgba(89, 186, 172, 1);
  --Dark-Teal-8: rgba(129, 207, 192, 1);
  --Dark-Teal-9: rgba(166, 227, 213, 1);
  --Dark-Teal-10: rgba(201, 248, 235, 1);
  --Dark-Green-1: rgba(38, 74, 45, 1);
  --Dark-Green-2: rgba(42, 90, 53, 1);
  --Dark-Green-3: rgba(48, 108, 62, 1);
  --Dark-Green-4: rgba(50, 126, 71, 1);
  --Dark-Green-5: rgba(54, 145, 80, 1);
  --Dark-Green-6: rgba(54, 163, 88, 1);
  --Dark-Green-7: rgba(96, 183, 118, 1);
  --Dark-Green-8: rgba(131, 202, 147, 1);
  --Dark-Green-9: rgba(165, 222, 176, 1);
  --Dark-Green-10: rgba(208, 240, 194, 1);
  --Dark-Lightgreen-1: rgba(59, 79, 40, 1);
  --Dark-Lightgreen-2: rgba(69, 98, 45, 1);
  --Dark-Lightgreen-3: rgba(78, 117, 48, 1);
  --Dark-Lightgreen-4: rgba(86, 136, 53, 1);
  --Dark-Lightgreen-5: rgba(92, 156, 56, 1);
  --Dark-Lightgreen-6: rgba(97, 176, 58, 1);
  --Dark-Lightgreen-7: rgba(124, 191, 92, 1);
  --Dark-Lightgreen-8: rgba(152, 207, 126, 1);
  --Dark-Lightgreen-9: rgba(179, 224, 159, 1);
  --Dark-Lightgreen-10: rgba(208, 240, 194, 1);
  --Dark-Yellow-1: rgba(84, 66, 35, 1);
  --Dark-Yellow-2: rgba(111, 87, 40, 1);
  --Dark-Yellow-3: rgba(137, 106, 42, 1);
  --Dark-Yellow-4: rgba(163, 126, 42, 1);
  --Dark-Yellow-5: rgba(190, 148, 42, 1);
  --Dark-Yellow-6: rgba(217, 169, 39, 1);
  --Dark-Yellow-7: rgba(223, 190, 82, 1);
  --Dark-Yellow-8: rgba(230, 210, 121, 1);
  --Dark-Yellow-9: rgba(239, 230, 160, 1);
  --Dark-Yellow-10: rgba(250, 248, 200, 1);
  --Dark-Orange-1: rgba(87, 57, 40, 1);
  --Dark-Orange-2: rgba(112, 72, 47, 1);
  --Dark-Orange-3: rgba(140, 88, 53, 1);
  --Dark-Orange-4: rgba(168, 104, 57, 1);
  --Dark-Orange-5: rgba(196, 118, 59, 1);
  --Dark-Orange-6: rgba(222, 135, 60, 1);
  --Dark-Orange-7: rgba(229, 162, 90, 1);
  --Dark-Orange-8: rgba(237, 188, 123, 1);
  --Dark-Orange-9: rgba(245, 212, 159, 1);
  --Dark-Orange-10: rgba(255, 236, 201, 1);
  --Dark-Red-1: rgba(89, 46, 46, 1);
  --Dark-Red-2: rgba(112, 58, 56, 1);
  --Dark-Red-3: rgba(138, 69, 65, 1);
  --Dark-Red-4: rgba(164, 81, 75, 1);
  --Dark-Red-5: rgba(191, 93, 84, 1);
  --Dark-Red-6: rgba(219, 102, 90, 1);
  --Dark-Red-7: rgba(230, 129, 118, 1);
  --Dark-Red-8: rgba(239, 155, 146, 1);
  --Dark-Red-9: rgba(247, 181, 175, 1);
  --Dark-Red-10: rgba(254, 211, 206, 1);
  --Dark-Pink-1: rgba(92, 50, 83, 1);
  --Dark-Pink-2: rgba(116, 59, 103, 1);
  --Dark-Pink-3: rgba(140, 66, 120, 1);
  --Dark-Pink-4: rgba(164, 74, 138, 1);
  --Dark-Pink-5: rgba(190, 80, 156, 1);
  --Dark-Pink-6: rgba(217, 85, 173, 1);
  --Dark-Pink-7: rgba(227, 121, 189, 1);
  --Dark-Pink-8: rgba(237, 153, 205, 1);
  --Dark-Pink-9: rgba(245, 185, 222, 1);
  --Dark-Pink-10: rgba(253, 215, 238, 1);
  --Dark-Purple-1: rgba(81, 53, 97, 1);
  --Dark-Purple-2: rgba(99, 59, 121, 1);
  --Dark-Purple-3: rgba(117, 67, 145, 1);
  --Dark-Purple-4: rgba(137, 75, 170, 1);
  --Dark-Purple-5: rgba(158, 82, 196, 1);
  --Dark-Purple-6: rgba(177, 89, 222, 1);
  --Dark-Purple-7: rgba(194, 123, 230, 1);
  --Dark-Purple-8: rgba(210, 155, 238, 1);
  --Dark-Purple-9: rgba(225, 186, 245, 1);
  --Dark-Purple-10: rgba(239, 216, 251, 1);
  --Dark-Gray-0: rgba(26, 30, 36, 1);
  --Dark-Gray-1: rgba(34, 39, 48, 1);
  --Dark-Gray-2: rgba(43, 48, 59, 1);
  --Dark-Gray-3: rgba(53, 58, 71, 1);
  --Dark-Gray-4: rgba(63, 70, 84, 1);
  --Dark-Gray-5: rgba(79, 86, 99, 1);
  --Dark-Gray-6: rgba(95, 102, 117, 1);
  --Dark-Gray-7: rgba(116, 120, 135, 1);
  --Dark-Gray-8: rgba(132, 138, 153, 1);
  --Dark-Gray-9: rgba(148, 153, 168, 1);
  --Dark-Gray-10: rgba(168, 172, 184, 1);
  --Dark-Gray-11: rgba(187, 190, 199, 1);
  --Dark-Gray-12: rgba(200, 201, 209, 1);
  --Dark-Gray-13: rgba(214, 215, 219, 1);
  --Dark-Gray-14: rgba(224, 225, 229, 1);
  --Dark-Gray-15: rgba(235, 235, 240, 1);
  --Dark-Gray-16: rgba(242, 243, 247, 1);
  --Basic-Black-0: rgba(0, 0, 0, 0);
  --Basic-Black-5: rgba(0, 0, 0, 0.05);
  --Basic-Black-10: rgba(0, 0, 0, 0.1);
  --Basic-Black-20: rgba(0, 0, 0, 0.2);
  --Basic-Black-30: rgba(0, 0, 0, 0.3);
  --Basic-Black-40: rgba(0, 0, 0, 0.4);
  --Basic-Black-50: rgba(0, 0, 0, 0.5);
  --Basic-Black-60: rgba(0, 0, 0, 0.6);
  --Basic-Black-70: rgba(0, 0, 0, 0.7);
  --Basic-Black-80: rgba(0, 0, 0, 0.8);
  --Basic-Black-90: rgba(0, 0, 0, 0.9);
  --Basic-Black-100: rgba(0, 0, 0, 1);
  --Basic-White-0: rgba(255, 255, 255, 0);
  --Basic-White-5: rgba(255, 255, 255, 0.05);
  --Basic-White-10: rgba(255, 255, 255, 0.1);
  --Basic-White-20: rgba(255, 255, 255, 0.2);
  --Basic-White-30: rgba(255, 255, 255, 0.3);
  --Basic-White-40: rgba(255, 255, 255, 0.4);
  --Basic-White-50: rgba(255, 255, 255, 0.5);
  --Basic-White-60: rgba(255, 255, 255, 0.6);
  --Basic-White-70: rgba(255, 255, 255, 0.7);
  --Basic-White-80: rgba(255, 255, 255, 0.8);
  --Basic-White-90: rgba(255, 255, 255, 0.9);
  --Basic-White-100: rgba(255, 255, 255, 1);
}
