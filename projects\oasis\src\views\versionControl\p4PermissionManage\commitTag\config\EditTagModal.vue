<template>
  <Modal
    :width="600"
    :open="show"
    :maskClosable="false"
    destroyOnClose
    centered
    :afterClose="modalDestroy"
    @cancel="() => modalCancel()"
  >
    <template #title>
      <div class="text-center">
        <span class="font-size-[16px] font-bold">
          <span>{{ title }}</span>
        </span>
      </div>
    </template>
    <div class="m-[20px] max-h-400px overflow-auto">
      <Form ref="formRef" :model="formValue" :labelCol="{ span: 7 }" :wrapperCol="{ span: 17 }" :rules="rules">
        <BorderBox label="tag配置">
          <FormItem label="tag名称" name="name">
            <Input v-model:value="formValue.name" placeholder="请输入" />
          </FormItem>
          <FormItem label="tag描述" name="description">
            <Textarea v-model:value="formValue.description" placeholder="请输入" :rows="3" />
          </FormItem>
        </BorderBox>
        <BorderBox :label="isUpdate ? '启用分支配置' : '同步启用'" :subLabel="subLabel">
          <FormItem label="选择分支（多选）" name="streamIDs">
            <Select
              v-model:value="formValue.streamIDs"
              :maxTagCount="8"
              :maxTagTextLength="6"
              :getPopupContainer="getPopupContainer"
              mode="multiple"
              placeholder="请选择"
              :options="fullOptions"
              optionFilterProp="label"
              showArrow
              @change="handleSelectChange"
            />
          </FormItem>
        </BorderBox>
      </Form>
    </div>
    <template #footer>
      <div class="mt flex justify-end">
        <BasicButton @click="modalCancel()">
          取消
        </BasicButton>
        <BasicButton type="primary" class="ml-2" @click="handleConfirm">
          {{ isUpdate ? '修改' : '添加' }}
        </BasicButton>
      </div>
    </template>
  </Modal>
</template>

<script lang="ts" setup>
import { Form, FormItem, Input, Modal, Select, Textarea } from 'ant-design-vue';
import type { ModalBaseProps } from '@hg-tech/utils-vue';
import { computed, onMounted, ref } from 'vue';
import type { BasicAddResult } from '/@/api/model/baseModel';
import { BorderBox } from '../../../../../components/Form/index.ts';
import { useP4DepotStream } from '../../hook.ts';
import type { TagListItem } from '/@/api/page/model/submitConfModel.ts';
import { BasicButton } from '/@/components/Button';

const props = defineProps< ModalBaseProps<{ updatedItem?: BasicAddResult | null | undefined }> & {
  title?: string;
  subLabel?: string;
  formValue: TagListItem;
  isUpdate?: boolean;
  sentReq?: (formValue: TagListItem) => Promise<BasicAddResult | null | undefined>;
}>();
const { getAllStreamList, allStreamList } = useP4DepotStream();
const formRef = ref();
const formValue = ref(props.formValue);
const fullOptions = computed(() => [
  { value: 'SELECT_ALL', label: '全部勾选' },
  ...allStreamList.value.map((item) => ({
    value: item.ID,
    label: item.description || item.path,
  })),
]);
const rules = {
  name: [{ required: true, message: '请输入tag名称' }],
  description: [{ required: true, message: '请输入tag描述' }],
};
const getPopupContainer = () => document.body;
// 处理全选逻辑
function handleSelectChange(values) {
  // 如果包含全选项
  if (values.includes('SELECT_ALL')) {
    // 过滤掉全选标识，只保留真实选项
    const realValues = values.filter((v) => v !== 'SELECT_ALL');
    // 判断是否要全选

    if (realValues.length === allStreamList.value.length) {
      formValue.value.streamIDs = []; // 取消全选
    } else {
      formValue.value.streamIDs = [...allStreamList.value.map((o) => o.ID)] as number[];
    }
  } else {
    formValue.value.streamIDs = values;
  }
}

onMounted(async () => {
  await getAllStreamList(null, false, true, true);
});
async function handleConfirm() {
  await formRef.value.validate();
  const updatedItem = await props.sentReq?.(formValue.value);
  return props.modalConfirm({ updatedItem });
}
</script>
