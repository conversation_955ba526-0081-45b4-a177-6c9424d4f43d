<template>
  <BasicTree
    v-bind="$attrs"
    ref="treeRef"
    class="three-state-tree"
    :treeData="treeData"
    checkable
    :loadData="getNodeData"
    :defaultExpandLevel="1"
    :selectable="false"
    :fieldNames="defaultFieldNames"
    @check="checkEvent"
  >
    <template #title="item">
      <div
        :class="{ 'font-bold': item.hasChange, [`three-state-tree__half-checked`]: item.halfChecked }"
      >
        <Icon :icon="getTypeIcon(item.type)" :color="getTypeIcon(item.type, true)" class="mr-1" />
        {{ item.name }}
      </div>
    </template>
  </BasicTree>
</template>

<script lang="ts" setup>
import { useRouter } from 'vue-router';
import type { TreeDataItem } from 'ant-design-vue/es/tree/Tree';
import { cloneDeep, difference, map, pullAll, union } from 'lodash-es';
import type { ComputedRef, Ref } from 'vue';
import { computed, nextTick, ref, unref, watchEffect } from 'vue';
import { getTree } from '../../../../../api/page/gitlab';
import type { BranchesBaseItemListItem, PermissionListItem } from '../../../../../api/page/model/gitlabModel';
import { Icon } from '../../../../../components/Icon';
import type { FieldNames, TreeActionType } from '../../../../../components/Tree';
import { BasicTree } from '../../../../../components/Tree';
import { useTree } from '../../../../../components/Tree/src/hooks/useTree';
import { useUserStore } from '../../../../../store/modules/user';
import { findNode } from '../../../../../utils/helper/treeHelper';
import { defaultFieldNames, getTypeIcon } from '../../../../../views/versionControl/p4PermissionManage/p4StateTree/p4StateTree.data';
import type { FilePathNode } from '../../../../../api/model/baseModel.ts';

const props = defineProps<{
  branchInfo?: BranchesBaseItemListItem;
  permissionList?: FilePathNode[];
}>();
const emit = defineEmits(['stateChange']);

const router = useRouter();
const gitlabProjectID = computed(() => Number(router.currentRoute.value.query.gitlabProjectID));
// 树ref
const treeRef = ref<Nullable<TreeActionType>>(null);
// 树数据
const treeData = ref<TreeDataItem[]>([]);
// 初始选中的节点key
const initCheckedKeys = ref<string[]>([]);
// 所有选中的节点key
const checkedKeys = ref<string[]>([]);
// 所有半选中的节点key
const halfCheckedKeys = ref<string[]>([]);

// 判断是否有修改过勾选状态
const hasChangeCheck = ref(false);

const userStore = useUserStore();

const { getAllKeys } = useTree(
  treeData as Ref<TreeDataItem[]>,
  defaultFieldNames as unknown as ComputedRef<FieldNames>,
);

// 是否初次加载
const isFirstLoad = ref<boolean>(true);

watchEffect(() => {
  const find = props.permissionList?.find((e) => e.path === '.');

  treeData.value = [
    {
      path: '.',
      name: '.',
      permit: find?.permit || 0,
      type: 1,
      isLeaf: false,
    },
  ] as unknown as TreeDataItem[];

  if (!isFirstLoad.value) {
    nextTick(() => {
      getNodeData(treeData.value[0]);
    });
  } else {
    isFirstLoad.value = false;
  }
});

async function getNodeData(treeNode: PermissionListItem) {
  if (!gitlabProjectID.value || !props.branchInfo?.name || treeNode.isLoaded) {
    return;
  }
  const { list } = await getTree(userStore.getProjectId, {
    iid: gitlabProjectID.value,
    ref: props.branchInfo?.name,
    path: treeNode.path,
  });
  const children = list
    ? map(list, (e) => {
      props.permissionList?.forEach((per) => {
        if (per.path === e.path) {
          e.permit = per.permit;
        } else if (e.kind === 'dir' && per.path?.startsWith(e.path)) {
          e.halfChecked = true;
        }
      });
      // 父节点为允许,当前节点不是拒绝,则当前节点为允许
      if (treeNode.permit === 1 && e.permit !== 2) {
        e.permit = 1;
      }
      // 是初次加载, 当前节点为允许且不是半选状态,则加入选中列表
      if (!hasChangeCheck.value && e.permit === 1 && !e.halfChecked) {
        initCheckedKeys.value.push(e.path);
      }
      // 半选中的节点需要从服务器获取子节点
      if (e.halfChecked) {
        getNodeData(e);
      }
      return {
        path: e.path,
        name: e.name,
        permit: e.permit,
        type: e.kind === 'file' ? 2 : 1,
        isLeaf: e.kind === 'file',
      };
    })
    : [];
  treeRef.value?.updateNodeByKey(treeNode.path!, {
    halfChecked: undefined,
    isLoaded: true,
    children,
  });
  nextTick(() => {
    !hasChangeCheck.value && treeRef.value?.setCheckedKeys(initCheckedKeys.value);
  });
}

// 过滤掉列表里所有子节点
function removeAllChildren(list: string[]) {
  // 列表中的所有子节点列表
  const childrenList = new Set<string>([]);
  list.forEach((a) => {
    if (a.endsWith('/')) {
      list.forEach((b) => {
        if (b !== a && b.includes(a)) {
          childrenList.add(b);
        }
      });
    }
  });
  // 过滤掉列表里所有子节点
  pullAll(list, [...childrenList]);
}

// 树选中事件
function checkEvent(cKeys, e) {
  if (!hasChangeCheck.value) {
    hasChangeCheck.value = true;
    removeAllChildren(initCheckedKeys.value);
  }
  checkedKeys.value = cloneDeep(cKeys);

  halfCheckedKeys.value = e.halfCheckedKeys;
  const findNodeItem = findNode(unref(treeData), (n) => n.path === e.node.path);
  if (findNodeItem) {
    const newCheckedKeys = cloneDeep(cKeys);
    removeAllChildren(newCheckedKeys);
    changeState(findNodeItem, newCheckedKeys);
    emit('stateChange');
  }
}

// 切换状态
function changeState(item: PermissionListItem, checkedKeys: string[]) {
  item.hasChange
      = initCheckedKeys.value.includes(item.path!) !== checkedKeys.includes(item.path!);
  if (item.children?.length) {
    item.children.forEach((e) => {
      changeState(e, checkedKeys);
    });
  }
}

// 获取最优权限列表
function getOptimalList(onlyCheckedList = false) {
  if (!unref(hasChangeCheck)) {
    return undefined;
  }
  // 选中列表
  const checkedKeyList = treeRef.value?.getCheckedKeys() as string[];
  removeAllChildren(checkedKeyList);
  // 未选中列表
  const uncheckedKeyList = difference(
    getAllKeys(),
    union(unref(checkedKeys), unref(halfCheckedKeys)),
  ) as string[];
  removeAllChildren(uncheckedKeyList);

  // 是否是选中列表更短
  const isCheckListShort
      = onlyCheckedList || checkedKeyList.length <= uncheckedKeyList.length + 1;
  // 最优列表
  const optimalList = isCheckListShort ? checkedKeyList : uncheckedKeyList;
  const formatList: { path?: string; permit?: number }[] = [];
  optimalList?.forEach((e) => {
    formatList.push({
      path: e,
      permit: isCheckListShort ? 1 : 2,
    });
  });
  // 若 未选中列表更优 则需要将 根节点选中并加入
  if (!isCheckListShort) {
    formatList.unshift({
      path: unref(treeData)[0].path,
      permit: 1,
    });
  }
  return formatList;
}

function setHasChangeCheck(check: boolean) {
  hasChangeCheck.value = check;
}
// 抛出ref, data 和 获取变化列表的方法
defineExpose({
  treeRef,
  treeData,
  getOptimalList,
  setHasChangeCheck,
});
</script>

<style lang="less" scoped>
.three-state-tree {
  .ant-tree-node-content-wrapper {
    background: none !important;

    &:hover {
      background-color: #f5f6f5 !important;
    }
  }
}
</style>
