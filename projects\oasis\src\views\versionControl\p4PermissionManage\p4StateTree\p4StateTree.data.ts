export const defaultFieldNames = {
  key: 'path',
  title: 'name',
  children: 'children',
};

export interface CheckListType {
  icon: string;
  class: string;
  text: string;
  permit: number;
}

export const checkList: CheckListType[] = [
  {
    icon: 'charm:tick',
    class: '!c-FO-Brand-Primary-Default',
    text: '读写',
    permit: 1,
  },
  {
    icon: 'charm:cross',
    class: '!c-FO-Functional-Error1-Default',
    text: '无权限',
    permit: 2,
  },
];

export function getTypeIcon(type: number, isColor = false, permit?: number) {
  if (permit === 2 && isColor) {
    return '#ccc';
  }
  if (type === 2) {
    return isColor ? '#3f8ffd' : 'ant-design:file-text-outlined';
  } else {
    return isColor ? '#f1b02a' : 'ant-design:folder-twotone';
  }
}
export enum importTypeMenu {
  includeImport = 1,
  fromImport = 2,
  point = 3,
}
