<template>
  <PageWrapper v-track:v="'ah366l2x14'" content-background :class="prefixCls">
    <div class="m-5 bg-FO-Container-Fill1">
      <ATypographyTitle :level="5">
        <div class="flex items-center">
          <router-link class="flex px-2" :to="{ name: PlatformEnterPoint.TrackingAnalysis }">
            <Icon class="c-FO-Content-Text1" icon="icon-park-outline:left" :size="24" title="返回" />
          </router-link>
          <span>埋点配置</span>
        </div>
      </ATypographyTitle>
      <div :class="`${prefixCls}__tool-list`">
        <div
          v-for="tool in toolList"
          :key="tool.ID"
          :class="{
            [`${prefixCls}__tool-list-item`]: true,
            [`${prefixCls}__tool-list-item-active`]: activeToolID === tool.ID,
          }"
          @click="handleToolClick(tool.ID!)"
        >
          <div :class="`${prefixCls}__tool-list-item-name`">
            {{ tool.toolName }}
          </div>
          <div
            v-if="activeToolID === tool.ID"
            :class="`${prefixCls}__tool-list-item-id`"
            @click.stop="copyToolID(tool.toolUUID!)"
            @dblclick.stop
          >
            {{ tool.toolUUID }}
            <Icon icon="ant-design:copy-outlined" size="12" title="复制ID" class="float-right" />
          </div>
        </div>
        <div>
          <EditableNode
            v-model:value="newAddToolName"
            v-model:is-edit="isAddingTool"
            @change="handleAddTool"
            @cancel="newAddToolName = ''"
          >
            <a-button block class="!h-[42px] !rounded" @click="isAddingTool = true">
              <Icon icon="ant-design:plus-outlined" />
            </a-button>
          </EditableNode>
        </div>
      </div>
      <div class="m-4 flex items-center">
        <span>编辑模式</span>
        <ASwitch v-model:checked="isEditMode" size="small" class="!ml-1" :disabled="!canManage" />
        <span v-if="isEditMode" class="ml-2 c-FO-Content-Text2">(双击节点可编辑名称)</span>
      </div>
      <Groups v-if="activeToolID" :tool-i-d="activeToolID" :is-edit-mode="isEditMode" />
      <Functions v-if="activeToolID" :tool-i-d="activeToolID" :is-edit-mode="isEditMode" />
    </div>
  </PageWrapper>
</template>

<script lang="ts" setup name="TrackingSettings">
import type { TrackingToolListItem } from '/@/api/page/model/trackingModel';
import { Switch as ASwitch, TypographyTitle as ATypographyTitle } from 'ant-design-vue';
import { ref } from 'vue';
import EditableNode from './components/EditableNode.vue';
import Functions from './functions/index.vue';
import Groups from './groups/index.vue';
import {
  addTrackingTool,
  getCurUserTrackingToolAuthority,
  getTrackingToolList,
} from '/@/api/page/tracking';
import Icon from '/@/components/Icon';
import { PageWrapper } from '/@/components/Page';
import { useDesign } from '/@/hooks/web/useDesign';
import { useMessage } from '/@/hooks/web/useMessage';
import { useUserStore } from '/@/store/modules/user';
import { copyText } from '/@/utils/copyTextToClipboard';
import { PlatformEnterPoint } from '@hg-tech/oasis-common';

const { prefixCls } = useDesign('tracking-settings');
const { createMessage } = useMessage();

const userStore = useUserStore();
const toolList = ref<TrackingToolListItem[]>([]);
const isAddingTool = ref<boolean>(false);
const newAddToolName = ref<string>('');
const activeToolID = ref<number>();
const isEditMode = ref<boolean>(false);
const canManage = ref<boolean>(false);

async function getAuthority() {
  if (!activeToolID.value) {
    return;
  }

  const { authority } = await getCurUserTrackingToolAuthority({ toolID: activeToolID.value });

  // 超管、工具创建者、工具管理员
  canManage.value = userStore.isSuperAdmin || authority.includes(0) || authority.includes(1);
}

async function getToolList() {
  const { list } = await getTrackingToolList();

  toolList.value = list || [];
  activeToolID.value = list?.[0]?.ID;
  getAuthority();
}

getToolList();

async function handleAddTool() {
  if (!newAddToolName.value) {
    createMessage.error('请输入工具名称!');

    return;
  }

  await addTrackingTool({
    toolName: newAddToolName.value,
  });
  newAddToolName.value = '';
  isAddingTool.value = false;
  getToolList();
}

function handleToolClick(ID: number) {
  activeToolID.value = ID;
  isEditMode.value = false;
  getAuthority();
}

function copyToolID(UUID: string) {
  if (!UUID) {
    return;
  }

  copyText(UUID);
}
</script>

<style lang="less">
@prefix-cls: ~'hypergryph-tracking-settings';
.@{prefix-cls} {
  &__tool-list {
    display: grid;
    justify-content: start;
    grid-template-columns: repeat(auto-fit, 250px);
    grid-gap: 16px;
    padding: 16px;

    @media (max-width: @screen-lg-max) {
      grid-template-columns: repeat(auto-fit, 200px);
      grid-gap: 10px;
    }

    &-item {
      border-radius: 5px;
      text-align: center;
      box-sizing: border-box;
      border: 1px solid @FO-Container-Stroke1;
      background-color: @FO-Container-Stroke1;
      cursor: pointer;
      overflow: hidden;
      height: fit-content;
      transition: all 0.3s ease-in-out;

      &:hover {
        border-color: @FO-Brand-Primary-Default;
        color: @FO-Brand-Primary-Default;
      }

      &-name {
        font-size: 16px;
        line-height: 42px;
      }

      &-id {
        font-size: 12px;
        color: @FO-Content-Text2;
        background-color: @FO-Container-Stroke1;
        line-height: 12px;
        padding: 2px;

        &:hover {
          color: @FO-Brand-Primary-Default;
        }
      }

      &-active {
        background-color: @FO-Brand-Primary-Default;
        color: @FO-Content-Components1;
        &:hover {
          color: @FO-Content-Components1;
        }
      }
    }
  }
}
</style>
