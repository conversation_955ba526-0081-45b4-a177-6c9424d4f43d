<template>
  <BasicModal
    :class="prefixCls"
    :title="`${name}埋点详情`"
    :footer="null"
    :width="1400"

    @register="register"
  >
    <BasicTable :max-height="600" :class="`${prefixCls}__table`" @register="registerTable">
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'CreatedAt'">
          {{ formatToDateTime(dayjs(record.CreatedAt)) }}
        </template>
      </template>
    </BasicTable>
    <APagination v-if="total > 10" v-model:current="current" v-model:page-size="pageSize" :total="total" show-less-items class="my flex justify-end" @change="pageChange" />
  </BasicModal>
</template>

<script lang="ts" setup>
import {
  Pagination as APagination,
} from 'ant-design-vue';
import { useDesign } from '/@/hooks/web/useDesign';
import { BasicModal, useModalInner } from '/@/components/Modal';
import type { BasicColumn } from '/@/components/Table';
import { BasicTable, useTable } from '/@/components/Table';
import { ref } from 'vue';
import { getTrackingLog } from '/@/api/page/tracking';
import dayjs from 'dayjs';
import { formatToDateTime } from '/@/utils/dateUtil';
import { formatNickName } from '/@/hooks/system/useUserList';

defineOptions({
  name: 'AdditionalInformationTableModal',
});

const { prefixCls } = useDesign('Additional-information-table-modal');

const current = ref(1);
const pageSize = ref(10);
const total = ref(0);
const projectID = ref<number>();
const functionID = ref<number>();
const name = ref<string>();
const transformedData = ref();
const [registerTable, { setTableData, setColumns }] = useTable({
  columns: [],
  dataSource: transformedData.value,
  showIndexColumn: false,
  pagination: false,
});
const [register, { redoModalHeight }] = useModalInner(async (data) => {
  let columns: BasicColumn[] = [];

  columns = [{
    title: '触发时间',
    dataIndex: 'CreatedAt',

  }, {
    title: 'ip',
    dataIndex: 'ip',
  }, {
    title: '用户',
    dataIndex: 'user',
    format: (_, record) => formatNickName(record.user) ? formatNickName(record.user) : '',
  }];
  name.value = data.functionName;
  projectID.value = data.projectID;
  functionID.value = data.functionID;
  current.value = 1;
  pageSize.value = 10;
  total.value = 0;

  const { ExtInfo, List, Total } = await getTrackingLog({
    page: current.value,
    pageSize: pageSize.value,
    projectId: data.projectID,
    functionId: data.functionID,
  });

  total.value = Total;
  // 处理字段
  JSON.parse(ExtInfo).forEach((item: any) => {
    columns.push({
      title: item.name,
      dataIndex: item.key,
      ellipsis: true,
    });
  });
  setColumns(columns);
  // 处理记录
  transformedData.value = List.map((item) => {
    const { extInfo, ...rest } = item; // 分离出extInfo和其他属性

    return { ...rest, ...extInfo }; // 将extInfo中的属性展开并合并到其他属性中
  });
  setTableData(transformedData.value);
  redoModalHeight();
});

async function pageChange() {
  if (!functionID.value) {
    return;
  }

  const { List } = await getTrackingLog({
    page: current.value,
    pageSize: pageSize.value,
    projectId: projectID.value || 0,
    functionId: functionID.value,
  });

  transformedData.value = List.map((item) => {
    const { extInfo, ...rest } = item; // 分离出extInfo和其他属性

    return { ...rest, ...extInfo }; // 将extInfo中的属性展开并合并到其他属性中
  });
  setTableData(transformedData.value);
  redoModalHeight();
}
</script>

<style lang="less">
@prefix-cls: ~'hypergryph-Additional-information-table-modal';
.@{prefix-cls} {
  &__table {
    .ant-table-body {
      height: auto !important;
    }
  }
}
</style>
