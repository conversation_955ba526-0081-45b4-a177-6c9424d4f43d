<template>
  <PageWrapper class="gitlab-gitlab-group" headerSticky @back="() => emit('back')">
    <template #title>
      <div class="flex items-center gap-[12px]">
        Git审查配置: {{ branchInfo?.name }}
        <StatusButton
          :enabled="branchInfo?.cherryPickEnabled"
          @click="handleShowCherrypickModal"
        >
          cherry-pick
        </StatusButton>
        <GroupButtons :repoID="repoID" :branchInfo="branchInfo" />
        <BasicButton
          class="custom-rounded-btn"
          borderColor="black"
          noIcon
          size="small"
          @click="deleteConfig"
        >
          删除配置
        </BasicButton>
      </div>
    </template>
    <div class="flex">
      <ScrollContainer class="w-400px">
        <Spin :spinning="loadingGroupList">
          <div class="max-h-[calc(100vh_-_206px)] rounded-md bg-FO-Container-Fill1 p-4">
            <div class="flex items-center justify-between">
              <div class="flex items-center font-bold">
                审查组
              </div>
              <BasicButton
                shape="round"
                class="custom-rounded-btn ml-2"
                size="small"
                borderColor="black"
                @click="handleCreate"
              >
                <Icon icon="ant-design:plus-outlined" :size="14" />
                添加
              </BasicButton>
            </div>
            <div class="my-3 text-xs c-FO-Content-Text2">
              配置审查路径和对应的审查员
            </div>
            <div class="gitlab-gitlab-group__tab">
              <div
                v-for="item in showGroupList"
                :key=" item.ID"
                class="gitlab-gitlab-group__tab-item"
                :active=" item.ID === activeGroupID"
                @click="() => activeGroupID = item.ID"
              >
                <div class="w-250px flex items-center">
                  <Icon icon="devGuard-auditor|svg" />
                  <EllipsisText class="ml-2">
                    {{ item.description }}
                  </EllipsisText>
                </div>
                <div>
                  <Popconfirm
                    :title="`确认${item.reviewEnable ? '关闭' : '开启'}审批配置？`"
                    @confirm="() => handleSwitchConfig(GitlabConfigType.Audit, !item.reviewEnable, item.ID)"
                  >
                    <BasicButton type="text" size="small" @click.stop>
                      <Icon
                        :icon=" item.reviewEnable ? 'majesticons:lock-line' : 'majesticons:unlock-open-line' "
                        :class=" item.reviewEnable ? 'c-#db851f' : 'c-FO-Content-Text3'"
                      />
                    </BasicButton>
                  </Popconfirm>
                  <Popconfirm
                    :title="`确认${item.approveEnable ? '关闭' : '开启'}Review配置？`"
                    @confirm="() => handleSwitchConfig(GitlabConfigType.Review, !item.approveEnable, item.ID)"
                  >
                    <BasicButton type="text" size="small" @click.stop>
                      <Icon
                        icon="devGuard-auditor-line|svg"
                        :class="item.approveEnable ? '!c-#db851f' : 'c-FO-Content-Text3'"
                      />
                    </BasicButton>
                  </Popconfirm>
                  <Popconfirm title="确定要删除该审查组吗" @confirm.stop="handleDelete(item)">
                    <BasicButton type="text" size="small" @click.stop>
                      <Icon class="!c-FO-Functional-Error1-Default" icon="ant-design:delete-outlined" />
                    </BasicButton>
                  </Popconfirm>
                </div>
              </div>
            </div>
          </div>
        </Spin>
      </ScrollContainer>
      <ScrollContainer class="flex-1">
        <div class="ml-4 max-h-[calc(100vh_-_206px)]">
          <div v-if="!showGroupList?.length" class="rounded-md bg-FO-Container-Fill1 p-4">
            <Empty :image="Empty.PRESENTED_IMAGE_SIMPLE" description="该分支未配置审查组">
              <BasicButton type="primary" @click="handleCreate">
                新增审查组
              </BasicButton>
            </Empty>
          </div>
          <Spin v-else-if="activeGroupID" :spinning="loadingActiveGroup">
            <PreviewPagePage
              :repoID="repoID"
              :branchInfo="branchInfo"
              :curGroup="activeGroup"
              @updated="handleUpdated"
            />
          </Spin>
        </div>
      </ScrollContainer>
    </div>
    <CherrypickModalHolder />
    <DeleteModalHolder />
  </PageWrapper>
</template>

<script lang="tsx" setup>
import { Empty, Popconfirm, Spin } from 'ant-design-vue';
import { ref, watch } from 'vue';
import { useLatestPromise, useModalShow } from '@hg-tech/utils-vue';
import {
  addReviewGroups,
  clearBranchExtras,
  deleteCherryPicks,
  deleteReviewGroups,
  getCherryPicksList,
  getReviewGroupsList,
  GitlabConfigType,
  selectReviewGroups,
  switchConfigEnable,
} from '../../../../api/page/gitlab';
import { getAllPaginationList } from '../../../../hooks/web/usePagination';
import { Icon } from '../../../../components/Icon';
import { PageWrapper } from '../../../../components/Page';
import { useUserStore } from '../../../../store/modules/user';
import type { BranchesBaseItemListItem } from '../../../../api/page/model/gitlabModel.js';
import { ScrollContainer } from '../../../../components/Container';
import { EllipsisText } from '../../../../components/EllipsisText';
import { BasicButton } from '../../../../components/Button';
import ModalCherryPick from './components/ModalCherryPick.vue';
import PreviewPagePage from './components/PreviewPage.vue';
import DeleteModal from '../../../../components/DeleteModal.vue';
import GroupButtons from './components/GroupButtons.vue';
import StatusButton from './components/StatusButton.vue';

const props = defineProps<{
  repoID: number;
  branchInfo: BranchesBaseItemListItem;
}>();

const emit = defineEmits<{
  (e: 'update'): void;
  (e: 'back'): void;
}>();

const userStore = useUserStore();

const activeGroupID = ref<number>();
const { data: activeGroup, execute: refreshActiveGroup, loading: loadingActiveGroup } = useLatestPromise(async () => {
  if (activeGroupID.value) {
    const res = await selectReviewGroups(userStore.getProjectId, activeGroupID.value);
    return res?.gitReviewGroup;
  }
});
watch(activeGroupID, () => refreshActiveGroup(), { immediate: true });

const { data: showGroupList, execute: getProjectGroupList, loading: loadingGroupList } = useLatestPromise(() => {
  if (!userStore.getProjectId || !props.branchInfo?.ID) {
    return [];
  }
  return getAllPaginationList(() => getAllPaginationList((p) => getReviewGroupsList(userStore.getProjectId, {
    ...p,
    repoID: props.repoID,
    branchID: props.branchInfo.ID,
  }))).then((res) => res?.list || []);
});
watch(showGroupList, (list) => {
  if (!activeGroupID.value || list?.every((i) => i.ID !== activeGroupID.value)) {
    activeGroupID.value = list?.[0]?.ID;
  }
});

// 获取审查组列表
watch([() => props.repoID, () => props.branchInfo?.ID], () => getProjectGroupList(), { immediate: true });

async function handleUpdated() {
  return Promise.all([
    getProjectGroupList(),
    refreshActiveGroup(),
  ]);
}

async function handleCreate() {
  const { id } = await addReviewGroups(userStore.getProjectId, {
    repoID: props.repoID,
    branchID: props.branchInfo.ID,
    description: `未命名-${Math.random().toString(16).slice(2, 7)}`,
  });
  await getProjectGroupList();
  activeGroupID.value = id;
}

async function handleSwitchConfig(type: GitlabConfigType, enable: boolean, id: number | undefined) {
  const res = await switchConfigEnable(userStore.getProjectId, {
    id,
    type,
    value: enable,
  });
  if (res?.code !== 7) {
    return handleUpdated();
  }
}

async function handleDelete(record: Recordable) {
  await deleteReviewGroups(userStore.getProjectId, record.ID);
  await getProjectGroupList();
}

const [CherrypickModalHolder, showCherrypickModal] = useModalShow(ModalCherryPick);
async function handleShowCherrypickModal() {
  await showCherrypickModal({
    repoID: props.repoID,
    branchInfo: props.branchInfo,
  });
  emit('update');
}

const [DeleteModalHolder, showDeleteModal] = useModalShow(DeleteModal);
async function deleteConfig() {
  await showDeleteModal({
    title: '删除可提交单号配置',
    description: (
      <div>
        <div>即将删除: </div>
        <div>
          <div>
            <span>该分支下的</span>
            <b>git审查配置</b>
          </div>
          <div>删除后，您可以随时重新配置</div>
        </div>
      </div>
    ),
    async onOk() {
      if (props.branchInfo.ID && props.repoID) {
        // 删除cherrypick
        const cherrypickInfoRes = await getCherryPicksList(userStore.getProjectId, {
          page: 1,
          pageSize: 1,
          repoID: props.repoID,
          branchID: props.branchInfo.ID,
        });
        const cherryPickId = cherrypickInfoRes?.list?.[0]?.ID;
        if (cherryPickId) {
          await deleteCherryPicks(userStore.getProjectId, cherryPickId);
        }
        await clearBranchExtras(userStore.getProjectId, props.branchInfo.ID, { repoID: props.repoID });
      }
    },
  });
  emit('update');
}
</script>

<style lang="less" scoped>
.gitlab-gitlab-group {
  &__tab {
    margin: 8px 0;

    &-item {
      display: flex;
      justify-content: space-between;
      margin: 8px 0;
      padding: 8px;
      border: 1px solid transparent;
      border-radius: 6px;
      cursor: pointer;

      &:hover {
        border-color: @FO-Container-Stroke1;
      }

      &[active='true'] {
        border-color: @FO-Brand-Primary-Default;
      }
    }
  }
}
</style>
