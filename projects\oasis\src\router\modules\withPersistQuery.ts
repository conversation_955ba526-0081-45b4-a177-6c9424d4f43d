import type { Router } from 'vue-router';

export function withPersistQuery(router: Router) {
  router.beforeResolve((to, from, next) => {
    if (
      (from.query?.fs === '1' && to.query?.fs == null)
      || (['1', '2'].includes(from.query?.oasis as string) && to.query?.oasis == null)
    ) {
      return next({
        ...to,
        query: {
          ...to.query,
          fs: to.query?.fs || from.query?.fs, // 隐藏框
          oasis: to.query?.oasis || from.query?.oasis, // oasis 内
        },
        replace: true,
      });
    }

    next();
  });
}
