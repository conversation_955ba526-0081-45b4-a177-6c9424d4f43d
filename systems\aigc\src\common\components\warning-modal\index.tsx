import { useNaiveModal } from '@/common/hooks/use-naive-modal.hook';
import { setItem, STORAGE_KEY } from '@/common/utils/localstorage';
import { LLMWarningModal } from './llm-warning-modal';
import { AIToolWarningModal } from './ai-tool-warning-modal';
import { NIcon } from 'naive-ui';
import { Security } from '../svg-icons';

function showAIWarningModal(showCountdown = true) {
  const { create, isModalOpen } = useNaiveModal();
  if (isModalOpen.value) {
    // 如果已经有弹窗在打开，则不再打开新的弹窗
    return;
  }

  const close = create({
    title: '语言大模型AI工具使用要求',
    preset: 'dialog',
    class: '!w-496px',
    icon: () => (
      <NIcon class="c-FO-Content-Icon1" size={24}>
        <Security />
      </NIcon>
    ),
    closeOnEsc: false,
    maskClosable: false,
    autoFocus: false,
    closable: false,
    content: () => (
      <LLMWarningModal
        countdown={showCountdown}
        onConfirm={() => {
          setItem(STORAGE_KEY.LLM_WARNING_MODEL_SHOW, '1');
          close();
        }}
      />
    ),
  });
}

function showAIToolWarningModal(showCountdown = true) {
  const { create, isModalOpen } = useNaiveModal();
  if (isModalOpen.value) {
    // 如果已经有弹窗在打开，则不再打开新的弹窗
    return;
  }

  const close = create({
    title: 'AI生图/音频/视频工具使用要求',
    preset: 'dialog',
    class: '!w-496px',
    icon: () => (
      <NIcon class="c-FO-Content-Icon1" size={24}>
        <Security />
      </NIcon>
    ),
    closeOnEsc: false,
    maskClosable: false,
    autoFocus: false,
    closable: false,
    content: () => (
      <AIToolWarningModal
        countdown={showCountdown}
        onConfirm={() => {
          setItem(STORAGE_KEY.AI_TOOL_WARNING_MODEL_SHOW, '1');
          close();
        }}
      />
    ),
  });
}

export {
  showAIToolWarningModal,
  showAIWarningModal,
};
