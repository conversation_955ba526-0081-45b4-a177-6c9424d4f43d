<template>
  <Error v-if="loaded" :status="403" />
</template>

<script setup lang="ts">
import { useRouter } from 'vue-router';
import { ref, watch } from 'vue';
import Error from './Error.vue';

const router = useRouter();
const loaded = ref(false);
watch(() => router.getRoutes(), async (routes) => {
  if (routes?.length) {
    // 去第一个可跳转的路由
    await router.replace(routes?.[0]);
  }

  loaded.value = true;
}, { immediate: true });
</script>
