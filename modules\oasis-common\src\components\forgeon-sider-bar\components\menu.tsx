import type { PropType } from 'vue';
import type { Key } from 'ant-design-vue/es/_util/type';
import type { ICommonMenuItem, PlatformEnterPoint } from '../../../configs';
import { PlatformRoutePath } from '../../../configs';
import { computed, defineComponent, ref, watch } from 'vue';
import { Menu } from 'ant-design-vue';
import OutsideLink from '../../../assets/svg/icons/outside-link.svg?component';
import Icon from '@ant-design/icons-vue';
import styles from '../style.module.less';
import { checkInsideRoutePath } from '../helper';

const SubMenu = Menu.SubMenu;
const MenuItem = Menu.Item;

const ForgeOnMenu = defineComponent({
  props: {
    activeMenu: {
      type: String as PropType<PlatformEnterPoint>,
    },
    menus: {
      type: Array as PropType<ICommonMenuItem[]>,
      required: true,
    },
    openKeys: {
      type: Array as PropType<PlatformEnterPoint[]>,
      default: () => [],
    },
    mode: {
      type: String as PropType<'inline' | 'vertical'>,
      default: 'inline',
    },
    onUpdateActiveMenu: {
      type: Function as PropType<(menu?: PlatformEnterPoint) => void>,
      default: () => {},
    },
    onUpdatedOpenKeys: {
      type: Function as PropType<(keys: PlatformEnterPoint[]) => void>,
      default: () => {},
    },
    onPathChange: {
      type: Function as PropType<(params: { path: PlatformRoutePath | string; key: PlatformEnterPoint | string }) => void>,
      default: () => {},
    },
  },
  setup(props) {
    // 控制菜单项的展开和折叠状态
    const openKeys = ref<PlatformEnterPoint[]>([]);

    const activeKeys = computed({
      get: () => [props.activeMenu],
      set: (value) => {
        if (checkInsideRoutePath(props.menus.find((item) => item.key === value[0])?.path ?? '')) {
          props.onUpdateActiveMenu(value[0]);
        }
      },
    });

    watch(() => props.openKeys, (value) => {
      openKeys.value = value;
    }, {
      immediate: true,
    });

    // 处理展开键变化
    const handleOpenKeysChange = (keys: Key[]) => {
      openKeys.value = keys as PlatformEnterPoint[];
      props.onUpdatedOpenKeys(openKeys.value);
    };

    const handleItemClick = (event: MouseEvent, item: ICommonMenuItem) => {
      if (event.target instanceof HTMLAnchorElement) {
        event.preventDefault();
      }
      // 可以在这里处理其他点击逻辑，比如路由跳转
      props.onPathChange({
        path: item.path!,
        key: item.key,
      });
    };

    const isActiveMenu = (item: ICommonMenuItem) => {
      if (item.children && item.children.length > 0) {
        return activeKeys.value[0] && item.children.some((i) => i.key === activeKeys.value[0]);
      } else {
        return activeKeys.value[0] && item.key === activeKeys.value[0];
      }
    };

    // 渲染菜单项和子菜单
    const renderMenuItems = (items: ICommonMenuItem[], isChild?: boolean) => {
      return items.map((item) => {
        if (item.children && item.children.length > 0) {
          return (
            <SubMenu key={item.key} popupClassName={styles.forgeonSubMenuPopup} popupOffset={[14, -8]}>
              {{
                title: () => (
                  <div class="h-full flex items-center FO-Font-R14">
                    {
                      item.svgIcon
                        ? (
                          <Icon
                            class={[styles.menuIcon, 'h-20px w-20px']}
                            component={isActiveMenu(item) && item.activeIcon ? item.activeIcon : item.svgIcon}
                            style={{
                              fontSize: '20px',
                            }}
                          />
                        )
                        : null
                    }
                    <div class="flex-1 FO-Font-R14">{item.title}</div>
                  </div>
                ),
                default: () => renderMenuItems(item.children ?? [], true),
              }}
            </SubMenu>
          );
        }

        return (
          <MenuItem key={item.key}>
            {{
              default: () => (
                <a
                  class={[
                    !item.svgIcon && isChild && props.mode !== 'vertical' ? 'pl-20px' : '',
                    'h-full block flex items-center',
                  ]}
                  href={item.path}
                  onClick={(event) => handleItemClick(event, item)}
                >
                  {
                    item.svgIcon
                      ? (
                        <Icon
                          class={[styles.menuIcon, 'h-20px w-20px']}
                          component={isActiveMenu(item) && item.activeIcon ? item.activeIcon : item.svgIcon}
                          style={{
                            fontSize: '20px',
                          }}
                        />
                      )
                      : null
                  }
                  <div class="FO-Font-R14">{item.title}</div>
                  {
                    Object.values(PlatformRoutePath).includes(item.path as PlatformRoutePath)
                      ? null
                      : (
                        <Icon
                          component={(
                            <OutsideLink class="c-FO-Content-Icon1 font-size-12px" />
                          )}
                          style={{
                            marginInlineStart: '4px',
                          }}
                        />
                      )
                  }
                </a>
              ),
            }}
          </MenuItem>
        );
      });
    };

    return () => (
      <Menu
        class={styles.forgeonMenu}
        inlineIndent={8}
        mode={props.mode}
        onOpenChange={handleOpenKeysChange}
        openKeys={props.mode === 'inline' ? openKeys.value : []}
        v-model:selectedKeys={activeKeys.value}
      >
        {renderMenuItems(props.menus)}
      </Menu>
    );
  },
});

export { ForgeOnMenu };
