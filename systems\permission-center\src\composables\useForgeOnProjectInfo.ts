import { useMicroAppInject, usePlatformConfigCtx } from '@hg-tech/oasis-common';
import { computed } from 'vue';

export function useForgeOnProjectInfo() {
  const { data } = useMicroAppInject(usePlatformConfigCtx);
  return {
    projectList: computed(() => data.value?.projectList),
    currentProjectId: computed(() => data.value?.currentProjectId),
    setCurrentProjectId: data.value?.setCurrentProjectId,
  };
}
