import { type DropdownOption, type InputInst, N<PERSON>utton, NDropdown, NEllipsis, NIcon, NInput, NScrollbar, NText, useMessage } from 'naive-ui';
import { type PropType, computed, defineComponent, nextTick, ref } from 'vue';
import styles from './communications.module.less';
import dayjs from 'dayjs';
import type { ChatSessionItem } from '@/models/chat';
import { Check, Delete, Dots, Edit, Error } from '@/common/components/svg-icons';
import { useNaiveModal } from '@/common/hooks/use-naive-modal.hook';

const Communications = defineComponent({
  props: {
    data: {
      type: Array as PropType<ChatSessionItem[]>,
      default: () => [],
    },
    active: {
      type: String as PropType<string | undefined>,
    },
    isLoading: {
      type: Boolean as PropType<boolean>,
      default: false,
    },
    isFinish: {
      type: Boolean as PropType<boolean>,
      default: false,
    },
    contentClass: {
      type: String as PropType<string>,
      default: '',
    },
    onScroll: {
      type: Function as PropType<(e: Event) => void>,
      default: () => {},
    },
    onDelete: {
      type: Function as PropType<(id: string) => void>,
      default: () => {},
    },
    onTitleChange: {
      type: Function as PropType<(data: { sessionId: string; title: string }) => void>,
      default: () => {},
    },
  },
  emits: ['update:active'],
  setup(props, { emit }) {
    const { create } = useNaiveModal();
    const message = useMessage();
    const editItem = ref<string | null>(null);
    const editTitle = ref<string>('');
    const editInputRef = ref<InputInst | null>(null);
    const isItemEdit = (id: string) => {
      return editItem.value === id;
    };
    const listGroup = computed(() => {
      const groups = [
        { label: '今天', items: [] as ChatSessionItem[] },
        { label: '昨天', items: [] as ChatSessionItem[] },
        { label: '7天内', items: [] as ChatSessionItem[] },
        { label: '按月份', items: [] as ChatSessionItem[] },
        { label: '按年', items: [] as ChatSessionItem[] },
      ];
      const today = dayjs();
      const yesterday = today.subtract(1, 'day');
      const last7Days = today.subtract(7, 'day');
      props.data.forEach((item) => {
        const itemDate = dayjs.unix((item.updateTime || 0));
        if (itemDate.isSame(today, 'day')) {
          groups[0].items.push(item);
        } else if (itemDate.isSame(yesterday, 'day')) {
          groups[1].items.push(item);
        } else if (itemDate.isAfter(last7Days)) {
          groups[2].items.push(item);
        } else if (itemDate.isSame(today, 'year')) {
          groups[3].label = itemDate.format('M月');
          groups[3].items.push(item);
        } else {
          groups[4].label = itemDate.format('YYYY年');
          groups[4].items.push(item);
        }
      });
      return groups;
    });

    const actionOptions: DropdownOption[] = [
      {
        label: '修改名称',
        key: 'rename',
        icon: () => (
          <NIcon>
            <Edit />
          </NIcon>
        ),
      },
      {
        label: '删除对话',
        key: 'delete',
        icon: () => (
          <NIcon>
            <Delete />
          </NIcon>
        ),
      },
    ];

    const onHandleActive = (id: string) => {
      emit('update:active', id);
    };

    const handleDropdownSelect = (key: string, item: ChatSessionItem) => {
      if (key === 'rename') {
        editItem.value = item.sessionId;
        editTitle.value = props.data.find((i) => i.sessionId === item.sessionId)?.title || '';
        nextTick(() => {
          editInputRef.value?.focus();
        });
      } else {
        const close = create({
          preset: 'dialog',
          title: '删除对话确认',
          content: () => <NText class="c-FO-Content-Text2 block p-8px FO-Font-R14">对话【{item.title}】，删除后不可恢复，请谨慎操作。</NText>,
          positiveText: '删除',
          negativeText: '取消',
          type: 'error',
          icon: () => <Error />,
          autoFocus: false,
          negativeButtonProps: {
            type: 'default',
            ghost: false,
          },
          onPositiveClick: () => {
            props.onDelete(item.sessionId);
            close();
          },
        });
      }
    };

    const onTitleEdit = () => {
      if (!editItem.value) {
        return;
      }
      if (editTitle.value.trim().length > 12) {
        message.warning('话题长度不能超过12个字');
        return;
      }
      props.onTitleChange({
        sessionId: editItem.value,
        title: editTitle.value,
      });
      editItem.value = null;
    };

    return () => (
      <NScrollbar onScroll={props.onScroll}>
        <div class={[styles.communicationContainer, props.contentClass]}>
          {
            listGroup.value.map((item) => {
              if (item.items.length === 0) {
                return null;
              }
              return (
                <div class="mb-12px">
                  <div class="group-title c-FO-Content-Text3 mb-8px select-none FO-Font-R12">
                    {item.label}
                  </div>
                  <div class="group-content">
                    {
                      item.items.map((i) => {
                        return isItemEdit(i.sessionId)
                          ? (
                            <NInput
                              class="h-40px py-6px"
                              maxlength={12}
                              onBlur={() => {
                                editItem.value = null;
                              }}
                              ref={editInputRef}
                              size="small"
                              v-model:value={editTitle.value}
                            >
                              {{
                                suffix: () => (
                                  <NButton
                                    class="h-24px px-3px py-2px"
                                    disabled={!editTitle.value.trim()}
                                    onClick={onTitleEdit}
                                    renderIcon={() => (
                                      <NIcon size={16}>
                                        <Check />
                                      </NIcon>
                                    )}
                                    secondary
                                    size="small"
                                    type="primary"
                                  />
                                ),

                              }}
                            </NInput>
                          )
                          : (
                            <div
                              class={
                                [
                                  styles.communicationItem,
                                  props.active === i.sessionId ? styles.active : '',
                                  'cursor-pointer rd-md my-4px p-8px flex-c-between',
                                ]
                              }
                              onClick={() => onHandleActive(i.sessionId)}
                            >

                              <NEllipsis
                                class="FO-Font-R14"
                                tooltip={{
                                  delay: 500,
                                  contentClass: 'max-w-500px',
                                }}
                              >
                                {i.title || '--'}
                              </NEllipsis>
                              <NDropdown
                                class="rd-12px"
                                onSelect={(key) => handleDropdownSelect(key, i)}
                                options={actionOptions}
                                placement="right-start"
                                trigger="click"
                              >
                                <NButton
                                  class={styles.communicationItemAction}
                                  onClick={(e) => e.stopPropagation()}
                                  renderIcon={() => <Dots />}
                                  size="small"
                                  text
                                />
                              </NDropdown>
                            </div>
                          );
                      })
                    }
                  </div>
                </div>

              );
            })
          }
          <div
            class="c-FO-Content-Text3 mb-20px text-center FO-Font-R12"
            v-show={props.data.length > 0}
          >
            {
              props.isLoading && !props.isFinish
                ? '加载中…'
                : '已经到底啦'
            }
          </div>
        </div>
      </NScrollbar>
    );
  },
});

export {
  Communications,
};
