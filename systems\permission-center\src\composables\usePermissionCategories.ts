import { computed } from 'vue';
import type { PermissionCheckPoint, PermissionCheckPointCategory } from '../api/manage.ts';
import { useLatestPromise } from '@hg-tech/utils-vue';
import { fetchPermissionCheckPointCategory, fetchPermissionCheckPointList } from '../api/manage.ts';

export const UNCATEGORIZED_SYMBOL = -1;

export interface PermissionCategoryWithUncategorized extends PermissionCheckPointCategory {
  id: number | typeof UNCATEGORIZED_SYMBOL;
  name: string;
  resources: PermissionCheckPoint[];
}

/**
 * 权限分类和未分类处理的共通逻辑
 */
export function usePermissionCategories() {
  // 获取权限分类数据
  const { data: checkPointCategoryRes, loading: loadingCategory, execute: fetchCategory } = useLatestPromise(fetchPermissionCheckPointCategory);

  // 获取权限点列表数据
  const { data: checkPointListRes, loading: loadingPointList, execute: fetchPointList } = useLatestPromise(fetchPermissionCheckPointList);

  const categories = computed(() => checkPointCategoryRes.value?.data?.data ?? []);
  const allPermissionPoints = computed(() => checkPointListRes.value?.data?.data ?? []);

  // 计算包含未分类的分类列表
  const categoriesWithUncategorized = computed<PermissionCategoryWithUncategorized[]>(() => {
    const result: PermissionCategoryWithUncategorized[] = [];

    // 收集所有已分类的权限点ID
    const categorizedPointIds = new Set<number>();

    // 处理已有分类
    categories.value.forEach((category) => {
      const categoryPoints = category.resources || [];
      categoryPoints.forEach((point) => {
        if (point.id) {
          categorizedPointIds.add(point.id);
        }
      });

      result.push({
        ...category,
        id: category.id!,
        name: category.name!,
        resources: categoryPoints,
      });
    });

    // 计算未分类的权限点
    const uncategorizedPoints = allPermissionPoints.value.filter((point) => {
      return point.id && !categorizedPointIds.has(point.id);
    });

    // 如果有未分类的权限点，添加到列表最前面
    if (uncategorizedPoints.length > 0) {
      result.unshift({
        id: UNCATEGORIZED_SYMBOL,
        name: '未分类',
        resources: uncategorizedPoints,
      });
    }

    return result;
  });

  // 刷新数据
  async function refreshData(appId?: number) {
    if (appId) {
      const [categoryRes, pointListRes] = await Promise.all([
        fetchCategory({ appId }, {}),
        fetchPointList({ appId }, {}),
      ]);
      return [categoryRes, pointListRes];
    }
  }

  return {
    categories,
    allPermissionPoints,
    categoriesWithUncategorized,
    loadingCategory,
    loadingPointList,
    loading: computed(() => loadingCategory.value || loadingPointList.value),
    fetchCategory,
    fetchPointList,
    refreshData,
  };
}
