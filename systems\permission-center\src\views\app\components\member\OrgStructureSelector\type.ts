import type { MaybeVNode } from '@hg-tech/oasis-common';
import type { PagedQueryParam, PagedRes } from '../../../../../api/_common.ts';

export interface OrgStructureCellInfo<K extends string = string> {
  key: string;
  /**
   * 单元格标题
   */
  title?: MaybeVNode | ((kw?: string) => MaybeVNode);
  /**
   * 单元格副标题
   */
  subTitle?: MaybeVNode | ((kw?: string) => MaybeVNode);
  /**
   * 单元格图标
   */
  avatar?: string;
  /**
   * 项目类型，用于区分人员、部门和组
   */
  type?: K;
}

export interface OrgStructureSchemaBase<K extends string, T extends OrgStructureCellInfo<K> = OrgStructureCellInfo<K>> {
  type: K;
  /**
   * 渲染分类标题
   * @example 部门
   */
  title: string;
  /**
   * 统计信息渲染
   * @example n=> `${n}部门`
   */
  summary: (total: number) => string;
  /**
   * 获取初始化数据函数
   */
  initFunc?: (pageInfo: PagedQueryParam) => Promise<PagedRes<T>>;
  /**
   * 搜索函数
   */
  searchFunc: (pageInfo: PagedQueryParam, keyword?: string) => Promise<PagedRes<T>>;
}

export interface OrgStructureSchemaTree<K extends string, T extends OrgStructureCellInfo<K> = OrgStructureCellInfo<K>> extends OrgStructureSchemaBase<K, T> {
  isTreeSchema: true;
  initFunc?: (pageInfo: PagedQueryParam, parentId?: T['key']) => Promise<PagedRes<T>>;
  breadcrumbRender: (item: T) => MaybeVNode;
}

export type OrgStructureSchema<K extends string, T extends OrgStructureCellInfo<K> = OrgStructureCellInfo<K>> =
  | OrgStructureSchemaBase<K, T>
  | OrgStructureSchemaTree<K, T>;

export type OrgStructureValue<K extends string> = Record<K, OrgStructureCellInfo<K>['key'][]>;
export interface OrgStructurePayload<K extends string, T extends OrgStructureCellInfo<K> = OrgStructureCellInfo<K>> {
  key: K;
  data: T;
}
