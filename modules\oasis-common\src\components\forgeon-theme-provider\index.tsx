import { type PropType, computed, defineComponent, onMounted, ref, watch } from 'vue';
import { ForgeonTheme, getCssDeclareOverrides } from '@hg-tech/forgeon-style';

/**
 * Forgeon Design主题提供器, 用于覆盖全局主题样式
 */
const ForgeonThemeProvider = defineComponent({
  props: {
    theme: {
      type: String as PropType<ForgeonTheme>,
      default: ForgeonTheme.Light,
    },
    override: {
      type: Object as PropType<Record<string, string>>,
      default: () => ({}),
    },
  },
  setup(props, { slots }) {
    const themeProviderRef = ref<HTMLElement | null>(null);
    const overrideValue = computed(() => {
      return Object.keys(props.override).map((key) => `${key}: ${props.override[key]};`).join('');
    });

    function triggerThemeChange(theme: ForgeonTheme) {
      themeProviderRef.value?.setAttribute('style', getCssDeclareOverrides(theme) + overrideValue.value);
    }

    watch(() => props.theme, (value) => {
      triggerThemeChange(value);
    });

    onMounted(() => {
      triggerThemeChange(props.theme);
    });

    return () => {
      return (
        <div class="forgeon-theme-provider" ref={themeProviderRef}>
          {slots.default?.()}
        </div>
      );
    };
  },
});

export {
  ForgeonThemeProvider,
};
