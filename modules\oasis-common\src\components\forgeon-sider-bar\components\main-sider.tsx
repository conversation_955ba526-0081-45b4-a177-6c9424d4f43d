import { ForgeonTheme } from '@hg-tech/forgeon-style';
import type { PropType } from 'vue';
import type { RoleListItem, UserInfoModel } from '../../../models';
import { defineComponent, ref, watch, watchEffect } from 'vue';
import ForgeonIcon from '../../../assets/svg/forgeon-icon.svg?component';
import styles from '../style.module.less';
import { ForgeonAvatar } from './forgeon-avatar';
import { type ICommonMenuItem, PlatformEnterPoint, PlatformRoutePath } from '../../../configs';
import { Popover } from 'ant-design-vue';
import { ForgeOnMenu } from './menu';
import { useThrottleFn } from '@vueuse/core';
import { useForgeOnSider } from '../useForgeOnSider';

const MainSider = defineComponent({
  props: {
    userInfo: {
      type: Object as PropType<UserInfoModel>,
      default: () => ({}),
    },
    theme: {
      type: String as PropType<ForgeonTheme>,
      default: ForgeonTheme.Dark,
    },
    displayModules: {
      type: Array as PropType<PlatformEnterPoint[]>,
      default: () => [],
    },
    onSwitchRole: {
      type: Function as PropType<(role?: RoleListItem) => void>,
      default: () => {},
    },
    onLogout: {
      type: Function as PropType<() => void>,
      default: () => {},
    },
    onLogin: {
      type: Function as PropType<() => void>,
      default: () => {},
    },
    onToggleDarkMode: {
      type: Function as PropType<(e: MouseEvent, mode: ForgeonTheme) => void>,
      default: () => {},
    },
    onPathChange: {
      type: Function as PropType<(params: { path: PlatformRoutePath | string; key: PlatformEnterPoint | string }) => void>,
      default: () => {},
    },
  },
  setup(props) {
    const { collapsed, modules, activeMenu, activeMainModule, openKeys, showSubSider, setModule, getMenuByModule } = useForgeOnSider();
    const visible = ref(false);
    const focusModuleRef = ref<HTMLElement | null>(null);
    const focusModuleName = ref<PlatformEnterPoint | null>(null);
    let hoverTimer: NodeJS.Timeout | null = null;

    const onPopoverVisibleOpen = useThrottleFn((v: boolean) => {
      visible.value = v;
    }, 50);

    const onMenuItemHover = (e: MouseEvent, module: ICommonMenuItem) => {
      hoverTimer = setTimeout(() => {
        focusModuleRef.value = e.currentTarget as HTMLElement;
        focusModuleName.value = module.key;
        (e.currentTarget as HTMLElement)?.focus();
      }, 100);
    };

    const onMenuItemHoverLeave = () => {
      clearTimeout(hoverTimer!);
    };

    /**
     * 页面可见性变化时，关闭所有弹出菜单
     */
    const onPageVisibleChange = () => {
      onPopoverVisibleOpen(false);
      focusModuleRef.value?.blur();
    };
    // 监听折叠状态变化，关闭所有弹出菜单
    watch(() => collapsed.value, onPageVisibleChange);
    // 监听当前激活的模块变化，如果不是当前激活的模块，关闭所有弹出菜单
    watch(() => activeMainModule.value, () => {
      if (activeMainModule.value !== focusModuleName.value) {
        focusModuleRef.value?.blur();
        focusModuleName.value = null;
        visible.value = false;
      }
    });

    const renderModuleItem = (module: ICommonMenuItem, index: number) => {
      return (
        <div
          class={[
            styles.moduleItem,
            module.key === activeMainModule.value ? styles.active : '',
            'flex flex-col cursor-pointer items-center justify-center',
          ]}
          key={module.key}
          onClick={() => setModule(module.key)}
          onMouseenter={(e) => onMenuItemHover(e, module)}
          onMouseleave={onMenuItemHoverLeave}
          tabindex={index}
        >
          <div class={styles.moduleIcon}>{
            module.key === activeMainModule.value && module.activeIcon
              ? module.activeIcon()
              : module.svgIcon?.()
          }
          </div>
          <div class="FO-Font-R12">{module.title}</div>
        </div>
      );
    };

    const renderModule = (module: ICommonMenuItem, index: number) => {
      const isCurrentModuleShowPopover = module.key === activeMainModule.value && collapsed.value;
      const isNotCurrentModuleShowPopover = module.key !== activeMainModule.value;
      const renderMenus = getMenuByModule(module.key);
      return (isNotCurrentModuleShowPopover || isCurrentModuleShowPopover) && renderMenus.length > 0
        ? (
          <Popover
            arrow={false}
            getPopupContainer={() => document.querySelector(`.${styles.forgeonSider}`)!}
            key={module.key}
            mouseLeaveDelay={0.5}
            onUpdate:open={(v) => (v ? onPopoverVisibleOpen(v) : visible.value = false)}
            open={visible.value && focusModuleName.value === module.key}
            overlayClassName={styles.menuPopover}
            placement="rightTop"
            trigger={['hover', 'focus']}
            v-slots={{
              content: () => (
                <ForgeOnMenu
                  key={module.name}
                  menus={renderMenus}
                  mode="vertical"
                  onPathChange={(params) => {
                    props.onPathChange(params);
                    visible.value = false;
                  }}
                  v-model:activeMenu={activeMenu.value}
                  v-model:openKeys={openKeys.value}
                />
              ),
              default: () => renderModuleItem(module, index),
            }}
          />
        )
        : renderModuleItem(module, index)
      ;
    };

    watchEffect((onCleanUp) => {
      window.addEventListener('visibilitychange', onPageVisibleChange);
      window.addEventListener('blur', onPageVisibleChange);
      onCleanUp(() => {
        window.removeEventListener('visibilitychange', onPageVisibleChange);
        window.removeEventListener('blur', onPageVisibleChange);
      });
    });

    return () => (
      <div class={`${styles.modulesWrapper}`}>
        <div class="flex flex-col items-center">
          <div
            class={[styles.icon, 'mb-32px cursor-pointer']}
            onClick={() => {
              showSubSider.value = false;
              props.onPathChange({
                key: PlatformEnterPoint.Home,
                path: PlatformRoutePath.Home,
              });
            }}
          >
            <ForgeonIcon />
          </div>

          <div class={[styles.modulesContainer, 'w-full']}>
            {modules.value.filter((item) => props.displayModules.includes(item.key)).map(renderModule)}
          </div>
        </div>
        <ForgeonAvatar
          onLogin={props.onLogin}
          onLogout={props.onLogout}
          onSwitchRole={props.onSwitchRole}
          onToggleDarkMode={props.onToggleDarkMode}
          theme={props.theme}
          userInfo={props.userInfo}
        />
      </div>
    );
  },
});

export { MainSider };
