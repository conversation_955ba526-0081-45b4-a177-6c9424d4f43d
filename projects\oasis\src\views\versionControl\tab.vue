<template>
  <div :class="prefixCls">
    <div :class="`${prefixCls}__header`">
      <div class="my-3 flex items-center justify-between">
        <div class="ml-[8px]">
          <LineTab
            :tabList="menuList"
            :defaultActiveTab="curActiveTab"
            tabMargin="24px"
            showIcon
            @change="handleChangeTab"
          />
        </div>
        <div class="flex items-center gap-[8px]">
          <slot name="action" />
          <Button class="custom-rounded-btn" @click="handleToManager">
            <div class="flex items-center">
              <SettingConfig class="mr-1 flex" theme="outline" />
              <span>项目配置</span>
            </div>
          </Button>
        </div>
      </div>
    </div>
    <div :class="`${prefixCls}__body`">
      <slot />
    </div>
  </div>
</template>

<script lang="ts" setup name="DevGuardTab">
import { Button } from 'ant-design-vue';
import { computed, ref } from 'vue';
import LineTab from '/@/components/LineTab';
import { useDesign } from '/@/hooks/web/useDesign';
import { SettingConfig } from '@icon-park/vue-next';
import { useRouter } from 'vue-router';
import { checkPermissionPass, PermissionPoint, PlatformEnterPoint } from '@hg-tech/oasis-common';
import { usePermissionInfo } from '../../service/permission/usePermission.ts';

const props = defineProps({
  curTab: {
    type: String,
    default: '',
  },
});

const TAB_LIST = [
  {
    name: PlatformEnterPoint.P4Depots,
    icon: 'menu-branch-manage|svg',
    title: 'P4分支管理平台',
    permissionDeclaration: {
      any: [PermissionPoint.P4Depots],
    },
  },
  {
    name: PlatformEnterPoint.P4MemberManagement,
    icon: 'menu-p4-member|svg',
    title: '项目成员配置',
    permissionDeclaration: {
      any: [PermissionPoint.P4MemberManagement],
    },
  },
  {
    name: PlatformEnterPoint.P4GroupManagement,
    icon: 'menu-p4-group|svg',
    title: 'P4组配置',
    permissionDeclaration: {
      any: [PermissionPoint.P4GroupManagement],
    },
  },
  {
    name: PlatformEnterPoint.ResourceCheckOld,
    icon: 'menu-submit-checks|svg',
    title: '提交检查配置【旧】',
    permissionDeclaration: {
      any: [PermissionPoint.ResourceCheckOld],
    },
  },
  {
    name: PlatformEnterPoint.Gitlab,
    icon: 'menu-gitlab|svg',
    title: 'Git分支管理',
    permissionDeclaration: {
      any: [PermissionPoint.Gitlab],
    },
  },
];

const router = useRouter();
const { permissionInfo } = usePermissionInfo();
const { prefixCls } = useDesign('dev-guard-tab');
const menuList = computed(() => {
  return TAB_LIST.filter((item) => checkPermissionPass(item.permissionDeclaration, permissionInfo.value));
});
const curActiveTab = ref(props.curTab || menuList.value[0]?.name);

function handleChangeTab(tabName: PlatformEnterPoint) {
  router.push({ name: tabName });
}

function handleToManager() {
  router.push({ name: 'ProjectsManagement' });
}
</script>

<style lang="less">
@prefix-cls: ~'hypergryph-dev-guard-tab';
.@{prefix-cls} {
  display: flex;
  flex-direction: column;
  height: 100%;

  &__header {
    flex: none;
    margin: 20px 20px 0 20px;
    padding: 16px;
    background-color: @FO-Container-Fill1;
    overflow: auto;
    border-radius: 8px;
  }

  &__body {
    flex: auto;
    overflow: auto;
  }
}
</style>
