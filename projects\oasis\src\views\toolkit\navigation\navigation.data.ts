import type { BasicColumn, FormSchema } from '/@/components/Table';

export const columns: BasicColumn[] = [
  {
    title: '标题',
    dataIndex: 'title',
    width: 200,
  },
  {
    title: '图标',
    dataIndex: 'icon',
    width: 100,
  },
  {
    title: '网址',
    dataIndex: 'url',
  },
];

export const formSchema: FormSchema[] = [
  {
    field: 'url',
    label: '网址',
    component: 'Input',
    required: true,
  },
  {
    field: 'title',
    label: '标题',
    component: 'Input',
    componentProps: {
      placeholder: '请输入标题',
    },
    required: true,
  },
  {
    field: 'icon',
    label: '图标',
    component: 'Upload',
    valueField: 'singleValue',
    rules: [{ required: true, message: '请上传图标' }],
    componentProps: {
      valueFormat: 'string',
      maxSize: 20,
      maxNumber: 1,
      multiple: false,
      isPicture: true,
      accept: ['.jpg', '.jpeg', '.gif', '.png', '.webp'],
    },
    required: true,
  },
];
