import { useCountdown } from '@/common/hooks/countdown.hook';
import { NButton, NP, NText } from 'naive-ui';
import { type PropType, defineComponent, onMounted } from 'vue';

const AIToolWarningModal = defineComponent({
  emits: ['confirm'],
  props: {
    countdown: {
      type: Boolean as PropType<boolean>,
      default: true,
    },
  },
  setup(props, { emit }) {
    const { loading, start, remainingTime } = useCountdown();

    const startCountdown = async () => {
      await start(5);
    };

    onMounted(() => {
      props.countdown && startCountdown();
    });

    return () => (
      <div>
        <div class="mb-12px FO-Font-R14">
          <NP>
            1. 特定AI工具的存在与使用属于公司保密信息，仅限有权限员工使用，禁止向无权限人员透露AI工具存在，包括其他部门员工、外包人员、合作伙伴等，不得通过口头、截图、录屏等方式展示工具界面或生成效果。

          </NP>
          <NText class="c-FO-Content-Text3">*非工作必要情况下，任何向无关/无权限人员主动提及工具名称、功能细节、
            生成效果的行为均属违反保密要求。
          </NText>
          <NP>2. 禁止通过非公司渠道（如个人网盘、社交软件）传输任何AI生成的内容；</NP>
          <NP>3. 所有生成内容未经部门leader明确确认，禁止直接导入游戏项目资源库、宣传物料或对外发布。</NP>
        </div>
        <div class="flex-c-between">
          <div />
          <NButton disabled={loading.value} loading={loading.value} onClick={() => emit('confirm')} type="primary">
            {
              loading.value
                ? `我已知悉(${remainingTime.value}秒)`
                : '我已知悉'
            }
          </NButton>
        </div>
      </div>
    );
  },
});

export {
  AIToolWarningModal,
};
