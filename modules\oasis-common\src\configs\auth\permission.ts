/**
 * 临时使用路由名作为权限点以兼容旧数据
 *
 * @deprecated 需要重构为真正的权限点
 */
export enum PermissionPoint {
  // 测试平台

  AccountSettings = 'AccountSettings',
  AssetLib = 'AssetLib',
  AudioPage = 'AudioPage',
  Automation = 'Automation',
  AutomationReport = 'AutomationReport',
  AutomationSet = 'AutomationSet',
  AutomationTask = 'AutomationTask',
  P4TouchProcess = 'P4TouchProcess',
  BaseSettings = 'BaseSettings',
  BugRob = 'BugRob',
  BugRobotChats = 'BugRobotChats',
  BugRobotReports = 'BugRobotReports',
  BugRobotSettings = 'BugRobotSettings',
  CloudTest = 'CloudTest',
  CommitCheck = 'CommitCheck',
  CrashClassDetail = 'CrashClassDetail',
  CrashCollect = 'CrashCollect',
  CrashDetail = 'CrashDetail',
  DeptAsset = 'DeptAsset',
  DeptAssetApplyManagement = 'DeptAssetApplyManagement',
  CloudDevice = 'CloudDevice',
  CloudDeviceDetail = 'CloudDeviceDetail',
  DeptAssetsManagement = 'DeptAssetsManagement',
  DeptManagement = 'DeptManagement',
  DeptMemberManagement = 'DeptMemberManagement',
  DevGuard = 'DevGuard',
  Development = 'Development',
  BugRobot = 'BugRobot',
  DeviceManagement = 'DeviceManagement',
  DeviceManagementAdminConfig = 'DeviceManagementAdminConfig',
  DeviceManagementFaultList = 'DeviceManagementFaultList',
  DeviceManagementLogs = 'DeviceManagementLogs',
  DM01GroupManagement = 'DM01GroupManagement',
  EngineCompatibleSet = 'EngineCompatibleSet',
  FeatureSettings = 'FeatureSettings',
  FileManager = 'FileManager',
  GameArchive = 'GameArchive',
  GamePackage = 'GamePackage',
  GamePackageCard = 'GamePackageCard',
  GamePackageDoctor = 'GamePackageDoctor',
  GamePackageDoctorCompare = 'GamePackageDoctorCompare',
  GamePackageDoctorProportion = 'GamePackageDoctorProportion',
  GamePackageDoctorTrend = 'GamePackageDoctorTrend',
  GamePackageSettings = 'GamePackageSettings',
  GamePackageSettingsVersions = 'GamePackageSettingsVersions',
  GamePackageVersionSimple = 'GamePackageVersionSimple',
  Gitlab = 'Gitlab',
  GitlabReviewList = 'GitlabReviewList',
  ******************** = '********************',
  GitlabSubmitDescriptionSpecifications = 'GitlabSubmitDescriptionSpecifications',
  HDALib = 'HDALib',
  HomeSettings = 'HomeSettings',
  InstructionCombinations = 'InstructionCombinations',
  InstructionComponents = 'InstructionComponents',
  // oasis聚合
  Oasis = 'OasisManagement',
  Instructions = 'Instructions',
  JenkinsAutoTask = 'JenkinsAutoTask',
  LoadTest = 'LoadTest',
  MaterialLib = 'MaterialLib',
  MenuManagement = 'MenuManagement',
  MessageCenter = 'MessageCenter',
  Admin = 'Admin',
  MessageTemplate = 'MessageTemplate',
  MyAccount = 'MyAccount',
  OasisAuthSet = 'OasisAuthSet',
  OrganizationManagement = 'OrganizationManagement',
  OriginInstructions = 'OriginInstructions',
  OutsourcingPlatform = 'OutsourcingPlatform',
  P4ClLabelManagement = 'P4ClLabelManagement',
  P4CompleteNoticeManagement = 'P4CompleteNoticeManagement',
  P4CustomGroupManagement = 'P4CustomGroupManagement',
  P4Depots = 'P4Depots',
  P4ExamSettings = 'P4ExamSettings',
  P4FormDiff = 'P4FormDiff',
  P4FormDiffDetail = 'P4FormDiffDetail',
  P4GroupManagement = 'P4GroupManagement',
  P4LdapGroupManagement = 'P4LdapGroupManagement',
  P4MemberManagement = 'P4MemberManagement',
  P4Onboarding = 'P4Onboarding',
  P4OnboardingSettings = 'P4OnboardingSettings',
  P4OnboardingSettingsChild = 'P4OnboardingSettingsChild',
  P4Pass = 'P4Pass',
  P4PermissionManagement = 'P4PermissionManagement',
  P4Training = 'P4Training',
  P4Trains = 'P4Trains',
  P4TrainsSettings = 'P4TrainsSettings',
  P4Triggers = 'P4Triggers',
  P4TriggersOverview = 'P4TriggersOverview',
  P4TriggersParamsSettings = 'P4TriggersParamsSettings',
  P4TriggersSettings = 'P4TriggersSettings',
  PackageManagement = 'PackageManagement',
  PeopleManage = 'PeopleManage',
  PerfdeepCardCompare = 'PerfdeepCardCompare',
  PerfdeepCardTrend = 'PerfdeepCardTrend',
  PerfdeepCase = 'PerfdeepCase',
  PerfdeepCaseDetail = 'PerfdeepCaseDetail',
  PerforceAccessLevelsSettings = 'PerforceAccessLevelsSettings',
  PerforceManagement = 'PerforceManagement',
  PerforceServersSettings = 'PerforceServersSettings',
  PerforceSettings = 'PerforceSettings',
  Performance = 'Performance',
  PerformanceCard = 'PerformanceCard',
  PerformanceCardCompare = 'PerformanceCardCompare',
  PerformanceCardTrend = 'PerformanceCardTrend',
  PerformanceCase = 'PerformanceCase',
  PerformanceCaseCompare = 'PerformanceCaseCompare',
  PerformanceCaseDetail = 'PerformanceCaseDetail',
  PerformanceMap = 'PerformanceMap',
  UnrealCase = 'UnrealCase',
  UnrealCaseDetail = 'UnrealCaseDetail',
  PerformanceReference = 'PerformanceReference',
  PerformanceHeatMap = 'PerformanceHeatMap',
  PerformanceHeatMapSettings = 'PerformanceHeatMapSettings',
  PlatformSettings = 'PlatformSettings',
  ProgramStore = 'ProgramStore',
  ProjectMember = 'ProjectMember',
  ProjectPermissionManagement = 'ProjectPermissionManagement',
  ProjectsManagement = 'ProjectsManagement',
  ProtocolTest = 'ProtocolTest',
  ProtocolTestDetail = 'ProtocolTestDetail',
  ProtocolTestDevices = 'ProtocolTestDevices',
  ProtocolTestHistory = 'ProtocolTestHistory',
  ResourceCheck = 'ResourceCheck',
  ResourceCheckIndexOld = 'ResourceCheckIndexOld',
  ResourceCheckItems = 'ResourceCheckItems',
  ResourceCheckOld = 'ResourceCheckOld',
  ResourceCheckReportCompare = 'ResourceCheckReportCompare',
  ResourceCheckReportDetail = 'ResourceCheckReportDetail',
  ResourceCheckReports = 'ResourceCheckReports',
  ResourceCheckRules = 'ResourceCheckRules',
  ResourceCheckRulesOld = 'ResourceCheckRulesOld',
  TCP4TSettings = 'TCP4TSettings',
  HomeSettingsChild = 'HomeSettingsChild',
  ResourceCheckSwitchesOld = 'ResourceCheckSwitchesOld',
  ResourceCheckSwitchesTemplate = 'ResourceCheckSwitchesTemplate',
  ResourceCheckTemplate = 'ResourceCheckTemplate',
  RoleManage = 'RoleManage',
  Secure = 'Secure',
  SecureChannelsSettings = 'SecureChannelsSettings',
  SecureProtections = 'SecureProtections',
  SecureSettings = 'SecureSettings',
  Services = 'Services',
  SwarmSettings = 'SwarmSettings',
  System = 'System',
  SystemPerforceManagement = 'SystemPerforceManagement',
  TCP4TOperationsSettings = 'TCP4TOperationsSettings',
  TCP4TSelectionsSettings = 'TCP4TSelectionsSettings',
  TCP4T = 'TCP4T',
  TempChart = 'TempChart',
  Test = 'Test',
  Tool = 'Tool',
  ToolNavigations = 'ToolNavigations',
  GroupChat = 'GroupChat',
  ToolGroupChat = 'ToolGroupChat',
  ToolWorkPlatform = 'ToolWorkPlatform',
  Toolkit = 'Toolkit',
  ToolkitDetail = 'ToolkitDetail',
  ToolkitPackageSettings = 'ToolkitPackageSettings',
  Tracking = 'Tracking',
  TrackingAnalysis = 'TrackingAnalysis',
  TrackingAnalysisSettings = 'TrackingAnalysisSettings',
  WebHookPlatform = 'WebHookPlatform',
  EfficacyPreview = 'EfficacyPreview',
  AIChat = 'AIChat',
}
