<template>
  <div class="mx-8 mb-6 mt-5">
    <MarkdownViewer v-if="curReadmeLink" :value="curReadmeLink" />
    <Empty v-else :image="emptyImg" />
  </div>
</template>

<script lang="ts" setup>
import { Empty } from 'ant-design-vue';
import { MarkdownViewer } from '/@/components/Markdown';

defineOptions({
  prefixCls: 'ToolkitReadMe',
});

defineProps({
  curReadmeLink: {
    type: String,
    default: () => '',
  },
});

const emptyImg = Empty.PRESENTED_IMAGE_SIMPLE;
</script>
