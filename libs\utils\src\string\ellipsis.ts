import type { Canvas, SKRSContext2D } from '@napi-rs/canvas';

interface EllipsisOptions {
  text: string;
  /**
   * 字体样式，必须包含字体大小和字体名称
   * 例如 "16px Arial" 或 "14px 'Microsoft YaHei'"
   */
  font: string;
  /**
   * 字间距，单位 px，默认为 0
   * 注意：如果设置了字间距，测量宽度时会考虑字间距
   */
  letterSpacing?: number;
  /**
   * 最大宽度，单位 px
   * 注意：如果文本宽度超过此值，将会被截断
   */
  maxWidth: number;
  /**
   * 最大行数，超过此行数将会被截断
   * @default 1
   * 注意：如果文本行数超过此值，将会被截断
   */
  maxLines?: number;
  /**
   * 如果造成省略需要保留的字符段长度, 不传则默认不保留尾部，直接...处理
   * @default 0
   */
  keepLastSegment?: number;
  /**
   * 是否插入省略号，默认 true
   * @default true
   * 如果为 false，则不会插入省略号，而是直接截断文本
   */
  insertEllipsis?: boolean;
  /**
   * 省略号符号
   * @default '…'
   * 可以自定义为其他符号，例如 '...' 或 '>>'
   */
  ellipsisSymbol?: string;
  /**
   * 绘图上下文，测试场景
   * 注意：如果在浏览器环境中使用，请确保传入的是 CanvasRenderingContext2D
   * 如果在 Node.js 环境中使用，请确保传入的是 SKRSContext2D
   */
  canvas?: Canvas | HTMLCanvasElement;
}

interface TextSegment {
  type: 'text' | 'ellipsis';
  content: string;
}

/**
 * 测量文本宽度，考虑字间距
 */
function measureTextWidth(ctx: SKRSContext2D | CanvasRenderingContext2D, text: string, letterSpacing: number = 0): number {
  if (text.length === 0) {
    return 0;
  }
  let width = 0;
  const metrics = ctx.measureText(text);
  width = metrics.width + (text.length - 1) * letterSpacing; // 考虑字间距
  return width > 0 ? width : 0; // 确保宽度不小于 0
}
/**
 * 文本截断处理
 * @param options 配置选项
 */
function multiEllipsis(options: EllipsisOptions): TextSegment[] {
  const {
    text,
    font,
    maxWidth,
    keepLastSegment = 0,
    letterSpacing = 0,
    maxLines = 1,
    insertEllipsis = true,
    ellipsisSymbol = '…',
    canvas: canvasFromOptions,
  } = options;

  // 区分node环境和浏览器环境
  const canvas = canvasFromOptions ?? document.createElement('canvas');
  const ctx = canvas.getContext('2d')!;
  ctx.font = font;

  const segments: TextSegment[] = [];
  const fullWidth = measureTextWidth(ctx, text, letterSpacing);
  const ellipsisWidth = measureTextWidth(ctx, ellipsisSymbol, letterSpacing);

  const maxTotalWidth = maxWidth * maxLines;
  // 计算每个字符的平均宽度
  const preCharLength = Math.floor(fullWidth / text.length);
  // 计算每行最多可以容纳的字符数
  const maxCharsPerLine = Math.floor(maxWidth / preCharLength);
  if (fullWidth <= maxTotalWidth) {
    // 文本宽度小于等于最大宽度，直接返回
    return [{ type: 'text', content: text }];
  }

  // 如果文本宽度超过最大宽度，进行截断处理
  const updateStringToFitWidth = (str: string, width: number, direction: 'left' | 'right'): string => {
    const computedPredictIndex = (maxLines - 1) * maxCharsPerLine;
    const preAllLineWidth = maxCharsPerLine * (maxLines - 1) * preCharLength;
    const lastLineCharLength = Math.floor((width - preAllLineWidth) / preCharLength);
    const predictIndex = Math.min(str.length, computedPredictIndex + lastLineCharLength);

    let updatedStr = direction === 'right' ? str.slice(0, predictIndex) : str.slice(predictIndex);
    if (measureTextWidth(ctx, updatedStr, letterSpacing) <= width) {
      // 如果更新后的字符串宽度小于等于目标宽度，则逐步增加字符直到达到宽度
      while (measureTextWidth(ctx, updatedStr, letterSpacing) < width && updatedStr.length < str.length) {
        if (direction === 'left') {
          updatedStr = str.slice(0, updatedStr.length + 1);
        } else {
          updatedStr = str.slice(0, updatedStr.length - 1);
        }
      }
      // 如果更新后的字符串宽度超过目标宽度，则返回当前字符串-1的长度
      return updatedStr.slice(0, updatedStr.length - 1);
    } else {
      // 如果更新后的字符串宽度超过目标宽度，则逐步减少字符直到达到宽度
      let currentWidth = measureTextWidth(ctx, updatedStr, letterSpacing);
      while (currentWidth > width && updatedStr.length > 0) {
        const index = Math.floor((currentWidth - width) / preCharLength);
        if (index < 1) {
          updatedStr = updatedStr.slice(0, -1);
        } else {
          if (direction === 'left') {
            updatedStr = updatedStr.slice(index);
          } else {
            updatedStr = updatedStr.slice(0, -index);
          }
        }
        currentWidth = measureTextWidth(ctx, updatedStr, letterSpacing);
      }
    }

    return updatedStr;
  };

  // 统一处理 keepLastSegment 的边界
  const shouldKeepTail = keepLastSegment > 0 && keepLastSegment < text.length + 1;

  if (shouldKeepTail) {
    // 保留尾部字符段
    let tail = text.slice(-keepLastSegment);
    let tailWidth = measureTextWidth(ctx, tail, letterSpacing);

    // 计算剩余可用宽度（减去尾部和省略号）
    let availableWidth = maxTotalWidth - tailWidth - (insertEllipsis ? ellipsisWidth : 0) - preCharLength;
    // 如果尾部太长，缩减尾部
    if (availableWidth < 0) {
      tail = updateStringToFitWidth(tail, maxTotalWidth - (insertEllipsis ? ellipsisWidth : 0) - preCharLength, 'left');
      tailWidth = measureTextWidth(ctx, tail, letterSpacing);
      availableWidth = maxTotalWidth - tailWidth - (insertEllipsis ? ellipsisWidth : 0);
    }

    // 截取头部
    let head = text.slice(0, text.length - tail.length);
    head = updateStringToFitWidth(head, availableWidth - preCharLength, 'right');
    const headWidth = measureTextWidth(ctx, head, letterSpacing);
    availableWidth = maxTotalWidth - tailWidth - (insertEllipsis ? ellipsisWidth : 0) - headWidth;
    // 如果整体宽度超过可用宽度，缩减头部
    if (availableWidth < 0) {
      head = updateStringToFitWidth(head, headWidth + availableWidth, 'left');
    }

    if (head) {
      segments.push({ type: 'text', content: head });
    }
    if (insertEllipsis) {
      segments.push({ type: 'ellipsis', content: ellipsisSymbol });
    }
    if (tail) {
      segments.push({ type: 'text', content: tail });
    }
  } else {
    // 不保留尾部，按正常逻辑处理
    const availableWidth = maxTotalWidth - (insertEllipsis ? ellipsisWidth : 0);
    const head = updateStringToFitWidth(text, availableWidth, 'right');
    if (head) {
      segments.push({ type: 'text', content: head });
    }
    if (insertEllipsis) {
      segments.push({ type: 'ellipsis', content: ellipsisSymbol });
    }
  }

  return segments;
}

export {
  measureTextWidth,
  multiEllipsis,
};
export type { EllipsisOptions, TextSegment };
