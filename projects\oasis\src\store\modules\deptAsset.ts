// TODO 文件移到对应业务文件夹，便于后面拆子系统
import { defineStore } from 'pinia';
import { store } from '/@/store';

interface DeptAssetState {
  isEdit: boolean;
  editType?: string;
  editingId?: number;
}

export const usedeptAssetStore = defineStore({
  id: 'app-deptAsset',
  state: (): DeptAssetState => ({
    isEdit: false,
    editType: undefined,
    editingId: undefined,
  }),
  getters: {
    getEditingId(state): number | undefined {
      return state.editingId;
    },
    getEditType(state): string | undefined {
      return state.editType;
    },
    isEditFunc(state): boolean {
      return state.isEdit;
    },
  },
  actions: {
    setIsEdit(isEdit: boolean) {
      this.isEdit = isEdit;
    },
    setEditingId(id: number | undefined) {
      this.editingId = id;
    },
    setEditType(type: string | undefined) {
      this.editType = type;
    },
  },
});

// Need to be used outside the setup
export function usedeptAssetStoreWithOut() {
  return usedeptAssetStore(store);
}
