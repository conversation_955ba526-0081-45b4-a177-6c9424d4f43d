import { NScrollbar } from 'naive-ui';
import { type PropType, type Ref, computed, defineComponent } from 'vue';
import { type ChatMessage, type ChatMessageSearch, ChatMessageStatus } from '@/models/chat';
import { ChatInput } from './components/chat-input';
import { ChatBubble } from './components/chat-bubble';

const ChatFeed = defineComponent({
  props: {
    chatLoading: {
      type: Boolean as PropType<boolean>,
      default: false,
    },
    sessionId: {
      type: String as PropType<string>,
      default: '',
    },
    currentModel: {
      type: String as PropType<string>,
      default: '',
    },
    isDeepThinkActive: {
      type: Boolean as PropType<boolean>,
      default: false,
    },
    isOnlineSearchActive: {
      type: Boolean as PropType<boolean>,
      default: false,
    },
    showDownBtn: {
      type: Boolean as PropType<boolean>,
      default: false,
    },
    scrollBarRef: {
      type: Object as PropType<Ref<HTMLElement | null>>,
      default: null,
    },
    containerRef: {
      type: Object as PropType<Ref<HTMLElement | null>>,
      default: null,
    },
    messages: {
      type: Array as PropType<ChatMessage[]>,
      default: () => [],
    },
    onStopChat: {
      type: Function as PropType<() => void>,
    },
    onSendMessage: {
      type: Function as PropType<(data: { message: string; isNew: boolean; editId?: number }) => void>,
      default: () => {},
    },
    onManualScroll: {
      type: Function as PropType<(e: Event) => void>,
      default: () => {},
    },
    onScrollToBottom: {
      type: Function as PropType<() => void>,
      default: () => {},
    },
    onScrollToItem: {
      type: Function as PropType<(y: number) => void>,
      default: () => {},
    },
    onShowSearch: {
      type: Function as PropType<(search: ChatMessageSearch[]) => void>,
      default: () => {},
    },
  },
  emits: ['update:currentModel', 'update:isDeepThinkActive', 'update:isOnlineSearchActive'],
  setup(props, { emit }) {
    const currentModel = computed({
      get: () => props.currentModel,
      set: (model) => {
        emit('update:currentModel', model);
      },
    });
    const isDeepThinkActive = computed({
      get: () => props.isDeepThinkActive,
      set: (isActive) => {
        emit('update:isDeepThinkActive', isActive);
      },
    });
    const isOnlineSearchActive = computed({
      get: () => props.isOnlineSearchActive,
      set: (isActive) => {
        emit('update:isOnlineSearchActive', isActive);
      },
    });

    return () => (
      <div class="flex flex-1 gap-16px overflow-hidden">
        <div
          class="chat-container w-full flex flex-1 flex-col"
          key={props.sessionId}
        >
          <NScrollbar
            class="flex-1"
            contentClass="overflow-y-hidden"
            onScroll={props.onManualScroll}
            ref={props.scrollBarRef}
          >
            <div class="mx-auto px-60px lg:max-w-864px max-sm:px-0px" ref={props.containerRef}>
              {
                props.messages.map((item) => (
                  <>
                    <ChatBubble
                      content={item.content}
                      id={item.id}
                      isFinished={item.state === ChatMessageStatus.Finish}
                      isLoading={props.chatLoading}
                      isUser={item.role === 'user'}
                      onScrollTo={(y) => props.onScrollToItem?.(y)}
                      onSendMessage={(data) => {
                        props.onSendMessage({
                          message: data.message,
                          isNew: false,
                          editId: data.editId,
                        });
                      }}
                      onShowSearch={props.onShowSearch}
                      originalContent={item._content}
                      search={item.search}
                    />
                  </>
                ))
              }
            </div>
          </NScrollbar>
          <div class="mx-auto w-full px-60px lg:max-w-864px max-sm:px-12px">
            <ChatInput
              loading={props.chatLoading}
              onCancel={props.onStopChat}
              onScrollToBottom={props.onScrollToBottom}
              onSubmit={(data) => props.onSendMessage({
                message: data.message,
                isNew: false,
              })}
              showDeepThink
              showDownBtn={props.showDownBtn}
              showOnlineSearch
              v-model:currentModel={currentModel.value}
              v-model:deepThink={isDeepThinkActive.value}
              v-model:onlineSearch={isOnlineSearchActive.value}
            />
          </div>
        </div>

      </div>
    );
  },
});

export {
  ChatFeed,
};
