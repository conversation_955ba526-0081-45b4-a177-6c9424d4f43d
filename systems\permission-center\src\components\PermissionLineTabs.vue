<template>
  <div class="flex gap-20px">
    <div
      v-for="item in tabList"
      :key="item.key"
      class="FO-Font-B16 cursor-pointer select-none transition-300"
      :class="[
        item.key === value ? 'c-FO-Content-Text1' : 'c-FO-Content-Text3',
      ]"
      @click="() => valueModel = item.key "
    >
      {{ item.label }}
      <div
        class="m-auto mt-4px h-4px w-16px b-rd-2px bg-FO-Brand-Primary-Default" :class="[
          item.key === value ? 'opacity-100' : 'opacity-0',
        ]"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { watch } from 'vue';

const props = defineProps<{
  tabList?: Array<{
    key: string;
    label: string;
  }>;
}>();

const valueModel = defineModel<string>('value');

watch([valueModel, () => props.tabList], ([v, list]) => {
  if (list?.length && !list.some((i) => i.key === v)) {
    valueModel.value = list[0].key;
  }
});
</script>
