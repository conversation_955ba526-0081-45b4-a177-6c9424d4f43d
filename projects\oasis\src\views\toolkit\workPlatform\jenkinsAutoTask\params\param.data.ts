import type { FormSchema } from '/@/components/Table';

// 参数类型下拉选项
export const paramTypeOptions = [
  {
    label: '文本',
    value: 1,
  },
  {
    label: '单选',
    value: 2,
  },
  {
    label: '多选',
    value: 3,
  },
];

// 表单配置
export const formSchema: FormSchema[] = [
  {
    field: 'name',
    label: '参数名称',
    component: 'Input',
    componentProps: {
      placeholder: '请输入参数名称, 只能包含字母、数字和下划线',
    },
    rules: [
      { required: true, message: '请输入参数名称' },
      { pattern: /^\w+$/, message: '参数名称只能包含字母、数字和下划线' },
    ],
    required: true,
  },
  {
    field: 'type',
    label: '参数类型',
    component: 'RadioButtonGroup',
    defaultValue: 3,
    componentProps: ({ formActionType }) => ({
      options: paramTypeOptions,
      onChange: async (value) => {
        await formActionType?.setFieldsValue({
          options: [],
          defaults: undefined,
        });
        await formActionType?.updateSchema({
          field: 'defaults',
          component: value === 1 ? 'Input' : value === 2 ? 'RadioGroup' : 'CheckboxGroup',
        });
      },
    }),
    required: true,
  },
  {
    field: 'options',
    label: '备选列表',
    component: 'Select',
    ifShow: ({ values }) => {
      return values.type !== 1;
    },
    componentProps: ({ formActionType }) => ({
      mode: 'tags',
      placeholder: '请输入备选参数, 回车添加',
      getPopupContainer: () => document.body,
      onChange: async () => {
        await formActionType?.setFieldsValue({
          defaults: [],
        });
      },
    }),
    required: true,
  },
  {
    field: 'defaults',
    label: '默认值',
    component: 'CheckboxGroup',
    componentProps: ({ formModel }) => ({
      placeholder: '请输入默认值',
      options: formModel.options?.map((e) => ({ label: e, value: e })) || [],
      getPopupContainer: () => document.body,
    }),
  },
  {
    field: 'description',
    label: '描述',
    component: 'InputTextArea',
    componentProps: {
      placeholder: '请输入描述',
      maxLength: 100,
    },
  },
];
