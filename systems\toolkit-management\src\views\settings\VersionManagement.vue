<template>
  <div v-track:v="'idzmwym4ek'" class="toolkit-package-version">
    <div class="mb-4 text-center text-lg font-bold">
      版本管理
    </div>
    <div class="toolkit-package-version__list">
      <div class="mb flex justify-between flex-items-center">
        <div class="flex flex-items-center">
          <div class="mr">
            工具
          </div>
          <Select
            v-model:value="toolkitID" style="width: 300px" :options="options" showSearch
            optionFilterProp="label" @change="getVersionList"
          />
        </div>
        <BasicButton
          v-if="options.length" size="small" shape="round"
          class="toolkit-package-version__list-item-btn" @click="handleCreate"
        >
          <div class="flex items-center">
            <Icon :icon="addOneIcon" />
            <span class="!ml-1">添加版本</span>
          </div>
        </BasicButton>
      </div>
      <List
        :grid="{ gutter: 16 }" :data-source="versionList" class="toolkit-package-version__card-list"
        :style="{ maxHeight: `calc(100vh - 152px)` }" :locale="{ emptyText: '暂无版本' }"
      >
        <template #renderItem="{ item }">
          <ListItem>
            <div :key="`${toolkitID}-${item.ID}`" class="toolkit-package-version__card-list-item">
              <div class="toolkit-package-version__card-list-item-content">
                <img :src="item.icon || LogoImg" alt="icon" class="h-[80px] w-[80px] rounded-2xl">
                <div class="toolkit-package-version__card-list-item-content-text">
                  <div class="w-[300px] flex">
                    <EllipsisText class="mr-2 font-bold">
                      {{ item.version }}
                    </EllipsisText>
                    <Icon
                      v-if="getPlatformIconByVal(item.platform)" :key="getPlatformIconByVal(item.platform)?.label"
                      :title="getPlatformIconByVal(item.platform)?.label"
                      :icon="getPlatformIconByVal(item.platform)?.icon"
                    />
                  </div>
                  <div class="flex font-size-[14px]">
                    <div>
                      大小：<b>{{ formatKBSize(item.sizeKB ?? 0) }}</b>
                    </div>
                    <div class="ml-2">
                      发布：<b>{{ dayjs(item.CreatedAt).format('MM月DD日 HH:mm') }}</b>
                    </div>
                  </div>

                  <div class="flex items-center text-xs c-FO-Content-Text2">
                    <EllipsisText class="text-xs" lines="2">
                      {{ item.releaseNote || '-' }}
                    </EllipsisText>
                  </div>
                </div>
              </div>
              <div class="relative w-[80px] flex flex-col justify-center">
                <div class="h-[32px] flex flex-col justify-center">
                  <BasicButton
                    shape="round" class="toolkit-package-version__list-item-btn mb-2" size="small"
                    @click="() => handleEdit(item)"
                  >
                    <div class="flex items-center">
                      <Icon :icon="deleteIcon" />
                      <span class="font-bold !ml-1">编辑</span>
                    </div>
                  </BasicButton>
                  <Popconfirm title="确定要删除吗" @confirm="() => handleDelete(item.ID)">
                    <BasicButton shape="round" class="toolkit-package-version__list-item-btn mt-2" size="small">
                      <div class="flex items-center">
                        <Icon :icon="editorIcon" />
                        <span class="font-bold !ml-1">删除</span>
                      </div>
                    </BasicButton>
                  </Popconfirm>
                </div>
              </div>
            </div>
          </ListItem>
        </template>
      </List>
    </div>
    <VersionDrawerHolder />
  </div>
</template>

<script lang="ts" setup>
import { List, ListItem, Popconfirm, Select } from 'ant-design-vue';
import dayjs from 'dayjs';
import { cloneDeep } from 'lodash';
import { onMounted, ref } from 'vue';
import { Icon } from '@iconify/vue';
import VersionDrawer from './branch/VersionDrawer.vue';
import {
  type ToolkitListItem,
  type ToolkitVersionListItem,
  deleteToolkitVersion,
  getToolkitByID,
  getToolkitListByPage,
  getToolkitVersionListByPage,
} from '../../api';
import { BasicButton, formatKBSize } from '@hg-tech/oasis-common/deprecated';
import { EllipsisText } from '@hg-tech/oasis-common';
import LogoImg from '/resource/img/logo.png';
import { vTrack } from '../../directives/track.ts';
import { platformOptions } from '../configs.tsx';
import { useModalShow } from '@hg-tech/utils-vue';
import addOneIcon from '@iconify-icons/icon-park-outline/add-one';
import deleteIcon from '@iconify-icons/icon-park-outline/delete';
import editorIcon from '@iconify-icons/icon-park-outline/editor';

const [VersionDrawerHolder, VersionDrawerShow] = useModalShow(VersionDrawer);
const clonePlatformOptions = cloneDeep(platformOptions);
const toolkitID = ref<number>();
const options = ref<{ value: number; label: string }[]>([]);
const versionList = ref<ToolkitVersionListItem[]>([]);
const platforms = ref();
const toolkitList = ref<ToolkitListItem[]>([]);

async function getList() {
  const res = await getToolkitListByPage({
    maintain: true,
    page: 1,
    pageSize: 999,
  }, {});
  toolkitList.value = res.data.data?.list || [];
}

async function getVersionList() {
  const res = await getToolkitVersionListByPage({
    toolID: String(toolkitID.value),
    page: 1,
    pageSize: 999,
  }, {});
  versionList.value = res.data.data?.list || [];
  await getToolkitDetail();
}

// 获取平台icon
function getPlatformIconByVal(val: number) {
  return clonePlatformOptions.find((e) => e.value === val);
}

async function getToolkitDetail() {
  const res = await getToolkitByID({ ID: toolkitID.value! }, {});
  const retool = res.data.data?.retool;
  if (!retool) {
    return;
  }
  platforms.value = retool.platforms || [];
}

function handleCreate() {
  VersionDrawerShow({ isUpdate: false, toolId: toolkitID.value, platforms: platforms.value });
}

function handleEdit(record: ToolkitVersionListItem) {
  VersionDrawerShow({
    record,
    isUpdate: true,
    toolId: toolkitID.value,
    platforms: platforms.value,
  });
}

async function handleDelete(ID: number) {
  await deleteToolkitVersion({ toolID: toolkitID.value!, editId: ID }, {});
  getVersionList();
}

async function handleSuccess() {
  await getVersionList();
}

onMounted(async () => {
  await getList();
  options.value = [];
  toolkitList.value.forEach((item) => {
    options.value.push({
      value: item.ID!,
      label: item.name!,
    });
  });
  toolkitID.value = options.value[0].value;
  getVersionList();
});
</script>

<style lang="less" scoped>
@import (reference) '@hg-tech/forgeon-style/vars.less';

.toolkit-package-version {
  position: relative;
  padding: 16px;
  border-radius: 8px;
  background-color: @FO-Container-Fill1;

  &-list {
    margin: 16px 0;

    &-item {
      position: relative;
      margin-bottom: 8px;
      overflow: hidden;

      &-delete {
        position: absolute;
        z-index: 1;
        top: 40%;
        right: 2px;
        color: @FO-Content-Text2;
        cursor: pointer;
      }
    }

    &-empty {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 100%;
      padding: 16px;
      color: @FO-Content-Text2;
      font-size: 13px;
    }
  }

  &__card-list {
    overflow: auto;

    .ant-row {
      margin: 0 !important;

      > div {
        margin-right: 1rem;
        margin-bottom: 1rem;
      }
    }

    & .ant-list .ant-list-item {
      padding: 0;
    }

    &-item {
      display: flex;
      position: relative;
      width: 500px;
      padding: 12px;
      transition: all 0.3s ease-in-out;
      border: 2px solid @FO-Container-Stroke1;
      border-radius: 20px;
      background-color: @FO-Container-Fill1;
      cursor: pointer;
      user-select: none;

      &-top {
        position: absolute;
        top: -2px;
        left: -2px;
        width: 60px;
        height: 60px;
        overflow: hidden;
        border-radius: 20px 0 0;

        &-pin {
          display: flex;
          position: absolute;
          z-index: 1;
          top: -30px;
          left: -30px;
          align-items: flex-end;
          justify-content: center;
          width: 60px;
          height: 60px;
          padding: 2px;
          transform: rotate(-45deg);
          background-color: #f8cc28;
          color: #000;
        }
      }

      &-check {
        position: absolute;
        z-index: 2;
        top: 43px;
        right: 43px;
        transform: scale(2.5);

        &:not([disabled='true']) {
          & .ant-checkbox-inner {
            border-color: @FO-Functional-Error1-Default !important;
            background-color: @FO-Container-Fill1 !important;
          }

          & .ant-checkbox-checked > .ant-checkbox-inner {
            background-color: @FO-Functional-Error1-Default !important;
          }

          &[mode='compare'] {
            & .ant-checkbox-inner {
              border-color: @FO-Functional-Warning1-Default !important;
              background-color: @FO-Container-Fill1 !important;
            }

            & .ant-checkbox-checked > .ant-checkbox-inner {
              background-color: @FO-Functional-Warning1-Default !important;
            }
          }
        }

        &[disabled='true'] {
          &::after {
            content: '未检测';
            position: absolute;
            z-index: 1;
            top: 42%;
            left: 2px;
            color: @FO-Content-Text2;
            font-size: 4px;
            line-height: 4px;
            cursor: not-allowed;
            user-select: none;
          }
        }

        &[isDifferent='true'] {
          &::after {
            content: '不同平台';
            position: absolute;
            z-index: 1;
            top: 30%;
            left: 3px;
            color: @FO-Content-Text2;
            font-size: 5px;
            line-height: 5px;
            cursor: not-allowed;
            user-select: none;
          }
        }
      }

      &-content {
        display: flex;
        flex: 1;
        align-items: center;
        width: 0;

        &-text {
          margin: 0 10px;
          line-height: 24px;
        }
      }

      &-hoverable {
        display: flex;
        position: relative;
        align-items: center;
        max-width: 300px;
        min-height: 24px;
        overflow: hidden;

        &:not(:hover) {
          &::after {
            content: '';
            position: absolute;
            z-index: 1;
            top: 0;
            right: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(to right, transparent 90%, @FO-Container-Fill1);
          }
        }

        &:not([disabled='true']):hover {
          z-index: 10;
          width: auto;
          max-width: 316px;
          overflow: visible;

          & > div {
            position: absolute;
            top: -4px;
            left: -8px;
            flex-wrap: wrap;
            padding: 8px;
            border-radius: 8px;
            background-color: @FO-Container-Fill1;
            box-shadow: 0 0 10px 0 #00000040;
            row-gap: 4px;
          }
        }
      }

      &-label {
        display: flex;
        align-items: center;
        margin-right: 4px;
        padding: 0 4px;
        border-radius: 4px;
        color: #fff;
        font-size: 10px;
        line-height: 16px;
        white-space: nowrap;

        &[isSingle='true'] {
          border-radius: 100px;
        }
      }
      // 菱形背景
      &-mark {
        position: relative;

        &-before {
          position: absolute;
          z-index: 1;
          top: 0;
          left: 0;

          &::before {
            content: ' ';
            position: absolute;
            z-index: 2;
            top: 0;
            left: 0;
            width: 0;
            height: 0;
            border-top: 8px solid @FO-Container-Fill1;
            border-right: 8px solid transparent;
            border-bottom: 8px solid @FO-Container-Fill1;
          }
        }

        &-name {
          display: flex;
          position: relative;
          align-items: center;
          margin-right: 4px;
          padding: 0 10px;
          color: #fff;
          font-size: 10px;
          line-height: 16px;
          white-space: nowrap;
        }

        &-after {
          position: absolute;
          top: 0;
          right: 0;

          &::after {
            content: ' ';
            position: absolute;
            z-index: 2;
            top: 0;
            left: -12px;
            width: 0;
            height: 0;
            border-top: 8px solid @FO-Container-Fill1;
            border-bottom: 8px solid @FO-Container-Fill1;
            border-left: 8px solid transparent;
          }
        }
      }
    }
  }

  &__list-item-btn {
    margin-right: 8px;
    border-color: @FO-Container-Fill6 !important;
    background-color: @FO-Container-Fill6 !important;
    color: @FO-Content-Components1 !important;

    &:hover {
      border-color: #616161 !important;
      background-color: #616161 !important;
    }
  }
}
</style>
