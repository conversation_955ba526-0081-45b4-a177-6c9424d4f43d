import { ForgeonTheme } from '@hg-tech/forgeon-style';
import { useRootSetting } from '/@/hooks/setting/useRootSetting';
import { preprocessFilePath } from '@hg-tech/oasis-common';
import DeviceDark from './Device-Dark.png';
import DeviceLight from './Device-Light.png';

export function useDeviceHolderImage() {
  const { getDarkMode } = useRootSetting();
  function holderUrl(picURL?: string) {
    if (picURL) {
      return preprocessFilePath(picURL);
    }
    return getDarkMode.value === ForgeonTheme.Light ? DeviceLight : DeviceDark;
  }
  return {
    holderUrl,
  };
}
