import { ForgeonTheme } from '@hg-tech/forgeon-style';
import type { GlobalTheme } from 'naive-ui';
import type { Ref } from 'vue';
import { darkTheme, lightTheme } from 'naive-ui';
import { ref } from 'vue';
import { setItem, STORAGE_KEY } from '../utils/localstorage';

const currentTheme = ref<ForgeonTheme>(ForgeonTheme.Light);
const appTheme = ref<GlobalTheme>(lightTheme);
interface IUseAppTheme {
  switchTheme: () => void;
  setTheme: (mode: ForgeonTheme) => void;
  currentTheme: Ref<ForgeonTheme>;
  appTheme: Ref<GlobalTheme>;
}

function useAppTheme(): IUseAppTheme {
  function setThemeAnimation() {
    document.documentElement.setAttribute('data-theme', currentTheme.value);
  }
  const switchTheme = () => {
    appTheme.value = currentTheme.value === 'light' ? darkTheme : lightTheme;
    currentTheme.value = currentTheme.value === 'light' ? ForgeonTheme.Dark : ForgeonTheme.Light;
    setItem(`${STORAGE_KEY.APP_THEME}${'1'}`, currentTheme.value);
    setThemeAnimation();
  };

  const setTheme = (current: ForgeonTheme) => {
    appTheme.value = current === 'light' ? lightTheme : darkTheme;
    setItem(`${STORAGE_KEY.APP_THEME}${'1'}`, currentTheme.value);
    currentTheme.value = current;
    setThemeAnimation();
  };

  return {
    appTheme,
    currentTheme,
    switchTheme,
    setTheme,
  };
}

export { IUseAppTheme, useAppTheme };
