<template>
  <div
    class="relative"
    @mouseenter="mouseenter"
    @mouseleave="mouseleave"
  >
    <div ref="defaultContent" class="overflow-hidden">
      <slot :hovered="isHover" />
    </div>
    <div
      class="absolute overflow-hidden b-rd-[12px] transition-all"
      :style="{
        top: `-${padding}px`,
        left: `-${padding}px`,
        padding: `${padding}px`,
        width: `${defaultW}px`,
        height: `${defaultH}px`,
        transitionProperty: 'width,height,opacity',
        transitionDuration: '0.3s',
        pointerEvents: 'none',
        backgroundColor: ForgeonThemeCssVar.ContainerFill6,
        opacity: 0,
        ...isHover ? {
          width: `${hoveredW + 2 * padding}px`,
          height: `${hoveredH + 2 * padding}px`,
          pointerEvents: 'auto',
          opacity: 1,
          zIndex: 99,
        } : undefined,
      }"
    >
      <div ref="hoveredContent" class="absolute flex flex-col gap-[8px]">
        <div class="flex">
          <slot :hovered="isHover" />
        </div>
        <slot name="addition" :hovered="isHover" />
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref } from 'vue';
import {ForgeonThemeCssVar} from '@hg-tech/forgeon-style'
import { useElementBounding } from '@vueuse/core';

const emits = defineEmits<{
  (e: 'changeHover', value: boolean): void;
}>();

const defaultContent = ref<HTMLElement>();
const hoveredContent = ref<HTMLElement>();
const isHover = ref(false);
const padding = 4;
const { width: defaultW, height: defaultH } = useElementBounding(defaultContent);
const { width: hoveredW, height: hoveredH } = useElementBounding(hoveredContent);

function mouseenter() {
  emits('changeHover', true);

  isHover.value = true;
}

function mouseleave() {
  emits('changeHover', false);

  isHover.value = false;
}
</script>
