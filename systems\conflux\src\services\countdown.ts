import { ref } from 'vue';

function useCountdown() {
  const remainingTime = ref(0);
  const loading = ref(false);
  let timer: NodeJS.Timeout | null = null;

  const stop = () => {
    if (timer) {
      clearInterval(timer);
      timer = null;
    }
    loading.value = false;
  };

  const start = (duration: number) => {
    return new Promise<void>((resolve) => {
      if (loading.value) {
        return;
      }
      loading.value = true;
      remainingTime.value = duration;

      timer = setInterval(() => {
        if (remainingTime.value > 0) {
          remainingTime.value -= 1;
        } else {
          stop();
          resolve();
        }
      }, 1000);
    });
  };

  return {
    start,
    stop,
    remainingTime,
    loading,
  };
}

export { useCountdown };
