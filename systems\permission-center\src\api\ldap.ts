import { apiService } from '../services/req.ts';
import type { PagedQueryParam, PermissionBaseRes, PermissionPagedRes } from './_common.ts';
import type { SysUserInfo } from './users.ts';

interface LdapInfo {
  id: number;
  createdAt: string;
  updatedAt: string;
  no: string;
  name: string;
  orgPath?: string;
}

/**
 * Ldap 搜索
 */
export const getPmpGroups = apiService.GET<
  PagedQueryParam & {
    /**
     * 搜索
     */
    query?: string;
    /**
     * org - 组织结构
     * group - 组
     */
    type?: 'org' | 'group';
  },
  Record<string, never>,
  PermissionPagedRes<LdapInfo>
>(`/api/auth/v1/ldap/search`);

interface OrgItem {
  id: number;
  createdAt: string;
  updatedAt: string;
  no: string;
  name: string;
  member: SysUserInfo[];
  orgPath: string;
  child: Omit<this, 'child' | 'member'>[];
}

/**
 * 获取组织架构列表
 */
export const getOrgLists = apiService.GET<
  {
    /**
     * 父部门no  不传返回一级部门
     */
    parent_no?: string;
  },
  Record<string, never>,
  PermissionBaseRes<{ child: OrgItem[]; member: SysUserInfo[] }>
>(`/api/auth/v1/org/list`);
