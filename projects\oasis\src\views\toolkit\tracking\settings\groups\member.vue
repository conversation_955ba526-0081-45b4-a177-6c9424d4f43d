<template>
  <div :class="prefixCls">
    <div v-if="group.authority === 0" :class="`${prefixCls}__creator`">
      {{ formatNickName(memberList?.[0]) || '-' }}
    </div>
    <UserSelect
      v-else
      v-model:value="memberIds"
      class="w-full"
      isMultiple
      :disabled="!isEditMode"
      :placeholder="isEditMode ? '请选择干员添加，可拼音首字母搜索' : '暂无干员'"
      @blur="handleChange"
      @deselect="handleChange"
    />
  </div>
</template>

<script lang="ts" setup name="TrackingToolGroupMember">
import { cloneDeep } from 'lodash-es';
import { ref } from 'vue';
import type { TrackingToolGroupsListItem } from '/@/api/page/model/trackingModel';
import {
  editTrackingToolGroupMemberList,
  getTrackingToolGroupMemberList,
} from '/@/api/page/tracking';
import type { UserInfoModel } from '/@/api/sys/model/userModel';
import { UserSelect } from '/@/components/Form';
import { formatNickName } from '/@/hooks/system/useUserList';
import { useDesign } from '/@/hooks/web/useDesign';

const props = defineProps({
  group: {
    type: Object as PropType<TrackingToolGroupsListItem>,
    required: true,
  },
  isEditMode: {
    type: Boolean,
    default: false,
  },
});

const { prefixCls } = useDesign('tracking-tool-group-member');
const memberList = ref<UserInfoModel[]>([]);
const memberIds = ref<number[]>([]);
const initMemberIds = ref<number[]>([]);

async function getMemberList() {
  if (!props.group.ID) {
    return;
  }

  const { users } = await getTrackingToolGroupMemberList(props.group.ID);

  memberList.value = users || [];
  memberIds.value = users?.map((item) => Number(item.ID)) || [];
  initMemberIds.value = cloneDeep(memberIds.value);
}

getMemberList();

async function handleChange() {
  if (initMemberIds.value.toString() === memberIds.value.toString()) {
    return;
  }

  await editTrackingToolGroupMemberList(
    {
      idList: memberIds.value,
    },
    props.group.ID!,
  );
  getMemberList();
}
</script>

<style lang="less">
@prefix-cls: ~'hypergryph-tracking-tool-group-member';
.@{prefix-cls} {
  &__creator {
    border-radius: 4px;
    line-height: 32px;
    padding: 0 10px;
    background-color: @FO-Container-Fill2;
  }

  .ant-select-disabled.ant-select-multiple .ant-select-selection-item {
    color: @FO-Content-Text1 !important;
  }
}
</style>
