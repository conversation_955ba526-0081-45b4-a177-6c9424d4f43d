<template>
  <div>
    <Collapse
      v-if="versionList?.length > 0"
      v-model:activeKey="collapseActiveKey"
      class="toolkit-detail-version__collapse"
      expandIconPosition="end"
      accordion
      @change="handleVersionClick"
    >
      <CollapsePanel
        v-for="(item, i) in versionList"
        :key="item.ID"
        class="toolkit-detail-version__collapse-panel"
      >
        <template #header>
          <div>
            <div class="font-bold" />
            <span
              class="toolkit-detail-version__title cursor-pointer"
              @click="handleVersionClick(item.ID)"
            >
              {{ item.version }}
            </span>
            <Tag v-if="i === 0" color="orange">
              最新版本
            </Tag>
            <Tag v-if="curVersionID === item.ID" color="blue">
              当前版本
            </Tag>
            <div class="toolkit-detail-version__author">
              作者：{{ formatNickName(item.author) }}
            </div>
          </div>
          <div>
            <span class="toolkit-detail-version__time">{{
              formatTISOToDate(item.UpdatedAt || item.CreatedAt)
            }}</span>
          </div>
        </template>
        <MarkdownViewer v-if="item.releaseNote" :value="item.releaseNote" theme="light" />
        <Empty v-else :image="emptyImg" description="暂无发版公告" />
      </CollapsePanel>
    </Collapse>
    <Empty v-else :image="emptyImg" description="暂无该平台版本信息" />
  </div>
</template>

<script lang="ts" setup>
import { type CollapseProps, Collapse, CollapsePanel, Empty, Tag } from 'ant-design-vue';
import { ref, unref } from 'vue';
import { useRouter } from 'vue-router';
import { type ToolkitVersionListItem, getToolkitVersionListByPage } from '../../api';
import { formatNickName, formatTISOToDate, MarkdownViewer } from '@hg-tech/oasis-common/deprecated';

defineOptions({
  prefixCls: 'ToolkitVersion',
});

const props = defineProps({
  toolID: {
    type: Number,
    default: () => null,
  },
  curVersionID: {
    type: Number,
    default: () => null,
  },
});

const versionList = ref<ToolkitVersionListItem[]>([]);
const { currentRoute, replace } = useRouter();
const versionId = Number(unref(currentRoute).query.v);
const platform = Number(unref(currentRoute).query.platform) || undefined;
const emptyImg = Empty.PRESENTED_IMAGE_SIMPLE;
const collapseActiveKey = ref<number>();

async function getToolkitVersionList(id: number) {
  const res = await getToolkitVersionListByPage({
    toolID: String(id),
    page: 1,
    pageSize: 999,
    platform,
  }, {});
  const list = res.data.data?.list || [];
  if (list?.length > 0) {
    versionList.value = list;
    collapseActiveKey.value = versionId || (list?.[0].ID as number);
  }
}

function handleVersionClick(ID: CollapseProps['onChange'] | number | undefined) {
  if (ID) {
    replace({ query: { tab: 'Version', v: ID, platform } });
  }
}

getToolkitVersionList(unref(props.toolID));
</script>

<style lang="less" scoped>
@import (reference) '@hg-tech/forgeon-style/vars.less';

.toolkit-detail-version {
  &__collapse {
    &-panel {
      display: block;
    }
  }

  &__item {
    position: relative;
  }

  &__title {
    font-size: 18px;
    margin-right: 8px;

    &:hover {
      color: @FO-Brand-Primary-Hover;
      text-decoration: underline;
    }
  }

  &__action {
    display: inline-block;
    padding: 0 16px;

    &:nth-child(1),
    &:nth-child(2) {
      border-right: 1px solid rgb(206 206 206 / 40%);
    }

    &-icon {
      margin-right: 3px;
    }
  }

  &__author {
    margin-top: 5px;
  }

  &__time {
    position: absolute;
    right: 40px;
    bottom: 28px;
  }
}
</style>
