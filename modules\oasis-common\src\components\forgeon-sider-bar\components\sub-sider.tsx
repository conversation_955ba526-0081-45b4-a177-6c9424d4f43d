import { type ICommonMenuItem, type PlatformEnterPoint, type PlatformRoutePath, ModulesMenuConfig } from '../../../configs';
import { type PropType, computed, defineComponent } from 'vue';
import { ForgeOnMenu } from './menu';
import styles from '../style.module.less';
import { Tooltip } from 'ant-design-vue';
import Menufold from '../../../assets/svg/icons/menu-fold.svg?component';
import { useForgeOnSider } from '../useForgeOnSider';

const SubSider = defineComponent({
  props: {
    subMenus: {
      type: Array as PropType<ICommonMenuItem[]>,
      default: () => [],
    },
    onPathChange: {
      type: Function as PropType<(params: { path: PlatformRoutePath | string; key: PlatformEnterPoint | string }) => void>,
      default: () => {},
    },
    onCollapseClick: {
      type: Function as PropType<() => void>,
      default: () => {},
    },
  },
  setup(props) {
    const { activeMenu, openKeys, activeMainModule } = useForgeOnSider();

    const headerTitle = computed(() => {
      return ModulesMenuConfig.find((item) => item.key === activeMainModule.value)?.title;
    });

    return () => (
      <div class={[styles.forgeonSubSider]}>
        <div class={[styles.subSiderHeader, 'px-10px']}>
          <div class="flex items-center gap-6px">
            <Tooltip placement="top" title="收起">
              <Menufold class={[styles.subSiderHeaderIcon, 'cursor-pointer']} onClick={props.onCollapseClick} />
            </Tooltip>
            <div class="c-FO-Content-Text1 FO-Font-B18">{headerTitle.value}</div>
          </div>
        </div>

        <ForgeOnMenu
          menus={props.subMenus}
          onPathChange={props.onPathChange}
          onUpdatedOpenKeys={(keys) => openKeys.value = keys}
          openKeys={openKeys.value}
          v-model:activeMenu={activeMenu.value}
        />
      </div>
    );
  },
});

export {
  SubSider,
};
