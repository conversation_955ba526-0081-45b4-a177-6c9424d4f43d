import Icon from '@ant-design/icons-vue';
import Close from '../../assets/svg/Close.svg?component';
import { <PERSON><PERSON>, Drawer } from 'ant-design-vue';
import { type PropType, computed, defineComponent, useTemplateRef, watch } from 'vue';
import { BasicVxeTable } from '@hg-tech/oasis-common';
import { type MergeV1ResolveConflict, MergeV1ResolveRule } from '@hg-tech/api-schema-merge';
import type { VxeGridInstance, VxeGridProps } from 'vxe-table';
import { useLatestPromise } from '@hg-tech/utils-vue';
import { mergeApi } from '../../api';
import BasicStrokeFile from '../../assets/svg/BasicStrokeFile.svg?component';
import { MergeV1ResolveRuleLabelMap } from '../../models/config.model';
import { useMergeHistory } from './use-merge-histroy';

const FileDrawer = defineComponent({
  props: {
    id: {
      type: String as PropType<string>,
    },
    visible: {
      type: Boolean as PropType<boolean>,
      default: false,
    },
  },
  emits: ['update:visible'],
  setup(props, { emit }) {
    const {
      execute: getResolvedFiles,
      data: resolvedFilesResponse,
      loading: getResolvedFilesLoading,
    } = useLatestPromise(mergeApi.v1.mergeServiceGetOperationDetail);
    const { currentProjectId } = useMergeHistory();
    const tableRef = useTemplateRef<VxeGridInstance<MergeV1ResolveConflict>>('baseTableRef');
    const fileList = computed(() => resolvedFilesResponse.value?.data?.data?.resolves || []);
    const tableRows = computed<MergeV1ResolveConflict[]>(() => fileList.value);

    const drawerTitle = computed(() => `文件修改内容(${fileList.value.length})`);

    const open = computed({
      get: () => props.visible,
      set: (value) => {
        emit('update:visible', value);
      },
    });

    const onClose = () => {
      open.value = false;
    };

    const tableColumns = computed<VxeGridProps<MergeV1ResolveConflict>['columns']>(() => [
      {
        type: null,
        width: 32,
        slots: {
          default: () => <Icon class="c-FO-Content-Icon3" component={<BasicStrokeFile />} />,
        },
      },
      {
        field: 'fileName',
        title: '文件完整路径',
        minWidth: 200,
        slots: {
          default({ row }) {
            if (row.resolveRule === MergeV1ResolveRule.ACCEPT_SOURCE) {
              return <span class="whitespace-pre-wrap">{row.sourceFile}</span>;
            }
            if (row.resolveRule === MergeV1ResolveRule.ACCEPT_TARGET) {
              return <span class="whitespace-pre-wrap">{row.targetFile}</span>;
            }
            if (row.resolveRule === MergeV1ResolveRule.RESOLVE_RULE_INVALID) {
              return <span class="whitespace-pre-wrap">{row.targetFile}</span>;
            }
            return '--';
          },
        },
      },
      {
        field: 'operationType',
        title: '操作详情',
        width: 200,
        slots: {
          default({ row }) {
            return row.resolveRule ? MergeV1ResolveRuleLabelMap[row.resolveRule] : '--';
          },
        },
      },
    ]);

    const gridOptions = computed(() => ({
      rowConfig: {
        keyField: 'id',
      },
      virtualYConfig: {
        enabled: true,
        gt: 20,
      },
      height: 'auto',
      showOverflow: true,
      loading: getResolvedFilesLoading.value,
      columns: tableColumns.value,
      data: tableRows.value,
    }) as VxeGridProps);

    watch(() => props.id, async (newVal) => {
      if (newVal) {
        await getResolvedFiles({
          id: currentProjectId.value!,
          operationId: newVal,
        }, {});
      }
    }, { immediate: true });

    const renderFileList = () => {
      return (
        <div class="file-history h-full flex flex-col rd-12px p-20px">
          <div class="mb-12px flex-1 overflow-hidden">
            <BasicVxeTable
              options={gridOptions.value}
              ref={tableRef}
            />
          </div>
        </div>
      );
    };

    return () => (
      <Drawer
        bodyStyle={{ padding: '8px', paddingTop: '12px' }}
        closable={false}
        destroyOnClose={true}
        mask={true}
        maskClosable={true}
        onClose={onClose}
        placement="right"
        title={drawerTitle.value}
        v-model:open={open.value}
        width={824}
      >
        {{
          extra: () => (
            <Button
              class="flex items-center justify-center"
              icon={(
                <Icon class="font-size-18px" component={<Close />} />
              )}
              onClick={onClose}
              type="text"
            />
          ),
          default: () => renderFileList(),
          footer: () => (
            <div class="flex justify-end gap-12px">
              <Button class="btn-fill-default" onClick={onClose} type="text">关闭</Button>
            </div>
          ),
        }}
      </Drawer>
    );
  },
});

export {
  FileDrawer,
};
