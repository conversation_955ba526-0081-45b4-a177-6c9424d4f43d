import { type PropType, computed, defineComponent, ref, Transition } from 'vue';
import { NButton, NIcon, NInput, NTooltip } from 'naive-ui';
import styles from './chat-input.module.less';
import { ArrowUp, Close, Collapse, Expand, GlobalOutlined, Lightbulb, Stop24Filled } from '@/common/components/svg-icons';

const ChatInput = defineComponent({
  props: {
    loading: { type: Boolean as PropType<boolean>, default: false },
    deepThink: { type: Boolean as PropType<boolean> },
    onlineSearch: { type: Boolean as PropType<boolean> },
    showDownBtn: { type: Boolean as PropType<boolean>, default: false },
    showDeepThink: { type: Boolean as PropType<boolean>, default: false },
    showOnlineSearch: { type: Boolean as PropType<boolean>, default: false },
    currentModel: { type: String as PropType<string>, default: '' },
    isEdit: { type: Boolean as PropType<boolean>, default: false },
    content: { type: String as PropType<string>, default: '' },
    onSubmit: { type: Function as PropType<(data: { message: string; isDeepThinkActive: boolean }) => void> },
    onScrollToBottom: { type: Function as PropType<() => void> },
    onCancel: { type: Function as PropType<() => void> },
  },
  emits: ['update:deepThink', 'update:onlineSearch', 'update:currentModel'],
  setup(props, { emit }) {
    const inputText = ref(props.content);
    const isExpended = ref(false);

    const isDeepThinkActive = computed({
      get: () => props.deepThink,
      set: (value) => {
        emit('update:deepThink', value);
      },
    });

    const isOnlineSearchActive = computed({
      get: () => props.onlineSearch,
      set: (value) => {
        emit('update:onlineSearch', value);
      },
    });

    const handleSend = () => {
      if (!inputText.value.trim()) {
        return;
      }
      isExpended.value = false;
      props.onSubmit?.({
        message: inputText.value,
        isDeepThinkActive: isDeepThinkActive.value!,
      });
      inputText.value = '';
    };

    const getPlaceholder = () => {
      switch (props.currentModel) {
        case '内部-安全模式':
          return '保密专用，数据全程本地处理，给 DeepSeek（内部-安全模式）发送消息';
        case '外部-通用模式':
          return '日常需求，加速处理，给 DeepSeek（外部-通用模式）发送消息';
        default:
          return '随时交流，给AI对话助手发送消息';
      }
    };

    const renderInputSubmitBtn = () => {
      return (
        <>
          {
            props.loading
              ? (
                <NButton
                  class="px-11px"
                  onClick={props.onCancel}
                  renderIcon={() => (
                    <NIcon>
                      <Stop24Filled />
                    </NIcon>
                  )}
                  type="primary"
                />
              )
              : (
                <NTooltip disabled={!!inputText.value.trim()} placement="top" trigger="hover">
                  {{
                    default: () => '请输入内容后发送',
                    trigger: () => (
                      <NButton
                        class="px-11px"
                        disabled={!inputText.value.trim()}
                        onClick={handleSend}
                        renderIcon={() => (
                          <NIcon>
                            <ArrowUp />
                          </NIcon>
                        )}
                        type="primary"
                      />
                    ),
                  }}
                </NTooltip>

              )
          }
        </>
      );
    };

    const renderBtnGroup = () => {
      return (
        <div class="flex gap-12px">
          <NButton
            class="px-11px"
            onClick={props.onCancel}
            renderIcon={() => (
              <NIcon>
                <Close />
              </NIcon>
            )}
          />
          <NTooltip disabled={!!inputText.value.trim()} placement="top" trigger="hover">
            {{
              default: () => '请输入内容后发送',
              trigger: () => (
                <NButton
                  class="px-11px"
                  disabled={!inputText.value.trim()}
                  onClick={handleSend}
                  renderIcon={() => (
                    <NIcon>
                      <ArrowUp />
                    </NIcon>
                  )}
                  type="primary"
                />
              ),
            }}
          </NTooltip>
        </div>
      );
    };

    return () => (
      <div class={[styles.chatInputContainer, 'pos-relative w-full flex flex-col']}>
        <Transition name="fade">
          <div
            class="chat-input-header to-FO-Container-Fill1 pos-absolute top--64px mb-8px h-64px w-full from-transparent bg-gradient-to-b text-center"
            v-show={props.showDownBtn}
          >
            <NTooltip>
              {{
                default: () => '返回底部',
                trigger: () => (
                  <NButton
                    bordered={false}
                    circle
                    class="bg-FO-Container-Fill3"
                    onClick={props.onScrollToBottom}
                    type="tertiary"
                  >
                    <NIcon class="c-FO-Content-Icon2" size={20}>
                      <ArrowUp class="rotate-180" />
                    </NIcon>
                  </NButton>
                ),
              }}
            </NTooltip>
          </div>
        </Transition>
        <div class="pos-relative w-full">
          <div class="pos-relative p-2px">
            <NInput
              autosize={!isExpended.value ? { minRows: 3, maxRows: 8 } : undefined}
              class={[styles.chatInput, 'w-full rd-16px pr-50px']}
              disabled={props.loading}
              onKeydown={(e) => {
                // 处理未输入完成的情况
                if (e.isComposing) {
                  return;
                }
                if (
                  e.key === 'Enter'
                  && !e.shiftKey
                  && inputText.value.trim()
                  && !props.loading && !e.isComposing
                ) {
                  e.preventDefault();
                  handleSend();
                }
              }}
              placeholder={getPlaceholder()}
              resizable={false}
              rows={20}
              size="large"
              type="textarea"
              v-model:value={inputText.value}
            />

            <div class="pos-absolute right-12px top-12px">
              <NButton
                class="px-11px"
                disabled={props.loading}
                onClick={() => isExpended.value = !isExpended.value}
                quaternary
                renderIcon={() => (!isExpended.value ? <Expand class="c-FO-Content-Icon2" /> : <Collapse class="c-FO-Content-Icon2" />)}
              />
            </div>
            <div class="pos-absolute bottom-12px right-12px">{props.isEdit ? renderBtnGroup() : renderInputSubmitBtn()}</div>
          </div>

          <div
            class={[
              styles.chatInputFooter,
              'pos-absolute left-12px bottom-12px flex justify-between items-end pr-24px overflow-hidden',
            ]}

          >
            <div class="action-group flex gap-4px">
              <NButton
                disabled={props.loading}
                onClick={() => isDeepThinkActive.value = !isDeepThinkActive.value}
                renderIcon={() => (
                  <NIcon>
                    <Lightbulb />
                  </NIcon>
                )}
                secondary
                type={isDeepThinkActive.value ? 'primary' : 'default'}
                v-show={props.showDeepThink}
              >深度思考
              </NButton>
              <NButton
                disabled={props.loading}
                onClick={() => isOnlineSearchActive.value = !isOnlineSearchActive.value}
                renderIcon={() => (
                  <NIcon>
                    <GlobalOutlined />
                  </NIcon>
                )}
                secondary
                type={isOnlineSearchActive.value ? 'primary' : 'default'}
                v-show={props.showOnlineSearch}
              />
            </div>
          </div>
        </div>
      </div>
    );
  },
});

export {
  ChatInput,
};
