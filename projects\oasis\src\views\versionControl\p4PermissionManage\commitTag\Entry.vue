<template>
  <PageConfig
    v-if="curStream?.tagConfig"
    :streamData="curStream"
    :allStreamList="allStreamList"
    @update="update"
  />

  <PageClone
    v-else-if="curStream"
    :streamData="curStream"
    :allStreamList="allStreamList"
    @update="update"
  />
  <Spin v-else :spinning="loadingStream">
    <Empty />
  </Spin>
</template>

<script lang="ts" setup>
import { Empty, Spin } from 'ant-design-vue';
import { useRouter } from 'vue-router';
import { computed, watch } from 'vue';
import { useCopyTagInfo } from '../hook.ts';
import PageClone from './PageClone.vue';
import PageConfig from './PageConfig.vue';
import { PlatformEnterPoint } from '@hg-tech/oasis-common';
import { useUserStore } from '../../../../store/modules/user.ts';

const router = useRouter();
const userStore = useUserStore();
const depotID = computed(() => Number(router.currentRoute.value.params.id));
const streamID = computed(() => Number(router.currentRoute.value.params.stream_id));
const serverID = computed(() => Number(router.currentRoute.value.params.server_id));

const { allStreamList, curStream, refresh, loadingStream, refreshStreamInfo } = useCopyTagInfo(depotID, streamID);
async function update() {
  await refreshStreamInfo();
  refresh();
}
watch(
  () => userStore.getProjectId,
  (v, oldValue) => {
    if (v && v !== oldValue) {
      // 切换项目后返回仓库列表
      router.push({ name: PlatformEnterPoint.P4Depots });
    }
  },
);
</script>
