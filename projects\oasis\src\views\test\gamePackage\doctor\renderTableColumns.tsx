import type { VxeGridPropTypes } from 'vxe-table';
import type { GamePackageDoctorDetailItem, GamePackageDoctorDetailUIPanelItem } from '../../../../api/page/model/testModel.ts';

export const duplicateColumns: VxeGridPropTypes.Column<GamePackageDoctorDetailItem>[] = [
  {
    title: 'Name',
    field: 'names',
    minWidth: 250,
    align: 'left',
    slots: {
      default({ row }) {
        return <div class="overflow-auto">{row.names?.map((i) => <div key={i}>{i}</div>)}</div>;
      },
    },
  },
  {
    title: 'Type',
    field: 'type',
    minWidth: 120,
    sortable: true,
    align: 'left',
    slots: {
      default({ row }) {
        return [
          <div>{row.type}</div>,
          <div>{row.duplicate ? `${row.size} * ${row.duplicate + 1}` : row.size}</div>,
        ];
      },
    },
  },
  {
    title: 'Wasted',
    field: 'wasted',
    minWidth: 100,
    sortable: true,
    sortBy: 'wasted_raw',
    align: 'left',
  },
  {
    title: 'Format',
    field: 'format',
    minWidth: 130,
    align: 'left',
    slots: {
      default({ row }) {
        return [
          <div>{row.dimension?.split(' ').map((i) => <div key={i}>{i}</div>)}</div>,
          <div>{row.format}</div>,
        ];
      },
    },
  },
  {
    title: 'Container',
    field: 'containers',
    minWidth: 800,
    align: 'left',
    slots: {
      default({ row }) {
        return row.containers?.map((i) => <div key={i}>{i}</div>) || [];
      },
    },
  },
  {
    title: 'Origin File',
    field: 'origin_files',
    minWidth: 800,
    align: 'left',
    slots: {
      default({ row }) {
        return row.origin_files?.map((i) => <div key={i}>{i}</div>) || [];
      },
    },
  },
];

export const modelMapColumns: VxeGridPropTypes.Column<GamePackageDoctorDetailItem>[] = [
  {
    title: 'Name',
    field: 'name',
    minWidth: 250,
    align: 'left',
  },
  {
    title: 'Size',
    field: 'size',
    formatter: ({ row }) => (row.duplicate ? `${row.size} * ${row.duplicate + 1}` : row.size || ''),
    sortable: true,
    sortBy: 'size_raw',
    minWidth: 100,
    align: 'left',
  },
  {
    title: 'Dimension',
    field: 'dimension',
    minWidth: 110,
    align: 'left',
  },
  {
    title: 'Format',
    field: 'format',
    minWidth: 130,
    align: 'left',
  },
];

export const uiPanelColumns: VxeGridPropTypes.Column<GamePackageDoctorDetailUIPanelItem>[] = [
  {
    title: 'Container',
    field: 'container',
    align: 'left',
  },
  {
    title: 'Size',
    field: 'size',
    sortable: true,
    sortBy: 'size_raw',
    minWidth: 100,
    align: 'left',
  },
];
