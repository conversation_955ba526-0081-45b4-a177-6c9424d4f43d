<template>
  <div :class="prefixCls">
    <EditableTree
      v-for="item in funcList"
      :key="item.ID"
      v-model:isAdding="isAdding"
      v-model:isEditing="isEditing"
      :item="item"
      :is-edit-mode="isEditMode"
      @add="handleAddFunc"
      @edit="handleEditFunc"
      @get-data="getFuncList"
    />
    <div v-if="!funcList?.length" class="my-20">
      <AEmpty :image="emptyImg">
        <EditableNode
          v-if="isEditMode"
          v-model:value="newAddRootFuncName"
          v-model:isEdit="isAdding"
          width="200px"
          @change="handleAddRootFunc()"
          @cancel="handleAddRootCancel()"
        >
          <a-button v-if="!isAdding" type="primary" @click="isAdding = true">
            + 添加第一个功能
          </a-button>
        </EditableNode>
      </AEmpty>
    </div>
  </div>
</template>

<script lang="ts" setup name="TrackingSettingsFunctions">
import { Empty as AEmpty } from 'ant-design-vue';
import { useMessage } from '/@/hooks/web/useMessage';
import { ref, watchEffect } from 'vue';
import EditableNode from '../components/EditableNode.vue';
import EditableTree from '../components/EditableTree.vue';
import type { TrackingToolFunctionsListItem } from '/@/api/page/model/trackingModel';
import {
  addTrackingToolFunction,
  editTrackingToolFunction,
  getTrackingToolFunctionsListByPage,
} from '/@/api/page/tracking';
import { useDesign } from '/@/hooks/web/useDesign';

const props = defineProps({
  toolID: {
    type: Number,
    required: true,
  },
  // 是否编辑模式
  isEditMode: {
    type: Boolean,
    default: false,
  },
});
const { prefixCls } = useDesign('tracking-settings-functions');
const { createMessage } = useMessage();
const emptyImg = AEmpty.PRESENTED_IMAGE_SIMPLE;
const funcList = ref<TrackingToolFunctionsListItem[]>([]);
const isAdding = ref(false);
const isEditing = ref(false);
const newAddRootFuncName = ref('');

function getUsableTree(list: TrackingToolFunctionsListItem[]) {
  const tree = list?.filter((e) => e.usable !== -1) || [];

  tree.forEach((e) => {
    if (e.children) {
      e.children = getUsableTree(e.children);
    }
  });

  return tree;
}

async function getFuncList() {
  const { list } = await getTrackingToolFunctionsListByPage(props.toolID);

  funcList.value = getUsableTree(list);
}

watchEffect(() => {
  // toolID或isEditMode变化时，重新获取数据
  if (props.toolID && typeof props.isEditMode === 'boolean') {
    getFuncList();
    isAdding.value = false;
    isEditing.value = false;
  }
});

async function handleAddFunc(newFunc: TrackingToolFunctionsListItem) {
  await addTrackingToolFunction({
    toolID: props.toolID,
    toolFuncName: newFunc.toolFuncName,
    parentFuncID: newFunc.parentFuncID,
  });
  getFuncList();
}

async function handleEditFunc(editFunc: TrackingToolFunctionsListItem) {
  await editTrackingToolFunction(
    {
      toolFuncName: editFunc.toolFuncName,
      usable: editFunc.usable,
      extInfo: editFunc.extInfo,
    },
    editFunc.ID!,
  );
  createMessage.success('保存成功！');
  getFuncList();
}

async function handleAddRootFunc() {
  handleAddFunc({
    toolFuncName: newAddRootFuncName.value,
    parentFuncID: 0,
  });
  newAddRootFuncName.value = '';
  isAdding.value = false;
}

function handleAddRootCancel() {
  newAddRootFuncName.value = '';
  isAdding.value = false;
}
</script>

<style lang="less">
@prefix-cls: ~'hypergryph-tracking-settings-functions';
@editable-tree: ~'hypergryph-editable-tree';
.@{prefix-cls} {
  overflow: auto;
  max-height: calc(100vh - 100px);
  min-height: 400px;
  margin-bottom: 100px;
  & > .@{editable-tree} {
    &::after {
      border: 0;
    }

    &::before {
      border: 0;
    }
  }
}
</style>
