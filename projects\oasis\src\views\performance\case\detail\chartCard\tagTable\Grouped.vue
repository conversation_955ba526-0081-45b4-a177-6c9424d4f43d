<template>
  <div>
    <div v-for="group in groupedParams" :key="group.chartTitle" class="mb-6">
      <div class="mb-2">
        <ACheckbox
          :checked="isGroupChecked(group.items)"
          :indeterminate="isGroupIndeterminate(group.items)"
          @change="(e) => handleGroupChange(e.target.checked, group.items)"
        >
          <span class="font-bold">{{ group.chartTitle }}</span>
        </ACheckbox>
      </div>
      <div class="grid grid-cols-3 gap-4 pl-6">
        <ACheckbox
          v-for="item in group.items"
          :key="(item.dataIndex as string)"
          :value="item.dataIndex"
          :checked="selectedParams.includes(item.dataIndex as string)"
          @change="(e) => handleItemChange(e.target.checked, item.dataIndex as string)"
        >
          {{ item.title }}
        </ACheckbox>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import type { BasicColumn } from '/@/components/Table';
import type { StatColumn } from '../../caseDetail.data';
import { Checkbox as ACheckbox } from 'ant-design-vue';
import { computed } from 'vue';
import { groupBy } from 'lodash-es';

const props = withDefaults(defineProps<{
  selectedParams?: string[];
  currentColumns?: BasicColumn[];
  paramsList?: StatColumn[];
}>(), {
  selectedParams: () => [],
  currentColumns: () => [],
  paramsList: () => [],
});
const emit = defineEmits<{
  changeSelectedParams: [params: string[]];
}>();
interface GroupedParams {
  chartTitle: string;
  items: BasicColumn[];
}
const selectedParams = computed(() => props.selectedParams);

const paramsList = computed(() => props.paramsList);
// 按 chartTitle 分组的参数列表
const groupedParams = computed<GroupedParams[]>(() => {
  const groups = groupBy(paramsList.value, (param) => param.chartTitle || '其他') as Record<string, any[]>;

  return Object.entries(groups).map(([chartTitle, items]) => ({
    chartTitle,
    items,
  }));
});

// 检查组是否全部选中
function isGroupChecked(items: BasicColumn[]) {
  return items.every((item) => selectedParams.value.includes(item.dataIndex as string));
}

// 检查组是否部分选中
function isGroupIndeterminate(items: BasicColumn[]) {
  const checkedCount = items.filter((item) =>
    selectedParams.value.includes(item.dataIndex as string),
  ).length;
  return checkedCount > 0 && checkedCount < items.length;
}

// 处理组选择变化
function handleGroupChange(checked: boolean, items: BasicColumn[]) {
  const itemDataIndexes = items.map((item) => item.dataIndex as string);

  if (checked) {
    // 添加组内所有未选中的项
    itemDataIndexes.forEach((dataIndex) => {
      if (!selectedParams.value.includes(dataIndex)) {
        selectedParams.value.push(dataIndex);
      }
    });
    emit('changeSelectedParams', selectedParams.value);
  } else {
    emit('changeSelectedParams', selectedParams.value.filter(
      (dataIndex) => !itemDataIndexes.includes(dataIndex),
    ));
  }
}

// 处理单个选项变化
function handleItemChange(checked: boolean, dataIndex: string) {
  if (checked && !selectedParams.value.includes(dataIndex)) {
    selectedParams.value.push(dataIndex);
    emit('changeSelectedParams', selectedParams.value);
  } else if (!checked) {
    emit('changeSelectedParams', selectedParams.value.filter((item) => item !== dataIndex));
  }
}
</script>
