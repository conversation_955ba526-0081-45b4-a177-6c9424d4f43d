<template>
  <div class="toolkit-package-classification">
    <div class="mb-4 text-center text-lg font-bold">
      分类配置
    </div>
    <div class="toolkit-package-classification__list">
      <VxeGrid v-bind="gridOptions" ref="tableRef" :editConfig="{ enabled: true, trigger: 'manual', mode: 'row' }" @editClosed="editClosed" @editActivated="editActived">
        <template #active="{ row }">
          <div v-if="hasEditStatus(row)" class="flex items-center">
            <Button type="link">
              <Icon
                :disabled="true"
                :icon="saveIcon"
                @click="handleSave(row)"
              />
            </Button>
            <Button type="link" danger>
              <Icon
                :icon="cancelIcon"
                @click="handleCancel(row)"
              />
            </Button>
          </div>
          <div v-else class="flex items-center">
            <Button :disabled="isActive" type="link">
              <Icon
                :disabled="true"
                :icon="editIcon"

                @click="handleEdit(row)"
              />
            </Button>
            <Button :disabled="isActive" type="link" danger>
              <Popconfirm
                title="是否删除分类"
                okText="确认"
                cancelText="取消"
                @confirm="handleDelete(row)"
              >
                <Icon
                  :icon="deleteIcon"
                />
              </Popconfirm>
            </Button>
          </div>
        </template>
      </VxeGrid>
      <Button
        v-if="!isActive"
        v-tippy="{ content: '新增一个工具分类', placement: 'bottom' }"
        type="dashed"
        class="mt b-#7c6aff! c-#7c6aff!"

        block
        @click="handleCreate"
      >
        +
      </Button>
    </div>
  </div>
</template>

<script lang="tsx" setup>
import { Button, Input, message, Popconfirm } from 'ant-design-vue';
import { onMounted, reactive, ref } from 'vue';
import { type CommonTypeListItem, addToolkitType, deleteToolkitType, editToolkitType, getToolkitTypeListByPage } from '../../api';
import type {
  EditRecordRow,
} from '@hg-tech/oasis-common/deprecated';
import { type VxeGridProps, type VxeTableInstance, VxeGrid } from 'vxe-table';
import { Icon } from '@iconify/vue';
import deleteIcon from '@iconify-icons/ant-design/delete-outlined';
import editIcon from '@iconify-icons/clarity/note-edit-line';
import saveIcon from '@iconify-icons/charm/tick';
import cancelIcon from '@iconify-icons/charm/cross';

const isActive = ref(false);
const toolkitTypeList = ref([] as CommonTypeListItem[]);
const tableRef = ref<VxeTableInstance<CommonTypeListItem>>();
const gridOptions = reactive<VxeGridProps<CommonTypeListItem> & { data: CommonTypeListItem[] }>({
  columns: [
    {
      title: '名称',
      field: 'name',
      width: 150,
      editRender: { },
      slots: {
        edit({ row }) {
          return <Input onChange={(e) => row.name = e.target.value} value={row?.name} />;
        },
      },
    },
    {
      title: '描述',
      field: 'description',
      editRender: { },
      slots: {
        edit({ row }) {
          return <Input onChange={(e) => row.description = e.target.value} value={row?.description} />;
        },
      },
    },
    { field: 'active', title: '操作', width: 100, fixed: 'right', slots: { default: 'active' } },
  ],
  data: toolkitTypeList.value,
  border: true,
  align: 'center',
  stripe: true,
});

function hasEditStatus(row: CommonTypeListItem) {
  return tableRef.value?.isEditByRow(row);
}
async function handleCreate() {
  const addRow: CommonTypeListItem = {
    name: '',
    description: '',
  };

  const result = await tableRef.value?.insertAt(addRow, -1);
  if (result) {
    const { row: newRow } = result;
    tableRef.value?.setEditCell(newRow, 'name');
  }
}

function handleEdit(record: any) {
  tableRef.value?.setEditRow(record);
}

async function handleDelete(record: any) {
  await deleteToolkitType({ editId: record.ID }, {});
  const res = await getToolkitTypeListByPage({ page: 1, pageSize: 999 }, {});
  toolkitTypeList.value = res.data.data?.list || [];
}

function handleCancel(record: EditRecordRow) {
  tableRef.value?.clearEdit();
}

async function handleSave(record: EditRecordRow) {
  if (!record.name) {
    message.warn('请输入分类名称');
    return;
  }
  if (!record.ID) {
    await addToolkitType({}, { name: record.name, description: record.description });
  } else {
    await editToolkitType({ editId: record.ID }, { name: record.name, description: record.description });
  }
  tableRef.value?.clearEdit();
  const res = await getToolkitTypeListByPage({ page: 1, pageSize: 999 }, {});
  tableRef.value?.reloadData(res.data.data?.list || []);
}
function editActived(row: any) {
  isActive.value = true;
}
function editClosed(row: any) {
  isActive.value = false;
}
onMounted(
  async () => {
    const res = await getToolkitTypeListByPage({ page: 1, pageSize: 999 }, {});
    tableRef.value?.reloadData(res.data.data?.list || []);
  },
);
</script>

<style lang="less" scoped>
@import (reference) '@hg-tech/forgeon-style/vars.less';

.toolkit-package-classification {
  position: relative;
  padding: 16px;
  border-radius: 8px;
  background-color: @FO-Container-Background;
}
</style>
