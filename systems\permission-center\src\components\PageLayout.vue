<template>
  <div class="h-100vh flex flex-col overflow-hidden">
    <PageHeader class="border-b border-FO-Container-Stroke1 border-b-solid">
      <template #title>
        <slot name="headerTitle" />
      </template>
      <template #actions>
        <slot name="headerActions" />
      </template>
    </PageHeader>
    <div class="min-h-0 flex-1 overflow-y-auto">
      <slot />
    </div>
  </div>
</template>

<script setup lang="ts">
import PageHeader from './PageHeader.vue';

defineSlots<{
  default: () => any;
  headerTitle: () => any;
  headerActions: () => any;
}>();
</script>
