/* This file is automatically generated. DO NOT EDIT it manually. */

/**
 * @deprecated 仅供当前仓库内部使用
 */
export const FigmaThemeVars = {
  light: {
    '$fo.$brand.primary-default': '$light.$violet.6',
    '$fo.$brand.primary-hover': '$light.$violet.5',
    '$fo.$brand.primary-active': '$light.$violet.7',
    '$fo.$brand.primary-disabled': '$light.$violet.3',
    '$fo.$brand.secondary-default': '$light.$violet.1',
    '$fo.$brand.secondary-hover': '$light.$violet.2',
    '$fo.$brand.secondary-active': '$light.$violet.3',
    '$fo.$brand.secondary-disabled': '$light.$violet.1',
    '$fo.$brand.tertiary-active': '$light.$violet.1',
    '$fo.$functional.success1-default': '$light.$green.6',
    '$fo.$functional.success1-hover': '$light.$green.5',
    '$fo.$functional.success1-active': '$light.$green.7',
    '$fo.$functional.success1-disabled': '$light.$green.3',
    '$fo.$functional.success2-default': '$light.$green.1',
    '$fo.$functional.success2-hover': '$light.$green.2',
    '$fo.$functional.success2-active': '$light.$green.3',
    '$fo.$functional.success2-disabled': '$light.$green.1',
    '$fo.$functional.warning1-default': '$light.$orange.6',
    '$fo.$functional.warning1-hover': '$light.$orange.5',
    '$fo.$functional.warning1-active': '$light.$orange.7',
    '$fo.$functional.warning1-disabled': '$light.$orange.3',
    '$fo.$functional.warning2-default': '$light.$orange.1',
    '$fo.$functional.warning2-hover': '$light.$orange.2',
    '$fo.$functional.warning2-active': '$light.$orange.3',
    '$fo.$functional.warning2-disabled': '$light.$orange.1',
    '$fo.$functional.error1-default': '$light.$red.6',
    '$fo.$functional.error1-hover': '$light.$red.5',
    '$fo.$functional.error1-active': '$light.$red.7',
    '$fo.$functional.error1-disabled': '$light.$red.3',
    '$fo.$functional.error2-default': '$light.$red.1',
    '$fo.$functional.error2-hover': '$light.$red.2',
    '$fo.$functional.error2-active': '$light.$red.3',
    '$fo.$functional.error2-disabled': '$light.$red.1',
    '$fo.$functional.info1-default': '$light.$blue.6',
    '$fo.$functional.info1-hover': '$light.$blue.5',
    '$fo.$functional.info1-active': '$light.$blue.7',
    '$fo.$functional.info1-disabled': '$light.$blue.3',
    '$fo.$functional.info2-default': '$light.$blue.1',
    '$fo.$functional.info2-hover': '$light.$blue.2',
    '$fo.$functional.info2-active': '$light.$blue.3',
    '$fo.$functional.info2-disabled': '$light.$blue.1',
    '$fo.$content.text0': '$basic.$white.100',
    '$fo.$content.text1': '$light.$gray.15',
    '$fo.$content.text2': '$light.$gray.11',
    '$fo.$content.text3': '$light.$gray.8',
    '$fo.$content.text4': '$light.$gray.6',
    '$fo.$content.icon0': '$basic.$white.100',
    '$fo.$content.icon1': '$light.$gray.14',
    '$fo.$content.icon2': '$light.$gray.10',
    '$fo.$content.icon3': '$light.$gray.7',
    '$fo.$content.icon4': '$light.$gray.5',
    '$fo.$content.components1': '$light.$gray.0',
    '$fo.$content.components2': '$light.$gray.0',
    '$fo.$content.link-default': '$light.$blue.6',
    '$fo.$content.link-hover': '$light.$blue.5',
    '$fo.$content.link-active': '$light.$blue.7',
    '$fo.$content.link-disabled': '$light.$blue.3',
    '$fo.$container.mask40': '$basic.$black.40',
    '$fo.$container.background': '$light.$gray.1',
    '$fo.$container.stroke0': '$basic.$white.100',
    '$fo.$container.stroke1': '$light.$gray.3',
    '$fo.$container.stroke2': '$light.$gray.4',
    '$fo.$container.stroke3': '$light.$gray.5',
    '$fo.$container.stroke4': '$light.$gray.6',
    '$fo.$container.fill0': '$basic.$white.0',
    '$fo.$container.fill1': '$basic.$white.100',
    '$fo.$container.fill2': '$light.$gray.1',
    '$fo.$container.fill3': '$light.$gray.2',
    '$fo.$container.fill4': '$light.$gray.3',
    '$fo.$container.fill5': '$light.$gray.4',
    '$fo.$container.fill6': '$dark.$gray.1',
    '$fo.$datavis.violet1': '$light.$violet.6',
    '$fo.$datavis.violet2': '$light.$violet.4',
    '$fo.$datavis.violet3': '$light.$violet.1',
    '$fo.$datavis.blue1': '$light.$blue.6',
    '$fo.$datavis.blue2': '$light.$blue.4',
    '$fo.$datavis.blue3': '$light.$blue.1',
    '$fo.$datavis.lightblue1': '$light.$lightblue.7',
    '$fo.$datavis.lightblue2': '$light.$lightblue.5',
    '$fo.$datavis.lightblue3': '$light.$lightblue.1',
    '$fo.$datavis.teal1': '$light.$teal.7',
    '$fo.$datavis.teal2': '$light.$teal.5',
    '$fo.$datavis.teal3': '$light.$teal.1',
    '$fo.$datavis.green1': '$light.$green.7',
    '$fo.$datavis.green2': '$light.$green.4',
    '$fo.$datavis.green3': '$light.$green.1',
    '$fo.$datavis.yellow1': '$light.$yellow.8',
    '$fo.$datavis.yellow2': '$light.$yellow.7',
    '$fo.$datavis.yellow3': '$light.$yellow.1',
    '$fo.$datavis.orange1': '$light.$orange.7',
    '$fo.$datavis.orange2': '$light.$orange.5',
    '$fo.$datavis.orange3': '$light.$orange.1',
    '$fo.$datavis.red1': '$light.$red.7',
    '$fo.$datavis.red2': '$light.$red.4',
    '$fo.$datavis.red3': '$light.$red.1',
    '$fo.$datavis.pink1': '$light.$pink.6',
    '$fo.$datavis.pink2': '$light.$pink.4',
    '$fo.$datavis.pink3': '$light.$pink.1',
    '$fo.$datavis.purple1': '$light.$purple.6',
    '$fo.$datavis.purple2': '$light.$purple.4',
    '$fo.$datavis.purple3': '$light.$purple.1',
  },
  dark: {
    '$fo.$brand.primary-default': '$dark.$violet.6',
    '$fo.$brand.primary-hover': '$dark.$violet.5',
    '$fo.$brand.primary-active': '$dark.$violet.4',
    '$fo.$brand.primary-disabled': '$dark.$violet.4',
    '$fo.$brand.secondary-default': '$dark.$violet.1',
    '$fo.$brand.secondary-hover': '$dark.$violet.2',
    '$fo.$brand.secondary-active': '$dark.$violet.3',
    '$fo.$brand.secondary-disabled': '$dark.$violet.1',
    '$fo.$brand.tertiary-active': '$dark.$violet.1',
    '$fo.$functional.success1-default': '$dark.$green.6',
    '$fo.$functional.success1-hover': '$dark.$green.5',
    '$fo.$functional.success1-active': '$dark.$green.4',
    '$fo.$functional.success1-disabled': '$dark.$green.2',
    '$fo.$functional.success2-default': '$dark.$green.2',
    '$fo.$functional.success2-hover': '$dark.$green.3',
    '$fo.$functional.success2-active': '$dark.$green.4',
    '$fo.$functional.success2-disabled': '$dark.$green.2',
    '$fo.$functional.warning1-default': '$dark.$orange.6',
    '$fo.$functional.warning1-hover': '$dark.$orange.5',
    '$fo.$functional.warning1-active': '$dark.$orange.4',
    '$fo.$functional.warning1-disabled': '$dark.$orange.2',
    '$fo.$functional.warning2-default': '$dark.$orange.1',
    '$fo.$functional.warning2-hover': '$dark.$orange.3',
    '$fo.$functional.warning2-active': '$dark.$orange.4',
    '$fo.$functional.warning2-disabled': '$dark.$orange.2',
    '$fo.$functional.error1-default': '$dark.$red.6',
    '$fo.$functional.error1-hover': '$dark.$red.5',
    '$fo.$functional.error1-active': '$dark.$red.4',
    '$fo.$functional.error1-disabled': '$dark.$red.2',
    '$fo.$functional.error2-default': '$dark.$red.1',
    '$fo.$functional.error2-hover': '$dark.$red.3',
    '$fo.$functional.error2-active': '$dark.$red.4',
    '$fo.$functional.error2-disabled': '$dark.$red.2',
    '$fo.$functional.info1-default': '$dark.$blue.6',
    '$fo.$functional.info1-hover': '$dark.$blue.5',
    '$fo.$functional.info1-active': '$dark.$blue.4',
    '$fo.$functional.info1-disabled': '$dark.$blue.2',
    '$fo.$functional.info2-default': '$dark.$blue.1',
    '$fo.$functional.info2-hover': '$dark.$blue.3',
    '$fo.$functional.info2-active': '$dark.$blue.4',
    '$fo.$functional.info2-disabled': '$dark.$blue.1',
    '$fo.$content.text0': '$dark.$gray.1',
    '$fo.$content.text1': '$dark.$gray.15',
    '$fo.$content.text2': '$dark.$gray.10',
    '$fo.$content.text3': '$dark.$gray.7',
    '$fo.$content.text4': '$dark.$gray.5',
    '$fo.$content.icon0': '$dark.$gray.1',
    '$fo.$content.icon1': '$dark.$gray.14',
    '$fo.$content.icon2': '$dark.$gray.9',
    '$fo.$content.icon3': '$dark.$gray.6',
    '$fo.$content.icon4': '$dark.$gray.5',
    '$fo.$content.components1': '$dark.$gray.16',
    '$fo.$content.components2': '$dark.$gray.11',
    '$fo.$content.link-default': '$dark.$blue.6',
    '$fo.$content.link-hover': '$dark.$blue.5',
    '$fo.$content.link-active': '$dark.$blue.4',
    '$fo.$content.link-disabled': '$dark.$blue.2',
    '$fo.$container.mask40': '$basic.$black.40',
    '$fo.$container.background': '$dark.$gray.0',
    '$fo.$container.stroke0': '$dark.$gray.2',
    '$fo.$container.stroke1': '$dark.$gray.3',
    '$fo.$container.stroke2': '$dark.$gray.4',
    '$fo.$container.stroke3': '$dark.$gray.6',
    '$fo.$container.stroke4': '$dark.$gray.7',
    '$fo.$container.fill0': '$basic.$black.0',
    '$fo.$container.fill1': '$dark.$gray.1',
    '$fo.$container.fill2': '$dark.$gray.2',
    '$fo.$container.fill3': '$dark.$gray.4',
    '$fo.$container.fill4': '$dark.$gray.5',
    '$fo.$container.fill5': '$dark.$gray.6',
    '$fo.$container.fill6': '$dark.$gray.0',
    '$fo.$datavis.violet1': '$dark.$violet.7',
    '$fo.$datavis.violet2': '$dark.$violet.4',
    '$fo.$datavis.violet3': '$dark.$violet.1',
    '$fo.$datavis.blue1': '$dark.$blue.7',
    '$fo.$datavis.blue2': '$dark.$blue.4',
    '$fo.$datavis.blue3': '$dark.$blue.1',
    '$fo.$datavis.lightblue1': '$dark.$lightblue.6',
    '$fo.$datavis.lightblue2': '$dark.$lightblue.3',
    '$fo.$datavis.lightblue3': '$dark.$lightblue.1',
    '$fo.$datavis.teal1': '$dark.$teal.6',
    '$fo.$datavis.teal2': '$dark.$teal.3',
    '$fo.$datavis.teal3': '$dark.$teal.1',
    '$fo.$datavis.green1': '$dark.$green.6',
    '$fo.$datavis.green2': '$dark.$green.3',
    '$fo.$datavis.green3': '$dark.$green.1',
    '$fo.$datavis.yellow1': '$dark.$yellow.5',
    '$fo.$datavis.yellow2': '$dark.$yellow.3',
    '$fo.$datavis.yellow3': '$dark.$yellow.1',
    '$fo.$datavis.orange1': '$dark.$orange.6',
    '$fo.$datavis.orange2': '$dark.$orange.4',
    '$fo.$datavis.orange3': '$dark.$orange.1',
    '$fo.$datavis.red1': '$dark.$red.7',
    '$fo.$datavis.red2': '$dark.$red.4',
    '$fo.$datavis.red3': '$dark.$red.1',
    '$fo.$datavis.pink1': '$dark.$pink.7',
    '$fo.$datavis.pink2': '$dark.$pink.4',
    '$fo.$datavis.pink3': '$dark.$pink.1',
    '$fo.$datavis.purple1': '$dark.$purple.7',
    '$fo.$datavis.purple2': '$dark.$purple.4',
    '$fo.$datavis.purple3': '$dark.$purple.1',
  },
};
