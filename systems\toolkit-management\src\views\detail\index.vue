<template>
  <div v-track:v="'8sarzmzigd'" class="toolkit-detail">
    <div class="toolkit-detail-back-btn mx-[16px] rounded-[8px]" @click="goBack()">
      <span class="flex cursor-pointer items-center gap-2 font-size-[18px] font-bold">
        <Icon :icon="PhCaretLeftBoldIcon" :size="16" /> 返回工具商店
      </span>
    </div>
    <div class="toolkit-detail-content w-[100%] flex">
      <div class="toolkit-detail-left">
        <Tabs v-model:activeKey="tabActiveKey" destroyInactiveTabPane @change="handleTabChange">
          <template v-for="item in achieveList" :key="item.key">
            <TabPane :tab="item.name">
              <component
                :is="item.component" :key="componentKey" :toolID="toolId"
                :curReadmeLink="toolDetail.readme" :curVersionID="curVerDetail.ID || versionDetail.ID"
              />
            </TabPane>
          </template>
        </Tabs>
      </div>
      <div class="toolkit-detail-right">
        <div class="toolkit-detail-right-top">
          <div class="toolkit-detail-right__share">
            <Tooltip placement="bottom">
              <template #title>
                分享该工具
              </template>
              <Icon
                :icon="ShareAltOutlinedIcon" color="#6b6" class="text-25px"
                @click="sharePage"
              />
            </Tooltip>
          </div>
          <div>
            <div>
              <div class="toolkit-detail-right__avatar">
                <Image
                  v-if="toolDetail.icon" :src="toolDetail.icon" :fallback="defaultImg" :preview="false" alt="icon"
                  class="!h-[60px] !w-[60px] !rounded-2xl"
                />
              </div>
            </div>
            <div>
              <div class="toolkit-detail-right__head">
                <div class="toolkit-detail-right__head-up">
                  <span class="toolkit-detail-right__title">{{ toolDetail.name }}</span>
                  <Tag class="toolkit-detail-right__tag">
                    {{ filterToolkitType(toolDetail.workTypeID!) }}
                  </Tag>
                </div>
                <div class="toolkit-detail-right__head-bottom">
                  {{ toolDetail.description }}
                </div>
              </div>
            </div>
            <div>
              <Tabs v-model:activeKey="curPlatform" centered @change="handlePlatformChange">
                <TabPane v-for="platform in platformList" :key="platform.value">
                  <template #tab>
                    <div class="flex items-center gap-2">
                      <component
                        :is="platform.icon" :toolID="toolId"
                      />
                      {{ platform.label }}
                    </div>
                  </template>
                </TabPane>
              </Tabs>
            </div>
            <div>
              <div class="toolkit-detail-right__detail">
                <p>
                  <span class="h-[100%] border rounded-1 p-[2px]">
                    {{ toolDetail?.project?.name ?? '通用' }}
                  </span>
                </p>

                <p class="flex items-center justify-center gap-1">
                  <Icon :icon="UserIcon" />
                  作者：{{
                    curVerDetail.author
                      ? formatNickName(curVerDetail.author)
                      : versionDetail.author
                        ? formatNickName(versionDetail.author)
                        : ''
                  }}
                </p>
                <p class="flex items-center justify-center gap-1">
                  <Icon :icon="FlashPaymentIcon" />
                  最新版本：
                  {{ versionDetail.version }}
                </p>
                <p class="flex items-center justify-center gap-1">
                  <Icon :icon="HomeIcon" />
                  当前版本：
                  <span
                    :class="{
                      'c-FO-Functional-Error1-Default':
                        curVerDetail.version && curVerDetail.version !== versionDetail.version,
                    }"
                  >
                    {{ curVerDetail.version || versionDetail.version }}
                  </span>
                </p>
                <p class="flex items-center justify-center gap-1">
                  <Icon :icon="FileSearchOneIcon" />
                  文件大小：
                  <span>
                    {{
                      curVerDetail.sizeKB || versionDetail.sizeKB
                        ? formatKBSize(curVerDetail.sizeKB || versionDetail.sizeKB!)
                        : ''
                    }}
                  </span>
                </p>
              </div>
            </div>
            <div v-if="curVerDetail.downloadLink || versionDetail.downloadLink">
              <Tooltip placement="bottom">
                <template #title>
                  下载当前版本
                </template>
                <Button
                  v-track="'lwxdjfwggf'"
                  class="toolkit-detail-right__download" :iconSize="20" type="primary" size="large" shape="circle"
                  :href="curVerDetail.downloadLink || versionDetail.downloadLink" download
                >
                  <template #icon>
                    <Icon :icon="DownloadIcon" />
                  </template>
                </Button>
              </Tooltip>

              <Popover placement="bottom">
                <template #content>
                  <QrCode :value="qrCodeUrl" tag="img" />
                  <div class="w-full text-center">
                    手机扫码下载
                  </div>
                </template>
                <Button
                  class="toolkit-detail-right__qrcode" :iconSize="18" size="large"
                  shape="circle" preIcon=""
                >
                  <template #icon>
                    <Icon :icon="QrCodeIcon" />
                  </template>
                </Button>
              </Popover>
            </div>
          </div>
        </div>
        <div v-show="tabActiveKey !== 'Version'" class="toolkit-detail-right-bottom">
          <div class="toolkit-detail-right-bottom__title">
            当前版本发版公告:
          </div>
          <MarkdownViewer
            v-if="curVerDetail.releaseNote || versionDetail.releaseNote"
            :value="curVerDetail.releaseNote || versionDetail.releaseNote || ''"
            theme="light"
            class="toolkit-detail-right-bottom__content"
          />
          <Empty v-else :image="Empty.PRESENTED_IMAGE_SIMPLE" description="暂无发版公告" />
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { Button, Empty, Image, message, Popover, TabPane, Tabs, Tag, Tooltip } from 'ant-design-vue';
import { computed, onBeforeMount, ref, unref } from 'vue';
import {
  downloadByUrl,
  formatKBSize,
  formatNickName,
  isMacOS,
  isWindows,
  MarkdownViewer,
  QrCode,
} from '@hg-tech/oasis-common/deprecated';
import { useClipboard } from '@vueuse/core';
import { useRouter } from 'vue-router';
import {
  type CommonTypeListItem,
  type ToolkitListItem,
  type ToolkitVersionListItem,
  getToolkitByID,
  getToolkitTypeListByPage,
  getToolkitVersionByID,
  getToolkitVersionListByPage,
} from '../../api';
import defaultImg from './default-img.png';
import { vTrack } from '../../directives/track.ts';
import ReadMe from './ReadMe.vue';
import Version from './Version.vue';
import { useDeprecatedTrackCtx, useMicroAppInject } from '@hg-tech/oasis-common';
import { type platformOptionType, platformOptions } from '../configs.tsx';
import { Icon } from '@iconify/vue';
import UserIcon from '@iconify-icons/icon-park-outline/user';
import PhCaretLeftBoldIcon from '@iconify-icons/ph/caret-left-bold';
import ShareAltOutlinedIcon from '@iconify-icons/ant-design/share-alt-outlined';
import FlashPaymentIcon from '@iconify-icons/icon-park-outline/flash-payment';
import HomeIcon from '@iconify-icons/icon-park-outline/home';
import FileSearchOneIcon from '@iconify-icons/icon-park-outline/file-search-one';
import DownloadIcon from '@iconify-icons/charm/download';
import QrCodeIcon from '@iconify-icons/carbon/qr-code';

interface TabItem {
  key: string;
  name: string;
  component: any;
}

const achieveList: TabItem[] = [
  {
    key: 'ReadMe',
    name: 'README',
    component: ReadMe,
  },
  {
    key: 'Version',
    name: '版本',
    component: Version,
  },
];

const { push, currentRoute, replace } = useRouter();
const toolId = Number(unref(currentRoute).params.id) || undefined;
const versionId = Number(unref(currentRoute).query.v) || undefined;
const tabActiveKey = ref<string>(unref(currentRoute).query.tab as string || 'ReadMe');
const toolDetail = ref({} as ToolkitListItem);
const versionDetail = ref({} as ToolkitVersionListItem);
const curVerDetail = ref({} as ToolkitVersionListItem);
const curPlatform = ref<number | undefined>(
  Number(unref(currentRoute).query.platform) || undefined,
);
const fs = Number(currentRoute.value.query?.fs);
const downloadNow = Number(currentRoute.value.query?.downloadNow);

const platformList = ref<platformOptionType[]>([]);
const componentKey = ref(0);
const commonLink = computed(() => {
  if (curVerDetail.value.downloadLink || versionDetail.value.downloadLink) {
    return `${import.meta.env?.VITE_BASE_API}/${curVerDetail.value.downloadLink || versionDetail.value.downloadLink}`;
  }

  return undefined;
});
const iosLink = computed(() => `itms-services://?action=download-manifest&url=${encodeURIComponent(`${import.meta.env?.VITE_BASE_API}/${curVerDetail.value.plistLink || versionDetail.value.plistLink}`)}`);
const qrCodeUrl = computed(() => (curPlatform.value === 2 ? iosLink.value : commonLink.value));

const typeList = ref<CommonTypeListItem[]>([]);

// 获取工具商店列表
async function getToolkitTypeList() {
  const res = await getToolkitTypeListByPage({ page: 1, pageSize: 999 }, {});
  const list = res.data.data?.list || [];
  if (list?.length > 0) {
    typeList.value = list;
  } else {
    typeList.value = [];
  }
}

// 获取工具信息
async function getToolkitDetail() {
  if (!toolId) {
    return;
  }
  const res = await getToolkitByID({ ID: toolId }, {});
  const retool = res.data.data?.retool;
  if (!retool) {
    return;
  }
  toolDetail.value = retool;
  platformList.value = platformOptions.filter((e) => retool.platforms?.includes(e.value));
}

// 获取工具最新版本信息
async function getLatestToolkitVersionDetail() {
  if (!toolId) {
    return;
  }

  const res = await getToolkitVersionListByPage({
    toolID: String(toolId),
    page: 1,
    pageSize: curPlatform.value ? 1 : 999,
    platform: curPlatform.value,
  }, {});
  const list = res.data.data?.list || [];
  if (list?.length > 0) {
    if (!curPlatform.value) {
      if (isMacOS) {
        versionDetail.value = list.find((e) => e.platform === 4) || list[0];
      } else if (isWindows) {
        versionDetail.value = list.find((e) => e.platform === 3) || list[0];
      } else {
        versionDetail.value = list[0];
      }
    } else {
      versionDetail.value = list[0];
    }

    curPlatform.value = versionDetail.value.platform;
  }
}

// 获取当前版本信息
async function getCurVerDetail() {
  if (versionId && toolId) {
    const res = await getToolkitVersionByID({ toolID: Number(toolId), ID: Number(versionId) }, {});
    const retoolVersion = res.data.data?.retoolVersion;
    if (!retoolVersion) {
      return;
    }
    curVerDetail.value = retoolVersion;
  }
}

// 对应工具商店名称
function filterToolkitType(type: number) {
  return type ? unref(typeList).find((e) => e.ID === type)?.name : '';
}

// 分享页面
const { copy } = useClipboard();
function sharePage() {
  copy(location.href);
  message.success('工具链接已经复制成功，快去分享吧！');
}

const { data: trackCtx } = useMicroAppInject(useDeprecatedTrackCtx);
async function handleDownload() {
  downloadByUrl({ url: `${import.meta.env?.VITE_BASE_API}/${curVerDetail.value.downloadLink}` });

  await trackCtx.value?.setTrack('lwxdjfwggf');
  message.success(fs === 1 ? '已唤起Oasis' : '已开始下载工具...');
}

onBeforeMount(async () => {
  getToolkitTypeList();
  getToolkitDetail();
  getLatestToolkitVersionDetail();
  await getCurVerDetail();

  if (downloadNow === 1) {
    handleDownload();
  }
});

async function handleTabChange(tabName: string | number) {
  replace({ query: { tab: tabName, v: versionId, platform: curPlatform.value, cardList: currentRoute.value.query?.cardList } });
  if (tabName === 'ReadMe') {
    await getToolkitDetail();
  }
}

async function handlePlatformChange(val: string | number) {
  replace({ query: { tab: tabActiveKey.value, platform: val, cardList: currentRoute.value.query?.cardList } });
  if (tabActiveKey.value === 'ReadMe') {
    await getToolkitDetail();
  } else {
    componentKey.value++;
  }
  await getLatestToolkitVersionDetail();
}

function goBack() {
  push({ name: 'Toolkit', query: { cardList: currentRoute.value.query?.cardList } });
}
</script>

<style lang="less">
@import (reference) '@hg-tech/forgeon-style/vars.less';

.toolkit-detail {
  display: flex;
  flex-flow: column wrap-reverse;

  &-back-btn {
    margin-top: 10px;
    margin-left: 10px;
    padding: 20px;
    border-radius: 8px;
    background: @FO-Container-Fill1;
  }

  &-col:not(:last-child) {
    padding: 0 10px;

    &:not(:last-child) {
      border-right: 1px dashed rgb(206 206 206 / 50%);
    }
  }

  &-right {
    display: flex;
    flex: 2;
    flex-direction: column;
    min-width: 400px;

    &-top {
      position: relative;
      min-height: 300px;
      margin: 10px 10px 10px 5px;
      padding: 30px;
      overflow: hidden;
      border-radius: 8px;
      background-color: @FO-Container-Fill1;
      text-align: center;
    }

    &-bottom {
      min-height: 200px;
      margin: 0 10px 30px;
      padding: 30px;
      border-radius: 8px;
      background-color: @FO-Container-Fill1;

      &__title {
        padding-bottom: 10px;
        border-bottom: 1px solid;
        border-bottom-color: @FO-Container-Stroke1;
        font-size: 16px;
        font-weight: bold;
      }

      &__content {
        transform: scale(0.95);
      }
    }
    &__qrcode {
      border-color: @FO-Functional-Warning1-Default !important;
      background-color: @FO-Functional-Warning1-Default!important;
      color: @FO-Content-Components1!important;
    }
    &__ribbon {
      /* top left corner */
      position: absolute;
      top: 20px;
      right: -45px;
      overflow: hidden;

      /* 45 deg ccw rotation */
      transform: rotate(45deg);
      background-color: @FO-Brand-Primary-Default;

      /* shadow */
      box-shadow: 0 0 10px #888;
      white-space: nowrap;

      &-text {
        display: block;
        width: 160px;
        margin: 1px 0;
        padding: 3px 40px;
        border: 1px solid #82aede;
        color: @FO-Content-Components1;
        font-weight: bold;
        text-align: center;
        text-decoration: none;
        text-shadow: 0 0 5px #444;
      }
    }

    &__share {
      position: absolute;
      z-index: 1;
      top: 20px;
      left: 20px;

      :hover {
        cursor: pointer !important;
      }
    }

    &__head {
      &-up {
        position: relative;
        margin-top: 25px;
        padding: 0 20px;
      }

      &-bottom {
        margin-top: 3px;
        font-size: 14px;
      }
    }

    &__title {
      margin-right: 5px;
      font-size: 20px;
      font-weight: bold;
    }

    &__tag {
      position: absolute;
      bottom: 3px;
    }

    &__detail {
      margin-top: 10px;
    }

    &__qrcode {
      margin-left: 16px;

      span.app-iconify {
        margin-top: 3px;
      }
    }

    &__team {
      &-item {
        display: inline-block;
        padding: 4px 24px;
      }

      span {
        margin-left: 3px;
      }
    }
  }

  &-left {
    flex: 3;
    min-width: 600px;
    margin: 10px 5px 10px 10px;
    padding: 20px;
    border-radius: 8px;
    background-color: @FO-Container-Fill1;
  }
}

@media screen and (width <= 960px) {
  .toolkit-detail-content {
    flex-direction: column;
  }
}
</style>
