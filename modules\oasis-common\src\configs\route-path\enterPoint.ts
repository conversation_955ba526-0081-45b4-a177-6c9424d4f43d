/**
 * 工业化平台全量菜单入口name枚举
 */
export enum PlatformEnterPoint {
  // Modules
  ModuleHome = 'home',
  ModuleDevelop = 'develop',
  ModuleTest = 'test',
  ModuleManage = 'manage',
  ModuleAI = 'ai',
  ModuleAccount = 'account',
  ModuleEfficacy = 'efficacy',

  // Subsystem
  SysAdmin = 'sys-admin',
  SysAigc = 'aigc',
  SysPermissionCenter = 'SysPermissionCenter',
  DeptAsset = 'deptAsset',
  Conflux = 'conflux',

  // Common
  /**
   * @deprecated 新增业务不允许使用
   */
  Redirect = 'Redirect',
  Error = 'Error',
  // forbidden
  Forbidden = 'Forbidden',
  // not found
  NotFound = 'NotFound',

  // Auth
  Login = 'Login',
  FeishuReturn = 'FeishuReturn',
  LinkToApp = 'LinkToApp',
  SSOReturn = 'SSOReturn',
  LinkToOther = 'LinkToOther',

  // 首页
  Home = 'Home',

  // 研发
  DevGuard = 'DevGuard',
  // 游戏包体中心
  GamePackage = 'GamePackage',
  GamePackageCard = 'GamePackageCard',
  GamePackageDoctor = 'GamePackageDoctor',
  GamePackageDoctorCompare = 'GamePackageDoctorCompare',
  GamePackageDoctorProportion = 'GamePackageDoctorProportion',
  GamePackageDoctorTrend = 'GamePackageDoctorTrend',
  GamePackageSettings = 'GamePackageSettings',
  GamePackageVersionSimple = 'GamePackageVersionSimple',
  // p4
  P4ClLabelManagement = 'P4ClLabelManagement',
  P4CommitParamsConfigs = 'P4CommitParamsConfigs',
  P4CommitTagConfigs = 'P4CommitTagConfigs',
  P4CompleteNoticeManagement = 'P4CompleteNoticeManagement',
  P4CustomGroupManagement = 'P4CustomGroupManagement',
  P4Depots = 'P4Depots',
  P4FormDiff = 'P4FormDiff',
  P4FormDiffDetail = 'P4FormDiffDetail',
  P4GroupManagement = 'P4GroupManagement',
  P4LdapGroupManagement = 'P4LdapGroupManagement',
  P4MemberManagement = 'P4MemberManagement',
  P4Onboarding = 'P4Onboarding',
  P4OnboardingSettings = 'P4OnboardingSettings',
  P4OnboardingSettingsChild = 'P4OnboardingSettingsChild',
  P4Pass = 'P4Pass',
  P4PermissionManagement = 'P4PermissionManagement',
  P4Training = 'P4Training',
  P4Trains = 'P4Trains',
  P4TrainsSettings = 'P4TrainsSettings',
  P4Triggers = 'P4Triggers',
  P4TriggersOverview = 'P4TriggersOverview',
  P4TriggersParamsSettings = 'P4TriggersParamsSettings',
  P4TriggersSettings = 'P4TriggersSettings',
  // Oasis聚合
  Oasis = 'OasisManagement',
  InstructionCombinations = 'InstructionCombinations',
  ToolNavigations = 'ToolNavigations',
  GroupChat = 'GroupChat',
  ToolGroupChat = 'ToolGroupChat',

  // 埋点平台
  Tracking = 'Tracking',
  TrackingAnalysis = 'TrackingAnalysis',
  TrackingAnalysisSettings = 'TrackingAnalysisSettings',

  // 权限管理中心
  PermissionCenterDashboard = 'PermissionCenterDashboard',
  PermissionCenterApp = 'PermissionCenterApp',
  PermissionCenterManagement = 'PermissionCenterManagement',

  // 测试平台
  AccountSettings = 'AccountSettings',
  AssetLib = 'AssetLib',
  Automation = 'Automation',
  AutomationReport = 'AutomationReport',
  AutomationSet = 'AutomationSet',
  AutomationTask = 'AutomationTask',
  P4TouchProcess = 'P4TouchProcess',
  BugRobot = 'BugRobot',
  BugRobotChats = 'BugRobotChats',
  CrashClassDetail = 'CrashClassDetail',
  CrashCollect = 'CrashCollect',
  CrashDetail = 'CrashDetail',
  DeptAssetApplyManagement = 'DeptAssetApplyManagement',
  CloudDevice = 'CloudDevice',
  CloudDeviceDetail = 'CloudDeviceDetail',
  DeptAssetsManagement = 'DeptAssetsManagement',
  DeptManagement = 'DeptManagement',
  DeptMemberManagement = 'DeptMemberManagement',
  DeviceManagement = 'DeviceManagement',
  DeviceManagementAdminConfig = 'DeviceManagementAdminConfig',
  DeviceManagementFaultList = 'DeviceManagementFaultList',
  DeviceManagementLogs = 'DeviceManagementLogs',
  DM01GroupManagement = 'DM01GroupManagement',
  GameArchive = 'GameArchive',

  Gitlab = 'Gitlab',
  GitlabReviewList = 'GitlabReviewList',
  ******************** = '********************',
  GitlabSubmitDescriptionSpecifications = 'GitlabSubmitDescriptionSpecifications',
  HDALib = 'HDALib',
  HomeSettings = 'HomeSettings',

  InstructionComponents = 'InstructionComponents',
  Instructions = 'Instructions',
  JenkinsAutoTask = 'JenkinsAutoTask',
  LoadTest = 'LoadTest',
  MaterialLib = 'MaterialLib',
  MenuManagement = 'MenuManagement',
  MessageCenter = 'MessageCenter',
  MessageTemplate = 'MessageTemplate',
  OrganizationManagement = 'OrganizationManagement',
  OriginInstructions = 'OriginInstructions',
  OutsourcingPlatform = 'OutsourcingPlatform',

  PerfdeepCardCompare = 'PerfdeepCardCompare',
  PerfdeepCardTrend = 'PerfdeepCardTrend',
  PerfdeepCase = 'PerfdeepCase',
  PerfdeepCaseDetail = 'PerfdeepCaseDetail',
  PerforceAccessLevelsSettings = 'PerforceAccessLevelsSettings',
  PerforceManagement = 'PerforceManagement',
  PerforceServersSettings = 'PerforceServersSettings',
  PerforceSettings = 'PerforceSettings',
  Performance = 'Performance',
  PerformanceCard = 'PerformanceCard',
  PerformanceCardCompare = 'PerformanceCardCompare',
  PerformanceCardTrend = 'PerformanceCardTrend',
  UnrealCase = 'UnrealCase',
  UnrealCaseDetail = 'UnrealCaseDetail',
  PerformanceCase = 'PerformanceCase',
  PerformanceCaseCompare = 'PerformanceCaseCompare',
  PerformanceCaseDetail = 'PerformanceCaseDetail',
  PerformanceMap = 'PerformanceMap',
  PerformanceReference = 'PerformanceReference',
  PerformanceHeatMap = 'PerformanceHeatMap',
  PerformanceHeatMapList = 'PerformanceHeatMapList',
  PerformanceHeatMapSettings = 'PerformanceHeatMapSettings',
  ProjectMember = 'ProjectMember',
  ProjectPermissionManagement = 'ProjectPermissionManagement',
  ProjectsManagement = 'ProjectsManagement',
  ProtocolTest = 'ProtocolTest',
  ProtocolTestDetail = 'ProtocolTestDetail',
  ProtocolTestDevices = 'ProtocolTestDevices',
  ProtocolTestHistory = 'ProtocolTestHistory',
  ResourceCheck = 'ResourceCheck',
  ResourceCheckIndexOld = 'ResourceCheckIndexOld',
  ResourceCheckItems = 'ResourceCheckItems',
  ResourceCheckOld = 'ResourceCheckOld',
  ResourceCheckReportCompare = 'ResourceCheckReportCompare',
  ResourceCheckReportDetail = 'ResourceCheckReportDetail',
  ResourceCheckReports = 'ResourceCheckReports',
  ResourceCheckRules = 'ResourceCheckRules',
  ResourceCheckRulesOld = 'ResourceCheckRulesOld',
  TCP4TSettings = 'TCP4TSettings',
  HomeSettingsChild = 'HomeSettingsChild',
  ResourceCheckSwitchesOld = 'ResourceCheckSwitchesOld',
  ResourceCheckSwitchesTemplate = 'ResourceCheckSwitchesTemplate',
  ResourceCheckTemplate = 'ResourceCheckTemplate',
  RoleManage = 'RoleManage',
  HomePageManagement = 'HomePageManagement',
  BannerManagement = 'BannerManagement',
  ProductionNews = 'ProductionNews',
  ProjectSettings = 'ProjectSettings',
  Secure = 'Secure',
  SecureChannelsSettings = 'SecureChannelsSettings',
  SecureProtections = 'SecureProtections',
  SecureSettings = 'SecureSettings',
  Services = 'Services',

  SwarmSettings = 'SwarmSettings',
  System = 'System',
  SystemPerforceManagement = 'SystemPerforceManagement',
  TCP4TOperationsSettings = 'TCP4TOperationsSettings',
  TCP4TSelectionsSettings = 'TCP4TSelectionsSettings',
  TCP4T = 'TCP4T',
  Test = 'Test',
  Tool = 'Tool',

  ToolWorkPlatform = 'ToolWorkPlatform',
  Toolkit = 'Toolkit',
  ToolkitDetail = 'ToolkitDetail',
  ToolkitPackageSettings = 'ToolkitPackageSettings',
  WebHookPlatform = 'WebHookPlatform',

  // efficacy modules
  EfficacyPreview = 'EfficacyPreview',

  // AI modules
  AIImage = 'AIImage',
  Voice = 'Voice',
  LLM = 'LLM',
  VersionNews = 'VersionNews',
  // AI生图
  AIImagePage = 'AIImagePage',
  // 快速抠图
  BgRemoval = 'BgRemoval',
  // 图片超分
  ExtraImage = 'ExtraImage',
  // 我的资产(图片)
  MyAssets = 'MyAssets',
  // 语音识别
  VoiceRecognition = 'VoiceRecognition',
  // 语音合成
  VoiceSynthesis = 'VoiceSynthesis',
  // 语音资产
  VoiceAssets = 'VoiceAssets',
  // 语音复刻
  VoiceRemake = 'VoiceRemake',
  // 版本要闻-版本报告（二次总结）
  VersionNewsSecondSummary = 'VersionNewsSecondSummary',
  // 版本要闻-版本要闻（一次总结）
  VersionNewsFirstSummary = 'VersionNewsFirstSummary',
  // 版本要闻-原始数据
  VersionNewsOriginData = 'VersionNewsOriginData',
  // AI TOOLS
  AITools = 'AITools',
  Watermask = 'Watermask',
  IconCut = 'IconCut',
  ImageTo3D = 'ImageTo3D',
  LineExtraction = 'LineExtraction',
  LocalTranslate = 'LocalTranslate',
  // chat助手
  AIChat = 'AIChat',
  // AI 404
  AINotFound = 'AINotFound',
  // AI 403
  AIForbidden = 'AIForbidden',

  // merge
  // history
  ConfluxHistory = 'ConfluxHistory',
  ConfluxTask = 'ConfluxTask',
}
