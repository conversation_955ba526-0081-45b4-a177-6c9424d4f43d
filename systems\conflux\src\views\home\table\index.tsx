import { BasicVxeTable, PlatformEnterPoint } from '@hg-tech/oasis-common';
import { type PropType, computed, defineComponent, ref, watch } from 'vue';
import { useMergeHome } from '../use-merge-home';
import type { VxeGridInstance, VxeGridListeners, VxeGridProps } from 'vxe-table';
import { groupBy, isEqual } from 'lodash';
import { Button, Modal, Popover, Tooltip } from 'ant-design-vue';
import { type RuleV1Rule, type UserUser, RuleV1StreamType } from '@hg-tech/api-schema-merge';
import Icon from '@ant-design/icons-vue';
import { RouterLink } from 'vue-router';
import { renderBranchTypeIcon } from '../../../models/config.model';
import { PermissionProvider } from '../../../components/PermissionProvider';
import { MergePermission } from '../../../constants/premission';
import { ForgeonThemeCssVar } from '@hg-tech/forgeon-style';

import StreamRuleArrow from '../../../assets/svg/StreamRuleArrow.svg?component';
import BasicFillPause from '../../../assets/svg/BasicFillPause.svg?component';
import BasicFillPlay from '../../../assets/svg/BasicFillPlay.svg?component';
import Edit from '../../../assets/svg/Edit.svg?component';
import Delete from '../../../assets/svg/Delete.svg?component';
import BasicFillCheck2 from '../../../assets/svg/BasicFillCheck2.svg?component';
import BasicFillBlock from '../../../assets/svg/BasicFillBlock.svg?component';
import BasicFillWarning from '../../../assets/svg/BasicFillWarning.svg?component';

interface RenderRow {
  id?: string;
  sourceStream?: string;
  targetStream?: string;
  sourceStreamId?: number;
  targetStreamId?: number;
  lastHandledCl?: string;
  pendingTaskCount?: number;
  count?: number;
  path?: string;
  status?: boolean;
  cl?: string;
  type: 'category' | 'item';
  excludeUserUsers?: UserUser[];
  defaultResolverUsers?: UserUser[];
  parentId?: string;
}

const MergeRuleTable = defineComponent({
  props: {
    onEditRule: {
      type: Function as PropType<(rule: RuleV1Rule) => void>,
      default: () => () => {},
    },
  },
  setup(props) {
    const { ruleList, currentBranchMap, onDeleteRule, onSetRuleEnable } = useMergeHome();
    const ruleTableRef = ref<VxeGridInstance<RenderRow>>();
    const renderBranch = (branchId: number) => {
      const branchInfo = currentBranchMap.value.get(branchId);
      return (
        <Popover>
          {{
            default: () => (
              <div class="branch-item flex items-center gap-4px truncate">
                {branchInfo && renderBranchTypeIcon[branchInfo?.streamType ?? RuleV1StreamType.DEVELOPMENT]()}
                <div class="branch-item-name truncate">{branchInfo ? branchInfo?.name : '未知分支'}</div>
              </div>
            ),
            title: () => (
              <div class="branch-info">
                <div class="branch-item-status-text FO-Font-B14 mb-4px flex items-center gap-4px">
                  {branchInfo && renderBranchTypeIcon[branchInfo?.streamType ?? RuleV1StreamType.DEVELOPMENT]()}
                  {branchInfo?.name || '未命名分支'}
                </div>
                <div class="branch-item-status-text FO-Font-R14 pl-24px c-FO-Content-Text2">
                  {branchInfo?.path || '未知分支路径'}
                </div>
              </div>
            ),
          }}
        </Popover>
      );
    };
    const tableRows = computed(() => {
      const groups = groupBy(ruleList.value ?? [], 'sourceStreamId');
      const rows: RenderRow[] = [];
      Object.keys(groups).forEach((key) => {
        const group = groups[key];
        rows.push({
          id: key,
          count: group.length,
          type: 'category',
          sourceStream: group[0].sourceStream,
          sourceStreamId: Number.parseInt(key),
        });
        group.forEach((item) => {
          rows.push({
            id: item.id,
            sourceStream: item.sourceStream,
            targetStream: item.targetStream,
            sourceStreamId: item.sourceStreamId,
            targetStreamId: item.targetStreamId,
            status: item.enable,
            lastHandledCl: item.lastHandledCl,
            pendingTaskCount: item.pendingTaskCount,
            type: 'item',
            excludeUserUsers: item.excludeUserUsers,
            defaultResolverUsers: item.defaultResolverUsers,
            parentId: item.sourceStreamId?.toString(),
          });
        });
      });
      return rows;
    });

    const expendedRows = ref<string[]>([]);
    watch(() => tableRows.value, (newRows) => {
      // 过滤出所有的分类节点，并提取它们的 ID
      const newExpandedRows = newRows.filter((row) => row.type === 'category').map((row) => row.id || '');
      // 如果新展开的行与当前展开的行相同，或者当前展开的行不为空，则不更新
      if (isEqual(expendedRows.value, newExpandedRows) || expendedRows.value.length !== 0) {
        return;
      }
      expendedRows.value = newExpandedRows;
    }, { immediate: true });
    watch([ruleTableRef, expendedRows, tableRows], () => {
      // 当 ruleTableRef、expendedRows 或 tableRows 变化时，设置树形表格的展开状态
      const rows = tableRows.value.filter((row) => row.id && expendedRows.value.includes(row.id));
      if (ruleTableRef.value && rows.length > 0) {
        // nextTick不能保证树形表格的展开状态正确设置，所以使用setTimeout 0
        setTimeout(() => {
          ruleTableRef.value?.setTreeExpand(rows, true);
        }, 0);
      }
    }, { deep: true });

    const tableColumns = computed<VxeGridProps<RenderRow>['columns']>(() => [
      { type: null, width: 40, resizable: false },
      {
        field: 'sourceStreamId',
        title: '分支别名',
        minWidth: 450,
        treeNode: true,
        slots: {
          default({ row }) {
            if (row.type === 'category') {
              return (
                <div class="flex items-center gap-8px">
                  <div class="whitespace-nowrap">
                    <span class="FO-Font-B14 mr-8px">{currentBranchMap.value.get(row.sourceStreamId!)?.name || '--'}</span>
                    (
                    <span class="FO-Font-R14 c-FO-Content-Text2">{row.sourceStream}</span>
                    )
                  </div>
                  <div class="rd-4px bg-FO-Container-Fill2 px-8px py-3px">{row.count}</div>
                </div>
              );
            }
            return (
              <div class="FO-Font-R14 flex items-center justify-between">
                <div class="flex-1">{renderBranch(row.sourceStreamId!)}</div>
                <StreamRuleArrow class="mx-8px w-62px flex-shrink-0" />
                <div class="flex-1">{renderBranch(row.targetStreamId!)}</div>
              </div>
            );
          },
        },
      },
      {
        field: 'path',
        title: '目标分支路径',
        minWidth: 200,
        showOverflow: 'tooltip',
        slots: {
          default({ row }) {
            if (row.type === 'category') {
              return;
            }
            return row.targetStream || '--';
          },
        },
      },
      { field: 'status', title: '状态', width: 100, slots: { default({ row }) {
        if (row.type === 'category') {
          return;
        }
        return (
          <div class="flex items-center gap-4px">
            <Icon
              class={[row.status ? 'c-FO-Datavis-Green1' : 'c-FO-Functional-Error1-Default', 'font-size-16px']}
              component={row.status ? <BasicFillCheck2 /> : <BasicFillBlock />}
            />
            <span>{row.status ? '启用中' : '停用中'}</span>
          </div>
        );
      } } },
      { field: 'lastHandledCl', title: '最新处理CL', width: 100, slots: { default({ row }) {
        if (row.type === 'category') {
          return;
        }
        return <span>{row.lastHandledCl || '--'}</span>;
      } } },
      { field: 'pendingTaskCount', title: '待处理任务', width: 80, slots: { default({ row }) {
        if (row.type === 'category') {
          return;
        }
        return row.pendingTaskCount
          ? (
            <RouterLink to={{
              name: PlatformEnterPoint.ConfluxTask,
              query: {
                ruleId: row.id,
              },
            }}
            >
              {row.pendingTaskCount}
            </RouterLink>
          )
          : '--';
      } } },
      {
        field: 'actions',
        title: '操作',
        width: 120,
        fixed: 'right',
        minWidth: 120,
        slots: {
          default({ row }) {
            if (row.type === 'category') {
              // 分类节点不显示操作按钮
              return [];
            }

            return [
              <PermissionProvider permission={{ any: [MergePermission.PauseAndResume] }}>
                {row.status
                  ? (
                    <Tooltip title="停用合并">
                      <Button
                        class="btn-fill-text"
                        icon={<Icon class="font-size-14px c-FO-Content-Icon1" component={<BasicFillPause />} />}
                        onClick={() => {
                          const m = Modal.confirm({
                            title: '停用合并',
                            icon: () => <Icon class="font-size-20px c-FO-Functional-Warning1-Default" component={<BasicFillWarning />} />,
                            content: () => (
                              <span class="FO-Font-R14 c-FO-Content-Text2">
                                停用分支【{currentBranchMap.value.get(row.sourceStreamId!)?.name} → {currentBranchMap.value.get(row.targetStreamId!)?.name}】合并，操作后合并暂停，是否继续？
                              </span>
                            ),
                            footer: () => (
                              <div class="flex justify-end gap-12px">
                                <Button class="btn-fill-default" onClick={() => m.destroy()} type="text">取消</Button>
                                <Button
                                  onClick={() => {
                                    onSetRuleEnable(row.id!, false);
                                    m.destroy();
                                  }}
                                  type="primary"
                                >
                                  停用
                                </Button>
                              </div>
                            ),
                          });
                        }}
                      />
                    </Tooltip>
                  )
                  : (
                    <Tooltip title="启用合并">
                      <Button
                        class="btn-fill-text"
                        icon={<Icon class="font-size-14px c-FO-Content-Icon1" component={<BasicFillPlay />} />}
                        onClick={() => {
                          const m = Modal.confirm({
                            title: '启用合并',
                            icon: () => <Icon class="font-size-20px c-FO-Functional-Warning1-Default" component={<BasicFillWarning />} />,
                            content: () => (
                              <span class="FO-Font-R14 c-FO-Content-Text2">
                                启用分支【{currentBranchMap.value.get(row.sourceStreamId!)?.name} → {currentBranchMap.value.get(row.targetStreamId!)?.name}】合并，操作后合并将恢复，是否继续？
                              </span>
                            ),
                            footer: () => (
                              <div class="flex justify-end gap-12px">
                                <Button class="btn-fill-default" onClick={() => m.destroy()} type="text">取消</Button>
                                <Button
                                  onClick={() => {
                                    onSetRuleEnable(row.id!, true);
                                    m.destroy();
                                  }}
                                  type="primary"
                                >
                                  启用
                                </Button>
                              </div>
                            ),
                          });
                        }}
                      />
                    </Tooltip>
                  )}
              </PermissionProvider>,
              <PermissionProvider permission={{ any: [MergePermission.EditRule] }}>
                <Tooltip title="编辑配置">
                  <Button
                    class="btn-fill-text"
                    icon={<Icon class="font-size-14px c-FO-Content-Icon1" component={<Edit />} />}
                    onClick={() => {
                      const rule = ruleList.value?.find((i: RuleV1Rule) => i.id === row.id);
                      if (rule) {
                        props.onEditRule(rule);
                      }
                    }}
                  />
                </Tooltip>
              </PermissionProvider>,
              <PermissionProvider permission={{ any: [MergePermission.DeleteRule] }}>
                <Tooltip title="删除配置">
                  <Button
                    class="btn-fill-text"
                    icon={<Icon class="font-size-14px c-FO-Content-Icon1" component={<Delete />} />}
                    onClick={() => {
                      const m = Modal.confirm({
                        title: '删除配置',
                        icon: () => <Icon class="font-size-20px c-FO-Functional-Error1-Default" component={<BasicFillWarning />} />,
                        content: () => (
                          <span class="FO-Font-R14 c-FO-Content-Text2">
                            删除合并配置【{currentBranchMap.value.get(row.sourceStreamId!)?.name} → {currentBranchMap.value.get(row.targetStreamId!)?.name}】，此操作不可恢复，请谨慎操作。
                          </span>
                        ),
                        footer: () => (
                          <div class="flex justify-end gap-12px">
                            <Button class="btn-fill-default" onClick={() => m.destroy()} type="text">取消</Button>
                            <Button
                              danger
                              onClick={() => {
                                onDeleteRule(row.id!);
                                m.destroy();
                              }}
                              type="primary"
                            >
                              删除
                            </Button>
                          </div>
                        ),
                      });
                    }}
                  />
                </Tooltip>
              </PermissionProvider>,
            ];
          },
        },
      },
    ] as VxeGridProps<RenderRow>['columns']);

    const gridOptions = computed(() => ({
      rowConfig: {
        keyField: 'id',
        isHover: true,
      },
      treeConfig: {
        transform: true,
        rowField: 'id',
        parentField: 'parentId',
        reserve: true,
      },
      showOverflow: false,
      columns: tableColumns.value,
      data: tableRows.value,
    }) as VxeGridProps);

    const gridEvent: VxeGridListeners<RenderRow> = ({
      toggleTreeExpand({ row, expanded }) {
        if (row.id) {
          if (expanded) {
            expendedRows.value.push(row.id);
          } else {
            expendedRows.value = expendedRows.value.filter((id) => id !== row.id);
          }
        }
      },
    });

    return () => (
      <BasicVxeTable
        cssVarOverrides={{
          '--vxe-ui-layout-background-color': ForgeonThemeCssVar.ContainerFill1,
        }}
        events={gridEvent}
        options={gridOptions.value}
        ref={ruleTableRef}
      />
    );
  },
});

export {
  MergeRuleTable,
};
