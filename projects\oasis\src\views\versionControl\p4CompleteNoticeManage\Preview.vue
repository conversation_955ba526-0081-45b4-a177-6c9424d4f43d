<template>
  <div :class="prefixCls" class="rounded-md bg-FO-Container-Fill1 p-4">
    <template v-if="!isNew && !isConfig">
      <div>
        <div v-if="!isEditName" class="h-32px" @click="changeToEditName()">
          <span class="text-lg font-bold">{{ activeNotice?.name }}</span>
          <Icon icon="bx:edit-alt" class="ml-1" />
        </div>
        <div v-else class="h-32px">
          <a-input
            ref="noticeNameRef"
            v-model:value.trim="noticeName"
            placeholder="请输入名称"
            class="!w-200px"
            :maxlength="20"
          />
          <a-button
            type="text"
            size="small"
            title="撤销"
            class="ml-2"
            @click="handleEditNameRevert()"
          >
            <Icon icon="icon-park-outline:return" />
          </a-button>
          <a-button type="text" size="small" title="保存" @click="handleEditName()">
            <Icon icon="charm:tick" class="!c-FO-Functional-Success1-Default" />
          </a-button>
        </div>
      </div>
      <BorderBox
        class="!relative !mt-6"
        label="通知规则"
        subLabel="符合规则的所选路径下的提交完成后，会发送至对应群聊，一次提交符合多个规则会发送至多个群聊，但每个群聊最多只通知一次（需先将提交机器人拉入群聊）"
      >
        <div
          v-if="isEditPage || isEditName"
          class="absolute z-10 h-full w-190"
          @click="disabledClick()"
        />
        <div class="ml-2 mt-4 min-w-750px">
          <div v-for="(item, i) in newItems" :key="item.ID" class="flex items-center not-last:mb-3">
            若
            <a-select
              v-model:value="item.ruleType"
              class="!mx-1 !ml-1 !min-w-120px"
              :options="ruleTypeList"
              size="small"
            />
            <a-input
              v-if="item.ruleType !== 3"
              v-model:value.trim="item.ruleFile"
              placeholder="请输入文件设置"
              class="mx-1 w-120px"
              size="small"
            />
            将通知发送至
            <a-select
              v-model:value="item.chatID"
              class="!mx-1 !w-275px"
              :options="chatList"
              :fieldNames="{
                label: 'name',
                value: 'chat_id',
              }"
              placeholder="请选择通知群"
              size="small"
            />
            <div
              v-if="newItems.length > 1"
              :class="`${prefixCls}__btn mr-1`"
              @click="handleDeleteItem(i)"
            >
              <Icon icon="ant-design:minus-outlined" />
            </div>
            <div
              v-if="newItems.length - 1 === i"
              :class="`${prefixCls}__btn`"
              @click="handleAddItem()"
            >
              <Icon icon="ant-design:plus-outlined" />添加条件
            </div>
          </div>
        </div>
        <div class="mt-4 flex items-center">
          <div :class="`${prefixCls}__btn ml-2`" @click="handleSaveItems()">
            保存
          </div>
          <div :class="`${prefixCls}__btn ml-2`" noBg @click="handleCancelItems()">
            取消
          </div>
        </div>
      </BorderBox>
      <BorderBox class="!mt-6" label="消息配置" subLabel="发送飞书消息卡片的规则配置">
        <div
          v-if="isEditPage || isEditName || !isEqual(newItems, activeNotice.items)"
          class="absolute left-56 top-11 z-10 h-8 w-16"
          @click="disabledClick()"
        />
        <div class="ml-2 mt-4">
          消息中显示的提交资源最大数量
          <a-select
            v-model:value="noticeMaxMessage"
            class="!mx-1 !ml-1 !min-w-60px"
            :options="maxMessageList"
            placeholder="请选择数量"
            size="small"
            @change="handleChangeMaxMessage(noticeMaxMessage)"
          />
        </div>
      </BorderBox>
      <BorderBox
        v-if="isPreview"
        class="!mt-6"
        label="提交路径"
        subLabel="只有勾选路径下的提交才会触发通知规则"
      >
        <div class="flex">
          <div :class="`${prefixCls}__editIcon mt-3 !mr-0 ml-3`" @click="changeToEditPage()">
            <Icon icon="ant-design:edit-filled" />
          </div>
          <P4AuditTree
            ref="p4TreeRef"
            :key="JSON.stringify(activeNotice)"
            :class="`${prefixCls}__tree`"
            :loading="treeLoading"
            disabled
            :permissionList="newPaths"
          />
        </div>
      </BorderBox>
    </template>
    <EditPage
      v-if="isEditPage"
      :isNew="isNew"
      :isPreview="isPreview"
      :isUpdate="isUpdate"
      :treeLoading="treeLoading"
      :activeNotice="activeNotice"
      :chatList="chatList"
      :initItem="initItem"
      @revert="handleRevert"
      @submit="handleSubmit"
    />
  </div>
</template>

<script lang="ts" setup name="P4CompleteNoticePreview">
import { cloneDeep, isEqual, omit, uniqWith } from 'lodash-es';
import { type PropType, ref, watch, watchEffect } from 'vue';
import EditPage from './Edit.vue';
import { maxMessageList, ruleTypeList } from './completeNotice.data';
import type { ItemsItem, P4CompleteNoticeListItem, PathItem } from '/@/api/page/model/p4Model';
import type { FeishuChatListItem } from '/@/api/page/model/systemModel';
import { addCompleteNotice, updateCompleteNoticeList } from '/@/api/page/p4';
import { BorderBox } from '/@/components/Form';
import { Icon } from '/@/components/Icon';
import { useTrack } from '/@/hooks/system/useTrack';
import { useDesign } from '/@/hooks/web/useDesign';
import { useMessage } from '/@/hooks/web/useMessage';
import { useUserStoreWithOut } from '/@/store/modules/user';
import P4AuditTree from '../swarmSettings/components/p4AuditTree.vue';

const props = defineProps({
  isConfig: {
    type: Boolean,
    default: false,
  },
  isNew: {
    type: Boolean,
    default: false,
  },
  isPreview: {
    type: Boolean,
    default: false,
  },
  isUpdate: {
    type: Boolean,
    default: false,
  },
  activeNotice: {
    type: Object as PropType<P4CompleteNoticeListItem>,
    default: () => ({}),
  },
  showNoticeList: {
    type: Array as PropType<P4CompleteNoticeListItem[]>,
    default: () => [],
  },
  chatList: {
    type: Array as PropType<FeishuChatListItem[]>,
    default: () => [],
  },
  initItem: {
    type: Object as PropType<ItemsItem>,
    default: () => {},
  },
});
const emit = defineEmits(['editOrRevert', 'success']);
const { prefixCls } = useDesign('p4-complete-notice-preview');
const { createMessage } = useMessage();
const userStore = useUserStoreWithOut();
const { setTrack } = useTrack();

const newPaths = ref<PathItem[]>([]);
const newItems = ref<ItemsItem[]>([]);

const noticeName = ref<string>('');
const noticeMaxMessage = ref<number>();
const isEditPage = ref<boolean>(false);
const isEditName = ref<boolean>(false);
const treeLoading = ref<boolean>(false);
const noticeNameRef = ref();
const p4TreeRef = ref();

function disabledClick() {
  if (isEditName.value) {
    createMessage.warning('请先保存或撤销 通知规则名称 的更改');
    return;
  }
  if (isEditPage.value) {
    createMessage.warning('请先保存或撤销 提交路径 的更改');
    return;
  }
  if (!isEqual(newItems.value, props.activeNotice.items)) {
    createMessage.warning('请先保存或取消 通知规则 的更改');
  }
}

async function handleChangeMaxMessage(curMaxMessage?: number) {
  const items = props.activeNotice.items?.map((item) =>
    omit(item, ['CreatedAt', 'UpdatedAt', 'ID', 'ruleID']),
  );
  const paths = props.activeNotice.paths?.map((item) => item.path);
  const submitData = Object.assign(
    omit(props.activeNotice, ['CreatedAt', 'UpdatedAt', 'ID', 'items', 'paths', 'maxMessage']),
    {
      items: items as ItemsItem[],
      paths: paths as PathItem[],
      maxMessage: curMaxMessage,
    },
  );
  await updateCompleteNoticeList(userStore.getProjectId, props.activeNotice.ID!, submitData);
  setTrack('bomfdp6ofm');
  emit('success', true, props.activeNotice.ID);
}

async function handleSaveItems() {
  if (isEqual(newItems.value, props.activeNotice.items)) {
    createMessage.warning('通知规则未修改, 无需保存');
    return;
  }
  const excludeRuleTypeThree = newItems.value?.filter((item) => item.ruleType !== 3);
  const isRuleFileEmpty = excludeRuleTypeThree.some((item) => !item.ruleFile);
  if (isRuleFileEmpty) {
    createMessage.warning('文件设置不能为空');
    return;
  }
  const paths = props.activeNotice.paths?.map((item) => item.path);
  const items = newItems.value.map((item) =>
    omit(item, ['CreatedAt', 'UpdatedAt', 'ID', 'ruleID']),
  );
  const submitData = Object.assign(
    omit(props.activeNotice, ['CreatedAt', 'UpdatedAt', 'ID', 'items', 'paths']),
    {
      items: items as ItemsItem[],
      paths: paths as PathItem[],
    },
  );
  const checkItemsDuplicate = uniqWith(submitData.items, isEqual).length !== items?.length;
  if (checkItemsDuplicate) {
    submitData.items = uniqWith(submitData.items, isEqual);
    createMessage.success('通知规则选项有重复，已自动去重');
  }
  await updateCompleteNoticeList(userStore.getProjectId, props.activeNotice.ID!, submitData);
  setTrack('mspy01vh4k');
  emit('success', true, props.activeNotice.ID);
}

function handleCancelItems() {
  newItems.value = cloneDeep(props.activeNotice.items) || [];
}

async function handleSubmit(submit: P4CompleteNoticeListItem) {
  if (isNameDuplicate(submit.name)) {
    return;
  }
  if (props.isUpdate) {
    await updateCompleteNoticeList(userStore.getProjectId, props.activeNotice.ID!, submit);
    setTrack('paeihcpwhj');
    emit('success', true, props.activeNotice.ID);
  } else if (props.isNew) {
    const { id } = await addCompleteNotice(userStore.getProjectId, submit);
    setTrack('jisf54kro8');
    emit('success', true, id);
  }
}

// 添加通知规则
function handleAddItem() {
  newItems.value.push(cloneDeep(props.initItem));
}

// 删除通知规则
function handleDeleteItem(index: number) {
  newItems.value.splice(index, 1);
}

function handleRevert() {
  isEditPage.value = false;
  emit('editOrRevert', false, props.activeNotice.ID);
}

function changeToEditPage() {
  if (isEditName.value) {
    createMessage.warning('请先保存或撤销 通知规则名称 的更改');
    return;
  }
  if (!isEqual(newItems.value, props.activeNotice.items)) {
    createMessage.warning('请先保存或取消 通知规则 的更改');
    return;
  }
  isEditPage.value = true;
  emit('editOrRevert', true);
}

watchEffect(() => {
  if (props.isPreview) {
    isEditPage.value = false;
    isEditName.value = false;
  }
  if (props.isNew) {
    isEditPage.value = true;
  }
});

watch(
  () => props.activeNotice,
  (val) => {
    if (val) {
      noticeName.value = val.name || '';
      noticeMaxMessage.value = val.maxMessage || undefined;
      isEditName.value = false;
      newItems.value = cloneDeep(props.activeNotice.items || []);
      newPaths.value = (props.activeNotice.paths || []).map((item) => ({
        ...item,
        permit: 1,
      }));
    }
  },
  {
    immediate: true,
  },
);

// 判断name是否在notice中重复
function isNameDuplicate(name?: string) {
  const findName = props.showNoticeList.find(
    (e) => e.ID !== props.activeNotice?.ID && e.name?.toLowerCase() === name?.toLowerCase(),
  );
  if (findName) {
    createMessage.warning('通知规则名称重复(不区分大小写)');
  }
  return findName;
}

// 保存修改组名
async function handleEditName() {
  if (isNameDuplicate(noticeName.value)) {
    return;
  }
  if (isEqual(noticeName.value, props.activeNotice.name)) {
    createMessage.warning('通知规则名称未修改, 无需保存');
    return;
  }
  const items = props.activeNotice.items?.map((item) =>
    omit(item, ['CreatedAt', 'UpdatedAt', 'ID', 'ruleID']),
  );
  const paths = props.activeNotice.paths?.map((item) => item.path);
  const submitData = {
    streamID: props.activeNotice!.streamID,
    name: noticeName.value,
    maxMessage: props.activeNotice.maxMessage,
    items,
    paths: paths as PathItem[],
  };
  await updateCompleteNoticeList(userStore.getProjectId, props.activeNotice.ID!, submitData);
  emit('success', true, props.activeNotice.ID);
  isEditName.value = false;
  noticeName.value = '';
}

// 撤销修改组名
function handleEditNameRevert() {
  isEditName.value = false;
  noticeName.value = props.activeNotice!.name!;
}

function changeToEditName() {
  if (isEditPage.value) {
    createMessage.warning('请先保存或撤销 提交路径 的更改');
    return;
  }
  if (!isEqual(newItems.value, props.activeNotice.items)) {
    createMessage.warning('请先保存或取消 通知规则 的更改');
    return;
  }
  isEditName.value = true;
}
</script>

<style lang="less">
@prefix-cls: ~'hypergryph-p4-complete-notice-preview';
.@{prefix-cls} {
  &__btn {
    color: @FO-Content-Components1;
    background-color: @FO-Container-Fill6;
    border: 1px solid @FO-Container-Fill6;
    cursor: pointer;
    padding: 2px;
    border-radius: 4px;
    user-select: none;

    &:hover {
      filter: brightness(0.9);
    }

    &[noBg] {
      background-color: transparent;
      color: @FO-Content-Text1;
    }
  }

  &__tree {
    margin: 10px 20px 0 10px;
  }

  &__editIcon {
    margin-right: 8px;
    padding: 3px;
    border-radius: 4px;
    height: fit-content;
    background-color: @FO-Brand-Primary-Default;
    color: @FO-Content-Components1;
    cursor: pointer;

    &[disabled='true'] {
      background-color: @FO-Container-Fill5;
      cursor: not-allowed;
    }
  }
}
</style>
