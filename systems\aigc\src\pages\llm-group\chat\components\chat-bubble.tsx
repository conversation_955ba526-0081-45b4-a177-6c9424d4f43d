import { <PERSON><PERSON>utt<PERSON>, NIcon, NImage, NPopover, NTooltip, useMessage } from 'naive-ui';
import { type PropType, computed, defineComponent, h, ref, watch } from 'vue';
import MarkdownIt from 'markdown-it';
import { full as emoji } from 'markdown-it-emoji';
import { happy } from 'random-jpn-emoji';
import styles from './chat-bubble.module.less';
import { useAppTheme } from '@/common/hooks';
import { ArrowForwardIosRound, Copy, Edit, Loading3QuartersOutlined } from '@/common/components/svg-icons';
import { markdownItKatexEnhance } from './markdown-plugin/latex-enhance';
import markdownKatex from 'markdown-it-katex';
import { useClipboard } from '@vueuse/core';

import '@/assets/styles/katex.min.css';
import 'katex/dist/katex.min.css';
import { CodeBlock } from './markdown-plugin/code-block';
import type Token from 'markdown-it/lib/token.mjs';
import { EllipsisContainer } from '@/common/components/ellipsis-container';
import type { ChatMessageSearch } from '@/models/chat';
import { ChatInput } from './chat-input';
import { SearchItem } from './search-item';

enum MarkdownNesting {
  Open = 1,
  Close = -1,
  SelfClose = 0,
}

const ChatBubble = defineComponent({
  props: {
    id: {
      type: Number as PropType<number>,
      required: true,
    },
    content: {
      type: String as PropType<string>,
      required: true,
    },
    originalContent: {
      type: String as PropType<string>,
      default: '',
    },
    isUser: {
      type: Boolean as PropType<boolean>,
      required: true,
    },
    isFinished: {
      type: Boolean as PropType<boolean>,
      default: false,
    },
    isLoading: {
      type: Boolean as PropType<boolean>,
      default: false,
    },
    search: {
      type: Array as PropType<ChatMessageSearch[]>,
      default: () => [],
    },
    onSendMessage: {
      type: Function as PropType<(data: { message: string; editId?: number }) => void>,
      default: () => {},
    },
    onShowSearch: {
      type: Function as PropType<(search: ChatMessageSearch[]) => void>,
      default: () => {},
    },
    onScrollTo: {
      type: Function as PropType<(y: number) => void>,
      default: () => {},
    },
  },
  setup(props) {
    const renderedMap = new Map();
    // 初始化 MarkdownIt 实例
    const md = new MarkdownIt()
      // 添加 emoji 插件，解析表情符号
      .use(emoji)
      .use(markdownKatex)
      .use(markdownItKatexEnhance, { blockClass: styles.katexBlock, inlineClass: styles.katexInline });

    const message = useMessage();
    const { currentTheme } = useAppTheme();
    const chatBubbleRef = ref<HTMLElement | null>(null);
    const isUserHover = ref(false);
    const isEditModel = ref(false);
    const editContent = ref<string>(props.content);
    /**
     * 根据token分割内容
     */
    function splitByBlockTokens(content: string): string[] {
      const tokens = md.parse(content, {});
      const lines = content.split('\n');
      const result: string[] = [];
      let lastEndLine = 0;
      for (const token of tokens) {
        if (!token.map) {
          continue;
        }
        const [start, end] = token.map;
        if (start >= lastEndLine) {
          const block = lines.slice(start, end).join('\n').trim();
          if (block) {
            result.push(block);
          }
          lastEndLine = end;
        }
      }
      return result;
    }

    const contentBlocks = computed(() => {
      const blocks = splitByBlockTokens(props.content);
      return blocks.map((block) => block.trim());
    });

    /**
     * 将某段block根据token解析为嵌套的tsx结构
     */
    function renderBlockToVNode(block: string) {
      const tokens = md.parse(block, {});
      const renderTokens = (tokens: Token[]): JSX.Element[] => {
        /**
         * 针对开闭标签的结构栈
         */
        const structStack: {
          tag: string;
          attrs: Record<string, string>;
          children: JSX.Element[];
        }[] = [];
        const result: JSX.Element[] = [];
        const pushElement = (el: JSX.Element) => {
          if (structStack.length > 0) {
            structStack[structStack.length - 1].children.push(el);
          } else {
            result.push(el);
          }
        };

        for (let i = 0; i < tokens.length; i++) {
          const token = tokens[i];
          // 处理code块
          if (token.type === 'fence') {
            const code = token.content;
            const lang = token.info.trim() || 'plaintext';
            pushElement(<CodeBlock code={code} lang={lang} />);
            continue;
          }
          // 处理image
          if (token.type === 'image') {
            pushElement(
              <div class="md-image-container flex-c-center">
                <NImage
                  alt={decodeURIComponent(token.attrGet('alt') || '')}
                  class="h-200px w-200px text-center"
                  objectFit="contain"
                  src={decodeURIComponent(token.attrGet('src') || '')}
                >
                  {{
                    error: () => (
                      <div class="h-full w-full cursor-not-allowed select-none bg-FO-Container-Fill2 flex-c-center">
                        图片加载失败
                      </div>
                    ),
                  }}
                </NImage>
              </div>,
            );
            continue;
          }

          // inline 内部 children 递归渲染
          if (token.type === 'inline' && token.children) {
            pushElement(<>{renderTokens(token.children)}</>);
            continue;
          }

          // 处理角标链接
          if (token.type === 'link_open') {
            const href = token.attrGet('href') || '';
            // 构造当前 link 的属性
            const linkAttrs = {
              class: 'c-FO-Brand-Primary-Default FO-Font-R14',
              href,
              rel: 'noopener noreferrer',
              target: '_blank',
            };
            // 判断是否为搜索结果中的链接
            const isSearchLink = props.search.some((s) => s.url === href);
            // 推入栈，children 由后续 token 处理
            structStack.push({
              tag: isSearchLink ? 'popover-link' : 'a',
              attrs: linkAttrs,
              children: [],
            });
            continue;
          }
          if (token.type === 'link_close') {
            const node = structStack.pop();
            if (node) {
              if (node.tag === 'popover-link') {
              // 用 NPopover 包裹
                const linkVNode = h(
                  'a',
                  node.attrs,
                  node.children.length === 1 ? node.children[0] : <>{node.children}</>,
                );
                const searchLink = props.search?.find((s) => s.url === node.attrs.href) || '';
                const popoverVNode = (
                  <NPopover contentClass="w-360px" trigger="hover">
                    {{
                      trigger: () => linkVNode,
                      default: () => (searchLink ? <SearchItem info={searchLink} showArrow={false} /> : null),
                    }}
                  </NPopover>
                );
                pushElement(popoverVNode);
              } else {
              // 普通 a 标签
                const vNode = h(
                  node.tag,
                  node.attrs,
                  node.children.length === 1 ? node.children[0] : <>{node.children}</>,
                );
                pushElement(vNode);
              }
            }
            continue;
          }

          // open 标签：推入栈
          if (token.nesting === MarkdownNesting.Open) {
            structStack.push({
              tag: token.tag || 'div',
              attrs: {
                ...(token.attrs?.reduce((acc, [key, value]) => {
                  acc[key] = value;
                  return acc;
                }, {} as Record<string, string>) || {}),
                ...(token.tag === 'a'
                  ? { target: '_blank' }
                  : {}),
              },
              children: [],
            });
          } else if (token.nesting === MarkdownNesting.Close) {
            // close 标签：弹出并组合
            const node = structStack.pop();
            if (node) {
              const vNode = h(
                node.tag,
                node.attrs,
                (node.children.length === 1 ? node.children[0] : node.children) as JSX.Element,
              );
              pushElement(vNode);
            }
          } else if (token.nesting === MarkdownNesting.SelfClose) {
            // 普通或自闭合 token
            if (token.tag === 'math' && token.content) {
              pushElement(<span v-html={md.renderer.render([token], md.options, {})} />);
            } else {
              pushElement(
                h(
                  token.tag || 'text',
                  {
                    // 处理内置文本信息，这里主要为了复用markdown-it 的渲染器，处理例如latex的场景
                    innerHTML: token.content ? md.renderer.render([token], md.options, {}) : '',
                    attrs: token.attrs,
                  },
                  token.content,
                ),
              );
            }
          }
        }

        return result;
      };
      return <div class="md-block-chunk">{renderTokens(tokens)}</div>;
    }

    watch(() => props.content, (newContent, old) => {
      if (old === newContent || props.isUser) {
        return;
      }
      // 根据token分割内容
      const newBlocks = splitByBlockTokens(newContent);
      if (newBlocks.length === 0) {
        return;
      }
      // 遍历每个块，若未渲染，则渲染为VNode
      for (const block of newBlocks) {
        const existingBlock = contentBlocks.value.find((b) => block.startsWith(b));
        if (existingBlock && existingBlock !== block) {
          // 适配流式渲染逐步增加的场景，避免分片异常
          renderedMap.set(existingBlock, renderBlockToVNode(block));
        } else if (!renderedMap.has(block)) {
          // 如果没有渲染过，则渲染
          const rendered = renderBlockToVNode(block);
          renderedMap.set(block, rendered);
        }
      }
    }, {
      immediate: true,
    });

    const onHandleMouseenter = () => {
      if (props.isFinished) {
        isUserHover.value = true;
      } else {
        isUserHover.value = false;
      }
    };

    const onHandleMouseleave = () => {
      isUserHover.value = false;
    };

    const onHandleEdit = () => {
      if (props.isLoading) {
        message.error('当前正在生成回答，请稍后再试');
        return;
      }
      const el = chatBubbleRef.value as HTMLElement;
      editContent.value = props.content;
      isEditModel.value = true;
      props.onScrollTo(el.offsetTop);
    };

    const renderUserBubble = () => {
      return (
        <>
          <div class="chat-row-item w-full flex justify-end">
            {
              !isEditModel.value
                ? (
                  <div class="max-w-80% flex flex-col items-end justify-end gap-8px">
                    <div
                      class="chat-bubble pos-relative overflow-hidden rd-16px bg-FO-Brand-Tertiary-Active px-20px py-12px c-FO-Content-Text1"
                    >
                      <EllipsisContainer class="flex flex-col items-end" contentClass="whitespace-pre-wrap break-anywhere" lineClamp={5} text={props.content}>
                        {{
                          default: ({ expanded, toggle }: { expanded: boolean; toggle: () => void }) => (
                            <NButton
                              class="cursor-pointer bg-FO-Brand-Tertiary-Active"
                              iconPlacement="right"
                              onClick={toggle}
                              renderIcon={
                                () => (
                                  <NIcon size={12}>
                                    <ArrowForwardIosRound class={expanded ? 'rotate--90' : 'rotate-90'} />
                                  </NIcon>
                                )
                              }
                              size="small"
                              text
                              type="primary"
                            >
                              {expanded ? '收起' : '展开'}
                            </NButton>
                          ),
                        }}
                      </EllipsisContainer>
                    </div>
                    <div class={['w-full flex-c-start transition-all', isUserHover.value ? 'opacity-100' : 'opacity-0']}>
                      <>
                        <NTooltip>
                          {{
                            default: () => `编辑当前内容并重新发送`,
                            trigger: () => (
                              <NButton
                                class="px-8px"
                                onClick={onHandleEdit}
                                quaternary
                                size="small"
                              >
                                <NIcon class="c-FO-Content-Icon2" size={20}>
                                  <Edit />
                                </NIcon>
                              </NButton>
                            ),
                          }}
                        </NTooltip>
                        <NTooltip>
                          {{
                            default: () => `复制当前内容`,
                            trigger: () => (
                              <NButton
                                class="px-8px"
                                onClick={() => {
                                  const { copy } = useClipboard();
                                  copy(props.content);
                                  message.success(`已复制当前内容到剪切板${happy()}`);
                                }}
                                quaternary
                                size="small"
                              >
                                <NIcon class="c-FO-Content-Icon2" size={20}>
                                  <Copy />
                                </NIcon>
                              </NButton>
                            ),
                          }}
                        </NTooltip>
                      </>
                    </div>
                  </div>
                )
                : (
                  <div class="chat-bubble-edited w-full">
                    <ChatInput
                      isEdit
                      onCancel={() => {
                        editContent.value = '';
                        isEditModel.value = false;
                      }}
                      onSubmit={({ message }) => {
                        props.onSendMessage({
                          message,
                          editId: props.id,
                        });
                        isEditModel.value = false;
                      }}
                      v-model:content={editContent.value}
                    />
                  </div>
                )
            }

          </div>
        </>
      );
    };

    const renderBotBubble = () => {
      return (
        <>
          {
            !props.content && (
              <div class="c-forgeon-text3-color my-12px gap-12px flex-c-start">
                正在思考中
                <NIcon class="ml-12px">
                  <Loading3QuartersOutlined class="animate-spin" />
                </NIcon>
              </div>
            )
          }
          {
            Array.isArray(props.search) && props.search.length > 0 && (
              <div class="chat-row-item mb-8px inline-block">
                <div
                  class="chat-bubble w-[fit-content] cursor-pointer rd-12px bg-FO-Container-Fill2 px-20px py-12px c-FO-Content-Text1 flex-c-between"
                  onClick={() => {
                    props.onShowSearch(props.search);
                  }}
                >
                  已搜索到
                  {props.search.length}
                  条相关信息
                  <NIcon>
                    <ArrowForwardIosRound class="ml-8px" />
                  </NIcon>
                </div>
                <div class="mt-8px text-12px c-FO-Content-Text2">
                  以上是根据您的问题，系统自动搜索到的相关信息
                  <span class="c-FO-Content-Text3">，如有错误请忽略</span>
                </div>
              </div>
            )
          }
          <div class="chat-row-item mb-8px w-full flex flex justify-start overflow-hidden">
            {/* 正文 */}
            <div class="chat-bubble inline-block flex-grow-1 overflow-hidden">
              <div
                class={[styles.markdownContent, 'markdown-body overflow-hidden']}
                data-theme={currentTheme.value}
              >
                {contentBlocks.value.map((block) => renderedMap.get(block))}
              </div>
            </div>
          </div>
          {/* bot操作按钮 */}
          {
            !props.isUser && props.isFinished && (
              <div class={['flex items-center gap-12px', isUserHover.value ? 'opacity-100' : 'opacity-0']}>
                <NTooltip>
                  {{
                    default: () => `复制当前内容`,
                    trigger: () => (
                      <NButton
                        class="px-8px"
                        onClick={() => {
                          const { copy } = useClipboard();
                          copy(props.originalContent);
                          message.success(`已复制当前内容到剪切板${happy()}`);
                        }}
                        quaternary
                        size="small"
                      >
                        <NIcon class="c-FO-Content-Icon2" size={20}>
                          <Copy />
                        </NIcon>
                      </NButton>
                    ),
                  }}
                </NTooltip>
              </div>
            )
          }
        </>
      );
    };

    return () => (
      <div
        class={[styles.chatBubble, 'flex w-full flex-col mb-20px pos-relative last:pb-40px FO-Font-R14']}
        onMouseenter={onHandleMouseenter}
        onMouseleave={onHandleMouseleave}
        ref={chatBubbleRef}
      >
        {
          props.isUser ? renderUserBubble() : renderBotBubble()
        }
      </div>
    );
  },
});

export {
  ChatBubble,
};
