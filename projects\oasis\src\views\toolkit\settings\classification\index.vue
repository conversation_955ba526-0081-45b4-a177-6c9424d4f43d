<template>
  <div :class="prefixCls">
    <div class="text-lg mb-4 font-bold text-center">分类配置</div>
    <div
      :class="`${prefixCls}__list`"
      :style="{
        maxHeight: `calc(100vh - 152px - ${headerHeightRef}px)`,
        height: `calc(100vh - 152px - ${headerHeightRef}px)`,
      }"
    >
      <BasicTable @register="registerTable" class="h-auto!">
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'action'">
            <TableAction :actions="createActions(record)" />
          </template>
        </template>
      </BasicTable>
      <a-button
        block
        type="dashed"
        @click="handleCreate"
        ghost
        color="success"
        v-show="!isEditing"
        v-tippy="{ content: '新增一个工具分类', placement: 'bottom' }"
      >
        +
      </a-button>
    </div>
  </div>
</template>
<script lang="ts" setup>
  import { ref, unref } from 'vue';
  import { columns } from './toolType.data';
  import {
    addToolkitType,
    deleteToolkitType,
    editToolkitType,
    getToolkitTypeListByPage,
  } from '/@/api/page/system';
  import {
    ActionItem,
    BasicTable,
    EditRecordRow,
    FetchParams,
    TableAction,
    useTable,
  } from '/@/components/Table';
  import { useDesign } from '/@/hooks/web/useDesign';
  import { useLayoutHeight } from '/@/layouts/default/content/useContentViewHeight';

  import { CommonTypeListItem } from '/@/api/page/model/systemModel';
  import { useMessage } from '/@/hooks/web/useMessage';

  defineOptions({
    name: 'ProjectPermissionManagement',
  });

  const emit = defineEmits(['success', 'register']);

  const { prefixCls } = useDesign('toolkit-package-classification');
  const { headerHeightRef } = useLayoutHeight();
  const { createMessage } = useMessage();
  const isEditing = ref<boolean>(false);
  const [registerTable, { reload, getDataSource }] = useTable({
    title: '',
    api: () => getToolkitTypeListByPage({ page: 1, pageSize: 999 }),
    columns,
    formConfig: {
      labelWidth: 120,
    },
    useSearchForm: false,
    showTableSetting: false,
    bordered: true,
    showIndexColumn: false,
    canResize: false,
    pagination: false,
    actionColumn: {
      width: 80,
      title: '操作',
      dataIndex: 'action',
      fixed: 'right',
    },
  });

  function handleCreate() {
    const data = getDataSource();
    const addRow: CommonTypeListItem = {
      name: '',
      description: '',
      editable: true,
      isNew: true,
    };
    data.push(addRow);
    isEditing.value = true;
  }

  function handleEdit(record: Recordable) {
    record.onEdit?.(true);
    isEditing.value = true;
  }

  async function handleDelete(record: Recordable) {
    await deleteToolkitType(record.ID);
    await handleSuccess({ page: 1 });
    isEditing.value = false;
  }

  function handleCancel(record: EditRecordRow) {
    record.onEdit?.(false);
    if (record.isNew) {
      const data = getDataSource();
      const index = data.findIndex((item) => item.key === record.key);
      data.splice(index, 1);
    }
    isEditing.value = false;
  }

  async function handleSave(record: EditRecordRow) {
    if (!unref(record.editValueRefs)?.name) {
      createMessage.warn('请输入分类名称');
      return;
    }
    if (!record.ID) {
      await addToolkitType(unref(record.editValueRefs) as CommonTypeListItem);
    } else {
      await editToolkitType(unref(record.editValueRefs) as CommonTypeListItem, record.ID);
    }
    record.onEdit?.(false, true);
    await handleSuccess();
    isEditing.value = false;
  }

  async function handleSuccess(opt?: FetchParams) {
    await reload(opt);
    emit('success', getDataSource());
  }

  function createActions(record: EditRecordRow): ActionItem[] {
    if (!record.editable) {
      return [
        {
          icon: 'clarity:note-edit-line',
          onClick: handleEdit.bind(null, record),
          disabled: isEditing.value,
        },
        {
          icon: 'ant-design:delete-outlined',
          color: 'error',
          disabled: isEditing.value,
          popConfirm: {
            title: '是否删除分类',
            placement: 'left',
            confirm: handleDelete.bind(null, record),
          },
        },
      ];
    }
    return [
      {
        icon: 'charm:tick',
        color: 'success',
        onClick: handleSave.bind(null, record),
      },
      {
        icon: 'charm:cross',
        color: 'error',
        popConfirm: {
          title: '是否取消编辑',
          placement: 'left',
          confirm: handleCancel.bind(null, record),
        },
      },
    ];
  }
</script>
<style lang="less">
@prefix-cls: ~'hypergryph-toolkit-package-classification';
  .@{prefix-cls} {
    position: relative;
    padding: 16px;
    border-radius: 8px;
    background-color: @FO-Container-Fill1;
  }
</style>
