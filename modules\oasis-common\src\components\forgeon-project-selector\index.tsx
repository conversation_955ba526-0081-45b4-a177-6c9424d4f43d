import Icon from '@ant-design/icons-vue';
import { Dropdown, <PERSON>u, <PERSON><PERSON><PERSON><PERSON><PERSON>, MenuItem } from 'ant-design-vue';
import type { Trigger } from 'ant-design-vue/es/dropdown/props';

import { type PropType, computed, defineComponent } from 'vue';

import ProjectItemIcon from '../../assets/svg/component/project-selector-title-icon.svg';
import ArrowDown from '../../assets/svg/component/arrow-down.svg';
import ItemCheck from '../../assets/svg/component/item-check.svg';

import style from './style.module.less';
import type { MenuInfo } from 'ant-design-vue/es/menu/src/interface';

const ForgeonProjectSelector = defineComponent({
  props: {
    value: {
      type: Number as PropType<number | undefined>,
      required: true,
    },
    trigger: {
      type: Array as PropType<Trigger[]>,
      default: () => ['click'],
    },
    simple: {
      type: Boolean as PropType<boolean>,
      default: false,
    },
    showMenuTitle: {
      type: Boolean as PropType<boolean>,
      default: false,
    },
    options: {
      type: Array as PropType<Array<{ id: number; name: string }>>,
      default: () => [],
    },
    onSelect: {
      type: Function as PropType<(id: number | undefined) => void>,
      default: () => {},
    },
    containerClass: {
      type: String as PropType<string>,
      default: '',
    },
  },
  setup(props) {
    const showProjectID = computed(() => props.value);
    const showProjectName = computed(() =>
      (props.options.length > 0
        ? props.options?.find((e) => e.id === showProjectID.value)?.name || '请选择项目'
        : '暂无可选项目'));

    async function changeProject({ key }: MenuInfo) {
      if (!props.simple) {
        props.onSelect(key as number);
      }
    }

    return () => (
      <Dropdown
        disabled={props.options.length <= 0}
        placement="bottomRight"
        trigger={props.trigger}
      >
        {{
          default: () => (
            <div class={[style.forgeonProjectSelector]}>
              <div class={[style.displayContainer, props.containerClass]} onClick={(e) => e.preventDefault()}>
                <div class="forgeon-display-title truncate">
                  {showProjectName.value}
                </div>
                <Icon class="font-size-14px" component={<ArrowDown />} />
              </div>

            </div>
          ),
          overlay: () => (
            <Menu class={style.forgeonSelectMenu} onClick={changeProject}>
              {
                props.showMenuTitle
                && (
                  <>
                    <MenuItem class={style.menuTitle} disabled key="projectTitle">
                      <Icon component={<ProjectItemIcon />} />
                      项目
                    </MenuItem>
                    <MenuDivider />
                  </>
                )
              }
              { props.options.map((item) => (
                <MenuItem
                  class={style.menuItem}
                  disabled={item.id === showProjectID.value}
                  key={item.id}
                >
                  {item.name}
                  {item.id === showProjectID.value && (
                    <Icon class={style.checked} component={<ItemCheck />} />
                  )}
                </MenuItem>
              ))}
            </Menu>
          ),
        }}
      </Dropdown>
    );
  },
});

export { ForgeonProjectSelector };
