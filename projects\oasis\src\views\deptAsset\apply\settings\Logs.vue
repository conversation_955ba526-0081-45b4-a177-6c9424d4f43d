<template>
  <Tab :curTab="PlatformEnterPoint.DeviceManagementLogs">
    <div class="bg-FO-Container-Fill1 m-4 rounded-md">
      <BasicTable @register="registerTable">
        <template #toolbar>
          <span class="c-FO-Content-Text2 w-full flex">共 {{ totalNum }} 条使用记录</span>
        </template>
        <template #bodyCell="{ column, record }">
          <template v-if="column.dataIndex === 'device'">
            <div :class="{ 'c-red': record.device?.deleted }" class="flex items-center gap-2">
              <EllipsisText>
                {{ record.device?.deviceName }}
              </EllipsisText>
              <div v-if="record.device?.deleted">
                [已删除]
              </div>
            </div>
            <div class="text-secondary">
              {{ record.device?.assetNo }}
            </div>
          </template>
          <template v-else-if="column.dataIndex === 'applyLog'">
            <template v-if="record.operationType === DeviceOptLogOperationTypeEnum.DIRECT_BORROW">
              <div>
                借用人：{{ getUserById(record.applyLog.proposerID)?.displayName }}
              </div>
              <div>
                预计归还时间：{{ formatReturnTime(record.applyLog.returnTime) }}
              </div>
            </template>
            <template v-else-if="record.operationType === DeviceOptLogOperationTypeEnum.APPLY">
              <div>
                预计归还时间：{{ formatReturnTime(record.applyLog.returnTime) }}
              </div>
              <EllipsisText v-if="record.applyLog.reason && record.applyLog.reason !== '无'">
                申请原因：{{ record.applyLog.reason }}
              </EllipsisText>
            </template>
            <EllipsisText
              v-else-if="![DeviceOptLogOperationTypeEnum.CONFIRM_RECEIVE, DeviceOptLogOperationTypeEnum.APPLY_RETURN].includes(record.operationType) && record.applyLog.remark && record.applyLog.remark !== '无'"
            >
              备注：{{ record.applyLog.remark }}
            </EllipsisText>
            <template v-else-if="record.comment">
              <EllipsisText v-for="item in record.comment.split('\n')" :key="item" class="block">
                {{ item }}
              </EllipsisText>
            </template>
          </template>
          <template v-else-if="column.dataIndex === 'action'">
            <a-button type="link" :disabled="record.device?.deleted" @click="handleDetail(record)">
              查看
            </a-button>
          </template>
        </template>
        <template #emptyText>
          <div class="flex items-center justify-center">
            <AEmpty description="暂无记录" />
          </div>
        </template>
      </BasicTable>
    </div>
  </Tab>
</template>

<script lang="ts" setup>
import type { BasicColumn, FormSchema } from '/@/components/Table';
import { BasicTable, useTable } from '/@/components/Table';
import { getDeviceOptLogListByPage } from '/@/api/page/deptAsset';
import { type DeviceOptLogListItem, DeviceOptLogOperationTypeEnum } from '/@/api/page/model/deptAssetModel';
import dayjs from 'dayjs';
import { onMounted, ref, watchEffect } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { Empty as AEmpty } from 'ant-design-vue';
import { deviceOptLogOperationTypeOptions } from '../device.data';
import { useDeptAssetApply } from '../hook';
import { EllipsisText } from '/@/components/EllipsisText';
import Tab from '../components/Tab.vue';
import { PlatformEnterPoint } from '@hg-tech/oasis-common';

const route = useRoute();
const { resolve } = useRouter();
const { formatReturnTime, getUserById } = useDeptAssetApply();
const totalNum = ref(0);
const columns: BasicColumn[] = [
  {
    title: '用户',
    dataIndex: 'operatorID',
    format: (_, record: DeviceOptLogListItem) => {
      return getUserById(record.operatorID)?.displayName || '';
    },
    width: 150,
  },
  {
    title: '操作类型',
    dataIndex: 'operationType',
    width: 150,
    format: (operationType) => {
      return deviceOptLogOperationTypeOptions.find((item) => item.value === operationType as DeviceOptLogOperationTypeEnum)?.label || '';
    },
  },
  {
    title: '设备',
    dataIndex: 'device',
    align: 'left',
    width: 150,
  },
  {
    title: '说明',
    dataIndex: 'applyLog',
    align: 'left',
    width: 300,
  },
  {
    title: '时间',
    dataIndex: 'CreatedAt',
    format: (text) => {
      return dayjs(text as string).format('YYYY-MM-DD HH:mm:ss');
    },
    width: 160,
  },
];

const searchFormSchema: FormSchema[] = [
  {
    field: 'operateTimeRange',
    label: '日期',
    component: 'RangePicker',
    componentProps: {
      valueFormat: 'X',
      placeholder: ['开始时间', '结束时间'],
      presets: [
        { label: '今天', value: [dayjs(), dayjs()] },
        { label: '最近一周', value: [dayjs().subtract(6, 'day'), dayjs()] },
        { label: '当前月', value: [dayjs().startOf('month'), dayjs()] },
      ],
      class: '!max-w-260px',
    },
  },
  {
    field: 'operatorIDs',
    label: '用户',
    component: 'UserSelect',
    componentProps: {
      isMultiple: true,
      maxTagCount: 3,
      class: '!max-w-260px',
    },
  },
  {
    field: 'operationTypes',
    label: '操作类型',
    component: 'Select',
    componentProps: {
      options: deviceOptLogOperationTypeOptions,
      placeholder: '请选择操作类型',
      mode: 'multiple',
      optionFilterProp: 'label',
      maxTagCount: 3,
      showArrow: true,
      class: '!max-w-260px',
    },
  },
  {
    field: 'deviceKeyword',
    label: '设备',
    component: 'Input',
    componentProps: {
      placeholder: '请输入设备名称或资产编号',
    },
  },
];

const [registerTable, { getRawDataSource }] = useTable({
  api: (p) => {
    const { operateTimeRange, ...rest } = p;
    const formattedOperateTimeRange = operateTimeRange?.length > 0 ? [dayjs.unix(operateTimeRange[0]).startOf('day').unix(), dayjs.unix(operateTimeRange[1]).endOf('day').unix()] : undefined;
    return getDeviceOptLogListByPage({ ...rest, deviceIDs: route.query.editId, operateTimeRange: formattedOperateTimeRange });
  },
  columns,
  inset: true,
  resizeHeightOffset: 36,
  useSearchForm: true,
  formConfig: {
    labelWidth: 120,
    showActionButtonGroup: false,
    layout: 'vertical',
    schemas: searchFormSchema,
    baseColProps: {
      span: 5,
      style: {
        marginRight: '10px',
      },
    },
  },
  showIndexColumn: false,
  bordered: true,
  pagination: {
    pageSize: 10,
    showTotal: () => '',
  },
  actionColumn: {
    width: 130,
    title: '操作',
    dataIndex: 'action',
    fixed: 'right',
  },
});

onMounted(() => {
  watchEffect(() => {
    const { total } = getRawDataSource();
    totalNum.value = total || 0;
  });
});
function handleDetail(record: DeviceOptLogListItem) {
  const { fullPath } = resolve({ name: 'DeptAssetApplyManagement', query: { editId: record.deviceID } });
  window.open(fullPath, '_blank');
}
</script>

<style lang="less" scoped>
::v-deep(.ant-form) {
  padding: 0 !important;
  margin-bottom: 0 !important;
}
</style>
