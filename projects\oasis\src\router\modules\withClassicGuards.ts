import type { Router } from 'vue-router';
import { useAppStore, useAppStoreWithOut } from '/@/store/modules/app';
import { useUserStore, useUserStoreWithOut } from '/@/store/modules/user';
import { useTransitionSetting } from '/@/hooks/setting/useTransitionSetting';
import { AxiosCanceler } from '/@/utils/http/axios/axiosCancel';
import { Modal, notification } from 'ant-design-vue';
import { warn } from '/@/utils/log';
import nProgress from 'nprogress';
import projectSetting from '/@/settings/projectSetting';
import { useMultipleTabStore } from '../../store/modules/multipleTab.ts';
import { PlatformEnterPoint } from '@hg-tech/oasis-common';

/**
 * Hooks for handling page state
 */
function createPageGuard(router: Router) {
  const loadedPageMap = new Map<string, boolean>();

  router.beforeEach(async (to) => {
    // The page has already been loaded, it will be faster to open it again, you don’t need to do loading and other processing
    to.meta.loaded = !!loadedPageMap.get(to.path);

    return true;
  });

  router.afterEach((to) => {
    loadedPageMap.set(to.path, true);
  });
}

// Used to handle page loading status
function createPageLoadingGuard(router: Router) {
  const userStore = useUserStoreWithOut();
  const appStore = useAppStoreWithOut();
  const { getOpenPageLoading } = useTransitionSetting();
  if (getOpenPageLoading) {
    router.beforeEach(async (to) => {
      if (!userStore.getToken && !userStore.getAccessToken) {
        return true;
      }
      if (to.meta.loaded) {
        return true;
      }

      await appStore.setPageLoadingAction(true);
      return true;
    });
    router.afterEach(async () => {
      /*
       * TODO Looking for a better way
       * The timer simulates the loading time to prevent flashing too fast,
       */
      setTimeout(() => {
        appStore.setPageLoading(false);
      }, 220);
      return true;
    });
  }
}

/**
 * The interface used to close the current page to complete the request when the route is switched
 * @param router
 */
function createHttpGuard(router: Router) {
  const { removeAllHttpPending } = projectSetting;
  let axiosCanceler: Nullable<AxiosCanceler>;
  if (removeAllHttpPending) {
    axiosCanceler = new AxiosCanceler();
  }
  router.beforeEach(async () => {
    // Switching the route will delete the previous request
    axiosCanceler?.removeAllPending();
    return true;
  });
}

/**
 * Used to close the message instance when the route is switched
 */
function createMessageGuard(router: Router) {
  const { closeMessageOnSwitch } = projectSetting;

  router.beforeEach(async () => {
    try {
      if (closeMessageOnSwitch) {
        Modal.destroyAll();
        notification.destroy();
      }
    } catch (error) {
      warn(`message guard error:${error}`);
    }
    return true;
  });
}

function createProgressGuard(router: Router) {
  const { getOpenNProgress } = useTransitionSetting();
  if (getOpenNProgress) {
    router.beforeEach(async (to) => {
      if (to.meta.loaded) {
        return true;
      }
      nProgress.start();
      return true;
    });

    router.afterEach(async () => {
      nProgress.done();
      return true;
    });
  }
}

function createStateGuard(router: Router) {
  router.afterEach((to) => {
    // Just enter the login page and clear the authentication information
    if (to.name === PlatformEnterPoint.Login) {
      const tabStore = useMultipleTabStore();
      const userStore = useUserStore();
      const appStore = useAppStore();
      appStore.resetAllState();
      tabStore.resetState();
      userStore.resetState();
    }
  });
}

/**
 * 旧路由守卫
 * Don't change the order of creation
 */
export function withClassicGuards(router: Router) {
  createPageGuard(router);
  createPageLoadingGuard(router);
  createHttpGuard(router);
  createMessageGuard(router);
  createProgressGuard(router);
  createStateGuard(router);
}
