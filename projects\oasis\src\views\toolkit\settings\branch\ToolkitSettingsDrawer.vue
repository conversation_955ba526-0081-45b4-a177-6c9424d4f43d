<template>
  <BasicDrawer
    v-bind="$attrs"
    showFooter
    :title="getTitle"
    width="760px"
    @register="registerDrawer"
    @ok="handleSubmit"
  >
    <BasicForm @register="registerForm">
      <template #toolType="{ model, field }">
        <ARadioGroup
          v-model:value="model[field]"
          showSearch
          :options="toolkitTypeList"
          placeholder="请选择工具类型"
          buttonStyle="solid"
          optionType="button"
        />
      </template>
      <template #projectID="{ model, field }">
        <a-select
          v-model:value="model[field]"
          showSearch
          optionFilterProp="name"
          :options="projectList"
          placeholder="请选择适用项目"
          :fieldNames="commonFieldNames"
          allowClear
        />
      </template>
      <template #mdContent="{ model, field }">
        <MarkDown v-model:value="model[field]" placeholder="请输入README" />
      </template>
    </BasicForm>
  </BasicDrawer>
</template>

<script lang="ts" setup>
import { RadioGroup as ARadioGroup } from 'ant-design-vue';
import type { RadioGroupProps } from 'ant-design-vue';
import { cloneDeep, omit } from 'lodash-es';
import { computed, ref, unref } from 'vue';
import { commonFieldNames, formSchema } from '../toolkitSettings.data';
import type { ProjectListItem } from '/@/api/page/model/systemModel';
import { addToolkit, editToolkit } from '/@/api/page/system';
import { BasicDrawer, useDrawerInner } from '/@/components/Drawer';
import { BasicForm, useForm } from '/@/components/Form/index';
import { MarkDown } from '/@/components/Markdown';

defineOptions({
  name: 'ToolkitSettingsDrawer',
});
const emit = defineEmits(['success', 'register']);

const isUpdate = ref(true);
const editId = ref();
const toolkitTypeList = ref<RadioGroupProps['options']>([]);
const projectList = ref<ProjectListItem[]>([]);
const recordData = ref();

const [registerForm, { resetFields, setFieldsValue, validate, clearValidate }] = useForm({
  labelWidth: 100,
  schemas: formSchema,
  showActionButtonGroup: false,
  baseColProps: { span: 22 },
});

const [registerDrawer, { setDrawerProps, closeDrawer }] = useDrawerInner(async (data) => {
  await resetFields();
  isUpdate.value = !!data?.isUpdate;
  editId.value = data?.record?.ID;
  recordData.value = data?.record;
  projectList.value = data?.projectList;
  toolkitTypeList.value = [];
  if (data?.toolkitTypeList) {
    cloneDeep(data.toolkitTypeList).forEach((e) => {
      toolkitTypeList.value?.push({
        label: e.name,
        value: e.ID,
      });
    });
  }
  if (unref(isUpdate)) {
    await setFieldsValue({
      ...data.record,
      projectID: data.record?.projectID ? data.record.projectID : undefined,
      adminIds: data.record?.adminIds ? data.record.adminIds : [],
    });
    await clearValidate();
  }
  setDrawerProps({ confirmLoading: false });
});

const getTitle = computed(() => (!unref(isUpdate) ? '新增工具' : '编辑工具'));

async function handleSubmit() {
  try {
    const values = await validate();
    setDrawerProps({ confirmLoading: true });
    if (!unref(isUpdate)) {
      await addToolkit(values);
      emit('success', 'add');
    } else if (unref(editId)) {
      const submitData = Object.assign(
        {},
        omit(unref(recordData), [
          'key',
          'CreatedAt',
          'UpdatedAt',
          'ID',
          'workType',
          'project',
          'latestVersion',
          'versions',
        ]),
        values,
      );
      await editToolkit(submitData, unref(editId));
      emit('success', 'edit');
    }
    closeDrawer();
  } finally {
    setDrawerProps({ confirmLoading: false });
  }
}
</script>
