<template>
  <BasicTree
    ref="treeRef"
    :key="treeKey"
    :class="prefixCls"
    :treeData="treeData"
    :loadData="getNodeData"
    :defaultExpandLevel="1"
    :selectable="false"
    :fieldNames="defaultFieldNames"
    :disabled="disabled"
    v-bind="$attrs"
  >
    <template #title="item">
      <APopover
        v-model:open="item.popVisible"
        trigger="click"
        :overlayClassName="`${prefixCls}__pop ${disabled ? '!hidden' : ''}`"
        :mouseLeaveDelay="0"
        @openChange="(v) => handlePopVisibleChange(v, item)"
      >
        <div class="relative mr-1">
          <Icon
            class="border-1 border-gray-300 rounded-sm dark:border-gray-700"
            :size="14"
            v-bind="getCheckAttrs(item.permit || item.parentPermit)"
            :class="disabled ? 'border-gray-100 bg-#d9d9d9 dark:bg-gray-700' : ''"
          />
          <div
            v-if="item.halfChecked && getChildCheckList(item)?.length"
            :class="`${prefixCls}__sup-mark`"
            :permit="item.permit || item.parentPermit"
          />
        </div>
        <template #content>
          <div>
            <div class="mb-2 ml-1">
              设置为
            </div>
            <div class="flex justify-around">
              <div
                v-for="check in showCheckList(item.path)"
                :key="check.permit"
                class="w-50px flex flex-col cursor-pointer items-center rounded p-1 hover:bg-gray-200 dark:hover:bg-gray-800"
                @click="handleSetPermit(item, check.permit)"
              >
                <Icon :size="24" :class="check.class" :icon="check.icon" />
                <span :class="`${check.class} text-xs`">
                  {{ check.text }}
                </span>
              </div>
            </div>
          </div>
        </template>
      </APopover>

      <div>
        <Icon
          :icon="getTypeIcon(item.type)"
          :color="getTypeIcon(item.type, true, item.permit || item.parentPermit)"
          class="mr-1"
        />
        <span
          :class="{
            'c-FO-Content-Text1': (item.permit || item.parentPermit) === 1,
            'c-FO-Content-Text2': (item.permit || item.parentPermit) === 2,
          }"
        >
          {{ item.showName }}
        </span>
      </div>

      <APopover
        v-if="item.halfChecked && getChildCheckList(item)?.length"
        v-model:open="item.childPopVisible"
        placement="bottom"
        trigger="click"
        :overlayClassName="`${prefixCls}__pop ${disabled ? '!hidden' : ''}`"
        :mouseLeaveDelay="0"
        @openChange="(v) => handlePopVisibleChange(v, item, true)"
      >
        <div
          class="ml-1 flex items-center justify-center border-1 border-gray-300 rounded-lg px-2px dark:border-gray-700"
          :class="disabled ? 'border-gray-100 bg-#d9d9d9  dark:bg-gray-700' : ''"
        >
          <Icon
            v-for="check in getChildCheckList(item)"
            :key="check.permit"
            :class="check.class"
            :icon="check.icon"
            :size="14"
          />
        </div>
        <template #content>
          <div>
            <div class="ml-1">
              去除所包含的
            </div>
            <div class="flex justify-around">
              <div
                v-for="check in getChildCheckList(item)"
                :key="check.permit"
                class="w-50px flex flex-col cursor-pointer items-center rounded p-1 hover:bg-gray-200 dark:hover:bg-gray-800"
                @click="removeChildPermit(item, check.permit)"
              >
                <Icon width="24" :class="check.class" :icon="check.icon" />
                <span :class="`${check.class} text-xs`">
                  {{ check.text }}
                </span>
              </div>
            </div>
          </div>
        </template>
      </APopover>
      <div class="ml-4px">
        <div v-if="item.importType === importTypeMenu.includeImport" class="FO-Font-R12 b-1 b-FO-Content-Text2 b-rd-4px p-2px c-FO-Content-Text2">
          包含import+
        </div>
        <div v-else-if="item.importType === importTypeMenu.fromImport" class="FO-Font-R12 b-1 b-rd-4px bg-FO-Content-Text2 p-2px c-FO-Container-Fill1">
          来自import+
        </div>
        <div v-else-if="item.importType === importTypeMenu.point" class="h-8px w-8px b-rd-4px bg-FO-Content-Text2" />
      </div>
    </template>
  </BasicTree>
</template>

<script lang="ts" setup name="P4StateTree">
import { Popover as APopover } from 'ant-design-vue';
import type { TreeDataItem } from 'ant-design-vue/es/tree';
import { cloneDeep } from 'lodash-es';
import { type PropType, nextTick, ref, watch } from 'vue';
import { type CheckListType, checkList, defaultFieldNames, getTypeIcon, importTypeMenu } from './p4StateTree.data';
import type { P4PermissionListItem } from '/@/api/page/model/p4Model';
import { getP4TreeCurNodeList } from '/@/api/page/p4';
import { Icon } from '/@/components/Icon';
import { type TreeActionType, BasicTree } from '/@/components/Tree/index';
import { useDesign } from '/@/hooks/web/useDesign';
import { useP4StoreWithOut } from '/@/store/modules/p4';
import { useUserStore } from '/@/store/modules/user';
import { findNode } from '/@/utils/helper/treeHelper';
import { isNullOrUnDef } from '/@/utils/is';
import { buildUUID } from '/@/utils/uuid';

const props = defineProps({
  // 初始权限列表
  permissionList: {
    type: Array as PropType<P4PermissionListItem[]>,
    default: () => [],
  },
  disabled: {
    type: Boolean,
    default: false,
  },
  onlyDir: {
    type: Boolean,
    default: false,
  },
  customCheckList: {
    type: Array as PropType<CheckListType[]>,
    default: () => checkList,
  },
});

const emit = defineEmits(['changeNodeCount']);

const { prefixCls } = useDesign('p4-state-tree');
// 树ref
const treeRef = ref<Nullable<TreeActionType>>(null);
// 树数据
const treeData = ref<TreeDataItem[]>([]);
const treeKey = ref<string>(buildUUID());

const userStore = useUserStore();
const p4Store = useP4StoreWithOut();
const originPermissionList = ref<P4PermissionListItem[]>([]);
const newPermissionList = ref<P4PermissionListItem[]>([]);
const loadedNodeList = ref<string[]>([]);
const changeNodeCount = ref(0);

watch(
  () => props.permissionList,
  (val) => {
    if (p4Store.getCurStream && val) {
      loadedNodeList.value = [];
      treeKey.value = buildUUID();
      const path = `${p4Store.getCurStream.path}/`;
      originPermissionList.value = val || [];
      newPermissionList.value = cloneDeep(val || []);
      const findRootPermission = originPermissionList.value.find((e) => e.path === path);
      if (!findRootPermission) {
        newPermissionList.value.push({
          path,
          permit: 2,
        });
      }
      treeData.value = [
        {
          path,
          showName: p4Store.getCurStream.path?.split('/')?.pop(),
          permit: findRootPermission?.permit || 2,
          type: 1,
          isLeaf: false,
          childTypeList: [0, 0, 0],
          halfChecked: false,
          isRoot: true,
        },
      ] as unknown as TreeDataItem[];
      nextTick(() => {
        setNodePermission(treeData.value[0]);
      });
    }
  },
  {
    immediate: true,
  },
);

// 设置节点权限
function setNodePermission(node: P4PermissionListItem) {
  node.hasChange = false;
  treeRef.value?.updateNodeByKey(node.path!, node);
  originPermissionList.value.forEach((per) => {
    if (per.path === node.path) {
      node.permit = per.permit;
      treeRef.value?.updateNodeByKey(node.path!, node);
    } else if (node.path!.endsWith('/') && per.path!.startsWith(node.path!)) {
      node.halfChecked = true;
      if (!node.childTypeList) {
        node.childTypeList = [0, 0, 0];
      }
      node.childTypeList[per.permit!]++;

      treeRef.value?.updateNodeByKey(node.path!, node);
      if (node.path) {
        getNodeData(node);
      }
    }
  });
  if (node.isRoot && !originPermissionList.value.length) {
    getNodeData(node);
  }
}

// 获取path中nodePath的下一层路径
function getNextLevelPaths(path: string, nodePath: string) {
  if (path.startsWith(nodePath)) {
    const nextSlashIndex = path.indexOf('/', nodePath.length);
    let nextLevelPath: string;
    if (nextSlashIndex !== -1) {
      nextLevelPath = path.slice(0, nextSlashIndex + 1);
    } else {
      nextLevelPath = path;
    }
    return nextLevelPath;
  }
}

async function getNodeData(treeNode: P4PermissionListItem) {
  if (
    !userStore.getProjectId
    || !p4Store.getCurStream
    || loadedNodeList.value.includes(treeNode.path!)
  ) {
    return;
  }
  loadedNodeList.value.push(treeNode.path!);
  const { items } = await getP4TreeCurNodeList(userStore.getProjectId, p4Store.getCurStream.ID!, {
    currentPath: treeNode.path,
  });

  const newItems = props.onlyDir ? items?.filter((e) => e.kind === 'dir') : items;
  // 判断originPermissionList里是否有当前节点的子节点, 且不在items里, 即已删除的节点
  const deletedItemList = newPermissionList.value.filter(
    (e) =>
      e.path !== treeNode.path
      && e.path?.startsWith(treeNode.path!)
      // e.path中与items同层级的路径不在items里
      && !newItems?.find((i) => i.path === getNextLevelPaths(e.path!, treeNode.path!)),
  );
  deletedItemList.forEach((e) => {
    setParentChildCheckList(treeNode.path!, 0, e.permit!);
  });
  // 去掉权限列表里的已删除节点
  newPermissionList.value = newPermissionList.value.filter(
    (e) => deletedItemList.findIndex((i) => i.path === e.path) === -1,
  );
  treeRef.value?.updateNodeByKey(treeNode.path!, {
    children: newItems || undefined,
    isLeaf: !newItems?.length,
  });
  newItems?.forEach((e) => {
    setNodePermission({
      path: e.path,
      showName: e.name,
      permit: e.permit || 0,
      type: e.kind === 'file' ? 2 : 1,
      isLeaf: e.kind === 'file',
      childTypeList: [0, 0, 0],
      parentPermit: treeNode.permit || treeNode.parentPermit,
      parentPath: treeNode.path,
      halfChecked: e.halfChecked,
    });
  });
}

// 根据权限获取勾选图标
function getCheckAttrs(permit?: number) {
  return props.customCheckList.find((e) => e.permit === permit) || props.customCheckList[1];
}

function showCheckList(path: string) {
  if (path !== `${p4Store.getCurStream?.path}/`) {
    return props.customCheckList;
  } else {
    return props.customCheckList.filter((e) => e.permit !== 0);
  }
}

// 获取子节点权限勾选状态列表
function getChildCheckList(node: P4PermissionListItem) {
  return props.customCheckList.filter(
    (e) =>
      e.permit
      && node.childTypeList?.[e.permit]
      && e.permit !== (node.permit || node.parentPermit),
  );
}

// 清除子节点上的父节点权限状态
function clearChildParentPermit(node: P4PermissionListItem) {
  const parentNode = findNode(treeData.value, (n) => n.path === node.parentPath);
  node.parentPermit = parentNode?.permit || parentNode?.parentPermit;
  node.parentPath = parentNode?.path;
  treeRef.value?.updateNodeByKey(node.path!, {
    parentPermit: node.parentPermit,
    parentPath: node.parentPath,
  });
  node.children?.forEach((e) => {
    clearChildParentPermit(e);
  });
}

// 移除子节点权限
function removeChildPermit(node: P4PermissionListItem, permit: number, isChild = false) {
  node.children?.forEach((e) => {
    if (e.permit === permit) {
      e.permit = 0;
      e.hasChange = judgeNodePermissionChange(e.path!, 0);
      setChangeNodeCount(e.hasChange);
      treeRef.value?.updateNodeByKey(e.path!, {
        permit: e.permit,
        hasChange: e.hasChange,
      });
      editNewPermissionList(e.path!, 0, false);
      setParentChildCheckList(e.parentPath!, 0, permit);
      clearChildParentPermit(e);
    }
    removeChildPermit(e, permit, true);
  });
  if (!isChild) {
    treeRef.value?.updateNodeByKey(node.path!, {
      childPopVisible: false,
    });
  }
}

/**
 * 根据父节点路径 设置父节点的子节点权限勾选状态
 * @param parentPath 父节点路径
 * @param permit 当前节点 新权限
 * @param oldPermit 当前节点 旧权限
 */
function setParentChildCheckList(parentPath: string, permit: number, oldPermit: number) {
  if (parentPath) {
    const parentNode = findNode(treeData.value, (n) => n.path === parentPath);
    if (!parentNode.childTypeList) {
      parentNode.childTypeList = [0, 0, 0];
    }
    if (!isNullOrUnDef(oldPermit)) {
      parentNode.childTypeList[oldPermit]--;
      parentNode.childTypeList[oldPermit] = Math.max(0, parentNode.childTypeList[oldPermit]);
    }
    parentNode.childTypeList[permit]++;
    const { childTypeList } = parentNode;
    parentNode.halfChecked = childTypeList[1] + childTypeList[2] > 0;
    treeRef.value?.updateNodeByKey(parentPath, {
      childTypeList: parentNode.childTypeList,
      halfChecked: parentNode.halfChecked,
    });

    setParentChildCheckList(parentNode.parentPath, permit, oldPermit);
  }
}

// 处理新权限列表
function editNewPermissionList(path: string, permit: number, isAdd = true) {
  const index = newPermissionList.value.findIndex((e) => e.path === path);
  if (index > -1) {
    if (isAdd) {
      newPermissionList.value[index].permit = permit;
    } else {
      newPermissionList.value.splice(index, 1);
    }
  } else {
    if (!isAdd) {
      return;
    }
    newPermissionList.value.push({
      path,
      permit,
    });
  }
}

// 判断节点权限是否改变
function judgeNodePermissionChange(path: string, permit: number) {
  const index = originPermissionList.value.findIndex((e) => e.path === path);
  if (index > -1) {
    return originPermissionList.value[index].permit !== permit;
  } else {
    return !!permit;
  }
}

function setChangeNodeCount(hasChange: boolean) {
  if (hasChange) {
    changeNodeCount.value++;
  } else {
    if (changeNodeCount.value > 0) {
      changeNodeCount.value--;
    }
  }
  emit('changeNodeCount', changeNodeCount.value);
}

// 设置节点权限
function setNodePermit(node: P4PermissionListItem, permit: number, isChild = false, onlyCurNode = false) {
  if (!isChild) {
    setParentChildCheckList(node.parentPath!, permit, node.permit!);
    node.permit = permit;
    editNewPermissionList(node.path!, permit);
    node.hasChange = judgeNodePermissionChange(node.path!, permit);
    setChangeNodeCount(node.hasChange);
  } else {
    const parentNode = findNode(treeData.value, (n) => n.path === node.parentPath);
    node.parentPermit = parentNode?.permit || parentNode?.parentPermit;
    node.parentPath = parentNode?.path;
  }
  treeRef.value?.updateNodeByKey(node.path!, {
    permit: node.permit,
    hasChange: node.hasChange,
    parentPermit: node.parentPermit,
    parentPath: node.parentPath,
  });
  if (onlyCurNode) {
    return;
  }
  node.children?.forEach((e) => {
    setNodePermit(e, permit, true);
  });
}

// 设置权限
function handleSetPermit(node: P4PermissionListItem, permit: number) {
  treeRef.value?.updateNodeByKey(node.path!, {
    popVisible: false,
  });
  if ((node.permit || node.parentPermit) === permit) {
    return;
  }
  setNodePermit(node, permit);
  removeChildPermit(node, permit);
}

// 过滤权限列表中权限相同的子节点, 以及继承的节点
function getOptimalList(): P4PermissionListItem[] {
  const temp = cloneDeep(newPermissionList.value).sort((a, b) => a.path!.length - b.path!.length);
  temp.forEach((e) => {
    // 如果和直接父层一样,则设为0
    const parentNode = findNode(
      treeData.value,
      (n) =>
        n.path
        === `${e.path
          ?.split('/')
          .slice(0, e.path!.endsWith('/') ? -2 : -1)
          .join('/')
        }/`,
    );
    if ((parentNode?.permit || parentNode?.parentPermit) === e.permit) {
      e.permit = 0;
    }
  });
  return temp
    .filter((e) => e.permit !== 0)
    .map((e) => ({
      path: e.path,
      permit: e.permit,
    }));
}

// 处理弹出框显示隐藏, 需要更新节点才能生效
function handlePopVisibleChange(visible: boolean, node: P4PermissionListItem, isChild = false) {
  treeRef.value?.updateNodeByKey(
    node.path!,
    isChild
      ? {
        childPopVisible: visible,
      }
      : {
        popVisible: visible,
      },
  );
}

function resetAll() {
  setNodePermit(treeData.value[0], 2, false);
  removeChildPermit(treeData.value[0], 1);
  removeChildPermit(treeData.value[0], 2);
}

// 抛出ref, data 和 获取变化列表的方法
defineExpose({
  treeRef,
  treeData,
  getOptimalList,
  resetAll,
});
</script>

<style lang="less">
@prefix-cls: ~'hypergryph-p4-state-tree';
.@{prefix-cls} {
  .ant-tree-node-content-wrapper {
    background: none !important;

    &:hover {
      background-color: #f5f6f5 !important;
    }
  }

  // 右下角小三角形角标
  &__sup-mark {
    position: absolute;
    right: 0;
    bottom: 5px;
    width: 0;
    height: 0;
    border-width: 0 0 6px 6px;
    border-style: solid;
    border-radius: 0 0 2px;
    border-color: transparent transparent @FO-Brand-Primary-Default;

    &[permit='1'] {
      border-color: transparent transparent @FO-Functional-Error1-Default;
    }
  }
}

html[data-theme='dark'] .@{prefix-cls} {
  .ant-tree-node-content-wrapper {
    &:hover {
      background-color: #272727 !important;
    }
  }
}
</style>
checkListType,CheckListType,CheckListType,
