<template>
  <Drawer
    :open="show"
    width="900px"
    :closable="false"
    placement="right"
    @afterOpenChange="v => !v && modalDestroy()"
    @close="() => modalConfirm()"
  >
    <div v-if="gridOptions.data?.length">
      <BasicVxeTable :options="gridOptions" />
      <div class="flex justify-end p-4">
        <div class="w-100% flex items-center justify-between">
          <div>
            共计
            {{ pagerConfig.total }}
            条数据
          </div>
          <Pagination
            v-model:current="pagerConfig.currentPage"
            v-model:pageSize="pagerConfig.pageSize"
            :total="pagerConfig.total"
            showSizeChanger
            :pageSizeOptions="['10', '20', '50', '100']"
            @change="handlePageData"
          />
        </div>
      </div>
    </div>

    <div v-else class="h-full flex items-center justify-center">
      <Empty :image="emptyImg" description="暂无数据" />
    </div>

    <template #title>
      <div class="flex items-center justify-between">
        <div>{{ props.type === UseHistoryTypeEnum.apply ? '借用历史' : '使用历史' }}</div>
        <Icon :icon="CloseIcon" class="cursor-pointer" @click="close" />
      </div>
    </template>
    <template #footer>
      <div class="flex justify-end">
        <BasicButton @click="close">
          关闭
        </BasicButton>
      </div>
    </template>
  </Drawer>
</template>

<script lang="tsx" setup>
import type { ModalBaseProps } from '@hg-tech/utils-vue';
import { UseHistoryTypeEnum } from '../uesHistory.data';
import { Drawer, Empty, Pagination } from 'ant-design-vue';
import { onMounted, reactive } from 'vue';
import type { VxeGridProps } from 'vxe-table';
import { getUsageLogList } from '/@/api/page/deptAsset';
import { BasicButton } from '/@/components/Button';
import dayjs from 'dayjs';
import Icon from '/@/components/Icon';
import CloseIcon from '@iconify-icons/ant-design/close';
import { useRouter } from 'vue-router';
import type { DeviceListItem, UsageLogListItem } from '/@/api/page/model/deptAssetModel';
import { BasicVxeTable, PlatformEnterPoint } from '@hg-tech/oasis-common';
import { DeviceCategoryTypeEnum } from '/@/api/page/model/deptAssetModel';

const props = defineProps<ModalBaseProps & {
  type: UseHistoryTypeEnum;
}>();
interface RowVO {
  returnTime?: string;
  deviceName?: string;
  assetNo?: string;
  usedTime?: string;
  device?: DeviceListItem;
  UpdatedAt?: string;
}
const { resolve } = useRouter();
const emptyImg = Empty.PRESENTED_IMAGE_SIMPLE;
const pagerConfig = reactive({
  total: 0,
  currentPage: 1,
  pageSize: 20,
});
const gridOptions = reactive<VxeGridProps<RowVO>>({
  showOverflow: true,
  maxHeight: '100%',
  border: 'none',
  columns: [
    { field: 'point', title: '', width: '30px', align: 'center', slots: { default() {
      return <div class="h-6px w-6px rd-half bg-FO-Container-Fill5" />;
    } } },
    { field: 'returnTime', title: '归还时间', width: '170px', align: 'left', slots: { default({ row }) {
      return dayjs(row.UpdatedAt).format('YYYY-MM-DD HH:mm:ss');
    } } },
    { field: 'deviceName', title: '设备名称', align: 'left', slots: { default({ row }) {
      return (
        <span>
          <span>{ row.device?.deviceName }</span>

          {row.device?.deleted ? <span>[已删除]</span> : null}
        </span>
      );
    } } },
    { field: 'assetNo', title: '资产编号', align: 'left', slots: { default({ row }) {
      return row.device?.assetNo || '';
    } } },
    { field: 'usedTime', title: props.type === UseHistoryTypeEnum.apply ? '借用时长' : '使用时长', width: '80px', align: 'center', slots: { default({ row }) {
      return formatUsedTime(row.usedTime);
    } } },
    { field: 'active', title: '操作', width: '110px', align: 'center', slots: { default({ row }) {
      return (
        <BasicButton
          className="p-0"
          disabled={row.device?.deleted}
          onClick={() => handleDetail(row)}
          type="link"
        >
          设备详情
        </BasicButton>
      );
    } } },
  ],
  cellClassName({ row, column }) {
    if (column.field === 'deviceName') {
      return row.device?.deleted ? 'c-red' : '';
    }
  },
  data: [],
  columnConfig: {
    resizable: false,
  },
});
async function handlePageData() {
  try {
    gridOptions.loading = true;
    const res = await getUsageLogList({
      userLog: true,
      deviceType: props.type === UseHistoryTypeEnum.apply ? DeviceCategoryTypeEnum.Common : DeviceCategoryTypeEnum.Cloud,
      page: pagerConfig.currentPage,
      pageSize: pagerConfig.pageSize,
    });
    pagerConfig.total = res.total;
    gridOptions.data = res.list;
  } finally {
    gridOptions.loading = false;
  }
}

function close() {
  return props.modalConfirm();
}

function formatUsedTime(time?: string) {
  if (!time) {
    return '0秒';
  }
  if (time.includes('h')) {
    const hour = Number(time.split('h')[0]);
    return hour > 24 ? `${Math.floor(hour / 24 + 1)}天` : `${hour}小时`;
  } else if (time.includes('m')) {
    const min = Number(time.split('m')[0]);
    return `${min}分钟`;
  } else {
    const sec = Number(time.split('s')[0]);
    return `${sec}秒`;
  }
}
function handleDetail(row: UsageLogListItem) {
  const { fullPath } = resolve({ name: props.type === UseHistoryTypeEnum.apply ? PlatformEnterPoint.DeptAssetApplyManagement : PlatformEnterPoint.CloudDevice, query: { editId: row.device?.id } });
  window.open(fullPath, '_blank');
}
onMounted(() => {
  handlePageData();
});
</script>
