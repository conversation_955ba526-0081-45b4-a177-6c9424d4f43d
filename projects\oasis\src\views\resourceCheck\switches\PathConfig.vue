<template>
  <Modal
    :width="600"
    :open="show"
    :maskClosable="false"
    destroyOnClose
    centered
    :afterClose="modalDestroy"
    @cancel="() => modalCancel()"
  >
    <template #title>
      <div class="text-center">
        <span class="FO-Font-B16">
          <span>适用路径</span>
        </span>
      </div>
    </template>
    <div class="my-20px ml-60px mr-20px max-h-70vh overflow-auto">
      <RadioGroup v-model:value="effectPathState">
        <Radio :style="radioStyle" :value="PathState.All">
          全部路径
        </Radio>
        <Radio :style="radioStyle" :value="PathState.Custom">
          <span>指定路径</span>
          <Button
            class="custom-rounded-btn ml-[8px]"
            size="small"
            @click="resetAll"
          >
            清空勾选
          </Button>
        </Radio>
      </RadioGroup>
      <P4AuditTree
        ref="p4TreeRef"
        class="ml-24px"
        :permissionList="paths"
      />
    </div>
    <template #footer>
      <div class="mt flex justify-center">
        <Button type="primary" class="ml-2" @click="handleConfirm">
          保存
        </Button>
        <Button @click="modalDestroy()">
          取消
        </Button>
      </div>
    </template>
  </modal>
</template>

<script lang="ts" setup>
import { Button, message, Modal, Radio, RadioGroup } from 'ant-design-vue';
import type { ModalBaseProps } from '@hg-tech/utils-vue';
import type { NullableBasicResult } from '/@/api/model/baseModel';
import type { ResourceCheckEffectPathItems } from '/@/api/page/model/resourceCheckModel';
import P4AuditTree from '../../versionControl/swarmSettings/components/p4AuditTree.vue';
import { reactive, ref } from 'vue';
import type { P4PermissionListItem } from '/@/api/page/model/p4Model';
import { PathState } from './path.data';

const props = defineProps< ModalBaseProps<{ updatedItem?: NullableBasicResult }> & {
  sentReq?: (formValue: ResourceCheckEffectPathItems) => Promise<NullableBasicResult | undefined>;
  effectPathState?: PathState;
  paths?: P4PermissionListItem[];
  tplSwitchID?: number;
  streamID?: number;
}>();

const effectPathState = ref(props.effectPathState ?? PathState.All);
const paths = ref(props.paths ?? []);
const p4TreeRef = ref();
const radioStyle = reactive({
  display: 'flex',
  height: '30px',
  lineHeight: '30px',
});
async function resetAll() {
  p4TreeRef.value?.treeRef?.setCheckedKeys([]);
  p4TreeRef.value?.setHasChangeCheck(true);
}
async function handleConfirm() {
  const reviewPaths = p4TreeRef.value?.getOptimalList() ?? paths.value;
  if (!reviewPaths?.length && effectPathState.value === PathState.Custom) {
    message.warning('请至少选择一个路径');
  } else {
    const updatedItem = await props.sentReq?.({
      streamID: props.streamID,
      tplSwitchID: props.tplSwitchID,
      effectPathState: effectPathState.value,
      paths: reviewPaths,
    });
    return props.modalConfirm({ updatedItem });
  }
}
</script>
