import { useCountdown } from '@/common/hooks/countdown.hook';
import { NButton, NP, NText } from 'naive-ui';
import { type PropType, defineComponent, onMounted } from 'vue';

const LLMWarningModal = defineComponent({
  emits: ['confirm'],
  props: {
    countdown: {
      type: Boolean as PropType<boolean>,
      default: true,
    },
  },
  setup(props, { emit }) {
    const { loading, start, remainingTime } = useCountdown();

    const startCountdown = async () => {
      await start(5);
    };

    onMounted(() => {
      props.countdown && startCountdown();
    });

    return () => (
      <div>
        <div class="my-24px FO-Font-R14">
          <NP class="c-FO-Content-Text2">
            1. AI工具作为辅助性工具，提供的所有输出均由人工智能模型答复，可能出现偏差或遗漏。生成内容仅供参考，使用人
            <NText class="c-FO-Content-Text1 FO-Font-B14">不应将输出的内容作为专业建议，不加判断直接使用</NText>
          </NP>
          <NP class="c-FO-Content-Text2">
            2. AI工具为公司为员工提供的提效工具，仅限内部使用，
            <NText class="c-FO-Content-Text1 FO-Font-B14">禁止以任何形式将账号、访问权限外借给非公司员工、外包人员或外部组织</NText>
          </NP>
          <NP class="c-FO-Content-Text2">
            3. 如需要使用AI工具处理公司
            <NText class="c-FO-Content-Text1 FO-Font-B14">保密信息，必须使用【内部-安全模式】</NText>
          </NP>
          <NP class="c-FO-Content-Text1 FO-Font-B14">4. 禁止利用公司AI工具生成涉黄、涉政、暴力等违法违规内容。</NP>
        </div>
        <div class="flex-c-between">
          <a class="c-FO-Content-Link-Default" href="https://hypergryph.feishu.cn/docx/V866deTICovaXYxnvzRcOa9fnWh" target="_blank">AI工具使用要求-违法违规内容参考</a>
          <NButton disabled={loading.value} loading={loading.value} onClick={() => emit('confirm')} type="primary">
            {
              loading.value
                ? `我已知晓(${remainingTime.value}秒)`
                : '我已知晓'
            }
          </NButton>
        </div>
      </div>
    );
  },
});

export {
  LLMWarningModal,
};
