import fs from 'node:fs/promises';
import { camelCase, kebabCase } from 'lodash-es';
import type { ForgeonDesignClass } from './type.ts';
import path from 'node:path';
import { AUTO_GENERATED_COMMENT } from '../src/utils/helper.ts';

export async function ensureDirectoryExists(dir: string) {
  if (!(await fs.stat(dir).then((s) => s.isDirectory(), () => false))) {
    return fs.mkdir(dir, { recursive: true });
  }
}

const formatCustomTokenCssVarName = (className: string, styleName: string) => `${className}${styleName.replace(/\w/, (i) => i.toUpperCase())}`;
const formatCustomTokenNameToVarName = (key: keyof ForgeonDesignClass) => `FO${key.replace(/[A-Z]/g, (i) => `-${i}`)}`;
function formatCustomTokenVars(token: ForgeonDesignClass) {
  return Object.entries(token).reduce((acc, [className, styles]) => ({
    ...acc,
    ...Object.entries(styles).filter((i) => i != null).reduce((sAcc, [styleName, value]) => ({
      ...sAcc,
      [formatCustomTokenCssVarName(className, styleName)]: value!.toString(),
    }), {} as Record<string, string>),
  }), {} as Record<string, string>);
}

export async function genCustomTokenStyles(genDir: string, name: string, token: ForgeonDesignClass) {
  await ensureDirectoryExists(path.join(genDir, name));
  const vars = formatCustomTokenVars(token);

  await Promise.all([
    await fs.writeFile(path.join(genDir, name, 'index.less'), `${AUTO_GENERATED_COMMENT}
    
/** ${name} defines  */
:root {
  ${Object.entries(vars).map(([key, value]) => `--${formatCustomTokenNameToVarName(key)}: ${value};`).join('\n  ')}
}

${Object.entries(token).map(([className, styles]) => `.${formatCustomTokenNameToVarName(className)} {
  ${Object.keys(styles).map((styleKey) => `${kebabCase(styleKey)}: var(--${formatCustomTokenNameToVarName(formatCustomTokenCssVarName(className, styleKey))});`).join('\n  ')}
}`).join('\n\n')}
`),
    await fs.writeFile(path.join(genDir, name, 'vars.less'), `${AUTO_GENERATED_COMMENT}
    
/** ${name} vars  */
${Object.keys(vars).map((key) => `@${formatCustomTokenNameToVarName(key)}: var(--${formatCustomTokenNameToVarName(key)});`).join('\n')}
`),

  ]);
}

export async function genCustomTokenTs(genDir: string, name: string, token: ForgeonDesignClass) {
  await ensureDirectoryExists(genDir);

  const vars = formatCustomTokenVars(token);
  await fs.writeFile(path.join(genDir, `${name}.ts`), `${AUTO_GENERATED_COMMENT}
  
export const ${camelCase(name)}CssVarName = {
${Object.entries(vars).map(([key]) => `${key}: 'var(--${formatCustomTokenNameToVarName(key)})',`).join('\n')}
}`);
}
