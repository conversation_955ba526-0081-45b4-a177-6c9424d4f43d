<template>
  <Modal
    width="700px"
    :open="show"
    :destroyOnClose="true"
    :centered="true"
    :afterClose="modalDestroy"
    @cancel="() => modalCancel()"
  >
    <template #title>
      <div class="w-full flex justify-center text-xl">
        {{ title }}
      </div>
    </template>
    <div class="my-4 w-full flex items-center justify-center">
      <div v-if="hasDeleteIcon">
        <Icon icon="devGuard-delete-stream|svg" :size="80" />
      </div>
      <div :class="hasDeleteIcon ? 'ml-10' : ''" class="text-xl leading-loose">
        <RenderMaybeVNode :nodes="description" />
      </div>
    </div>
    <template #footer>
      <div class="flex justify-center">
        <Button danger type="primary" class="mr-[24px]" @click="handleSubmit">
          删除
        </Button>
        <Button @click="() => modalCancel()">
          取消
        </Button>
      </div>
    </template>
  </Modal>
</template>

<script lang="ts" setup>
import { Button, Modal } from 'ant-design-vue';
import { type MaybeVNode, RenderMaybeVNode } from '@hg-tech/oasis-common';
import { ref } from 'vue';
import Icon from './Icon';
import type { ModalBaseProps } from '@hg-tech/utils-vue';

const props = withDefaults(defineProps<ModalBaseProps & {
  title?: string;
  description?: MaybeVNode;
  onOk?: () => Promise<unknown>;
  hasDeleteIcon?: boolean;
}>(), {
  hasDeleteIcon: true,
});
const isDeleting = ref(false);

async function handleSubmit() {
  isDeleting.value = true;
  try {
    await props.onOk?.();
    props.modalConfirm();
  } finally {
    isDeleting.value = false;
  }
}
</script>
