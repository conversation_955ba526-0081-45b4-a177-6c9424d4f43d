<template>
  <div class="h-full flex flex-col gap-16px">
    <!-- 顶部控制区域 -->
    <div class="flex-none px-16px">
      <!-- 有选中项时显示操作按钮组 -->
      <div v-if="selectedMemberIds.length > 0" class="flex items-center gap-20px">
        <span class="FO-Font-R14 rounded-6px bg-FO-Brand-Tertiary-Active px-8px py-5px c-FO-Content-Text1">
          已选择{{ selectedMemberIds.length }}/{{ tableRows.length }}项
        </span>
        <div class="flex items-center gap-8px">
          <Button class="btn-fill-default" @click="selectedMemberIds = []">
            取消
          </Button>
          <Button class="btn-fill-secondary" @click="handleSelectAllMembers">
            全选
          </Button>
          <Button class="btn-fill-error" @click="handleBatchDeleteMembersConfirm">
            <template #icon>
              <DeleteIcon class="c-FO-Functional-Error1-Default" />
            </template>
            批量删除
          </Button>
        </div>
      </div>

      <!-- 无选中项时显示标题区 -->
      <div v-else class="flex items-center justify-between">
        <div class="flex items-center gap-12px">
          <div class="FO-Font-B14 c-FO-Content-Text2">
            成员列表
          </div>
          <!-- 成员统计 -->
          <MemberCountDisplay :memberCount="memberCount" />
        </div>
        <div class="flex items-center gap-12px">
          <Input v-model:value="searchKeyword" allowClear placeholder="搜索成员" class="w-200px" @input="debouncedSearch">
            <template #prefix>
              <SearchIcon class="c-FO-Content-Icon1" />
            </template>
          </Input>
          <Button class="btn-fill-primary" @click="handleAddMember">
            <template #icon>
              <AddIcon />
            </template>
            添加成员
          </Button>
        </div>
      </div>
    </div>

    <!-- 表格区域 -->
    <div class="flex-1 overflow-hidden px-16px">
      <BasicVxeTable
        ref="tableRef"
        :options="gridOptions"
        :cssVarOverrides="{ '--vxe-ui-layout-background-color': ForgeonThemeCssVar.ContainerFill1 }"
      />
    </div>

    <MemberHolder />
  </div>
</template>

<script setup lang="tsx">
import { computed, nextTick, ref, watch } from 'vue';
import { Button, Input, message, Modal, Tooltip } from 'ant-design-vue';
import type { VxeGridInstance, VxeGridProps } from 'vxe-table';
import { BasicVxeTable } from '@hg-tech/oasis-common';
import { useLatestPromise, useModalShow } from '@hg-tech/utils-vue';
import MemberDrawer from './MemberDrawer.vue';
import { type PermissionAppGroupListItem, addMemberToGroup, fetchPermissionGroupDetailV2, OrgStructureType, removeMemberFromGroup, searchPermissionGroupMembers } from '../../../../api/group.ts';
import type { PermissionAppInfo } from '../../../../api/app.ts';
import SearchIcon from '../../../../assets/icons/fill-search.svg?component';
import AddIcon from '../../../../assets/icons/fill-add.svg?component';
import DeleteIcon from '../../../../assets/icons/fill-delete.svg?component';
import WarningErrorIcon from '../../../../assets/icons/fill-warning-error.svg?component';
import DepartmentIcon from '../../../../assets/icons/fill-department.svg?component';
import GroupIcon from '../../../../assets/icons/fill-group-outline.svg?component';
import { filter, includes } from 'lodash';
import dayjs from 'dayjs';
import MemberCountDisplay from '../MemberCountDisplay.vue';
import MemberPopover from './MemberPopover.vue';
import { type SysUserInfo, getLdapGroupDetail } from '../../../../api/users.ts';
import { useDebounceFn } from '@vueuse/core';
import { ForgeonThemeCssVar } from '@hg-tech/forgeon-style';

interface MemberItem {
  id: number;
  name: string;
  avatar?: string;
  type: OrgStructureType;
  lastActiveTime: string;
  autoRemoveTime?: string;
  rawData?: any;
  orgType?: OrgStructureType;
  resourceId: string;
}

interface RenderRow {
  id: number;
  name: string;
  type: string;
  lastActiveTime: string;
  autoRemoveTime: string;
  item: MemberItem;
}

const props = defineProps<{
  appId?: PermissionAppInfo['id'];
  activeGroupId?: PermissionAppGroupListItem['id'];
  tenantId?: number;
}>();

// 响应式数据
const selectedMemberIds = ref<number[]>([]);
const tableRef = ref<VxeGridInstance>();
const searchKeyword = ref('');

// 成员数据缓存
const memberCache = ref<Record<string, SysUserInfo[]>>({});

// 获取权限组详情
const { data: groupDetailRes, execute: fetchGroupDetail } = useLatestPromise(fetchPermissionGroupDetailV2);

// 删除权限组成员
const { execute: removeGroupMembers } = useLatestPromise(removeMemberFromGroup);

// 搜索权限组成员
const { data: searchMemberRes, execute: searchMembers } = useLatestPromise(searchPermissionGroupMembers);

// 获取自定义组成员
const { execute: fetchGroupMembers, loading: loadingGroupMembers } = useLatestPromise(getLdapGroupDetail);

// 防抖搜索函数
const debouncedSearch = useDebounceFn(async () => {
  selectedMemberIds.value = [];
  refreshMemberData();
}, 200);

// 监听activeGroupId变化，获取成员数据
watch(() => props.activeGroupId, () => {
  searchKeyword.value = '';
  selectedMemberIds.value = [];
  tableRef.value?.clearFilter();
  tableRef.value?.clearSort();
  refreshMemberData();
}, { immediate: true });

// 计算成员统计 - 使用后端返回的计数字段
const memberCount = computed(() => {
  const detail = groupDetailRes.value?.data?.data;

  return {
    users: detail?.memberCnt || 0,
    departments: detail?.orgCnt || 0,
    groups: detail?.groupCnt || 0,
  };
});

async function refreshMemberData() {
  if (props.appId && props.activeGroupId) {
    await fetchGroupDetail({
      appId: props.appId,
      groupId: props.activeGroupId,
      tenantId: props.tenantId,
    }, {});
    if (searchKeyword.value) {
      await searchMembers({
        appId: props.appId,
        groupId: props.activeGroupId,
        search: searchKeyword.value,
        tenantId: props.tenantId,
      }, {});
    }
  }
}

// 转换成员数据格式的公共函数
function transformMemberData(member: any) {
  return {
    key: `${member.type}-${member.id}`,
    id: member.id,
    name: member.name,
    avatar: '', // 新结构中没有头像信息
    type: member.type,
    lastActiveTime: dayjs(member.createdAt).format('YYYY-MM-DD HH:mm:ss'),
    autoRemoveTime: member.autoRemoveTime ? dayjs(member.autoRemoveTime).format('YYYY-MM-DD HH:mm:ss') : '',
    rawData: member,
    orgType: member.type,
    resourceId: member.resourceId,
  };
}

// 转换成员数据格式 - 适配新的数据结构
const memberList = computed(() => {
  const groupDetail = groupDetailRes.value?.data?.data;
  if (!groupDetail?.members) {
    return [];
  }

  return groupDetail.members.map(transformMemberData);
});

// 计算已存在成员的keys，用于禁用已添加的成员
const disabledKeys = computed(() => {
  return memberList.value.map((member) => {
    return member.resourceId;
  }).filter(Boolean);
});

// 转换搜索结果数据格式
const searchMemberList = computed(() => {
  const searchResult = searchMemberRes.value?.data?.data;
  if (!searchResult) {
    return [];
  }

  return searchResult.map(transformMemberData);
});

// 最终显示的成员列表 - 有搜索关键词时显示搜索结果，否则显示完整列表
const filteredMemberList = computed(() => {
  if (searchKeyword.value) {
    return searchMemberList.value;
  }
  return memberList.value || [];
});

// 表格数据
const tableRows = computed<RenderRow[]>(() => {
  return filteredMemberList.value.map((member) => ({
    key: member.key,
    id: member.id,
    name: member.name || '',
    type: getTypeLabel(member.type),
    lastActiveTime: member.lastActiveTime || '',
    autoRemoveTime: member.autoRemoveTime || '-',
    item: member,
  }));
});

// 工具函数
function getTypeLabel(type: OrgStructureType): string {
  const typeMap: Record<OrgStructureType, string> = {
    [OrgStructureType.Member]: '干员',
    [OrgStructureType.Department]: '部门',
    [OrgStructureType.CustomGroup]: '自定义组',
  };
  return typeMap[type] || type;
}

// 获取成员数据的函数
async function fetchMemberData(type: OrgStructureType, row: RenderRow) {
  try {
    let members: SysUserInfo[] = [];
    const id = row.item.resourceId;

    // 获取自定义组或部门成员
    const res = await fetchGroupMembers({ no: id }, {});
    members = res?.data?.data?.member || [];

    // 缓存结果
    memberCache.value[`${type}-${id}`] = members;
    return members;
  } catch (error) {
    console.error('获取成员数据失败:', error);
    return [];
  }
}

// 获取缓存的成员数据
function getCachedMembers(type: OrgStructureType, row: RenderRow): SysUserInfo[] {
  const id = row.item.resourceId;
  return memberCache.value[`${type}-${id}`] || [];
}

// 表格列配置 - checkbox始终显示
const tableColumns = computed<VxeGridProps<RenderRow>['columns']>(() => [
  { type: 'checkbox', width: 60 },
  {
    field: 'name',
    title: '名称',
    minWidth: 200,
    slots: {
      default({ row }: { row: RenderRow }) {
        return (
          <div class="flex items-center gap-8px">
            <span class="FO-Font-R14">
              {row.name}
            </span>
            {row.item.orgType === OrgStructureType.Department && (
              <Tooltip title="查看组内成员">
                <MemberPopover
                  loading={loadingGroupMembers.value}
                  members={getCachedMembers(OrgStructureType.Department, row)}
                  onClick={() => fetchMemberData(OrgStructureType.Department, row)}
                  title={`包含成员 ${getCachedMembers(OrgStructureType.Department, row).length} 名`}
                >
                  <DepartmentIcon class="cursor-pointer c-FO-Content-Icon3" />
                </MemberPopover>
              </Tooltip>
            )}
            {row.item.orgType === OrgStructureType.CustomGroup && (
              <Tooltip title="查看组内成员">
                <MemberPopover
                  loading={loadingGroupMembers.value}
                  members={getCachedMembers(OrgStructureType.CustomGroup, row)}
                  onClick={() => fetchMemberData(OrgStructureType.CustomGroup, row)}
                  title={`包含成员 ${getCachedMembers(OrgStructureType.CustomGroup, row).length} 名`}
                >
                  <GroupIcon class="cursor-pointer c-FO-Content-Icon3" />
                </MemberPopover>
              </Tooltip>
            )}
          </div>
        );
      },
    },
  },
  {
    field: 'type',
    title: '类型',
    width: 100,
    filters: [
      { label: '干员', value: '干员' },
      { label: '部门', value: '部门' },
      { label: '自定义组', value: '自定义组' },
    ],
    slots: {
      default({ row }: { row: RenderRow }) {
        return <span class="FO-Font-R14">{row.type}</span>;
      },
    },
  },
  {
    field: 'lastActiveTime',
    title: '添加时间',
    width: 180,
    sortable: true,
    slots: {
      default({ row }: { row: RenderRow }) {
        return <span class="FO-Font-R14">{row.lastActiveTime}</span>;
      },
    },
  },
  {
    field: 'autoRemoveTime',
    title: '自动移除时间',
    width: 180,
    slots: {
      default({ row }: { row: RenderRow }) {
        return <span class="FO-Font-R14">{row.autoRemoveTime}</span>;
      },
    },
  },
  {
    field: 'actions',
    title: '操作',
    width: 80,
    slots: {
      default({ row }: { row: RenderRow }) {
        return (
          <Tooltip title="删除成员">
            <Button class="btn-fill-text px-8px" onClick={() => handleDeleteMemberConfirm(row)}>
              <DeleteIcon class="text-16px c-FO-Content-Icon1" />
            </Button>
          </Tooltip>
        );
      },
    },
  },
]);

const gridOptions = computed(() => ({
  rowConfig: {
    keyField: 'key',
    isHover: true,
  },
  height: 'auto',
  columns: tableColumns.value,
  data: tableRows.value,
  checkboxConfig: {
    checkRowKeys: selectedMemberIds.value,
    reserve: true,
  },
  columnConfig: {
    resizable: false,
  },
  onCheckboxChange: handleCheckboxChange,
  onCheckboxAll: handleCheckboxChange,
}) as VxeGridProps<RenderRow>);

// checkbox变化处理
function handleCheckboxChange() {
  if (tableRef.value) {
    const checkedRecords = tableRef.value.getCheckboxRecords() as RenderRow[];
    selectedMemberIds.value = checkedRecords.map((record) => record.id);
  }
}

// 监听selectedMemberIds变化，同步到表格
watch(selectedMemberIds, async (newIds) => {
  await nextTick();
  if (tableRef.value) {
    // 先清空所有选择
    tableRef.value.clearCheckboxRow();
    // 再设置新的选择
    if (newIds.length > 0) {
      const rowsToCheck = filter(tableRows.value, (row) => includes(newIds, row.id));
      tableRef.value.setCheckboxRow(rowsToCheck, true);
    }
  }
}, { deep: true });

const [MemberHolder, showMemberDrawer] = useModalShow(MemberDrawer);

async function handleAddMember() {
  try {
    await showMemberDrawer({
      appId: props.appId,
      groupId: props.activeGroupId,
      disabledKeys: disabledKeys.value,
      sentReq: async (formData: any) => {
        try {
          const res = await addMemberToGroup(
            {
              appId: props.appId,
              groupId: props.activeGroupId,
              tenantId: props.tenantId,
            },
            formData.selectedPayloads.map((i: any) => ({
              type: i.key,
              resourceId: i.data.key,
              autoRemoveTime: formData.expireTime?.unix(),
            })),
          );
          return res?.data?.code === 0;
        } catch (error) {
          console.error('添加成员失败:', error);
          return false;
        }
      },
    });

    // 添加成员成功后刷新列表
    await refreshMemberData();
    message.success('操作成功');
  } catch {
    // 用户取消或其他错误，不需要处理
  }
}

// 删除确认弹窗
function handleDeleteMemberConfirm(row: RenderRow) {
  Modal.confirm({
    icon: null,
    width: 496,
    okText: '删除',
    okButtonProps: {
      type: 'primary',
      danger: true,
    },
    cancelButtonProps: {
      // @ts-expect-error cancelButtonProps支持class但没有类型定义
      class: 'btn-fill-default',
    },
    centered: true,
    closable: true,
    title: () => {
      return (
        <div class="flex items-center">
          <WarningErrorIcon class="c-FO-Functional-Error1-Default" />
          <div class="FO-Font-B16 ml-8px">删除成员</div>
        </div>
      );
    },
    content() {
      return (
        <div class="mt-12px pb-8px c-FO-Content-Text2">
          删除成员【{row.name}】，此操作不可恢复，请谨慎操作。
        </div>
      );
    },
    onOk() {
      handleDeleteMember(row.id);
    },
  });
}

async function handleDeleteMember(memberId: number) {
  if (!props.appId || !props.activeGroupId) {
    message.error('缺少必要参数');
    return;
  }

  try {
    const res = await removeGroupMembers({
      appId: props.appId,
      groupId: props.activeGroupId,
      tenantId: props.tenantId,
    }, {
      ids: [memberId],
    });

    if (res?.data?.code === 0) {
      message.success('操作成功');
      // 重新获取成员数据
      await refreshMemberData();
      selectedMemberIds.value = selectedMemberIds.value.filter((id) => id !== memberId);
    }
  } catch (error) {
    console.error('删除成员失败:', error);
  }
}

// 批量删除确认弹窗
function handleBatchDeleteMembersConfirm() {
  if (selectedMemberIds.value.length === 0) {
    message.warning('请先选择要删除的成员');
    return;
  }

  // 获取选中的成员信息
  const selectedMembers = memberList.value.filter((member) =>
    selectedMemberIds.value.includes(member.id),
  );

  if (selectedMembers.length === 0) {
    message.warning('请先选择要删除的成员');
    return;
  }

  const firstMemberName = selectedMembers[0].name;
  const totalCount = selectedMembers.length;

  Modal.confirm({
    icon: null,
    width: 496,
    okText: '删除',
    okButtonProps: {
      danger: true,
    },
    cancelText: '取消',
    cancelButtonProps: {
      // @ts-expect-error cancelButtonProps支持class但没有类型定义
      class: 'btn-fill-default',
    },
    centered: true,
    closable: true,
    title: () => {
      return (
        <div class="flex items-center gap-8px">
          <WarningErrorIcon class="text-20px c-FO-Functional-Error1-Default" />
          <span class="FO-Font-B16 c-FO-Content-Text1">删除成员</span>
        </div>
      );
    },
    content: () => {
      return (
        <div class="FO-Font-R14 mt-8px c-FO-Content-Text2">
          {totalCount > 1
            ? `删除【${firstMemberName}】等${totalCount}名成员，此操作不可恢复，请谨慎操作。`
            : `删除成员【${firstMemberName}】，此操作不可恢复，请谨慎操作。`}
        </div>
      );
    },
    onOk() {
      handleBatchDeleteMembers();
    },
  });
}

// 批量删除成员
async function handleBatchDeleteMembers() {
  if (!props.appId || !props.activeGroupId) {
    message.error('缺少必要参数');
    return;
  }

  try {
    // 调用新的删除API
    const res = await removeGroupMembers({
      appId: props.appId,
      groupId: props.activeGroupId,
      tenantId: props.tenantId,
    }, {
      ids: selectedMemberIds.value,
    });

    if (res?.data?.code === 0) {
      message.success('操作成功');

      // 清空选择并刷新数据
      selectedMemberIds.value = [];
      await refreshMemberData();
    }
  } catch (error) {
    console.error('批量删除成员失败:', error);
  }
}

function handleSelectAllMembers() {
  selectedMemberIds.value = tableRows.value.map((row) => row.id);
}
</script>
