/// <reference types="vite/client" />
/// <reference types="vite-svg-loader" />

interface ImportMeta {
  readonly env: {
    /**
     * 后端请求地址
     */
    readonly VITE_GLOB_API_URL?: string;
    // ---- 子系统 ----
    /**
     * 【管理后台】入口
     */
    readonly VITE_SUB_SYS_URL_ADMIN: string;
    /**
     * 【管理后台】API
     */
    readonly VITE_BASE_API_ORIGIN_FORGEON_ADMIN: string;
    /**
     * 【AIGC】入口
     */
    readonly VITE_SUB_SYS_URL_AIGC: string;
    /**
     * 【AIGC】API
     */
    readonly VITE_BASE_API_ORIGIN_AIGC: string;
    /**
     * 【权限管理中心】 入口
     */
    readonly VITE_SUB_SYS_URL_PERMISSION_CENTER: string;
    /**
     * 【权限管理中心】 API
     */
    readonly VITE_BASE_API_ORIGIN_PERMISSION_CENTER?: string;
    /**
     * 【工具中心】入口
     */
    readonly VITE_SUB_SYS_URL_TOOLKIT_MANAGEMENT: string;
  };
}
