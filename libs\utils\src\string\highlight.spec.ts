import { describe, expect, it } from 'vitest';
import { toHighlightSegments } from './highlight';

const TEST_STR = 'Rhodes Island Pharmaceutical is seeking strategic cooperation with various organizations to combat Originium infection.';

describe('基础功能', () => {
  it('常规匹配', () => {
    expect(toHighlightSegments(TEST_STR, 'strategic')).toStrictEqual([
      { text: 'Rhodes Island Pharmaceutical is seeking ' },
      { highlight: true, text: 'strategic' },
      { text: ' cooperation with various organizations to combat Originium infection.' },
    ]);
  });

  it('部分匹配', () => {
    expect(toHighlightSegments(TEST_STR, 'ori')).toStrictEqual([
      { text: 'Rhodes Island Pharmaceutical is seeking strategic cooperation with various organizations to combat ' },
      { highlight: true, text: 'Ori' },
      { text: 'ginium infection.' },
    ]);
  });

  it('多次匹配', () => {
    expect(toHighlightSegments(TEST_STR, 'is')).toStrictEqual([
      { text: 'Rhodes ' },
      { highlight: true, text: 'Is' },
      { text: 'land Pharmaceutical ' },
      { highlight: true, text: 'is' },
      { text: ' seeking strategic cooperation with various organizations to combat Originium infection.' },
    ]);
  });

  it('空输入', () => {
    expect(toHighlightSegments('', 'test')).toEqual([]);
    expect(toHighlightSegments('text', '')).toEqual([{ text: 'text' }]);
    expect(toHighlightSegments('', '')).toEqual([]);
  });
});

describe('多关键词匹配', () => {
  it('字符串数组', () => {
    expect(toHighlightSegments(TEST_STR, ['strategic', 'is'])).toStrictEqual([
      { text: 'Rhodes ' },
      { highlight: true, text: 'Is' },
      { text: 'land Pharmaceutical ' },
      { highlight: true, text: 'is' },
      { text: ' seeking ' },
      { highlight: true, text: 'strategic' },
      { text: ' cooperation with various organizations to combat Originium infection.' },
    ]);
  });

  it('多个结果重叠取前一个结果', () => {
    expect(toHighlightSegments(TEST_STR, ['seeking', 'seek'])).toStrictEqual([
      { text: 'Rhodes Island Pharmaceutical is ' },
      { highlight: true, text: 'seeking' },
      { text: ' strategic cooperation with various organizations to combat Originium infection.' },
    ]);
    expect(toHighlightSegments(TEST_STR, ['eking', 'seek'])).toStrictEqual([
      { text: 'Rhodes Island Pharmaceutical is ' },
      { highlight: true, text: 'seek' },
      { text: 'ing strategic cooperation with various organizations to combat Originium infection.' },
    ]);
    expect(toHighlightSegments(TEST_STR, ['eking', 'ing'])).toStrictEqual([
      { text: 'Rhodes Island Pharmaceutical is se' },
      { highlight: true, text: 'eking' },
      { text: ' strategic cooperation with various organizations to combat Originium infection.' },
    ]);
  });
});

describe('大小写敏感性', () => {
  it('大小写不敏感（默认行为）', () => {
    expect(toHighlightSegments(TEST_STR, 'STRATEGIC')).toStrictEqual([
      { text: 'Rhodes Island Pharmaceutical is seeking ' },
      { highlight: true, text: 'strategic' },
      { text: ' cooperation with various organizations to combat Originium infection.' },
    ]);

    expect(toHighlightSegments(TEST_STR, 'rhodes')).toStrictEqual([
      { highlight: true, text: 'Rhodes' },
      { text: ' Island Pharmaceutical is seeking strategic cooperation with various organizations to combat Originium infection.' },
    ]);
  });

  it('显式指定大小写不敏感', () => {
    expect(toHighlightSegments(TEST_STR, 'STRATEGIC', { caseSensitive: false })).toStrictEqual([
      { text: 'Rhodes Island Pharmaceutical is seeking ' },
      { highlight: true, text: 'strategic' },
      { text: ' cooperation with various organizations to combat Originium infection.' },
    ]);
  });

  it('大小写敏感', () => {
    // 大写不匹配小写
    expect(toHighlightSegments(TEST_STR, 'STRATEGIC', { caseSensitive: true })).toStrictEqual([
      { text: TEST_STR },
    ]);

    // 精确匹配：首字母大写
    expect(toHighlightSegments(TEST_STR, 'Rhodes', { caseSensitive: true })).toStrictEqual([
      { highlight: true, text: 'Rhodes' },
      { text: ' Island Pharmaceutical is seeking strategic cooperation with various organizations to combat Originium infection.' },
    ]);

    // 精确匹配：首字母大写 + 匹配多个关键词
    expect(toHighlightSegments(TEST_STR, ['Rhodes', 'Island', 'Pharmaceutical'], { caseSensitive: true })).toStrictEqual([
      { highlight: true, text: 'Rhodes' },
      { text: ' ' },
      { highlight: true, text: 'Island' },
      { text: ' ' },
      { highlight: true, text: 'Pharmaceutical' },
      { text: ' is seeking strategic cooperation with various organizations to combat Originium infection.' },
    ]);
  });
});

describe('转换函数', () => {
  it('使用转换函数匹配数字', () => {
    // 将数字转换为对应的英文单词进行匹配
    const numberTransform = (char: string) => {
      const map: Record<string, string> = {
        1: 'one',
        2: 'two',
        3: 'three',
        4: 'four',
        5: 'five',
      };
      return map[char] || char;
    };

    const textWithNumbers = 'Rhodes Island has 2 main facilities and 5 research labs.';

    // 应该匹配转换后文本中的 "two"，但结果显示原始的 "2"
    expect(toHighlightSegments(textWithNumbers, 'two', { transform: numberTransform })).toStrictEqual([
      { text: 'Rhodes Island has ' },
      { highlight: true, text: '2' },
      { text: ' main facilities and 5 research labs.' },
    ]);

    // 应该匹配转换后文本中的 "five"，但结果显示原始的 "5"
    expect(toHighlightSegments(textWithNumbers, 'five', { transform: numberTransform })).toStrictEqual([
      { text: 'Rhodes Island has 2 main facilities and ' },
      { highlight: true, text: '5' },
      { text: ' research labs.' },
    ]);
  });

  it('使用转换函数处理特殊字符', () => {
    // 自定义大小写转换，处理特殊字符
    const customCaseTransform = (char: string) => char.normalize('NFD').replace(/[\u0300-\u036F]/g, '').toLowerCase();

    const textWithAccents = 'Résumé and Café are common loan words in English.';

    // 应该使用不区分重音的搜索匹配 "Résumé" 中的 "resume"
    expect(toHighlightSegments(textWithAccents, 'resume', { transform: customCaseTransform })).toStrictEqual([
      { highlight: true, text: 'Résumé' },
      { text: ' and Café are common loan words in English.' },
    ]);

    // 应该使用不区分重音的搜索匹配 "Café" 中的 "cafe"
    expect(toHighlightSegments(textWithAccents, 'cafe', { transform: customCaseTransform })).toStrictEqual([
      { text: 'Résumé and ' },
      { highlight: true, text: 'Café' },
      { text: ' are common loan words in English.' },
    ]);
  });

  it('使用转换函数忽略特定字符', () => {
    // 移除空格和标点符号的转换函数
    const removeSpacesAndPunctuation = (char: string) => {
      if (char.match(/[\s.,;!?-]/)) {
        return '';
      }
      return char;
    };

    const textWithSpaces = 'Rhodes Island, Pharmaceutical Inc. - a bio-tech company';

    // 应该能匹配"RhodesIsland"（忽略空格和标点）
    expect(toHighlightSegments(textWithSpaces, 'rhodesisland', {
      transform: removeSpacesAndPunctuation,
    })).toStrictEqual([
      { highlight: true, text: 'Rhodes Island, ' },
      { text: 'Pharmaceutical Inc. - a bio-tech company' },
    ]);

    // 应该能匹配"biotech"（忽略连字符）
    expect(toHighlightSegments(textWithSpaces, 'biotech', {
      transform: removeSpacesAndPunctuation,
    })).toStrictEqual([
      { text: 'Rhodes Island, Pharmaceutical Inc. - a ' },
      { highlight: true, text: 'bio-tech ' },
      { text: 'company' },
    ]);
  });

  it('大小写敏感与转换函数组合使用', () => {
    const textWithAccents = 'Résumé and Café are common loan words in English.';
    const customCaseTransform = (char: string) => char.normalize('NFD').replace(/[\u0300-\u036F]/g, '');

    // 大小写不敏感：可以匹配不同大小写
    expect(toHighlightSegments(textWithAccents, 'resume', {
      transform: customCaseTransform,
      caseSensitive: false,
    })).toStrictEqual([
      { highlight: true, text: 'Résumé' },
      { text: ' and Café are common loan words in English.' },
    ]);

    // 大小写敏感：只有完全匹配才行
    expect(toHighlightSegments(textWithAccents, 'Resume', {
      transform: customCaseTransform,
      caseSensitive: true,
    })).toStrictEqual([
      { highlight: true, text: 'Résumé' },
      { text: ' and Café are common loan words in English.' },
    ]);

    // 大小写敏感：不匹配不同大小写
    expect(toHighlightSegments(textWithAccents, 'resume', {
      transform: customCaseTransform,
      caseSensitive: true,
    })).toStrictEqual([
      { text: textWithAccents },
    ]);
  });

  it('转换函数返回空数组时应跳过匹配', () => {
    const emptyTransform = () => [];
    expect(toHighlightSegments('test', 'test', { transform: emptyTransform }))
      .toEqual([{ text: 'test' }]);
  });
});

describe('多语言支持', () => {
  it('中文文本匹配测试', () => {
    const chineseText = '罗德岛制药公司正在寻求与各种组织的战略合作，以对抗源石感染。';

    // 基本匹配
    expect(toHighlightSegments(chineseText, '战略')).toStrictEqual([
      { text: '罗德岛制药公司正在寻求与各种组织的' },
      { highlight: true, text: '战略' },
      { text: '合作，以对抗源石感染。' },
    ]);

    // 多词匹配
    expect(toHighlightSegments(chineseText, ['战略', '源石'])).toStrictEqual([
      { text: '罗德岛制药公司正在寻求与各种组织的' },
      { highlight: true, text: '战略' },
      { text: '合作，以对抗' },
      { highlight: true, text: '源石' },
      { text: '感染。' },
    ]);
  });

  it('繁简体转换测试', () => {
    const traditionalText = '羅德島製藥公司正在尋求與各種組織的戰略合作，以對抗源石感染。';
    const traditionalToSimplified = (char: string) => {
      const map: Record<string, string> = {
        羅: '罗',
        島: '岛',
        製: '制',
        藥: '药',
        尋: '寻',
        與: '与',
        種: '种',
        組: '组',
        織: '织',
        戰: '战',
        對: '对',
      };
      return map[char] || char;
    };

    expect(toHighlightSegments(traditionalText, '战略', {
      transform: traditionalToSimplified,
    })).toStrictEqual([
      { text: '羅德島製藥公司正在尋求與各種組織的' },
      { highlight: true, text: '戰略' },
      { text: '合作，以對抗源石感染。' },
    ]);
  });

  it('中文与大小写敏感', () => {
    const chineseText = '罗德岛公司和Rhodes Island都是相同的机构。';

    // 中文搜索不受大小写影响
    expect(toHighlightSegments(chineseText, '罗德岛', { caseSensitive: true })).toStrictEqual([
      { highlight: true, text: '罗德岛' },
      { text: '公司和Rhodes Island都是相同的机构。' },
    ]);

    // 英文受大小写影响
    expect(toHighlightSegments(chineseText, 'rhodes', { caseSensitive: false })).toStrictEqual([
      { text: '罗德岛公司和' },
      { highlight: true, text: 'Rhodes' },
      { text: ' Island都是相同的机构。' },
    ]);

    expect(toHighlightSegments(chineseText, 'rhodes', { caseSensitive: true })).toStrictEqual([
      { text: chineseText },
    ]);
  });

  it('中文标点符号处理', () => {
    const chineseText = '罗德岛（RI）是一家制药公司，致力于研究源石技术。';
    const removeChinesePunctuation = (char: string) => {
      if (char.match(/[（）、，。；："'！？【】《》]/)) {
        return '';
      }
      return char;
    };

    // 应该能匹配"罗德岛RI"（忽略括号）
    expect(toHighlightSegments(chineseText, '罗德岛ri', {
      transform: (char) => removeChinesePunctuation(char).toLowerCase(),
    })).toStrictEqual([
      { highlight: true, text: '罗德岛（RI）' },
      { text: '是一家制药公司，致力于研究源石技术。' },
    ]);
  });
});

describe('多结果转换函数', () => {
  it('支持transform函数返回多个匹配结果（字符串数组）', () => {
    // 转换函数返回多个可能的匹配结果
    const multiTransform = (char: string) => {
      const map: Record<string, string[]> = {
        2: ['2', 'two'],
        5: ['5', 'five'],
      };
      return map[char] || char;
    };

    const textWithNumbers = 'Rhodes Island has 2 main facilities and 5 research labs.';

    // 应该能够匹配数字或其英文表达
    expect(toHighlightSegments(textWithNumbers, 'two', { transform: multiTransform })).toStrictEqual([
      { text: 'Rhodes Island has ' },
      { highlight: true, text: '2' },
      { text: ' main facilities and 5 research labs.' },
    ]);

    expect(toHighlightSegments(textWithNumbers, 'five', { transform: multiTransform })).toStrictEqual([
      { text: 'Rhodes Island has 2 main facilities and ' },
      { highlight: true, text: '5' },
      { text: ' research labs.' },
    ]);

    expect(toHighlightSegments(textWithNumbers, ['2', 'five'], { transform: multiTransform })).toStrictEqual([
      { text: 'Rhodes Island has ' },
      { highlight: true, text: '2' },
      { text: ' main facilities and ' },
      { highlight: true, text: '5' },
      { text: ' research labs.' },
    ]);
  });

  it('支持汉字繁简体和拼音同时匹配', () => {
    const traditionalText = '羅德島製藥公司研究源石技術。';

    // 返回简体和拼音的转换函数
    const charToSimplifiedAndPinyin = (char: string) => {
      const mapping: Record<string, [string, string]> = {
        '羅': ['罗', 'luo'],
        '德': ['德', 'de'],
        '島': ['岛', 'dao'],
        '製': ['制', 'zhi'],
        '藥': ['药', 'yao'],
        '研': ['研', 'yan'],
        '究': ['究', 'jiu'],
        '源': ['源', 'yuan'],
        '石': ['石', 'shi'],
        '技': ['技', 'ji'],
        '術': ['术', 'shu'],
        '。': ['。', '.'],
      };
      return mapping[char] || [char, char];
    };

    // 简体字匹配
    expect(toHighlightSegments(traditionalText, '制药', { transform: charToSimplifiedAndPinyin })).toStrictEqual([
      { text: '羅德島' },
      { highlight: true, text: '製藥' },
      { text: '公司研究源石技術。' },
    ]);

    // 拼音匹配
    expect(toHighlightSegments(traditionalText, 'yuanshi', { transform: charToSimplifiedAndPinyin })).toStrictEqual([
      { text: '羅德島製藥公司研究' },
      { highlight: true, text: '源石' },
      { text: '技術。' },
    ]);

    // 多匹配
    expect(toHighlightSegments(traditionalText, '制yao', { transform: charToSimplifiedAndPinyin })).toStrictEqual([
      { text: '羅德島' },
      { highlight: true, text: '製藥' },
      { text: '公司研究源石技術。' },
    ]);

    // 混合匹配
    expect(toHighlightSegments(traditionalText, ['制药', 'yuanshi'], { transform: charToSimplifiedAndPinyin })).toStrictEqual([
      { text: '羅德島' },
      { highlight: true, text: '製藥' },
      { text: '公司研究' },
      { highlight: true, text: '源石' },
      { text: '技術。' },
    ]);
    expect(toHighlightSegments(traditionalText, ['zhi药', '源shi'], { transform: charToSimplifiedAndPinyin })).toStrictEqual([
      { text: '羅德島' },
      { highlight: true, text: '製藥' },
      { text: '公司研究' },
      { highlight: true, text: '源石' },
      { text: '技術。' },
    ]);
  });
});
