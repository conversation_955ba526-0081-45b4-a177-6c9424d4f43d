import { describe, expect, it } from 'vitest';
import { measureTextWidth, multiEllipsis } from './ellipsis';
import { createCanvas } from '@napi-rs/canvas';

describe('multiEllipsis', () => {
  const font = '14px Arial';
  const maxWidth = 200;
  const maxLines = 2;
  const canvas = createCanvas(400, 400);
  const ctx = canvas.getContext('2d')!;

  it('短文本不需要截段', () => {
    const result = multiEllipsis({
      text: '短文本',
      font,
      maxWidth,
      maxLines,
      canvas,
    });

    const combined = result.map((seg) => seg.content).join('');
    expect(combined).toBe('短文本');
    expect(result.every((seg) => seg.type === 'text')).toBe(true);
  });

  it('长文本需要截断', () => {
    const text = 'ghost_surprised.anim.meta';
    const result = multiEllipsis({
      text,
      font,
      maxWidth: 150,
      maxLines: 1,
      canvas,
      keepLastSegment: 8,
    });
    // 断言省略号存在
    const ellipsisSegment = result.find((seg) => seg.type === 'ellipsis');
    ctx.font = font;
    const actualWidth = measureTextWidth(ctx, text);
    const resText = result.map((seg) => seg.content).join('');
    const resWidth = measureTextWidth(ctx, resText);
    expect(ellipsisSegment).toBeDefined();
    expect(actualWidth).toBeGreaterThan(150);
    expect(resWidth).toBeLessThanOrEqual(150);
  });
  it('多行需要截断', () => {
    const text = '这是一个非常长的文本，需要被截断以适应最大宽度。'.repeat(3);
    const result = multiEllipsis({
      text,
      font,
      maxWidth,
      maxLines: 2,
      canvas,
    });

    // 断言省略号存在
    const ellipsisSegment = result.find((seg) => seg.type === 'ellipsis');
    ctx.font = font;
    const actualWidth = measureTextWidth(ctx, result.map((seg) => seg.content).join(''));
    expect(ellipsisSegment).toBeDefined();
    expect(actualWidth).toBeLessThanOrEqual(maxWidth * 2);
  });
  it('多行但文本不足的情况下不需要截短', () => {
    const text = '这是一个中短文本，应该不用裁';
    const result = multiEllipsis({
      text,
      font,
      maxWidth,
      maxLines: 2,
      canvas,
    });

    const combined = result.map((seg) => seg.content).join('');
    const actualWidth = measureTextWidth(ctx, result.map((seg) => seg.content).join(''));
    expect(combined).toBe('这是一个中短文本，应该不用裁');
    expect(result.every((seg) => seg.type === 'text')).toBe(true);
    expect(actualWidth).toBeLessThanOrEqual(maxWidth * 2);
  });
  it('单行保留最后一段', () => {
    const text = '这是一个非常长的文本，需要被截断以适应最大宽度。';
    const result = multiEllipsis({
      text,
      font,
      maxWidth,
      maxLines: 1,
      keepLastSegment: 3,
      canvas,
    });
    // 断言最后一段被保留
    const lastSegment = result[result.length - 1];
    expect(lastSegment.type).toBe('text');
    expect(lastSegment.content).toBe('宽度。');
  });

  it('多行保留最后一段', () => {
    const text = '这是一个非常长的文本，需要被截断以适应最大宽度。'.repeat(3);
    const result = multiEllipsis({
      text,
      font,
      maxWidth,
      maxLines: 3,
      keepLastSegment: 5,
      canvas,
    });
    // 断言最后一段被保留
    const lastSegment = result[result.length - 1];
    expect(lastSegment.type).toBe('text');
    expect(lastSegment.content).toBe('最大宽度。');
    expect(result.length).toBe(3); // 确保只截断为3段
  });

  it('不返回省略号', () => {
    const text = '这是一个非常长的文本，需要被截断以适应最大宽度。'.repeat(3);
    const result = multiEllipsis({
      text,
      font,
      maxWidth,
      maxLines: 3,
      keepLastSegment: 3,
      insertEllipsis: false,
      canvas,
    });
    // 断言省略号不存在
    const ellipsisSegment = result.find((seg) => seg.type === 'ellipsis');
    expect(ellipsisSegment).toBeUndefined();
    expect(result.every((seg) => seg.type === 'text')).toBe(true);
  });

  it('自定义省略号符号', () => {
    const text = '这是一个非常长的文本，需要被截断以适应最大宽度。';
    const customEllipsis = '--';
    const result = multiEllipsis({
      text,
      font,
      maxWidth,
      maxLines: 1,
      ellipsisSymbol: customEllipsis,
      keepLastSegment: 3,
      canvas,
    });
    // 断言自定义省略号存在
    const ellipsisSegment = result.find((seg) => seg.type === 'ellipsis');
    expect(ellipsisSegment).toBeDefined();
    expect(ellipsisSegment?.content).toBe(customEllipsis);
  });
});
