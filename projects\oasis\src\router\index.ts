import type { App } from 'vue';
import { createRouter, createWebHistory } from 'vue-router';
import { withHashCompatible } from './modules/withHashCompatible.ts';
import { withClassicGuards } from './modules/withClassicGuards.ts';
import { withAuthCheck } from './modules/withAuthCheck.ts';
import { routes } from './routes';
import { withProjectEnter } from './modules/withProjectEnter.ts';
import { withTrack } from './modules/withTrack.ts';
import { withPermission } from './modules/withPermission.ts';
import { withPersistQuery } from './modules/withPersistQuery.ts';

export const router = createRouter({
  history: createWebHistory('/'),
  routes,
  scrollBehavior: () => ({ left: 0, top: 0 }),
});

export function setupRouter(app: App<Element>) {
  app.use(router);

  // with plugins
  withPersistQuery(router);
  withAuth<PERSON><PERSON>ck(router);
  withHashCompatible(router);
  withProjectEnter(router);
  withPermission(router);
  withClassicGuards(router);
  withTrack(router);

  return router;
}
