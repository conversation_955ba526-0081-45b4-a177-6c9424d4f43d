<template>
  <PageWrapper contentBackground :class="prefixCls">
    <div class="m-5 bg-FO-Container-Fill1">
      <div class="mb-5 flex justify-between">
        <ATypographyTitle :level="5">
          光照烘焙任务列表
        </ATypographyTitle>
        <a-button v-if="isSuperAdminOrProjectAdmin" type="primary" @click="handleAdd">
          新增任务
        </a-button>
      </div>

      <div :class="`${prefixCls}__list`">
        <a-collapse v-if="jobList.length" v-model:activeKey="activeKey" ghost>
          <a-collapse-panel
            v-for="job in jobList"
            :key="job.ID?.toString()"
            :showArrow="false"
            :class="`${prefixCls}__list-item`"
          >
            <template #header>
              <div class="w-full flex items-center justify-between">
                <div class="ml-3 flex items-center">
                  <div class="w-200px flex items-center">
                    <ATypographyText :ellipsis="{ tooltip: true }" :content="job.description" />
                    <div
                      v-if="isSuperAdminOrProjectAdmin"
                      :class="`${prefixCls}__hover-icon`"
                    >
                      <a-button
                        type="text"
                        class="!px-2 !c-FO-Brand-Primary-Default"
                        title="编辑任务"
                        @click.stop="handleEdit(job)"
                      >
                        <Icon icon="clarity:note-edit-line" />
                      </a-button>
                      <APopconfirm
                        placement="right"
                        title="确定要删除该任务吗？"
                        @confirm="handleDelete(job)"
                      >
                        <a-button type="text" danger class="!px-2" title="删除任务" @click.stop>
                          <Icon icon="clarity:trash-line" />
                        </a-button>
                      </APopconfirm>
                    </div>
                  </div>
                  <div class="ml-6 mr-3">
                    <span class="c-FO-Content-Text2">状态:</span>
                    <ATag :color="getJobStatus(job.ID!).tagColor" class="!ml-1">
                      <Icon
                        :icon="getJobStatus(job.ID!).icon"
                        :size="13"
                        :spin="!!getJobStatus(job.ID!).spin"
                      />
                      {{ getJobStatus(job.ID!).name }}
                    </ATag>
                  </div>
                  <div class="c-FO-Content-Text2">
                    {{ getCurLastBuildMsg(job.ID!) }}
                  </div>
                  <a-button
                    v-if="getCurLastBuild(job.ID!)"
                    type="text"
                    class="!c-FO-Brand-Primary-Default"
                    shape="round"
                    @click.stop="openBuildLogModal(job.ID!)"
                  >
                    执行记录
                  </a-button>
                </div>
                <div>
                  <a-button
                    v-if="
                      activeKey?.includes(job.ID!.toString())
                        && !['RUNNING', 'ENQUEUE'].includes(getCurLastBuild(job.ID!)?.status || '')
                    "
                    type="text"
                    class="!c-FO-Functional-Success1-Default"
                    shape="round"
                    @click.stop="startTask(job)"
                  >
                    <Icon icon="clarity:play-line" :size="13" />开始执行
                  </a-button>
                </div>
              </div>
            </template>
            <ParamsListPage :jobID="job.ID" @change="handleSubmitParamChange" />
          </a-collapse-panel>
        </a-collapse>
        <AEmpty v-else :image="emptyImg" description="暂无任务" />
        <APagination
          v-if="false"
          v-model:current="pagination.page"
          v-model:pageSize="pagination.pageSize"
          class="flex justify-end !mt-6"
          size="small"
          :total="pagination.totalNum"
          showSizeChanger
          :pageSizeOptions="['10', '20', '50', '100']"
          :showTotal="(total) => `共 ${total} 条数据`"
          @change="handlePageChange"
        />
      </div>
    </div>
    <TaskModal @register="registerModal" @success="handleSuccess" />
    <BuildLogModal @register="registerLogModal" />
  </PageWrapper>
</template>

<script lang="ts" setup name="JenkinsAutoTask">
import {
  Empty as AEmpty,
  Pagination as APagination,
  Popconfirm as APopconfirm,
  Tag as ATag,
  TypographyText as ATypographyText,
  TypographyTitle as ATypographyTitle,
} from 'ant-design-vue';
import { map } from 'lodash-es';
import { onBeforeMount, ref, watch } from 'vue';
import { useRequest } from 'vue-request';
import BuildLogModal from './BuildLogModal.vue';
import TaskModal from './TaskModal.vue';
import { useJenkinsBuildState } from './hook';
import ParamsListPage from './params/index.vue';
import {
  addJobBuild,
  deleteJob,
  getJobBuildsListByPage,
  getJobsListByPage,
} from '/@/api/page/jenkins';
import type { JobBuildsListItem, JobsListItem } from '/@/api/page/model/jenkinsModel';
import type { TableFormType } from '/@/components/Form';
import Icon from '/@/components/Icon';
import { useModal } from '/@/components/Modal';
import { PageWrapper } from '/@/components/Page';
import { useDesign } from '/@/hooks/web/useDesign';
import { useUserStoreWithOut } from '/@/store/modules/user';
import { formatTISOToDate } from '/@/utils/dateUtil';
import { useAdmin } from '../../../../hooks/useProjects.ts';

const { prefixCls } = useDesign('jenkins-auto-task');
const emptyImg = AEmpty.PRESENTED_IMAGE_SIMPLE;
const userStore = useUserStoreWithOut();
const [registerModal, { openModal }] = useModal();
const [registerLogModal, { openModal: openLogModal }] = useModal();

const jobList = ref<JobsListItem[]>([]);
const buildList = ref<JobBuildsListItem[]>([]);
const activeKey = ref<string[]>([]);
const submitParam = ref<Recordable[]>([]);
const needLoadBuildList = ref(true);
const { isSuperAdminOrProjectAdmin } = useAdmin();

const pagination = ref({
  page: 1,
  pageSize: 999,
  totalNum: 0,
});

async function getList(ID?: number) {
  const { page, pageSize } = pagination.value;
  const { list, total } = await getJobsListByPage(userStore.getProjectId, {
    page,
    pageSize,
    jobPurpose: 1,
  });
  pagination.value.totalNum = total || 0;
  jobList.value = list || [];
  // 默认展开第一个
  const validId = ID || list?.[0]?.ID;
  activeKey.value = validId ? [validId.toString()] : [];
}

async function getBuildList() {
  const { list } = await getJobBuildsListByPage(userStore.getProjectId, {
    page: 1,
    pageSize: 999,
    jobPurpose: 1,
  });
  buildList.value = list || [];
  // 如果有正在执行的任务, 则需要定时获取任务状态
  const statusList = map(buildList.value, 'status');
  needLoadBuildList.value = statusList.includes('RUNNING') || statusList.includes('ENQUEUE');
}

// 每5秒获取一次任务执行状态, 页面隐藏时不执行
const { run } = useRequest(getBuildList, {
  pollingInterval: 5000,
  manual: true,
  ready: needLoadBuildList,
});

onBeforeMount(() => {
  getList();
  run();
});

function getCurBuildList(ID: number) {
  return buildList.value.filter((item) => item.jobID === ID);
}

// 获取当前任务的最后一次执行记录
const getCurLastBuild = (ID: number) => getCurBuildList(ID)?.[0];

function getJobStatus(ID: number) {
  const curBuild = getCurLastBuild(ID);
  const { getBuildStateModel } = useJenkinsBuildState(curBuild?.status || '');

  return getBuildStateModel();
}

function getCurLastBuildMsg(ID: number) {
  const curBuild = getCurLastBuild(ID);
  if (!curBuild) {
    return '';
  }
  if (curBuild.status === 'RUNNING') {
    return `执行人：${curBuild.trigger?.nickName}，执行时间：${formatTISOToDate(
      curBuild.CreatedAt,
      true,
      true,
    )}`;
  } else {
    return `上次执行: ${formatTISOToDate(curBuild.CreatedAt, true, true)}`;
  }
}
function handleAdd() {
  openModal(true, {
    isUpdate: false,
  });
}

function handleEdit(record: JobsListItem) {
  openModal(true, {
    record,
    isUpdate: true,
  });
}

async function handleDelete(record: JobsListItem) {
  await deleteJob(userStore.getProjectId, record.ID!);
  await handleSuccess();
}

async function handleSuccess(type?: TableFormType, ID?: number) {
  switch (type) {
    case 'add':
      await getList(ID);
      break;
    case 'edit':
      await getList(ID);
      break;
    default:
      await getList();
      break;
  }
}

// 分页处理
function handlePageChange(p: number, size: number) {
  pagination.value.page = p;
  pagination.value.pageSize = size;
  handleSuccess();
}

function openBuildLogModal(ID: number) {
  openLogModal(true, {
    buildLogList: getCurBuildList(ID),
  });
}
function handleSubmitParamChange(param: Recordable, jobID: number) {
  // 遍历param,如果value为数组,则转换为字符串
  submitParam.value[jobID] = Object.keys(param).reduce((acc, cur) => {
    if (Array.isArray(param[cur])) {
      acc[cur] = param[cur].join(';');
    } else {
      acc[cur] = param[cur];
    }
    return acc;
  }, {});
}

async function startTask(record: JobsListItem) {
  await addJobBuild(userStore.getProjectId, {
    jobID: record.ID!,
    params: submitParam.value[record.ID!],
  });
  needLoadBuildList.value = true;
  run();
}

watch(
  () => userStore.getProjectId,
  (v, oldValue) => {
    if (v && v !== oldValue) {
      pagination.value.page = 1;
      getList();
    }
  },
);
</script>

<style lang="less">
@prefix-cls: ~'hypergryph-jenkins-auto-task';
.@{prefix-cls} {
  &__list {
    & .ant-collapse {
      border: none !important;
    }

    &-item {
      margin-bottom: 10px;
      overflow: hidden;
      border: none !important;
      border-radius: 8px !important;

      & .ant-collapse-header {
        background-color: @table-border-color;
      }

      & .ant-collapse-content {
        background-color: @FO-Container-Background !important;
      }

      &:hover {
        & .@{prefix-cls}__hover-icon {
          opacity: 1;
        }
      }
    }
  }

  &__hover-icon {
    display: inline-block;
    width: 70px;
    transition: all 0.3s ease-in-out;
    opacity: 0;
  }
}
</style>
