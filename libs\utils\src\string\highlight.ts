import { safeToArray } from '../collections/array';

// 预处理关键词 - 过滤并标准化
function prepareKeywords(keywordsInput: string | string[] | undefined, caseSensitive: boolean): string[] {
  return safeToArray(keywordsInput)
    .map((kw) => (kw ? String(kw) : ''))
    .filter((kw) => kw.length > 0)
    .map((kw) => (caseSensitive ? kw : kw.toLowerCase()));
}

interface MatchResult {
  start: number; // 匹配开始位置
  end: number; // 匹配结束位置
  text: string; // 匹配的文本
}

// 查找所有匹配项
function findAllMatches(
  rawText: string,
  keywords: string[],
  caseSensitive: boolean,
  transformFunc?: (char: string) => string | string[],
): Array<MatchResult> {
  return transformFunc
    ? findMatchesWithTransform(rawText, keywords, caseSensitive, transformFunc)
    : findMatchesStandard(rawText, keywords, caseSensitive);
}

// 标准匹配（无转换函数）
function findMatchesStandard(
  rawText: string,
  keywords: string[],
  caseSensitive: boolean,
): Array<MatchResult> {
  const matches: Array<MatchResult> = [];
  const textToSearchOn = caseSensitive ? rawText : rawText.toLowerCase();

  // 过滤掉空关键词并规范化
  const normalizedKeywords = keywords
    .filter((kw) => kw.length > 0)
    .map((kw) => (caseSensitive ? kw : kw.toLowerCase()));

  if (normalizedKeywords.length === 0 || textToSearchOn.length === 0) {
    return matches;
  }

  // 按关键词长度降序排列，优先匹配更长的关键词
  normalizedKeywords.sort((a, b) => b.length - a.length);

  // 创建一个字典，按第一个字符索引关键词，加速查找过程
  const keywordsByFirstChar: Record<string, string[]> = {};
  for (const kw of normalizedKeywords) {
    const firstChar = kw[0];
    if (!keywordsByFirstChar[firstChar]) {
      keywordsByFirstChar[firstChar] = [];
    }
    keywordsByFirstChar[firstChar].push(kw);
  }

  // 单次扫描文本
  for (let i = 0; i < textToSearchOn.length; i++) {
    const char = textToSearchOn[i];
    const possibleKeywords = keywordsByFirstChar[char];

    // 如果当前字符没有可能的关键词，跳过
    if (!possibleKeywords) {
      continue;
    }

    // 检查每个可能的关键词
    for (const keyword of possibleKeywords) {
      if (i + keyword.length <= textToSearchOn.length) {
        // 只比较需要的子串，避免不必要的字符串生成
        let isMatch = true;
        for (let j = 0; j < keyword.length; j++) {
          if (textToSearchOn[i + j] !== keyword[j]) {
            isMatch = false;
            break;
          }
        }

        if (isMatch) {
          matches.push({
            start: i,
            end: i + keyword.length,
            text: rawText.slice(i, i + keyword.length),
          });
          break; // 找到最长匹配后不再继续查找
        }
      }
    }
  }

  return matches;
}

// 带转换函数的匹配
function findMatchesWithTransform(
  rawText: string,
  keywords: string[],
  caseSensitive: boolean,
  transformFunc: (char: string) => string | string[],
): Array<MatchResult> {
  const matches: Array<MatchResult> = [];

  // 过滤空关键词
  const filteredKeywords = keywords.filter((kw) => kw.length > 0);
  if (filteredKeywords.length === 0 || rawText.length === 0) {
    return matches;
  }

  // 标准化关键词
  const normalizedKeywords = filteredKeywords.map((kw) =>
    (caseSensitive ? kw : kw.toLowerCase()),
  );

  // 缓存转换结果，避免重复计算
  const transformCache = new Map<string, string[]>();

  // 预处理所有字符的转换结果
  const transformedChars: string[][] = Array.from({ length: rawText.length });
  for (let i = 0; i < rawText.length; i++) {
    const char = rawText[i];
    if (!transformCache.has(char)) {
      const transformed = safeToArray(transformFunc(char))
        .map((part) => (caseSensitive ? part : part.toLowerCase()));
      transformCache.set(char, transformed);
    }
    transformedChars[i] = transformCache.get(char)!;
  }

  // 优化的匹配过程
  for (const keyword of normalizedKeywords) {
    // 跳过开始位置可能不存在字符（转换为空）的情况
    for (let startIdx = 0; startIdx < rawText.length; startIdx++) {
      const startTransformed = transformedChars[startIdx];

      // 跳过空转换结果
      if (startTransformed.length === 0 || startTransformed.every((p) => p.length === 0)) {
        continue;
      }

      // 尝试匹配当前关键词
      const matchResult = tryMatchWithCache(
        rawText,
        startIdx,
        keyword,
        transformedChars,
      );

      if (matchResult) {
        matches.push({
          start: startIdx,
          end: matchResult.endIdx,
          text: rawText.slice(startIdx, matchResult.endIdx),
        });
      }
    }
  }

  return matches;
}

// 使用缓存的字符转换结果进行匹配
function tryMatchWithCache(
  rawText: string,
  startIdx: number,
  keyword: string,
  transformedChars: string[][],
): { endIdx: number } | null {
  let kwIdx = 0; // 关键词指针
  let currIdx = startIdx; // 原文指针

  // 向前查找开始位置的有效字符（跳过空转换）
  while (currIdx > 0) {
    const prevTransformed = transformedChars[currIdx - 1];
    if (prevTransformed.length === 0 || prevTransformed.every((p) => p.length === 0)) {
      currIdx--;
    } else {
      break;
    }
  }

  // 匹配过程
  while (kwIdx < keyword.length && currIdx < rawText.length) {
    const transformed = transformedChars[currIdx];

    // 如果转换结果为空，跳过
    if (transformed.length === 0) {
      currIdx++;
      continue;
    }

    // 检查是否有任何转换结果能够匹配
    let matched = false;
    for (const part of transformed) {
      if (part.length === 0) {
        matched = true;
        break;
      }

      if (keyword.startsWith(part, kwIdx)) {
        kwIdx += part.length;
        matched = true;
        break;
      }
    }

    if (!matched) {
      return null;
    }
    currIdx++;
  }

  // 关键词成功匹配完整
  if (kwIdx >= keyword.length) {
    // 检查后续可跳过字符
    let endIdx = currIdx;
    while (endIdx < rawText.length) {
      const parts = transformedChars[endIdx];
      if (parts.length === 0 || parts.every((p) => p.length === 0)) {
        endIdx++;
      } else {
        break;
      }
    }
    return { endIdx };
  }

  return null;
}

export interface HighlightSegment {
  text: string; // 文本片段
  highlight?: boolean; // 是否高亮
}

/**
 * 将文本按照高亮词分段，优先匹配最靠前的、最长的词。
 * @returns 分段后的文本，每段都包含是否高亮的信息
 */
export function toHighlightSegments(
  rawText: string, // 原始文本
  keywordsInput?: string | string[], // 需要高亮的词或词数组
  options?: {
    /**
     * 字符转换函数，用于匹配时将原字符转换为其他字符。
     * 匹配时使用转换后的字符，但输出结果保留原始字符。
     */
    transform?: (char: string) => string | string[];
    /**
     * 是否大小写敏感。
     * @default false - 默认不区分大小写。
     */
    caseSensitive?: boolean;
  },
): HighlightSegment[] {
  // 如果原始文本为空，直接返回空数组
  if (!rawText) {
    return [];
  }

  const caseSensitive = options?.caseSensitive ?? false;
  const transformFunc = options?.transform;

  // 预处理关键词
  const keywords = prepareKeywords(keywordsInput, caseSensitive);

  // 如果没有有效的高亮词，直接返回原文
  if (!keywords.length) {
    return [{ text: rawText }];
  }

  // 查找所有匹配项
  const allMatches = findAllMatches(rawText, keywords, caseSensitive, transformFunc);

  // 如果没有找到匹配项，返回原文
  if (!allMatches.length) {
    return [{ text: rawText }];
  }

  // 排序：优先匹配靠前的、更长的词
  allMatches.sort((a, b) => {
    if (a.start !== b.start) {
      return a.start - b.start;
    }
    return b.end - a.end; // 结束位置大的（即更长的匹配）排在前面
  });

  const segments: HighlightSegment[] = [];
  let lastEnd = 0;

  for (const match of allMatches) {
    // 只处理不重叠的匹配
    if (match.start >= lastEnd) {
      // 添加非高亮部分
      if (match.start > lastEnd) {
        segments.push({ text: rawText.slice(lastEnd, match.start) });
      }
      // 添加高亮部分
      segments.push({ text: match.text, highlight: true });
      lastEnd = match.end;
    }
  }

  // 添加最后一段非高亮文本
  if (lastEnd < rawText.length) {
    segments.push({ text: rawText.slice(lastEnd) });
  }

  return segments;
}
