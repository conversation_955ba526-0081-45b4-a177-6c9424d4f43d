<template>
  <div class="flex items-center gap-8px">
    <Input
      v-model:value="formValue"
      size="small"
      placeholder="输入分类名称"
      class="w-380px"
      :maxlength="20"
      autofocus
    >
      <template #suffix>
        {{ formValue?.length || 0 }}/20
      </template>
    </Input>

    <div class="flex items-center">
      <Button size="small" type="text" class="!bg-transparent !px-8px" @click="() => onFinished(true)">
        <span class="FO-Font-R12">
          取消
        </span>
      </Button>
      <Button :loading="loading" size="small" type="link" class="!px-8px" @click="handleSave">
        <span class="FO-Font-R12">
          {{ loading ? '保存中...' : '保存' }}
        </span>
      </Button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { Button, Input } from 'ant-design-vue';
import { ref } from 'vue';
import { useLatestPromise } from '@hg-tech/utils-vue';

const props = defineProps<{
  initValue?: string;
  sentReq: (name?: string) => Promise<unknown>;
  onFinished: (cancelled: boolean) => void;
}>();

const formValue = ref(props.initValue || '');
const { execute, loading } = useLatestPromise(props.sentReq);

async function handleSave() {
  try {
    const result = await execute(formValue.value);
    if (result) {
      props.onFinished(false);
    }
  } catch (error) {
    console.error('保存失败:', error);
  }
}
</script>
