import { createRequestService } from '@hg-tech/oasis-common';
import { store } from '../store/pinia.ts';
import { useUserAuthStore } from '../store/modules/userAuth.ts';
import { useRouteNavigationStore } from '../store/modules/routeNavigation.ts';
import { UrlParamSplitter } from '@hg-tech/request-api';

const authStore = useUserAuthStore(store);
const routeNavigationStore = useRouteNavigationStore(store);

export const apiService = createRequestService(
  {
    authTokens: [
      {
        accessTokenKey: 'Access-Token',
        getAccessToken: () => authStore.userAuthInfo?.privateToken,
      },
      {
        accessTokenKey: 'X-Token',
        getAccessToken: () => authStore.userAuthInfo?.accessToken,
        newTokenKey: 'new-token',
        setNewToken: authStore.userAuthInfo?.setAccessToken,
      },
    ],
    onUnauthorized() {
      routeNavigationStore.onUnauthorized();
    },
    onForbidden() {
      routeNavigationStore.onForbidden();
    },
  },
  {
    baseURL: import.meta.env.VITE_BASE_API_ORIGIN,
  },
  {
    urlTemplateSplitter: UrlParamSplitter.Brace,
  },
);
