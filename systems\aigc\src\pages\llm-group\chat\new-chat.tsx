import { DsChatAvatar } from '@/common/components/svg-icons';
import { NIcon, NImage, NText } from 'naive-ui';
import { type PropType, computed, defineComponent } from 'vue';
import { ChatInput } from './components/chat-input';
import ChatAssistant from '@/assets/images/chat-assistant.png';

const NewChat = defineComponent({
  props: {
    currentModel: {
      type: String as PropType<string>,
      default: '',
    },
    isDeepThinkActive: {
      type: Boolean as PropType<boolean>,
      default: false,
    },
    isOnlineSearchActive: {
      type: Boolean as PropType<boolean>,
      default: false,
    },
    chatLoading: {
      type: Boolean as PropType<boolean>,
      default: false,
    },
    onStopChat: {
      type: Function as PropType<() => void>,
      default: () => {},

    },
    onSendMessage: {
      type: Function as PropType<(data: { message: string }) => void>,
      default: () => {},
    },
  },
  emits: ['update:currentModel', 'update:isDeepThinkActive', 'update:isOnlineSearchActive'],
  setup(props, { emit }) {
    const currentModel = computed({
      get: () => props.currentModel,
      set: (model) => {
        emit('update:currentModel', model);
      },
    });
    const isDeepThinkActive = computed({
      get: () => props.isDeepThinkActive,
      set: (isActive) => {
        emit('update:isDeepThinkActive', isActive);
      },
    });
    const isOnlineSearchActive = computed({
      get: () => props.isOnlineSearchActive,
      set: (isActive) => {
        emit('update:isOnlineSearchActive', isActive);
      },
    });

    return () => (
      <div class="chat-container mx-auto w-full flex-1 flex-col transform-translate-z-0 px-20px flex-c-center lg:max-w-864px">
        <div class="welcome-tip pos-relative mb-24px w-417px px-24px py-12px flex-c-center">
          <div class="mr-12px">
            <NIcon size={80}>
              <DsChatAvatar class="c-#4e4e4e" />
            </NIcon>
          </div>
          <div class="flex-1">
            <NText class="block font-size-[30px] font-600 line-height-[42px]">AI 对话助手来啦！</NText>
            <NText class="block font-size-[16px] font-400 line-height-[33px]">今天想和我聊什么呢？</NText>
          </div>
          <NImage class="pos-absolute right-[-8px] top-10px" height={40} previewDisabled src={ChatAssistant} width={40} />
        </div>
        <ChatInput
          class="max-w-744px w-full flex-c-center"
          loading={props.chatLoading}
          onCancel={props.onStopChat}
          onSubmit={(data) => props.onSendMessage?.({
            message: data.message,
          })}
          showDeepThink
          showOnlineSearch
          v-model:currentModel={currentModel.value}
          v-model:deepThink={isDeepThinkActive.value}
          v-model:onlineSearch={isOnlineSearchActive.value}
        />
      </div>
    );
  },
});

export {
  NewChat,
};
