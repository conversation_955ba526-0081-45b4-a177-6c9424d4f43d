import type { BasicPageParams } from '../model/baseModel';
import type { NavigationsListGetResultModel, NavigationsListItem } from './model/navigtionModel';
import { defHttp } from '/@/utils/http/axios';

/**
 * 获取项目群聊列表
 * @param projectID 项目id
 * @param params 筛选条件
 */
export function getFeishuGroupChatsListByPage(projectID: number, params?: BasicPageParams) {
  return defHttp.get<NavigationsListGetResultModel>({
    url: `/api/v1/projects/${projectID}/toolbox/invite_chats`,
    params,
  });
}

/**
 * 添加项目群聊列表
 * @param projectID 项目id
 * @param params 筛选条件
 */
export function addFeishuGroupChatsList(projectID: number, params?: NavigationsListItem) {
  return defHttp.post<null>({
    url: `/api/v1/projects/${projectID}/toolbox/invite_chats`,
    params,
  });
}

/**
 * 编辑项目群聊列表
 * @param projectID 项目id
 * @param params 筛选条件
 */
export function editFeishuGroupChatsList(projectID: number, params?: NavigationsListItem, editId?: number) {
  return defHttp.put<null>({
    url: `/api/v1/projects/${projectID}/toolbox/invite_chats/${editId}`,
    params,
  });
}

/**
 * 删除项目群聊列表
 * @param projectID 项目id
 * @param editId 项目群聊id
 */
export function deleteFeishuGroupChatsList(projectID: number, editId: number) {
  return defHttp.delete<null>({
    url: `/api/v1/projects/${projectID}/toolbox/invite_chats/${editId}`,
  });
}

/**
 * 批量更新项目群聊排序
 * @param projectID 项目id
 * @param idList 项目群聊id列表
 */
export function sortFeishuGroupChatsList(projectID: number, idList: number[]) {
  return defHttp.put<null>({
    url: `/api/v1/projects/${projectID}/toolbox/invite_chats/sort`,
    params: {
      idList,
    },
  });
}

/**
 * 获取工具反馈群聊列表
 * @param params 筛选条件
 * @param params.page 页码
 * @param params.pageSize 页大小
 */
export function getFeishuFeedbackChatsListByPage(params?: BasicPageParams) {
  return defHttp.get<NavigationsListGetResultModel>({
    url: `/api/v1/feedback_chats`,
    params,
  });
}

/**
 * 创建工具反馈群聊
 * @param params 筛选条件
 */
export function addFeishuFeedbackChats(params?: NavigationsListItem) {
  return defHttp.post<null>({
    url: `/api/v1/feedback_chats`,
    params,
  });
}

/**
 * 更新工具反馈群聊
 * @param feedbackChatsID 工具反馈群聊id
 * @param params 筛选条件
 */
export function updateFeishuFeedbackChats(feedbackChatsID: number, params?: NavigationsListItem) {
  return defHttp.put<null>({
    url: `/api/v1/feedback_chats/${feedbackChatsID}`,
    params,
  });
}

/**
 * 更新工具反馈群聊
 * @param feedbackChatsID 工具反馈群聊id
 */
export function deleteFeishuFeedbackChats(feedbackChatsID: number) {
  return defHttp.delete<null>({
    url: `/api/v1/feedback_chats/${feedbackChatsID}`,
  });
}

/**
 * 批量更新工具反馈群聊排序
 * @param idList 工具反馈群聊id列表
 */

export function sortFeishuFeedbackChatsList(idList: number[]) {
  return defHttp.put<null>({
    url: `/api/v1/feedback_chats/sort`,
    params: {
      idList,
    },
  });
}
