import { defineViteConfigProjectReact, getPreviewBasePath } from '@hg-tech/configs';
import process from 'node:process';
import UnoCSS from 'unocss/vite';
import { sentryVitePlugin } from '@sentry/vite-plugin';

// https://vitejs.dev/config/
export default defineViteConfigProjectReact(({ mode }) => {
  const isRndMode = mode === 'rnd';
  const isPreMode = mode === 'pre';
  const isProdMode = mode === 'production';

  const sentryAuthToken = process.env.SENTRY_AUTH_TOKEN;

  if (!sentryAuthToken) {
    console.log('SENTRY_AUTH_TOKEN not found！');
  }

  const enableSentry = Boolean(sentryAuthToken);
  if (enableSentry) {
    console.log('Sentry enabled！');
    process.env.VITE_ENABLE_SENTRY = 'true';
  }

  return ({
    base: getPreviewBasePath({
      mode,
      projectName: 'webhook-management',
    }),
    build: {
      sourcemap: enableSentry,
    },
    plugins: [
      UnoCSS(),
      ...[enableSentry
        ? sentryVitePlugin({
          authToken: sentryAuthToken,
          org: 'hypergryph',
          project: 'webhook',
          url: 'https://sentry.hypergryph.com/',
          sourcemaps: {
            filesToDeleteAfterUpload: isProdMode ? '*.js.map' : undefined,
          },
        })
        : []],
    ],
    server: {
      host: true,
      port: 8576,
      proxy: {
        '/api': {
          target: process.env.VITE_API_DEV_PROXY_SERVER,
          secure: false,
          changeOrigin: true,
        },
      },
    },
  });
}, {
  analyze: process.argv.includes('--analyze'),
});
