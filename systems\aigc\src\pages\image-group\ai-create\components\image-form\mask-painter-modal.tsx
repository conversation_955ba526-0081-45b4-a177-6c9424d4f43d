import type {
  UploadFileInfo,
} from 'naive-ui';
import type {
  PropType,
} from 'vue';
import type {
  IEditorContext,
  IEditorDrawPlugin,
} from '@/common/components/editor';
import {
  NButton,
  NButtonGroup,
  NIcon,
  NInputNumber,
  NSlider,
  NTooltip,
  useMessage,
} from 'naive-ui';
import {
  computed,
  defineComponent,
  onBeforeUnmount,
  onMounted,
  ref,
} from 'vue';

import {
  Editor,
  EditorAction,
  EditorDrawPlugin,
} from '@/common/components/editor';
import {
  ArrowBack,
  ArrowForward,
  BrushFreehand,
  DragDrop,
  Eraser20Regular,
  Pencil,
  ScaleFit16Regular,
} from '@/common/components/svg-icons';
import { useAppTheme } from '@/common/hooks';

const MaskPainterModal = defineComponent({
  props: {
    image: {
      type: Object as PropType<UploadFileInfo>,
      required: true,
    },
  },
  emits: ['confirm', 'cancel'],
  setup(props, { emit }) {
    const editorRef = ref<
      IEditorContext<{
        editorDrawPlugin: IEditorDrawPlugin;
      }>
    >();
    const editorDrawPlugin = computed(
      () => editorRef.value?.plugins.editorDrawPlugin,
    );
    const editorEventPlugin = computed(
      () => editorRef.value?.plugins.eventPlugin,
    );
    const editorInstancePlugin = computed(
      () => editorRef.value?.plugins.instancePlugin,
    );
    const editorIns = computed(() => editorRef.value?.editor);
    const { currentTheme } = useAppTheme();
    const message = useMessage();

    onMounted(async () => {
      if (props.image) {
        await editorInstancePlugin.value?.setBaseImage(props.image);
        editorDrawPlugin.value?.startDrawMask();
      }
    });

    onBeforeUnmount(() => {
      editorInstancePlugin.value?.removeBaseImage();
      editorDrawPlugin.value?.stopDrawMask();
    });

    const onHandleMask = async () => {
      const url = editorDrawPlugin.value?.exportMask();
      emit('confirm', {
        id: 'mask',
        name: `MASK-${Date.now()}`,
        status: 'finished',
        url,
        thumbnailUrl: url,
      });
    };

    return () => (
      <div class="modal-content">
        <div class="mb-12px flex-c-between">
          <NButtonGroup
            class="overflow-hidden bg-FO-Container-Fill1"
          >
            <NTooltip>
              {{
                default: () => <>拖拽模式</>,
                trigger: () => (
                  <NButton
                    onClick={() =>
                      editorEventPlugin.value?.switchAction(EditorAction.Mover)}
                    tertiary
                    type={
                      editorEventPlugin.value?.currentAction === EditorAction.Mover
                        ? 'primary'
                        : 'default'
                    }
                  >
                    <NIcon size={20}>
                      <DragDrop class="cursor-pointer" />
                      ,
                    </NIcon>
                  </NButton>
                ),
              }}
            </NTooltip>
            <NTooltip>
              {{
                default: () => <>绘制模式</>,
                trigger: () => (
                  <NButton
                    onClick={() =>
                      editorEventPlugin.value?.switchAction(
                        EditorAction.DrawMask,
                      )}
                    tertiary
                    type={
                      editorEventPlugin.value?.currentAction === EditorAction.DrawMask
                        ? 'primary'
                        : 'default'
                    }
                  >
                    <NIcon size={20}>
                      <Pencil class="cursor-pointer" />
                      ,
                    </NIcon>
                  </NButton>
                ),
              }}
            </NTooltip>
            <NTooltip>
              {{
                default: () => <>擦除模式</>,
                trigger: () => (
                  <NButton
                    onClick={() =>
                      editorEventPlugin.value?.switchAction(
                        EditorAction.DrawMaskErase,
                      )}
                    tertiary
                    type={
                      editorEventPlugin.value?.currentAction === EditorAction.DrawMaskErase
                        ? 'primary'
                        : 'default'
                    }
                  >
                    <NIcon size={20}>
                      <Eraser20Regular class="cursor-pointer" />
                      ,
                    </NIcon>
                  </NButton>
                ),
              }}
            </NTooltip>

            <NTooltip>
              {{
                default: () => <>适配画布</>,
                trigger: () => (
                  <NButton
                    onClick={() => editorInstancePlugin.value?.imageFitKonva()}
                    tertiary
                  >
                    <NIcon size={20}>
                      <ScaleFit16Regular class="cursor-pointer" />
                      ,
                    </NIcon>
                  </NButton>
                ),
              }}
            </NTooltip>
            <NTooltip>
              {{
                default: () => <>撤销（Ctrl+Z）</>,
                trigger: () => (
                  <NButton
                    disabled={editorIns.value?.history?.isUndoEmpty()}
                    onClick={() => editorIns.value?.history?.undo()}
                    tertiary
                  >
                    <NIcon size={20}>
                      <ArrowBack class="cursor-pointer" />
                      ,
                    </NIcon>
                  </NButton>
                ),
              }}
            </NTooltip>
            <NTooltip>
              {{
                default: () => <>前进（Ctrl+Shift+Z）</>,
                trigger: () => (
                  <NButton
                    disabled={editorIns.value?.history?.isRedoEmpty()}
                    onClick={() => editorIns.value?.history?.redo()}
                    tertiary
                  >
                    <NIcon size={20}>
                      <ArrowForward class="cursor-pointer" />
                      ,
                    </NIcon>
                  </NButton>
                ),
              }}
            </NTooltip>
          </NButtonGroup>
          <div class="flex overflow-hidden">
            <NTooltip placement="bottom" trigger="click">
              {{
                default: () => (
                  <NSlider
                    class="w-150px"
                    max={100}
                    min={30}
                    onUpdate:value={editorDrawPlugin.value?.setBrushSize}
                    step={5}
                    value={editorDrawPlugin.value?.brushSize ?? 30}
                  />
                ),
                trigger: () => (
                  <NTooltip class="">
                    {{
                      default: () => <>调整笔刷大小</>,
                      trigger: () => (
                        <NButton tertiary>
                          <NIcon size={20}>
                            <BrushFreehand class="cursor-pointer" />
                            ,
                          </NIcon>
                        </NButton>
                      ),
                    }}
                  </NTooltip>
                ),
              }}
            </NTooltip>
            <NTooltip>
              {{
                default: () => (
                  <>
                    缩放范围在1-300%之间，可以操作
                    {' '}
                    <strong>滚轮</strong>
                    {' '}
                    进行缩放
                  </>
                ),
                trigger: () => (
                  <NInputNumber
                    button-placement="both"
                    class="w-120px bg-FO-Container-Fill1 bg-op-65 text-center"
                    format={(val: number | null) =>
                      `${Number(val?.toFixed(2))}%`}
                    max={300}
                    min={1}
                    onUpdateValue={(num: number | null) =>
                      editorInstancePlugin.value?.setImageRatio(num! / 100 || 0)}
                    parse={(val: string) => Number(val.replace('%', ''))}
                    step={1}
                    value={(editorInstancePlugin.value?.radio ?? 0) * 100}
                  />
                ),
              }}
            </NTooltip>
          </div>
        </div>
        <div class="editor-wrapper mb-12px w-full">
          <Editor
            class="h-600px w-full"
            onMessage={(msgInfo) =>
              message.create(msgInfo.msg, { type: 'warning' })}
            ref={editorRef}
          >
            <EditorDrawPlugin />
          </Editor>
        </div>
        <div class="modal-footer flex items-center justify-end">
          <NButton class="mr-8px" onClick={() => emit('cancel')}>
            取消
          </NButton>
          <NButton onClick={onHandleMask} type="primary">
            应用
          </NButton>
        </div>
      </div>
    );
  },
});

export { MaskPainterModal };
