@import (reference) '@hg-tech/forgeon-style/vars.less';

.forgeonSider {
  display: flex;
  height: 100vh;

  .modulesWrapper {
    width: 72px;
    padding: 16px 0;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    background-color: @FO-Container-Fill1;
    border-right: 1px solid @FO-Container-Stroke1;

    .icon {
      height: 32px;
      width: 32px;
      background: #fff;
      border-radius: 8px;
    }

    .modulesContainer {
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 12px;
    }

    .moduleItem {
      width: 56px;
      height: 56px;
      border-radius: 8px;
      font-size: 12px;
      transition: all 0.3s;
      user-select: none;
      color: @FO-Content-Text3;

      .moduleIcon {
        height: 24px;
        width: 24px;
        font-size: 24px;
        margin-bottom: 2px;
        display: flex;
        align-items: center;
        justify-content: center;
        color: @FO-Content-Icon3;
      }

      &:hover {
        background-color: @FO-Container-Fill2;
        transition: all 0.3s;
      }
      &.active {
        transition: all 0.3s;
        color: @FO-Content-Text1;
      }
    }
  }

  .avatarWrapper {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 4px 12px;

    .avatar {
      width: 32px;
      height: 32px;
      border-radius: 50%;
      overflow: hidden;
      img {
        width: 100%;
        height: 100%;
      }
    }
  }

  .forgeonSubSider {
    width: 224px;
    padding: 19px 8px;
    background-color: @FO-Container-Fill1;
    overflow: scroll;
    border-right: 1px solid @FO-Container-Stroke1;
    height: 100%;
    scrollbar-width: none;
    ::-webkit-scrollbar {
      width: 0;
      height: 0;
      display: none;
    }
    .subSiderHeader {
      font-size: 18px;
      font-weight: 500;
      line-height: 32px;
      margin-bottom: 15px;
      user-select: none;
    }
    .subSiderHeaderIcon {
      box-sizing: content-box;
      height: 20px;
      width: 20px;
      padding: 2px;
      color: @FO-Content-Icon1;
      &:hover {
        background-color: @FO-Container-Fill2;
        border-radius: 4px;
      }
    }
  }

  .menuPopover {
    padding-left: 2px;
    :global {
      .ant-popover-inner {
        padding: 4px;
        width: 260px;
        border-radius: 8px;
        background-color: @FO-Container-Fill1;
      }
    }
  }
}

.forgeonMenu {
  border-inline-end: 0 !important;
  .menuIcon {
    margin-right: 8px;
  }
  :global {
    .ant-menu-submenu.ant-menu-submenu-inline.ant-menu-submenu-open {
      .ant-menu-item.ant-menu-item-only-child:last-of-type {
        margin-bottom: 0;
      }
    }
    .ant-menu-submenu.ant-menu-submenu-inline .ant-menu-submenu-title {
      margin-bottom: 0;
    }
    .ant-menu-title-content {
      transition: none !important;
    }
    .ant-menu-submenu-arrow {
      color: @FO-Content-Icon3;
    }
    .ant-menu-item,
    .ant-menu-submenu-title {
      padding-left: 8px;
    }
  }
}

.forgeonSubMenuPopup {
  width: 260px;
  border-radius: 8px;
  background: @FO-Container-Fill1;
  :global {
    .ant-menu {
      padding: 4px !important;
    }
  }
}

.avatarPopover {
  :global {
    .ant-menu-submenu-selected .ant-menu-title-content {
      color: @FO-Content-Text1;
    }
    .ant-menu-submenu-arrow {
      transform: translateX(6px) !important;
      color: @FO-Content-Icon1;
    }
  }
}

.avatarSubMenuPopup {
  width: 120px;
  border-radius: 8px;
  background: @FO-Container-Fill1;
  :global {
    .ant-menu {
      padding: 4px !important;
    }
  }
}
