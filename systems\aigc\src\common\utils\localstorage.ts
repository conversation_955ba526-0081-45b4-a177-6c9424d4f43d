/**
 * 从 LocalStorage 中获取一项数据.
 *
 * @param {string} key
 * @return {string}
 */
function getItem(key: string): string | null {
  try {
    return localStorage.getItem(key);
  } catch (e) {
    return null;
  }
}

/**
 * 保存数据至 LocalStorage.
 *
 * @param {string} key
 * @param {string} value
 */
function setItem(key: string, value: string): void {
  try {
    localStorage.setItem(key, value);
  } catch (e) {
    // ...
  }
}

function removeItem(key: string) {
  try {
    localStorage.removeItem(key);
  } catch (e) {
    // ...
  }
}

enum STORAGE_KEY {
  APP_THEME = 'APP_THEME_',
  /**
   * 侧边栏折叠
   */
  ASIDE_UNFOLD = 'ASIDE_UNFOLD',
  /**
   * 超分提示关闭
   */
  EXTRA_TIPS_CLOSE = 'EXTRA_TIPS_CLOSE',
  /**
   * 技术中心token
   */
  TECH_TOKEN = 'TECH_TOKEN',
  /**
   * 抠图提示禁止自动展开
   */
  REMOVAL_TIPS_AUTO_OPEN_CLOSED = 'REMOVAL_TIPS_AUTO_OPEN_CLOSED',
  /**
   * 首次进入ai生图模块
   */
  AI_IMAGE_FIRST_ENTRY = 'AI_IMAGE_FIRST_ENTRY',
  /**
   * ai生图模块tip是否隐藏
   */
  HIDDEN_AI_IMAGE_TIPS = 'SHOW_AI_IMAGE_TIPS',
  /**
   * 版本要闻所属空间选择
   */
  VERSION_NEWS_NAMESPACE = 'AI_VERSION_NEWS_NAMESPACE',
  /**
   * AI Chat 当前模型
   */
  AI_CHAT_CURRENT_MODEL = 'AI_CHAT_CURRENT_MODEL',
  /**
   * AI Chat 联网搜索状态
   */
  AI_CHAT_ONLINE_SEARCH = 'AI_CHAT_ONLINE_SEARCH',
  /**
   * AI Chat 深度思考状态
   */
  AI_CHAT_DEEP_THINK = 'AI_CHAT_DEEP_THINK',
  /**
   * AI Chat 切换模型提示是否记住
   */
  AI_CHAT_MODEL_SWITCH_TIP = 'AI_CHAT_MODEL_SWITCH_TIP',
  /**
   * 大语言警告弹窗是否展示过
   */
  LLM_WARNING_MODEL_SHOW = 'LLM_WARNING_MODEL_SHOW',
  /**
   * AI其他工具警告弹窗是否展示过
   */
  AI_TOOL_WARNING_MODEL_SHOW = 'AI_TOOL_WARNING_MODEL_SHOW',
}

export { getItem, removeItem, setItem, STORAGE_KEY };
