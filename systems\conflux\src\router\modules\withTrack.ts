import type { Router } from 'vue-router';
import { getMicroAppData, useForgeonTrackCtx } from '@hg-tech/oasis-common';

export function withTrack(router: Router) {
  if (window.__MICRO_APP_ENVIRONMENT__) {
    const trackCtx = getMicroAppData(useForgeonTrackCtx);
    router.afterEach(async (cur, from) => {
      trackCtx?.sendPV?.({
        data: {
          route_name: cur.name as string,
          route_path: cur.path,
          prv_route_name: from?.name as string,
          prv_route_path: from?.name ? from?.path : undefined,
        },
      });
    });
  } else {
    // TODO 如果需要独立启动则需要单独实现
  }
}
