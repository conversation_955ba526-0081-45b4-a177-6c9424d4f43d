import { plainVars, themeVarNames, themeVars } from './parsedFigmaVars';
import path from 'node:path';
import fs from 'node:fs/promises';
import {
  AUTO_GENERATED_COMMENT,
  formatCssVarName,
  normalizeFigmaVarName,
  toCssVarDeclares,
} from '../../src/utils/helper.ts';
import { ensureDirectoryExists } from '../helper.ts';

/**
 * 生成 css 变量文件
 */
export async function genDefineVarsFile(genDir: string) {
  await ensureDirectoryExists(genDir);
  // plain
  const plainCssContent = `${AUTO_GENERATED_COMMENT}
    
/** basic color defines  */
:root {
  ${Object.entries(plainVars).map(([key, value]) => `${formatCssVarName(key)}: ${value};`).join('\n  ')}
}\n`;
  await fs.writeFile(path.join(genDir, 'defines.less'), plainCssContent);
  // theme
  const themes = Object.entries(themeVars).map(([themeName, styles]) => ({
    themeName,
    vars: styles as Record<string, string>,
  }));
  const themeCssContentMap = themes.map(({ themeName, vars }) => ({
    themeName,
    content: `${AUTO_GENERATED_COMMENT}
    
/** map theme vars to basic var for ${themeName}.  */
:root[data-theme='${themeName}'] {
  ${toCssVarDeclares(vars).join('\n  ')}
}\n`,
  }));
  await Promise.all(themeCssContentMap.map(({ themeName, content }) => {
    return fs.writeFile(path.join(genDir, `theme-${themeName}.less`), content);
  }));
  // index
  await fs.writeFile(path.join(genDir, 'index.less'), `${AUTO_GENERATED_COMMENT}

@import './defines.less';
${themeCssContentMap.map(({ themeName }) => `@import './theme-${themeName}.less';`).join('\n')}
`);
}

/**
 * 生成 Less 变量文件
 */
export async function genLessVarFile(genDir: string) {
  const lessFileContent = `${AUTO_GENERATED_COMMENT}
  
/* plain colors  */
${Object.keys(plainVars).map((i) => `@${normalizeFigmaVarName(i)}: var(${formatCssVarName(i)});`).join('\n')}
/* color vars  */
${themeVarNames.map((i) => `@${normalizeFigmaVarName(i)}: var(${formatCssVarName(i)});`).join('\n')}
`;
  await ensureDirectoryExists(genDir);
  await fs.writeFile(path.join(genDir, 'vars.less'), lessFileContent);
}
