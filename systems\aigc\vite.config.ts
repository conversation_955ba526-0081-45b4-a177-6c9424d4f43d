import UnoCSS from 'unocss/vite';
import svgLoader from 'vite-svg-loader';
import process from 'node:process';
import mkcert from 'vite-plugin-mkcert';
import packageJSON from './package.json';
import path from 'node:path';
import { sentryVitePlugin } from '@sentry/vite-plugin';
import swc from 'unplugin-swc';
import { defineViteConfigProjectVue } from '@hg-tech/configs';

const sentryAuthToken = process.env.SENTRY_AUTH_TOKEN;

// https://vitejs.dev/config/
export default defineViteConfigProjectVue(() => ({
  esbuild: false,
  define: {
    'process.env': {
      NODE_ENV: process.env.NODE_ENV,
      HG_ENV: process.env.HG_ENV,
      VERSION: packageJSON.version,
    },
  },
  resolve: {
    alias: {
      '@': path.resolve(__dirname, './src/'),
    },
  },
  plugins: [
    swc.vite({}),
    UnoCSS(),
    svgLoader(),
    mkcert(),
    process.env.HG_ENV !== 'dev'
      ? sentryVitePlugin({
        org: 'hypergryph',
        project: 'aigc',
        authToken: sentryAuthToken,
        release: {
          name: `${packageJSON.version}${process.env.HG_ENV === 'prod' ? '' : `-${process.env.HG_ENV}`}`,
          deploy: {
            env: process.env.HG_ENV ?? 'local',
          },
        },
      })
      : null,
  ],
  css: {
    preprocessorOptions: {
      less: {
        javascriptEnabled: true,
      },
    },
    modules: {
      localsConvention: 'camelCaseOnly',
    },
  },
  server: {
    hmr: true,
    https: {
      key: path.resolve(__dirname, 'public/keys/agent2-key.pem'),
      cert: path.resolve(__dirname, 'public/keys/agent2-cert.pem'),
    },
    port: 444,
    host: true,
    proxy: {
      '^/aigc-api': {
        target:
          'https://api-aigc-biz-tech-center-efficiency-test.hypergryph.net',
        secure: false,
        changeOrigin: true,
        rewrite: (path) => path.replace(/^\/aigc-api/, '/aigc'),
      },
      '^/tts': {
        target:
          'https://api-aigc-biz-tech-center-efficiency-test.hypergryph.net',
        secure: false,
        changeOrigin: true,
      },
      '^/api': {
        target: 'https://t-tech.int.hypergryph.com',
        secure: false,
        changeOrigin: true,
      },
    },
  },
  build: {
    assetsInlineLimit: 4096,
    minify: 'terser',
    chunkSizeWarningLimit: 2048,
    sourcemap: true,
  },
}), {
  analyze: process.env.HG_ENV === 'analyze',
});
