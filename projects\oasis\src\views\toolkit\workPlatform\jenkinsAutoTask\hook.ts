export interface BuildStatusModelType {
  name: string;
  tagColor: string;
  color: string;
  classColor: string;
  icon: string;
  spin?: boolean;
}

export function useJenkinsBuildState(state: string) {
  const stateMap = {
    SUCCESS: '执行成功',
    FAILURE: '执行失败',
    ABORTED: '已终止',
    UNSTABLE: '不稳定',
    NOT_BUILT: '未构建',
    ENQUEUE: '排队中',
    RUNNING: '运行中',
  };

  const getBuildStateModel = (): BuildStatusModelType => {
    switch (state) {
      case 'SUCCESS':
        return {
          name: '执行成功',
          tagColor: 'success',
          classColor: 'c-FO-Functional-Success1-Default',
          color: 'green',
          icon: 'ant-design:check-circle-outlined',
        };
      case 'FAILURE':
        return {
          name: '执行失败',
          tagColor: 'error',
          classColor: 'c-FO-Functional-Error1-Default',
          color: 'red',
          icon: 'ant-design:close-circle-outlined',
        };
      case 'RUNNING':
        return {
          name: '执行中',
          tagColor: 'processing',
          classColor: 'c-FO-Brand-Primary-Default',
          color: 'blue',
          icon: 'ant-design:sync-outlined',
          spin: true,
        };
      case 'ABORTED':
        return {
          name: '已终止',
          tagColor: 'warning',
          classColor: 'c-FO-Functional-Warning1-Default',
          color: 'orange',
          icon: 'ant-design:minus-circle-outlined',
        };
      case 'UNSTABLE':
        return {
          name: '不稳定',
          tagColor: 'warning',
          classColor: 'c-FO-Functional-Warning1-Default',
          color: 'orange',
          icon: 'ant-design:warning-outlined',
        };
      case 'NOT_BUILT':
        return {
          name: '未构建',
          tagColor: 'warning',
          classColor: 'c-FO-Functional-Warning1-Default',
          color: 'orange',
          icon: 'ant-design:warning-outlined',
        };
      case 'ENQUEUE':
        return {
          name: '排队中',
          tagColor: 'warning',
          classColor: 'c-FO-Functional-Warning1-Default',
          color: 'orange',
          icon: 'ant-design:clock-circle-outlined',
        };
      default:
        return {
          name: '未执行',
          tagColor: 'default',
          classColor: 'text-gray',
          color: 'gray',
          icon: 'ant-design:question-circle-outlined',
        };
    }
  };
  return {
    stateName: stateMap[state],
    getBuildStateModel,
  };
}
