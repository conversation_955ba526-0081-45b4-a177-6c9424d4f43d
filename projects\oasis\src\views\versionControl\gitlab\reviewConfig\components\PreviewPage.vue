<template>
  <div class="gitlab-preview-page rounded-md bg-FO-Container-Fill1 p-4">
    <div class="flex items-center">
      <div class="flex items-center gap-[4px]">
        <template v-if="!isEditName">
          <span class="py-[6px] font-size-20px font-bold">{{ curGroup?.description }}</span>
          <Icon class="cursor-pointer" icon="bx:edit-alt" @click="changeToEditName" />
        </template>
        <template v-else>
          <Input
            ref="groupNameRef"
            v-model:value="groupName"
            placeholder="请输入名称"
            class="w-[200px]"
            size="large"
            :maxlength="20"
          />
          <BasicButton :disabled="updatingGroupName" type="text" size="small" title="撤销" @click="handleEditNameRevert">
            <Icon icon="icon-park-outline:return" />
          </BasicButton>
          <BasicButton :disabled="updatingGroupName" type="text" size="small" title="保存" @click="handleEditName">
            <LoadingOutlined v-if="updatingGroupName" />
            <Icon v-else icon="charm:tick" class="!c-FO-Functional-Success1-Default" />
          </BasicButton>
        </template>
      </div>
    </div>
    <div class="relative">
      <BorderBox>
        <template #title="{ titleClass }">
          <div :class="[titleClass]">
            <span class="mr-[6px]">
              审批配置
            </span>
            <Popconfirm :title="`确认${curGroup?.reviewEnable ? '关闭' : '开启'}审批配置？`" @confirm="() => handleSwitchConfig(GitlabConfigType.Audit, !curGroup?.reviewEnable)">
              <Switch
                :checked="curGroup?.reviewEnable"
                checkedChildren="开"
                unCheckedChildren="关"
                size="small"
              />
            </Popconfirm>
          </div>
        </template>
        <template #subTitle="{ subTitleClass }">
          <div class="flex justify-between" :class="subTitleClass">
            <div>若开启审批，MR 通过approve后会向审批人发送审批通知，审批通过后merge可通过。</div>
            <a class="ml-[4px] px-[4px] c-FO-Content-Text2" @click="handleEditAuditConfig">
              <Icon class="edit-icon" icon="bx:edit-alt" />
            </a>
          </div>
        </template>
        <div class="font-bold">
          审批人：
        </div>
        <UserDisplayRow
          :users="curGroup?.reviewerIDs"
          :userGroupNames="curGroup?.reviewerGroups"
        />
        <div v-if="curGroup?.issueQaReviewer" class="mt-[12px] flex items-center gap-[4px]">
          <FaceRecognition class="flex" theme="outline" size="22" />
          优先从单子中获取QA作为审批人
        </div>
      </BorderBox>
      <BorderBox>
        <template #title="{ titleClass }">
          <div :class="[titleClass]">
            <span class="mr-[6px]">
              Review配置
            </span>
            <Popconfirm :title="`确认${curGroup?.approveEnable ? '关闭' : '开启'}Review配置？`" @confirm="() => handleSwitchConfig(GitlabConfigType.Review, !curGroup?.approveEnable)">
              <Switch
                :checked="curGroup?.approveEnable"
                checkedChildren="开"
                unCheckedChildren="关"
                size="small"
              />
            </Popconfirm>
          </div>
        </template>
        <template #subTitle="{ subTitleClass }">
          <div class="flex justify-between" :class="subTitleClass">
            <div>若开启审查，MR创建后会向Reviewers发送审查通知，审查通过后approve可通过。</div>
            <a class="ml-[4px] px-[4px] c-FO-Content-Text2" @click="handleEditReviewConfig">
              <Icon class="edit-icon" icon="bx:edit-alt" />
            </a>
          </div>
        </template>
        <div class="font-bold">
          Reviewer：
        </div>
        <UserDisplayRow
          :users="curGroup?.approverIDs"
          :userGroupNames="curGroup?.approverGroups"
        />
        <div v-if="curGroup?.excludeSubmitter" class="mt-[12px] flex items-center gap-[4px]">
          <ReduceUser class="flex" theme="outline" size="22" />
          通知Reviewer时排除提交人
        </div>
      </BorderBox>
      <BorderBox label="审查路径">
        <template #subTitle="{ subTitleClass }">
          <div class="flex justify-between" :class="subTitleClass">
            <div>若干员的git提交包括所勾选的路径，则会依照上方配置触发审查和审批流程。</div>
            <a class="ml-[4px] px-[4px] c-FO-Content-Text2" @click="handleEditPathConfig">
              <Icon class="edit-icon" icon="bx:edit-alt" />
            </a>
          </div>
        </template>
        <AuditAuditTree
          :key="JSON.stringify(curGroup)"
          class="gitlab-preview-page__tree"
          :branchInfo="branchInfo"
          :permissionList="curGroup?.reviewPaths"
          disabled
        />
      </BorderBox>
    </div>
    <ModalHolderEditAudit />
    <ModalHolderEditReview />
    <ModalHolderEditPath />
  </div>
</template>

<script lang="ts" setup>
import { Input, Popconfirm, Switch } from 'ant-design-vue';
import { LoadingOutlined } from '@ant-design/icons-vue';
import { FaceRecognition, ReduceUser } from '@icon-park/vue-next';
import { nextTick, ref } from 'vue';
import AuditAuditTree from './AuditTree.vue';
import { GitlabConfigType, switchConfigEnable, updateReviewGroupName } from '../../../../../api/page/gitlab';
import { BorderBox } from '../../../../..//components/Form';
import { Icon } from '../../../../..//components/Icon';
import { useUserStore } from '../../../../..//store/modules/user';
import { BasicButton } from '../../../../../components/Button';
import type { BranchesBaseItemListItem, SelectReviewGroupsItem } from '../../../../../api/page/model/gitlabModel.ts';
import { useLatestPromise, useModalShow } from '@hg-tech/utils-vue';
import UserDisplayRow from '../../../../../components/UserDisplayRow.vue';
import ModalEditAuditConfig from './ModalEditAuditConfig.vue';
import ModalEditReviewConfig from './ModalEditReviewConfig.vue';
import ModalEditPathConfig from './ModalEditPathConfig.vue';

const props = defineProps<{
  repoID: number;
  branchInfo: BranchesBaseItemListItem;
  curGroup?: SelectReviewGroupsItem;
}>();

const emit = defineEmits<{
  (e: 'updated'): void;
}>();

const userStore = useUserStore();
const groupName = ref('');
const isEditName = ref(false);
const groupNameRef = ref<HTMLInputElement>();

const { execute: updateGroupName, loading: updatingGroupName } = useLatestPromise(updateReviewGroupName);

async function changeToEditName() {
  groupName.value = props.curGroup?.description || '';
  isEditName.value = true;
  await nextTick();
  groupNameRef.value?.focus();
}

function handleEditNameRevert() {
  isEditName.value = false;
}

async function handleEditName() {
  const res = await updateGroupName(userStore.getProjectId, {
    id: props.curGroup?.ID,
    name: groupName.value,
  });
  if (res?.code === 7) {
    return;
  }

  emit('updated');
  isEditName.value = false;
}

async function handleSwitchConfig(type: GitlabConfigType, enable: boolean) {
  const res = await switchConfigEnable(userStore.getProjectId, {
    id: props.curGroup?.ID,
    type,
    value: enable,
  });
  if (res?.code === 7) {
    return;
  }
  emit('updated');
}

const [ModalHolderEditAudit, showEditAuditModal] = useModalShow(ModalEditAuditConfig);
async function handleEditAuditConfig() {
  await showEditAuditModal({
    groupInfo: props.curGroup,
  });
  emit('updated');
}

const [ModalHolderEditReview, showEditReviewModal] = useModalShow(ModalEditReviewConfig);
async function handleEditReviewConfig() {
  await showEditReviewModal({
    groupInfo: props.curGroup,
  });
  emit('updated');
}

const [ModalHolderEditPath, showEditPathModal] = useModalShow(ModalEditPathConfig);
async function handleEditPathConfig() {
  await showEditPathModal({
    branchInfo: props.branchInfo,
    groupInfo: props.curGroup,
  });
  emit('updated');
}
</script>

<style lang="less" scoped>
.gitlab-preview-page {
  position: relative;

  &__card {
    display: flex;
    position: relative;
    flex-wrap: wrap;
    align-items: center;
    padding: 6px 12px;
    border-radius: 8px;
    background-color: @member-card-background;
    cursor: pointer;

    &-tag {
      display: inline-block;
      width: fit-content;
      margin: 3px 6px 3px 0;
      padding: 4px 8px;
      border: 1px solid #bbb;
      border-radius: 6px;
    }
  }

  &__tree {
    margin: 10px 20px 0 10px;
  }
}
</style>
